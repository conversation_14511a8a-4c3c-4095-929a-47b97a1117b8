<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTrackingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trackings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->nullable()->constrained('orders');
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->foreignId('review_id')->nullable()->constrained('reviews');
            $table->foreignId('cash_reward_id')->nullable()->constrained('cash_rewards');
            $table->foreignId('reward_id')->nullable()->constrained('rewards');
            $table->longText('response')->nullable();
            $table->longText('content');
            $table->string('from_email');
            $table->string('to_email');
            $table->longText('sms_content')->nullable();
            $table->integer('sms_type')->nullable();
            $table->string('from_phone')->nullable();
            $table->string('to_phone')->nullable();
            $table->string('subject');
            $table->string('lead_source');
            $table->string('form_type')->nullable();
            $table->ipAddress('ip')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trackings');
    }
}
