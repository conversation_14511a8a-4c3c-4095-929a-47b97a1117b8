@extends('layouts.admin')
@section('title',$admin_page_title)
@section('content')
<section id="sub-nav">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-6">
        <div class="block sub-nav-utility mb-0 pb-0 clearfix">
          <a href="{{ route('admin.stores.index') }}" class="btn btn-primary float-left">Close</a>
        </div>
      </div>
      <div class="col-md-6">
        <div class="block sub-nav-utility mb-0 pb-0 clearfix">
          <div class="float-right">
            <button type="submit" form="social-setting-form" class="btn btn-primary float-right" name="action" value="social-info">Save</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<section>
  <div class="container-fluid">
    <div class="col-md-12">
      <div class="block px-0">
        <a class="btn btn-primary" href="{{ route('admin.stores.edit', $item->id) }}">Main</a>
        <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'seo']) }}">SEO</a>
        <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'social']) }}">Social</a>
        <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'contact']) }}">Contact</a>
        <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'hours']) }}">Hours</a>
      </div>
    </div>
  </div>
</section>
<section>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-10">
        <div class="block block-content clearfix">
          <div class="block-content-inner clearfix">
            <form action="{{ route('admin.stores.update', [$item->id,'social']) }}" method="POST" id="social-setting-form">
              @csrf
              <div class="clerfix pt-3">
                <div id="socials">
                  @foreach ($item->socials as $key => $social)
                  <div class="form-group form-row" id="social_id_{{ $key }}">
                    <div class="col-md-2">
                      <input type="text" class="form-control" placeholder="Font Awesome icon class name" name="names[]" value="{{ $social->name }}">
                    </div>
                    <div class="col-md-3">
                      <input type="text" class="form-control" placeholder="Font Awesome icon class name" name="icons[]" value="{{ $social->icon }}">
                    </div>
                    <div class="col-md-2">
                      <input type="text" class="form-control" placeholder="Social link" name="links[]" value="{{ $social->url }}">
                    </div>
                    <div class="col-md-2">
                      <input type="text" class="form-control" placeholder="Css Class name" name="css_class_names[]" value="{{ $social->social_css_class_name }}">
                    </div>
                     <div class="col-md-2">
                      <input type="text" class="form-control" placeholder="Email icon url" name="email_icon[]" value="{{ $social->email_icon ?? '' }}">
                    </div>
                    <div class="col-md-3">
                      <button type="button" class="btn btn-danger remove-social remove-social-{{ $key }}" data-name="social_{{ $key }}" data-toggle="{{ $key }}">Remove</button>
                      <i class="{{ $social->icon }}"></i>
                    </div>
                  </div>
                  @endforeach
                </div>
                <button type='button' class="btn btn-primary add_more_social">Add Social</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
@endsection

@section('scripts')
<script>
  $('.add_more_social').click(function(e) {
    e.preventDefault();
    count = $("#socials > div").length;
    if (count == 19) {
      $('.add_more_social').remove();
    }
    if (count < 20) {
      var social_button = '<div class="form-group form-row" id="social_id_' + count + '">';
      social_button += '<div class="col-md-2">';
      social_button += '<input type="text" class="form-control" placeholder="Name" name="names[]" />';
      social_button += '</div>';
      social_button += '<div class="col-md-3">';
      social_button += '<input type="text" class="form-control" placeholder="Icon class name" name="icons[]" />';
      social_button += '</div>';
      social_button += '<div class="col-md-2">';
      social_button += '<input type="text" class="form-control" placeholder="Social link" name="links[]">';
      social_button += '</div>';
      social_button += '<div class="col-md-2">';
      social_button += '<input type="text" class="form-control" placeholder="Css Class name" name="css_class_names[]">';
      social_button += '</div>';
      social_button += '<div class="col-md-2">';

      social_button += '<input type="text" class="form-control" placeholder="Email icon" name="email_icon[]">';
      social_button += '</div>';

      social_button += '<div class="col-md-3">';
      social_button += '<button type="button" class="btn btn-danger remove-social-' + count + '" data-name="social_' + count + '" data-toggle="' + count + '">Remove</button>';
      social_button += '</div>';
      social_button += '</div>';
    } else {
      alert("your limit finish");
    }
    $('#socials').append(social_button);
    $('.remove-social-' + count).click(function(e) {
      var name = $(this).attr('data-name');
      let social_id = $(this).attr('data-toggle');
      $.confirm({
        title: 'Are you sure to remove?',
        content: 'Confirm to delete this' + ' <strong>' + name + '</strong> ' + 'permanently.',
        type: 'purple',
        buttons: {
          ok: {
            text: "Confirm",
            btnClass: 'btn-primary',
            keys: ['enter'],
            action: function() {
              $('#' + 'social_id_' + social_id).remove();
            }
          },
          cancel: function() {}
        }
      });
    });
  });
  $('.remove-social').click(function(e) {
    var name = $(this).attr('data-name');
    let social_id = $(this).attr('data-toggle');
    $.confirm({
      title: 'Are you sure to remove?',
      content: 'Confirm to delete this' + ' <strong>' + name + '</strong> ' + 'permanently.',
      type: 'purple',
      buttons: {
        ok: {
          text: "Confirm",
          btnClass: 'btn-primary',
          keys: ['enter'],
          action: function() {
            $('#' + 'social_id_' + social_id).remove();
          }
        },
        cancel: function() {}
      }
    });
  });
</script>
@endsection