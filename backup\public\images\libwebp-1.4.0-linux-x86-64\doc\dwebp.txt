4mDWEBP24m(1)                    General Commands Manual                   4mDWEBP24m(1)

1mNAME0m
       dwebp - decompress a WebP file to an image file

1mSYNOPSIS0m
       1mdwebp 22m[4moptions24m] 4minput_file.webp0m

1mDESCRIPTION0m
       This manual page documents the 1mdwebp 22mcommand.

       1mdwebp  22mdecompresses WebP files into PNG, PAM, PPM or PGM images.  Note:
       Animated WebP files are not supported.

1mOPTIONS0m
       The basic options are:

       1m-h     22mPrint usage summary.

       1m-version0m
              Print the version number (as major.minor.revision) and exit.

       1m-o 4m22mstring0m
              Specify the name of the output file (as PNG format by  default).
              Using "-" as output name will direct output to 'stdout'.

       1m-- 4m22mstring0m
              Explicitly  specify the input file. This option is useful if the
              input file starts with an '-' for instance. This option must ap-
              pear 1mlast22m.  Any other options afterward will be ignored. If  the
              input file is "-", the data will be read from 4mstdin24m instead of a
              file.

       1m-bmp   22mChange the output format to uncompressed BMP.

       1m-tiff  22mChange the output format to uncompressed TIFF.

       1m-pam   22mChange the output format to PAM (retains alpha).

       1m-ppm   22mChange the output format to PPM (discards alpha).

       1m-pgm   22mChange  the  output  format  to  PGM.  The  output  consists  of
              luma/chroma samples instead of RGB, using the IMC4 layout.  This
              option is mainly for verification and debugging purposes.

       1m-yuv   22mChange  the  output  format  to  raw YUV. The output consists of
              luma/chroma-U/chroma-V samples instead  of  RGB,  saved  sequen-
              tially as individual planes. This option is mainly for verifica-
              tion and debugging purposes.

       1m-nofancy0m
              Don't  use the fancy upscaler for YUV420. This may lead to jaggy
              edges (especially the red ones), but should be faster.

       1m-nofilter0m
              Don't use the in-loop filtering process even if it  is  required
              by  the  bitstream.  This may produce visible blocks on the non-
              compliant output, but it will make the decoding faster.

       1m-dither 4m22mstrength0m
              Specify a dithering 1mstrength 22mbetween 0 and 100. Dithering  is  a
              post-processing  effect  applied  to  chroma components in lossy
              compression.  It helps by smoothing gradients and avoiding band-
              ing artifacts.

       1m-alpha_dither0m
              If the compressed file contains a transparency  plane  that  was
              quantized during compression, this flag will allow dithering the
              reconstructed  plane  in order to generate smoother transparency
              gradients.

       1m-nodither0m
              Disable all dithering (default).

       1m-mt    22mUse multi-threading for decoding, if possible.

       1m-crop 4m22mx_position24m 4my_position24m 4mwidth24m 4mheight0m
              Crop the decoded picture to a rectangle with top-left corner  at
              coordinates  (1mx_position22m,  1my_position22m)  and size 1mwidth 22mx 1mheight22m.
              This cropping area must be fully  contained  within  the  source
              rectangle.   The top-left corner will be snapped to even coordi-
              nates if needed.  This option is  meant  to  reduce  the  memory
              needed for cropping large images.  Note: the cropping is applied
              4mbefore24m any scaling.

       1m-flip  22mFlip decoded image vertically (can be useful for OpenGL textures
              for instance).

       1m-resize22m, 1m-scale 4m22mwidth24m 4mheight0m
              Rescale  the  decoded  picture to dimension 1mwidth 22mx 1mheight22m. This
              option is mostly intended to reducing the memory needed  to  de-
              code  large  images, when only a small version is needed (thumb-
              nail, preview, etc.). Note: scaling is applied  4mafter24m  cropping.
              If either (but not both) of the 1mwidth 22mor 1mheight 22mparameters is 0,
              the value will be calculated preserving the aspect-ratio.

       1m-quiet 22mDo not print anything.

       1m-v     22mPrint extra information (decoding time in particular).

       1m-noasm 22mDisable all assembly optimizations.


1mBUGS0m
       Please     report     all     bugs     to     the     issue    tracker:
       https://bugs.chromium.org/p/webp
       Patches welcome! See this page  to  get  started:  https://www.webmpro-
       ject.org/code/contribute/submitting-patches/


1mEXAMPLES0m
       dwebp picture.webp -o output.png
       dwebp picture.webp -ppm -o output.ppm
       dwebp -o output.ppm -- ---picture.webp
       cat picture.webp | dwebp -o - -- - > output.ppm


1mAUTHORS0m
       1mdwebp 22mis a part of libwebp and was written by the WebP team.
       The   latest  source  tree  is  available  at  https://chromium.google-
       source.com/webm/libwebp

       This  manual  page  was  written  by  Pascal   Massimino   <pascal.mas-
       <EMAIL>>, for the Debian project (and may be used by others).


1mSEE ALSO0m
       1mcwebp22m(1), 1mgif2webp22m(1), 1mwebpmux22m(1)
       Please  refer  to  https://developers.google.com/speed/webp/  for addi-
       tional information.

   1mOutput file format details0m
       PAM: http://netpbm.sourceforge.net/doc/pam.html
       PGM: http://netpbm.sourceforge.net/doc/pgm.html
       PPM: http://netpbm.sourceforge.net/doc/ppm.html
       PNG: http://www.libpng.org/pub/png/png-sitemap.html#info

                               November 17, 2021                      4mDWEBP24m(1)
