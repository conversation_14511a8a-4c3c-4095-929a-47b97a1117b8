<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUsesPerCouponToPromosTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('promos') && !Schema::hasColumn('promos', 'uses_per_coupon')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->integer('uses_per_coupon')->nullable();
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('promos') && Schema::hasColumn('promos', 'uses_per_coupon')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->dropColumn('uses_per_coupon');
            });
        }
    }
}
