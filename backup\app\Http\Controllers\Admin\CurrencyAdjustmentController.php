<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\CurrencyAdjustment;
use App\Http\Controllers\Controller;

class CurrencyAdjustmentController extends Controller
{
    public function index()
    {
        $admin_page_title = 'Items';

        $adjustments = CurrencyAdjustment::all();
        $currencies = getCurrencylist(); // extend as needed

        return view('admin.currency_adjustments', compact('adjustments', 'currencies', 'admin_page_title'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'from_currency' => 'required|string|size:3|different:to_currency',
            'to_currency' => 'required|string|size:3|different:from_currency',
            'adjustment_percent' => 'required|numeric|between:-100,100',
        ]);

        CurrencyAdjustment::updateOrCreate(
            [
                'from_currency' => strtoupper($request->from_currency),
                'to_currency' => strtoupper($request->to_currency)
            ],
            [
                'adjustment_percent' => $request->adjustment_percent
            ]
        );

        return redirect()->back()->with('success', 'Adjustment saved successfully.');
    }
    // Route::delete('admin/currency-adjustments/{id}', ...)

public function destroy($id)
{
    CurrencyAdjustment::findOrFail($id)->delete();
    return back()->with('success', 'Adjustment deleted.');
}

}
