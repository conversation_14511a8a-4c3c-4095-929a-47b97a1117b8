<?php

namespace App\Listeners;

use App\Events\OrderDelivered;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendOrderDeliveredNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(OrderDelivered $event)
    {
        $this->notifier->sendCustomerOrderDeliveredNotification($event->order, $event->cart, $event->message_status);
        //$this->notifier->sendAdminOrderDeliveredNotifications($event->order, $event->cart);
    }
}
