<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductAttribute;
use App\Models\ProductAttributeItem;
use Illuminate\Http\Request;
use Validator;

class ProductAttributeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Attributes';
        $items = ProductAttribute::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        foreach($items as $item){
            $attribute_item_exist = ProductAttributeItem::where('product_attribute_id', $item->id)->exists();
            $item->button_text = 'Add Items';
            if($attribute_item_exist){
                $item->button_text = 'Edit Items';
            }
        }

        if ($request->view == 'html') {
            return view('admin.jquery_live.attributes', compact('items', 'sl'));
        }

        return view('admin.product.attributes.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'description' => 'nullable|string|max:4000',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'failed',
                'messages' => $validator->messages(),
            ], 200);
        }

        $item = new ProductAttribute;
        $item->title = $request->get('title');
        $item->description = $request->get('description');
        $item->ordering = $request->get('ordering') ?? 0;
        $item->status = $request->get('status') ?? 0;
        $item->save();

        $message = '<strong>' . $item->title . '</strong> added successful';
        return response()->json([
            'item' => $item,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductAttribute  $productAttribute
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = ProductAttribute::find($id);
        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductAttribute  $productAttribute
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'description' => 'nullable|string|max:4000',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'failed',
                'messages' => $validator->messages(),
            ], 200);
        }

        $item = ProductAttribute::findOrFail($id);
        $item->title = $request->get('title');
        $item->description = $request->get('description');
        $item->ordering = $request->get('ordering') ?? 0;
        $item->status = $request->get('status') ?? 0;
        $item->save();

        $message = '<strong>' . $item->title . '</strong> update successful';
        return response()->json([
            'item' => $item,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductAttribute  $productAttribute
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = ProductAttribute::findOrFail($id);
        $item->delete();

        $message = '<strong>' . $item->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }
}
