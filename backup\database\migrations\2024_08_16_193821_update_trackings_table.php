<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTrackingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('trackings', function (Blueprint $table) {
            $table->foreignId('order_id')->onDelete('cascade')->change();
            $table->foreignId('user_id')->onDelete('cascade')->change();
            $table->foreignId('review_id')->onDelete('cascade')->change();
            $table->foreignId('cash_reward_id')->onDelete('cascade')->change();
            $table->foreignId('reward_id')->onDelete('cascade')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
