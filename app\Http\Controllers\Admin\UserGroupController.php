<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AlertPreference;
use App\Models\UserGroup;
use App\Models\Component;
use App\Models\UserAccessKey;
use App\Models\UserPermission;
use Illuminate\Http\Request;

class UserGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'User Groups';
        $access_permissions = UserAccessKey::where(['status' => true, 'type' => 'access'])->get();
        $alert_preferences = AlertPreference::whereStatus(true)->get();
        $items = UserGroup::paginate($this->itemPerPage);

        $items->each(function ($item) use ($access_permissions) {
            $user_access_permissions = explode(',', $item->user_permission_accesses_id);
            $accesses_title = '';
            foreach ($access_permissions as $permission) {
                if (in_array($permission->id, $user_access_permissions)) {
                    $accesses_title .= $permission->title . ", ";
                }
            }
            $item->access = substr($accesses_title, 0, -2) . "";
        });
        // dd($items);
        $sl = SLGenerator($items);

        return view('admin.setting.user_group.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create User Group';
        $components = Component::whereStatus(true)->get();
        // $alert_preferences = AlertPreference::whereStatus(true)->get();
        $permissions = UserAccessKey::whereStatus(true)->where('type', 'component')->get();
        $accesses = UserAccessKey::whereStatus(true)->where('type', 'access')->get();
        // $preferences = UserAccessKey::whereStatus(true)->where('type', 'preference')->get();
        // $user_groups = UserGroup::get();
        $user_groups = $this->userGroups();
        return view('admin.setting.user_group.create', compact('user_groups', 'components', 'permissions','accesses', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'user_group_parent_id' => 'required|exists:user_groups,id',
            'access.*' => 'required|integer|exists:user_access_keys,id',
            'permissions.*' => 'nullable|string',
            'title' => 'required|string',
            'status' => 'required|boolean',
            'btn' => 'required|string'
        ]);

        $userAccessPermissionsArray = $request->get('access');
        $userAccessPermissionsStr = "";
        foreach ($userAccessPermissionsArray as $access) {
            $userAccessPermissionsStr .= $access . ",";
        }
        $userAccessPermissionsStr = substr($userAccessPermissionsStr, 0, -1);

        $item = new UserGroup();
        $item->user_permission_accesses_id = $userAccessPermissionsStr;
        $item->title = $request->get('title');
        $item->status = $request->get('status');
        $item->user_group_parent_id = $request->get('user_group_parent_id');
        $item->save();

        $componentPermissionArray = $request->get('permissions');

        if (is_array($componentPermissionArray)) {
            foreach ($componentPermissionArray as $componentPermission) {
                $componentPermissionArray = explode('-', $componentPermission);

                $componentPermission = new UserPermission();
                $componentPermission->user_group_id = $item->id;
                $componentPermission->component_id = $componentPermissionArray[0];
                $componentPermission->user_permission_components_id = $componentPermissionArray[1];
                $componentPermission->save();
            }
        }

        return (($request->btn == 'save') ? back() : redirect()->route('admin.user-groups.index'))->with('success', 'New User Group Added Successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserGroup  $userGroup
     * @return \Illuminate\Http\Response
     */
    public function show(UserGroup $userGroup)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\UserGroup  $userGroup
     * @return \Illuminate\Http\Response
     */
    public function edit(UserGroup $userGroup)
    {
        $admin_page_title = 'Edit ' . $userGroup->title;
        $components = Component::whereStatus(true)->get();
        // $alert_preferences = AlertPreference::whereStatus(true)->get();
        $permissions = UserAccessKey::whereStatus(true)->where('type', 'component')->get();
        $accesses = UserAccessKey::whereStatus(true)->where('type', 'access')->get();
        // $preferences = UserAccessKey::whereStatus(true)->where('type', 'preference')->get();
        // $user_groups = UserGroup::get();
        $user_groups = $this->userGroups();

        $user_access_permissions = explode(',', $userGroup->user_permission_accesses_id);
        $user_group_permissions = UserPermission::where('user_group_id', $userGroup->id)->get();

        $user_group_permissions_array = [];
        foreach ($user_group_permissions as $user_group_permission) {
            if (!array_key_exists($user_group_permission->component_id, $user_group_permissions_array)) {
                $user_group_permissions_array[$user_group_permission->component_id] = [];
            }
            array_push($user_group_permissions_array[$user_group_permission->component_id], $user_group_permission->user_permission_components_id);
        }
        // dd($userGroup);
        return view('admin.setting.user_group.edit', compact('user_groups', 'components', 'permissions', 'accesses', 'userGroup', 'user_access_permissions', 'user_group_permissions', 'user_group_permissions_array', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserGroup  $userGroup
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UserGroup $userGroup)
    {
        $this->validate($request, [
            'user_group_parent_id' => 'required|exists:user_groups,id',
            'access.*' => 'required|integer|exists:user_access_keys,id',
            'permissions.*' => 'nullable|string',
            'title' => 'required|string',
            'status' => 'required|boolean',
            'btn' => 'required|string'
        ]);

        $userAccessPermissionsArray = $request->get('access');
        $userAccessPermissionsStr = "";
        foreach ($userAccessPermissionsArray as $access) {
            $userAccessPermissionsStr .= $access . ",";
        }
        $userAccessPermissionsStr = substr($userAccessPermissionsStr, 0, -1);
        // dd($request->get('user_group_parent_id'));
        // $userGroup = UserRole::findOrFail($id);
        $userGroup->user_permission_accesses_id = $userAccessPermissionsStr;
        $userGroup->title = $request->get('title');
        $userGroup->user_group_parent_id = $request->get('user_group_parent_id');
        $userGroup->status = $request->get('status');
        $userGroup->update();

        $componentPermissionArray = $request->get('permissions');

        UserPermission::where('user_group_id', $userGroup->id)->delete();

        if (is_array($componentPermissionArray)) {
            foreach ($componentPermissionArray as $componentPermission) {
                $componentPermissionArray = explode('-', $componentPermission);

                $componentPermission = new UserPermission();
                $componentPermission->user_group_id = $userGroup->id;
                $componentPermission->component_id = $componentPermissionArray[0];
                $componentPermission->user_permission_components_id = $componentPermissionArray[1];
                $componentPermission->save();
            }
        }

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.user-groups.index'))->with('success', 'User Group updated successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UserGroup  $userGroup
     * @return \Illuminate\Http\Response
     */
    public function destroy(UserGroup $userGroup)
    {
        UserRole::findOrFail($userGroup->id)->delete();
        UserPermission::where('user_group_id', $userGroup->id)->delete();

        $access_permissions = UserAccessKey::where(['status' => true, 'type' => 'access'])->get();
        $items = UserGroup::paginate($this->itemPerPage);

        $items->each(function ($item) use ($access_permissions) {
            $user_access_permissions = explode(',', $item->user_permission_accesses_id);
            $accesses_title = '';
            foreach ($access_permissions as $permission) {
                if (in_array($permission->id, $user_access_permissions)) {
                    $accesses_title .= $permission->title . ", ";
                }
            }
            $item->access = substr($accesses_title, 0, -2) . "";
        });

        $sl = SLGenerator($items);

        return view('admin.jquery_live.user_roles', compact('items', 'sl'));
    }
}
