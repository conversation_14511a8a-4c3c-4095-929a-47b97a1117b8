<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'coupon_type',
        'code',
        'value_type',
        'discount_type',
        'discount_amount',
        'description',
        'status',
        'customer_groups',
        'guest_checkout',
        'condition_type',
        'min_subtotal',
        'uses_per_coupon',
        'uses_per_customer',
        'from_date',
        'to_date',
        'never_expire',
        'image',
        'show_in_promo_popup'
    ];

    protected $casts = [
        'customer_groups' => 'array',
        'guest_checkout' => 'boolean',
        'never_expire' => 'boolean',
        'show_in_promo_popup' => 'boolean',
        'from_date' => 'date',
        'to_date' => 'date',
        'discount_amount' => 'decimal:2',
        'min_subtotal' => 'decimal:2'
    ];

    /**
     * Relationships
     */
    public function productCategories()
    {
        return $this->belongsToMany(ProductCategory::class, 'coupon_product_categories');
    }

    public function productBrands()
    {
        return $this->belongsToMany(ProductBrand::class, 'coupon_product_brands');
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'coupon_products');
    }

    public function productItems()
    {
        return $this->belongsToMany(ProductItem::class, 'coupon_product_items');
    }

    public function productAttributes()
    {
        return $this->belongsToMany(ProductAttribute::class, 'coupon_product_attributes');
    }

    public function couponUsages()
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Helper Methods
     */
    
    /**
     * Get discount text for display
     */
    public function getDiscountText(): string
    {
        if ($this->value_type === 'free_shipping') {
            return 'Free Shipping';
        }

        if (in_array($this->discount_type, ['percentage', 'percent'])) {
            return number_format($this->discount_amount, 0) . '% OFF';
        }

        return getDefaultCurrencySymbol() . number_format($this->discount_amount, 2) . ' OFF';
    }

    /**
     * Get condition description for display
     */
    public function getConditionDescription(): string
    {
        switch ($this->condition_type) {
            case 'subtotal':
                if ($this->min_subtotal) {
                    return 'Minimum order of ' . getDefaultCurrencySymbol() . number_format($this->min_subtotal, 2);
                }
                return 'No minimum order required';

            case 'category':
                $categories = $this->productCategories()->pluck('name')->toArray();
                if (!empty($categories)) {
                    return 'Valid for: ' . implode(', ', $categories);
                }
                return 'Valid for selected categories';

            case 'brand':
                $brands = $this->productBrands()->pluck('name')->toArray();
                if (!empty($brands)) {
                    return 'Valid for: ' . implode(', ', $brands);
                }
                return 'Valid for selected brands';

            case 'product':
                $productCount = $this->products()->count();
                return 'Valid for ' . $productCount . ' selected product' . ($productCount !== 1 ? 's' : '');

            case 'attribute':
                $attributeCount = $this->productAttributes()->count();
                return 'Valid for products with specific attributes';

            case 'none':
            default:
                return 'Valid for all items';
        }
    }

    /**
     * Check if coupon is currently valid (date-wise)
     */
    public function isDateValid(): bool
    {
        if ($this->never_expire) {
            return true;
        }

        $now = now();
        
        if ($this->from_date && $now->lt($this->from_date)) {
            return false;
        }
        
        if ($this->to_date && $now->gt($this->to_date)) {
            return false;
        }

        return true;
    }

    /**
     * Check if coupon usage limit is reached
     */
    public function isUsageLimitReached(): bool
    {
        if (!$this->uses_per_coupon) {
            return false;
        }

        $totalUsage = $this->couponUsages()->count();
        return $totalUsage >= $this->uses_per_coupon;
    }

    /**
     * Check if user has reached usage limit for this coupon
     */
    public function hasUserReachedLimit(?int $userId): bool
    {
        if (!$userId || !$this->uses_per_customer) {
            return false;
        }

        $userUsage = $this->couponUsages()->where('user_id', $userId)->count();
        return $userUsage >= $this->uses_per_customer;
    }

    /**
     * Get remaining uses for this coupon
     */
    public function getRemainingUses(): ?int
    {
        if (!$this->uses_per_coupon) {
            return null; // Unlimited
        }

        $used = $this->couponUsages()->count();
        return max(0, $this->uses_per_coupon - $used);
    }

    /**
     * Get remaining uses for a specific user
     */
    public function getRemainingUsesForUser(?int $userId): ?int
    {
        if (!$userId || !$this->uses_per_customer) {
            return null; // Unlimited
        }

        $used = $this->couponUsages()->where('user_id', $userId)->count();
        return max(0, $this->uses_per_customer - $used);
    }

    /**
     * Scope for active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for currently valid coupons (considering dates)
     */
    public function scopeCurrentlyValid($query)
    {
        return $query->where(function ($q) {
            $q->where('never_expire', true)
              ->orWhere(function ($subQuery) {
                  $subQuery->where('from_date', '<=', now())
                           ->where('to_date', '>=', now());
              });
        });
    }

    /**
     * Scope for promo popup coupons
     */
    public function scopeForPromoPopup($query)
    {
        return $query->where('show_in_promo_popup', true)
                     ->active()
                     ->currentlyValid();
    }

    /**
     * Get formatted expiry date
     */
    public function getFormattedExpiryDate(): ?string
    {
        if ($this->never_expire) {
            return 'Never expires';
        }

        if ($this->to_date) {
            return $this->to_date->format('M j, Y');
        }

        return null;
    }

    /**
     * Calculate discount for a given amount
     */
    public function calculateDiscount(float $amount): float
    {
        if ($this->value_type === 'free_shipping') {
            return 0; // Handle shipping separately
        }

        if (in_array($this->discount_type, ['percentage', 'percent'])) {
            return round($amount * ($this->discount_amount / 100), 2);
        }

        // Fixed amount - don't exceed the amount
        return min($this->discount_amount, $amount);
    }

    /**
     * Check if coupon is applicable to guest checkout
     */
    public function isGuestCheckoutAllowed(): bool
    {
        return $this->guest_checkout;
    }

    /**
     * Get coupon status badge color for admin
     */
    public function getStatusBadgeColor(): string
    {
        switch ($this->status) {
            case 'active':
                return 'success';
            case 'inactive':
                return 'secondary';
            default:
                return 'secondary';
        }
    }

    /**
     * Get condition type display name
     */
    public function getConditionTypeDisplayName(): string
    {
        switch ($this->condition_type) {
            case 'none':
                return 'No conditions';
            case 'subtotal':
                return 'Minimum subtotal';
            case 'category':
                return 'Specific categories';
            case 'brand':
                return 'Specific brands';
            case 'product':
                return 'Specific products';
            case 'attribute':
                return 'Product attributes';
            default:
                return ucfirst($this->condition_type);
        }
    }
}