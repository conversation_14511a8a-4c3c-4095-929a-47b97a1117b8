<?php

namespace App\Listeners;

use App\Events\OrderStatusChange;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendOrderStatusChangeNotifications implements ShouldQueue
{
    protected $notifier;

    
    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(OrderStatusChange $event)
    {
        $this->notifier->sendCustomerOrderStatusChangeNotifications($event->order, $event->user, $event->message_status,  $event->template);
        //$this->notifier->sendAdminOrderDeliveredNotifications($event->order, $event->cart);
    }
}
