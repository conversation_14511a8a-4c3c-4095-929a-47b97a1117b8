<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateCountryColumnInProductItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('product_items') && !Schema::hasColumn('product_items', 'country_code')) {
            Schema::table('product_items', function (Blueprint $table) {
                $table->string('country_code')->after('product_item_images');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('product_items') && Schema::hasColumn('product_items', 'country_code')) {
            Schema::table('product_items', function (Blueprint $table) {
                $table->dropColumn('country_code');
            });
        }
    }
}
