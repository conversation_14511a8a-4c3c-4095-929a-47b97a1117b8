<?php

namespace App\Listeners;

use App\Events\ReviewFormSubmitted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendReviewFormNotifications
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\ReviewFormSubmitted  $event
     * @return void
     */
    public function handle(ReviewFormSubmitted $event)
    {
        //
    }
}
