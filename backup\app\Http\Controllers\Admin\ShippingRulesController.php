<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShippingMethod;
use App\Models\ShippingMethodOption;
use App\Models\Setting;
use App\Models\ShippingRule;

use Illuminate\Http\Request;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;
use Illuminate\Support\Facades\DB;

class ShippingRulesController extends Controller
{
    private $folderPath = 'shipping_method_options/';
    // private $optionFolderPath = 'shipping_method_options/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
{
    $admin_page_title = 'Shipping Rules';
    
    // Fetch all rules with store
    $allRules = ShippingRule::with('store')->get();
    
    // Load countries.json file
    $countriesFile = storage_path('countries.json');
    $countriesList = [];
    $allCountryCodes = [];
    
    if (file_exists($countriesFile)) {
        $countriesJson = file_get_contents($countriesFile);
        $countriesArray = json_decode($countriesJson, true);
        
        // Create a map of code => name
        foreach ($countriesArray as $country) {
            $countriesList[$country['code']] = $country['name'];
            $allCountryCodes[] = $country['code'];
        }
    }
    
    // Group and map shipping rules with country names and shipping status
    $grouped = $allRules->groupBy('store_id')->map(function ($group) use ($countriesList, $allCountryCodes) {
        $item = $group->first();
        
        // Get unique country codes for this store
        $storeCountryCodes = $group->pluck('destination_country')->unique()->values()->all();
        
        // Check if -1 is present, which means all countries are selected
        $hasAllCountriesFlag = in_array('-1', $storeCountryCodes);
        
        // Determine countries and shipping status
        $countryNames = [];
        $shippingStatus = '';
        
        if ($hasAllCountriesFlag) {
            // If -1 is present, it ships to all countries
            $shippingStatus = 'Can ship to all countries';
            $countryNames = ['All Countries'];
        } else {
            // Convert country codes to names for specific countries
            $countryNames = collect($storeCountryCodes)
                ->map(function ($code) use ($countriesList) {
                    return $countriesList[$code] ?? $code;  // fallback to code if name not found
                })->values()->all();
            
            // Determine shipping status for specific country selections
            if (count($storeCountryCodes) == count($allCountryCodes)) {
                $shippingStatus = 'Can ship to all countries';
            } else {
                // Find countries not included in the store's shipping rules
               $totalCountries = count($allCountryCodes);
$excludedCountryCodes = array_diff($allCountryCodes, $storeCountryCodes);
$excludedCount = count($excludedCountryCodes);
$includedCountryCodes = array_intersect($allCountryCodes, $storeCountryCodes);
$includedCount = count($includedCountryCodes);

if ($excludedCount <= $includedCount) {
    // Prefer "all countries except..." phrasing
    if ($excludedCount === 0) {
        $shippingStatus = 'Can ship to all countries';
    } else {
        $excludedCountryNames = collect($excludedCountryCodes)
            ->map(fn($code) => $countriesList[$code] ?? $code)
            ->values()->all();

        $shippingStatus = 'Can ship to all countries except: ' . implode(', ', $excludedCountryNames);
    }
} else {
    // Prefer "only ship to..." phrasing
    $includedCountryNames = collect($includedCountryCodes)
        ->map(fn($code) => $countriesList[$code] ?? $code)
        ->values()->all();

    $shippingStatus = 'Can only ship to: ' . implode(', ', $includedCountryNames);
}

            }
        }
        
        return (object) [
            'id' => $item->id,
            'store_id' => $item->store_id,
            'site_name' => $item->store->site_name ?? 'Store #' . $item->store_id,
            'countries' => $countryNames,
            'shipping_status' => $shippingStatus,
        ];
    })->values();
    
    // Manually paginate
    $page = request()->get('page', 1);
    $perPage = $this->itemPerPage;
    $items = new \Illuminate\Pagination\LengthAwarePaginator(
        $grouped->forPage($page, $perPage),
        $grouped->count(),
        $perPage,
        $page,
        ['path' => request()->url(), 'query' => request()->query()]
    );
    
    // Generate serial numbers
    $sl = SLGenerator($items);
    
    // View rendering
    if ($request->view == 'html') {
        return view('admin.jquery_live.shipping_rules', compact('sl', 'items', 'admin_page_title'));
    }
    
    return view('admin.shipping_rules.index', compact('items', 'sl', 'admin_page_title'));
}

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create A Shipping Rule';
        $shipping_option_icon_info = Session::get('shipping_option_icon');
        $countries = $this->countries();
        $currencies = $this->currenciesList();
        $jsonPath = storage_path('bordering-countries.json');

        if (!file_exists($jsonPath)) {
            // Handle error, file not found
            abort(404, 'Countries JSON file not found.');
        }

        $jsonString = file_get_contents($jsonPath);
        $countries_neighbours = json_decode($jsonString, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // Handle JSON parse error
            abort(500, 'Invalid JSON format in countries.json');
        }
        $stores = Setting::all();
        $collection = collect(Session::get('shipping_rule_options')) ?? [];
        $shipping_rule_options = $collection->sortBy('ordering')->sortBy('ordering') ?? [];

        return view('admin.shipping_rules.create', compact('stores', 'countries_neighbours', 'shipping_rule_options', 'shipping_option_icon_info', 'countries', 'currencies', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $isSelectedAllCountries =  $request->input('selected_all_countries');
        $storeId = $request->input('store_id');
        $countries = $request->input('final_countries', []); // Array of unique country codes

        if (!$storeId || empty($countries)) {
            return back()->withErrors('Please select a store and at least one country.');
        }

        // Delete existing rules for this store first to avoid duplicates
        ShippingRule::where('store_id', $storeId)->delete();

        // Insert new rules
        $insertData = [];

        // Get the selection mode
        $isSelectedAllCountries = request('isSelectedAllCountries', '0');
        $finalCountries = request('final_countries', []);
        $excludedCountries = request('excluded_countries', []);

        if ($isSelectedAllCountries == '1') {
            // Check if we're dealing with "all countries" or "all except some"
            if (empty($excludedCountries) && (in_array('-1', $finalCountries) || empty($finalCountries))) {
                // True "all countries" case - just insert the special -1 code
                $insertData[] = [
                    'store_id' => $storeId,
                    'destination_country' => '-1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            } else {
                // "All except some" case - insert all countries from final_countries
                // The JS code will have already filtered out the excluded ones
                foreach ($finalCountries as $countryCode) {
                    $insertData[] = [
                        'store_id' => $storeId,
                        'destination_country' => $countryCode,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        } else {
            // Regular specific countries mode
            foreach ($finalCountries as $countryCode) {
                $insertData[] = [
                    'store_id' => $storeId,
                    'destination_country' => $countryCode,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }
        ShippingRule::insert($insertData);


        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.shipping-rules.index'))->with('success', 'Shipping Rule created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function show(ShippingMethod $shippingMethod)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($storeId)
    {
        $admin_page_title = 'Edit Shipping Rule';

        $allRules = ShippingRule::with('store')
            ->where('store_id', $storeId)
            ->get();

        $countriesFile = storage_path('countries.json');
        $countriesList = [];

        if (file_exists($countriesFile)) {
            $countriesJson = file_get_contents($countriesFile);
            $countriesArray = json_decode($countriesJson, true);

            foreach ($countriesArray as $country) {
                $countriesList[$country['code']] = $country['name'];
            }
        }

        $grouped = $allRules->groupBy('store_id')->map(function ($group) use ($countriesList) {
            $item = $group->first();

            $countryNames = $group->pluck('destination_country')
                ->unique()
                ->map(fn($code) => $countriesList[$code] ?? $code)
                ->values()->all();

            // Prepare raw country codes array
            $destinationCountries = $group->pluck('destination_country')->unique()->values()->all();

            // Included neighbours array from your model
            $includedNeighbours = $item->included_neighbours ?? [];

            return (object) [
                'id' => $item->id,
                'store_id' => $item->store_id,
                'site_name' => $item->store->site_name ?? 'Store #' . $item->store_id,
                'countries' => $countryNames,
                'destination_countries' => $destinationCountries,     // <---- add this
                'included_neighbours' => $includedNeighbours,         // <---- add this
                'status' => true
            ];
        })->values();

        $item = $grouped->first();

        // Prepare previewData for JS
        $previewData = [];
        if (!empty($item->destination_countries)) {
            foreach ($item->destination_countries as $code) {
                $previewData[$code] = $item->included_neighbours[$code] ?? [];
            }
        }
        $stores = Setting::all();
        $jsonPath = storage_path('bordering-countries.json');

        if (!file_exists($jsonPath)) {
            // Handle error, file not found
            abort(404, 'Countries JSON file not found.');
        }

        $jsonString = file_get_contents($jsonPath);
        $countries_neighbours = json_decode($jsonString, true);
        return view('admin.shipping_rules.edit', compact('stores', 'countries_neighbours', 'item', 'countriesList', 'admin_page_title', 'previewData'));
    }



    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {

        // dd($request->all());

        $storeId = $request->input('store_id');
        $countries = $request->input('final_countries', []); // Array of unique country codes
        $isSelectedAllCountries =  $request->input('selected_all_countries');

        if (!$storeId || empty($countries)) {
            return back()->withErrors('Please select a store and at least one country.');
        }

        // Delete existing rules for this store first to avoid duplicates
        ShippingRule::where('store_id', $storeId)->delete();

        // Insert new rules
        $insertData = [];

        // Get the selection mode
        $isSelectedAllCountries = request('isSelectedAllCountries', '0');
        $finalCountries = request('final_countries', []);
        $excludedCountries = request('excluded_countries', []);

        if ($isSelectedAllCountries == '1') {
            // Check if we're dealing with "all countries" or "all except some"
            if (empty($excludedCountries) && (in_array('-1', $finalCountries) || empty($finalCountries))) {
                // True "all countries" case - just insert the special -1 code
                $insertData[] = [
                    'store_id' => $storeId,
                    'destination_country' => '-1',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            } else {
                // "All except some" case - insert all countries from final_countries
                // The JS code will have already filtered out the excluded ones
                foreach ($finalCountries as $countryCode) {
                    $insertData[] = [
                        'store_id' => $storeId,
                        'destination_country' => $countryCode,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        } else {
            // Regular specific countries mode
            foreach ($finalCountries as $countryCode) {
                $insertData[] = [
                    'store_id' => $storeId,
                    'destination_country' => $countryCode,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }
        ShippingRule::insert($insertData);



        $message = 'Shipping Rules successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.shipping-rules.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function destroy($store_id)
    {

        // Fetch items with the given store_id
        $items = ShippingRule::where('store_id', $store_id)->get();

        if ($items->isEmpty()) {
            return response()->json([
                'message' => 'No shipping rules found for this store.',
                'status' => 'error',
            ], 404);
        }

        // Delete all matching items
        ShippingRule::where('store_id', $store_id)->delete();

        return response()->json([
            'message' => 'All shipping rules for the store have been deleted.',
            'status' => 'success',
        ], 200);
    }
}
