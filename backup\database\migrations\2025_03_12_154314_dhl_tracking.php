<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DhlTracking extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dhl_trackings', function (Blueprint $table) {
            $table->id();
            $table->string('shipmentTrackingNumber')->nullable();
            $table->string('canacelPickupUrl')->nullable();
            $table->string('trackingUrl')->nullable();
            $table->json('packages')->nullable();   
            $table->json('documents')->nullable();
            $table->string('estimateDeliveryDate')->nullable();
            $table->unsignedBigInteger('order_id')->nullable(); // Foreign key column
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dhl_trackings');
    }
}
