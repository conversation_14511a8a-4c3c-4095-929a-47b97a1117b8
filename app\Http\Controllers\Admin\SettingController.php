<?php



namespace App\Http\Controllers\Admin;



use Validator;

use ArrayIterator;

use MultipleIterator;

use App\Models\Setting;

use Illuminate\Http\Request;

use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\Auth;

use App\Models\GeneralSetting;


class SettingController extends Controller

{

    public $path = 'setting/';



    public function index()

    {

        $admin_page_title = 'Stores';



        // dd($this->stores());



        $items  = collect($this->stores())->paginate($this->itemPerPage);

        // $items  = Setting::where('id', '!=', 1)->paginate($this->itemPerPage);

        foreach ($items as $value) {

            $value->details = $value->site_name . '<br />' . $value->email . ' ' . $value->tel_cell_country_code . $value->tel_cell;
        }

        $sl = SLGenerator($items);

        return view('admin.setting.store.index', compact('sl', 'items', 'admin_page_title'));
    }



    public function show(Page $page)

    {

        abort('404');
    }



    public function create()
    {

        $admin_page_title = 'New Store';

        $times_zones = $this->timezones();

        return view('admin.setting.store.new', compact('admin_page_title', 'times_zones'));
    }



    public function store(Request $request)
    {

        // dd($request->all());

        $this->validate($request, [

            'site_name' => 'required|string',

            'site_slogan' => 'nullable|string',

            'terms_condition' => 'required|string',

            'site_time_zone' => 'nullable|string',

            'site_currency' => 'nullable|string',

            'order_return_days' => 'nullable|integer',

            'order_warranty_days' => 'nullable|integer',

            'site_link' => 'nullable|string',

            'country' => 'nullable|string',
            'price_range' => 'nullable|string'

        ]);



        $item = new Setting();

        $item->site_name = $request->site_name;

        $item->site_slogan = $request->site_slogan;

        $item->terms_condition = $request->terms_condition;

        $item->site_time_zone = $request->site_time_zone;

        $item->site_currency = $request->site_currency;

        $item->order_return_days = $request->order_return_days;

        $item->order_warranty_days = $request->order_warranty_days;

        $item->site_link = $request->site_link;

        $item->country = $request->country;
        $item->price_range = $request->price_range;
        $item->store_pickup = $request->get('store_pickup') ?? 0;

        $item->save();



        $message = 'Store successfully added.';

        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.stores.index')->with('success', $message);
    }



    public function edit($id, $type = null)

    {

        $admin_page_title = 'Edit Store';

        // $item = $setting;

        // return view('admin.setting.site_info', compact('admin_page_title', 'item'));



        // $admin_page_title = 'General';

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');



        $item = Setting::find($id);

        $times_zones = $this->timezones();

        $countries = $this->countries();

        $parent_store_list = Setting::whereNotIn('id', [1, $id])->whereNull('parent_store_id')->get();

        // dd($item);



        if ($type == 'seo') {

            $admin_page_title = 'Seo';

            return view('admin.setting.store.seo_info', compact('item', 'countries', 'parent_store_list', 'admin_page_title'));
        } else if ($type == 'social') {

            $admin_page_title = 'Socials';

            $item->socials = $item->socials ? unserialize($item->socials) : array();

            return view('admin.setting.store.social_info', compact('item', 'countries', 'parent_store_list', 'admin_page_title'));
        } else if ($type == 'contact') {

            $admin_page_title = 'Contact';

            $item->setting_meta = unserialize($item->setting_meta);

            return view('admin.setting.store.contact_info', compact('item', 'countries', 'parent_store_list', 'admin_page_title'));
        } else if ($type == 'hours') {

            $admin_page_title = 'Business Hours';

            $item->business_hours = $item->business_hours ? unserialize($item->business_hours) : array();

            return view('admin.setting.store.business_hours', compact('item', 'countries', 'parent_store_list', 'admin_page_title'));
        }



        return view('admin.setting.store.edit', compact('item', 'times_zones', 'countries', 'parent_store_list', 'admin_page_title'));
    }



    public function update(Request $request, $id, $type = null)

    {



        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');



        if ($type == 'seo') {

            $this->validate($request, [

                'meta_title' => 'nullable|string',

                'meta_keywords' => 'nullable|string',

                'meta_description' => 'nullable|string'

            ]);
        } else if ($type == 'social') {
        } else if ($type == 'contact') {

            $this->validate($request, [

                "country_code" => "nullable|string",

                'tel_cell' => 'nullable|digits:10',

                'email' => 'nullable|email',

                'country' => 'required|string',

                'state' => 'required|string',

                'city' => 'required|string',

                'zip_code' => 'required|string',

                'address' => 'required|string',

                'address_2' => 'nullable|string'

            ]);
        } else if ($type == 'hours') {
        } else {

            $this->validate($request, [

                'site_logo' => 'nullable|image|mimes:jpg,jpeg,gif,png,svg,webp|max:5120',

                'admin_logo' => 'nullable|image|mimes:jpg,jpeg,gif,png,svg,webp|max:5120',

                'email_template_logo' => 'nullable|mimes:jpg,jpeg,gif,png,svg|image|max:5120',

                'favicon' => 'nullable|image|mimes:jpg,jpeg,gif,png,svg,webp|max:5120',

                'site_name' => 'required|string',

                'site_slogan' => 'nullable|string',

                'terms_condition' => 'required|string',

                'site_time_zone' => 'nullable|string',

                'site_currency' => 'nullable|string',

                'order_return_days' => 'nullable|integer',

                'order_warranty_days' => 'nullable|integer',

                'site_link' => 'nullable|string',
                'country' => 'nullable|string',
                'price_range' => 'nullable|string',
            ]);
        }





        if ($request->hasFile('site_logo')) {

            $this->deleteFile($this->path, site_info('site_logo'));

            $logoName = 'logo.' . $request->site_logo->extension();

            $request->site_logo->storeAs($this->path, $logoName);
        }



        if ($request->hasFile('email_template_logo')) {

            $this->deleteFile($this->path, site_info('email_template_logo'));

            $logoName = 'email_template_logo.' . $request->site_logo->extension();

            $request->site_logo->storeAs($this->path, $logoName);
        }



        // Storing Admin Logo

        if ($request->hasFile('admin_logo')) {

            $this->deleteFile($this->path, site_info('admin_logo'));

            $adminLogoName = 'admin_logo.' . $request->admin_logo->extension();

            $request->admin_logo->storeAs($this->path, $adminLogoName);
        }



        // Storing Favicon

        if ($request->hasFile('favicon')) {

            $this->deleteFile($this->path, site_info('favicon'));

            $faviconName = 'favicon.' . $request->favicon->extension();

            $request->favicon->storeAs($this->path, $faviconName);
        }



        // Storing OfflineLogo

        if ($request->hasFile('offline_logo')) {

            $this->deleteFile($this->path, site_info('offline_logo'));

            $offlineLogoName = 'offline_logo.' . $request->offline_logo->extension();

            $request->offline_logo->storeAs($this->path, $offlineLogoName);
        }



        // Saving Data

        $setting =  Setting::find($id);



        if ($type == 'seo') {

            $setting->meta_title = $request->get('meta_title');

            $setting->meta_keywords = $request->get('meta_keywords');

            $setting->meta_description = $request->get('meta_description');
        } else if ($type == 'social') {

            $get_socials = null;

            if ($request->links) {

                $social_icons_array = new ArrayIterator($request->icons);

                $social_links_array = new ArrayIterator($request->links);

                $social_names_array = new ArrayIterator($request->names);

                $social_css_class_names_array = new ArrayIterator($request->css_class_names);

                $social_email_icon_array = new ArrayIterator($request->email_icon);


                $socials = new MultipleIterator;

                $socials->attachIterator($social_icons_array);

                $socials->attachIterator($social_links_array);

                $socials->attachIterator($social_names_array);

                $socials->attachIterator($social_css_class_names_array);

                $socials->attachIterator($social_email_icon_array);


                foreach ($socials as $social) {

                    $social_key = array('icon', 'url', 'name', 'social_css_class_name', 'email_icon');

                    $get_socials[] = (object)array_combine($social_key, $social);
                }
            }

            $setting->socials = serialize((object)$get_socials);
        } else if ($type == 'contact') {

            $get_more_tel_cells = array();

            if ($request->more_tel_cell) {

                $more_tel_cells_array = new ArrayIterator($request->more_tel_cell);

                $more_tel_cells = new MultipleIterator;

                $more_tel_cells->attachIterator($more_tel_cells_array);





                foreach ($more_tel_cells as $more_tel_cell) {

                    $more_tel_cell_key = array('extra_tel_cell');

                    $get_more_tel_cells[] = (object)array_combine($more_tel_cell_key, $more_tel_cell);
                }
            }



            $get_more_emails = array();

            if ($request->more_email) {

                $more_emails_array = new ArrayIterator($request->more_email);

                $more_emails = new MultipleIterator;

                $more_emails->attachIterator($more_emails_array);



                foreach ($more_emails as $more_email) {

                    $more_email_key = array('extra_email');

                    $get_more_emails[] = (object)array_combine($more_email_key, $more_email);
                }
            }



            $setting_meta = (object)[

                'extra_tel_cells' => $get_more_tel_cells,

                'extra_emails' => $get_more_emails,

            ];



            $setting->tel_cell = $request->get('tel_cell');

            if ($setting->tel_cell) {

                //$setting->tel_cell_country_code = '+' . $request->get('tel_cell_country_code');
            }

            $setting->email = $request->get('email');

            $setting->country = $request->get('country');

            $setting->city = $request->get('city');

            $setting->state = $request->get('state');

            $setting->zip_code = $request->get('zip_code');

            $setting->address = $request->get('address');

            $setting->address_2 = $request->get('address_2');

            $setting->setting_meta = serialize($setting_meta);
        } else if ($type == 'hours') {



            $day_name_array = new ArrayIterator($request->days);

            $day_status_array = new ArrayIterator($request->status);

            $day_start_time_array = new ArrayIterator($request->start_time);

            $day_end_time_array = new ArrayIterator($request->end_time);



            $business_hours = new MultipleIterator;

            $business_hours->attachIterator($day_name_array);

            $business_hours->attachIterator($day_status_array);

            $business_hours->attachIterator($day_start_time_array);

            $business_hours->attachIterator($day_end_time_array);



            $get_business_hours = null;

            foreach ($business_hours as $business_hour) {

                $day_name_key = array('day_name', 'status', 'start_time', 'end_time');

                $get_business_hours[] = (object)array_combine($day_name_key, $business_hour);
            }





            $setting->business_hours = serialize($get_business_hours);

            //  @dd($setting->business_hours);

            $setting->business_hours_description = $request->business_hours_description;
        } else {

            $setting->site_name = $request->get('site_name');

            if ($request->hasFile('site_logo')) {

                $setting->site_logo = $logoName;
            }

            if ($request->hasFile('admin_logo')) {

                $setting->admin_logo = $adminLogoName;
            }

            if ($request->hasFile('favicon')) {

                $setting->favicon = $faviconName;
            }

            if ($request->hasFile('offline_logo')) {

                $setting->offline_logo = $offlineLogoName;
            }



            $setting->country = $request->get('country');

            $setting->status = $request->get('status') ?? 0;

            $setting->default_store = $request->get('default_store') ?? 0;

            $setting->ordering = $request->get('ordering') ?? 0;

            $setting->parent_store_id = $request->get('parent_store_id');

            $setting->site_slogan = $request->get('site_slogan');

            $setting->terms_condition = $request->get('terms_condition');

            $setting->site_time_zone = $request->get('site_time_zone');

            $setting->site_currency = $request->get('site_currency');

            $setting->order_return_days = $request->get('order_return_days');

            $setting->order_warranty_days = $request->get('order_warranty_days');
            $setting->site_link = $request->get('country');

            $setting->site_link = $request->get('site_link');
            $setting->price_range = $request->get('price_range');
        }
        $setting->store_pickup = $request->get('store_pickup') ?? 0;

        $setting->update();





        return back()->with('success', 'General Setting Saved Successful.');
    }



    public function editSite()

    {

        $admin_page_title = 'General';

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');



        $siteInfo = Setting::first();

        $times_zones = $this->timezones();

        $countries = $this->countries();

        $currencies = storage_path('currency.json');
        $currencyList = [];

        if (file_exists($currencies)) {
            $currenciesJson = file_get_contents($currencies);
            $currenciesArray = json_decode($currenciesJson, true);

            // Create a map of code => name
            foreach ($currenciesArray as $currency) {
                $currenciesList[$currency['code']] = $currency['name'];
            }
        }
        //dd($currenciesList);
        $default_currency = GeneralSetting::where('key', 'default_currency')->value('value') ?? 'USD';

        return view('admin.setting.site_info', compact('siteInfo', 'times_zones', 'countries', 'admin_page_title', 'currenciesList', 'default_currency'));
    }



    public function editSocial()

    {

        $admin_page_title = 'Socials';

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $siteSocialInfo = Setting::first();

        if ($siteSocialInfo->socials) {

            $siteSocialInfo->socials = unserialize($siteSocialInfo->socials);
        } else {

            $siteSocialInfo->socials = array();
        }
        return view('admin.setting.social_info', compact('siteSocialInfo', 'admin_page_title'));
    }



    public function editSeoInfo()

    {

        $admin_page_title = 'Seo';

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $siteSeoInfo = Setting::first();



        return view('admin.setting.seo_info', compact('siteSeoInfo', 'admin_page_title'));
    }



    public function editContactInfo()

    {

        $admin_page_title = 'Contact';

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $siteContactInfo = Setting::first();

        $siteContactInfo->setting_meta = unserialize($siteContactInfo->setting_meta);

        $countries = $this->countries();



        return view('admin.setting.contact_info', compact('siteContactInfo', 'countries', 'admin_page_title'));
    }



    public function editBusinessHoursInfo()

    {

        $admin_page_title = 'Business Hours';

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $siteBusinessHoursInfo = Setting::first();

        $siteBusinessHoursInfo->business_hours = unserialize($siteBusinessHoursInfo->business_hours);



        return view('admin.setting.business_hours', compact('siteBusinessHoursInfo', 'admin_page_title'));
    }



    public function updateSite(Request $request)

    {

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $this->validate($request, [

            'site_logo' => 'nullable|image|mimes:jpg,jpeg,gif,png,svg,webp|max:5120',

            'admin_logo' => 'nullable|image|mimes:jpg,jpeg,gif,png,svg,webp|max:5120',

            'email_template_logo' => 'nullable|mimes:jpg,jpeg,gif,png,svg,webp|image|max:5120',

            'favicon' => 'nullable|image|mimes:jpg,jpeg,gif,png,svg,webp|max:5120',

            'site_name' => 'required|string',

            'site_slogan' => 'nullable|string',

            // 'site_offline' => 'required|boolean',

            // 'offline_message' => 'required|string',

            // 'error_404_message' => 'required|string',

            // 'error_403_message' => 'required|string',

            'terms_condition' => 'required|string',

            // 'offline_logo' => 'nullable|image|max:5120',

            'site_time_zone' => 'nullable|string',

            'site_currency' => 'nullable|string',

            'order_return_days' => 'nullable|integer',

            'order_warranty_days' => 'nullable|integer',

            'site_link' => 'nullable|string',
            'price_range' => 'nullable|string',

        ]);

        // dd($request->all());

        // Storing Logo

        if ($request->hasFile('site_logo')) {

            $this->deleteFile($this->path, site_info('site_logo'));

            $logoName = 'logo.' . $request->site_logo->extension();

            $request->site_logo->storeAs($this->path, $logoName);
        }



        if ($request->hasFile('email_template_logo')) {

            $this->deleteFile($this->path, site_info('email_template_logo'));

            $logoName = 'email_template_logo.' . $request->site_logo->extension();

            $request->site_logo->storeAs($this->path, $logoName);
        }



        // Storing Admin Logo

        if ($request->hasFile('admin_logo')) {

            $this->deleteFile($this->path, site_info('admin_logo'));

            $adminLogoName = 'admin_logo.' . $request->admin_logo->extension();

            $request->admin_logo->storeAs($this->path, $adminLogoName);
        }



        // Storing Favicon

        if ($request->hasFile('favicon')) {

            $this->deleteFile($this->path, site_info('favicon'));

            $faviconName = 'favicon.' . $request->favicon->extension();

            $request->favicon->storeAs($this->path, $faviconName);
        }



        // Storing OfflineLogo

        if ($request->hasFile('offline_logo')) {

            $this->deleteFile($this->path, site_info('offline_logo'));

            $offlineLogoName = 'offline_logo.' . $request->offline_logo->extension();

            $request->offline_logo->storeAs($this->path, $offlineLogoName);
        }



        // Saving Data

        $setting = Setting::first();

        $setting->site_name = $request->get('site_name');

        if ($request->hasFile('site_logo')) {

            $setting->site_logo = $logoName;
        }

        if ($request->hasFile('admin_logo')) {

            $setting->admin_logo = $adminLogoName;
        }

        if ($request->hasFile('favicon')) {

            $setting->favicon = $faviconName;
        }

        if ($request->hasFile('offline_logo')) {

            $setting->offline_logo = $offlineLogoName;
        }



        $setting->site_slogan = $request->get('site_slogan');

        $setting->terms_condition = $request->get('terms_condition');

        $setting->site_time_zone = $request->get('site_time_zone');

        $setting->site_currency = $request->get('site_currency');

        $setting->order_return_days = $request->get('order_return_days');

        $setting->order_warranty_days = $request->get('order_warranty_days');

        $setting->site_link = $request->get('site_link');
        $setting->price_range = $request->get('price_range');

        $setting->update();
        GeneralSetting::updateOrCreate(
            ['key' => 'default_currency'],
            [
                'value' => $request->currency ?? 'USD',
                'type' => 'admin',
                'description' => 'Controls the default currency'
            ]
        );


        return back()->with('success', 'General Setting Saved Successful.');
    }



    public function updateSeoInfo(Request $request)

    {

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $this->validate($request, [

            'meta_title' => 'nullable|string',

            'meta_keywords' => 'nullable|string',

            'meta_description' => 'nullable|string'

        ]);



        $setting = Setting::first();

        $setting->meta_title = $request->get('meta_title');

        $setting->meta_keywords = $request->get('meta_keywords');

        $setting->meta_description = $request->get('meta_description');

        $setting->update();



        return back()->with('success', 'SEO Information Saved Successful.');
    }



    public function updateSocialInfo(Request $request)

    {

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $get_socials = null;

        if ($request->links) {

            $social_icons_array = new ArrayIterator($request->icons);

            $social_links_array = new ArrayIterator($request->links);

            $social_names_array = new ArrayIterator($request->names);

            $social_css_class_names_array = new ArrayIterator($request->css_class_names);



            $socials = new MultipleIterator;

            $socials->attachIterator($social_icons_array);

            $socials->attachIterator($social_links_array);

            $socials->attachIterator($social_names_array);

            $socials->attachIterator($social_css_class_names_array);



            foreach ($socials as $social) {

                $social_key = array('icon', 'url', 'name', 'social_css_class_name');

                $get_socials[] = (object)array_combine($social_key, $social);
            }
        }



        $setting = Setting::first();

        $setting->socials = serialize((object)$get_socials);

        $setting->update();



        return back()->with('success', 'Social Information Saved Successful.');
    }



    public function updateContactInfo(Request $request)

    {
        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $this->validate($request, [

            "country_code" => "nullable|string",

            'mobile' => 'nullable|digits:10',

            'email' => 'nullable|email',

            'country' => 'required|string',

            'state' => 'required|string',

            'city' => 'required|string',

            'zip_code' => 'required|string',

            'address' => 'required|string',

            'address_2' => 'nullable|string'

        ]);



        $get_more_tel_cells = array();

        if ($request->more_tel_cell) {

            $more_tel_cells_array = new ArrayIterator($request->more_tel_cell);

            $more_tel_cells = new MultipleIterator;

            $more_tel_cells->attachIterator($more_tel_cells_array);





            foreach ($more_tel_cells as $more_tel_cell) {

                $more_tel_cell_key = array('extra_tel_cell');

                $get_more_tel_cells[] = (object)array_combine($more_tel_cell_key, $more_tel_cell);
            }
        }



        $get_more_emails = array();

        if ($request->more_email) {

            $more_emails_array = new ArrayIterator($request->more_email);

            $more_emails = new MultipleIterator;

            $more_emails->attachIterator($more_emails_array);



            foreach ($more_emails as $more_email) {

                $more_email_key = array('extra_email');

                $get_more_emails[] = (object)array_combine($more_email_key, $more_email);
            }
        }



        $setting_meta = (object)[

            'extra_tel_cells' => $get_more_tel_cells,

            'extra_emails' => $get_more_emails,

        ];



        $setting = Setting::first();

        $setting->tel_cell = $request->get('tel_cell');

        if ($setting->tel_cell) {

            $setting->tel_cell_country_code = '+' . $request->get('tel_cell_country_code');
        }

        $setting->email = $request->get('email');

        $setting->country = $request->get('country');

        $setting->city = $request->get('city');

        $setting->state = $request->get('state');

        $setting->zip_code = $request->get('zip_code');

        $setting->address = $request->get('address');

        $setting->address_2 = $request->get('address_2');

        $setting->setting_meta = serialize($setting_meta);

        $setting->update();



        return back()->with('success', 'Contact Information Saved Successful.');
    }



    public function updateBusinessHoursInfo(Request $request)

    {

        if (!Auth::user()->permissions('setting', 'access')) return redirect()->route('admin.dashboard')->with('warning', 'you are not authorized to access this area');

        $day_name_array = new ArrayIterator($request->days);

        $day_status_array = new ArrayIterator($request->status);

        $day_start_time_array = new ArrayIterator($request->start_time);

        $day_end_time_array = new ArrayIterator($request->end_time);



        $business_hours = new MultipleIterator;

        $business_hours->attachIterator($day_name_array);

        $business_hours->attachIterator($day_status_array);

        $business_hours->attachIterator($day_start_time_array);

        $business_hours->attachIterator($day_end_time_array);



        $get_business_hours = null;

        foreach ($business_hours as $business_hour) {

            $day_name_key = array('day_name', 'status', 'start_time', 'end_time');

            $get_business_hours[] = (object)array_combine($day_name_key, $business_hour);
        }

        $setting = Setting::first();

        $setting->business_hours = serialize($get_business_hours);

        $setting->business_hours_description = $request->business_hours_description;

        $setting->update();



        return back()->with('success', 'Business Hours Information Saved Successful.');
    }



    public function fileUpload(Request $request, $id, $target)

    {

        $validator = Validator::make($request->all(), [

            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,

        ]);



        if ($validator->fails()) {

            foreach ($validator->messages()->all() as $key => $message) {

                return response()->json(

                    [

                        'status' => 'failed',

                        'message' =>  $message

                    ],

                    200

                );

                break;
            }
        }



        // $settings_file = Setting::findOrFail($target); // write for multi vendor

        $settings = Setting::findOrFail($id);

        $this->deleteFile($this->path, $settings->$target); // deleting old File

        $logoName = $target . '_' . rand(99, 999999999) . '.' . $request->$target->extension();

        $folderName = $this->folderName();

        $request->$target->storeAs($this->path . $folderName, $logoName);

        $settings->$target = $folderName . '/' . $logoName;

        $settings->update();



        $url = url('/storage/setting/' . $folderName . '/' . $logoName);



        return response()->json(

            [

                'status' => 'success',

                'file_url' => $url

            ],

            200

        );
    }



    public function fileRemove($id, $target)

    {

        // $settings_file = Setting::findOrFail($target); // write for multi vendor

        $settings = Setting::findOrFail($id);

        $this->deleteFile($this->path, $settings->$target);

        $settings->$target = null;

        $settings->update();



        return response()->json('success', 200);
    }

    public function destroy($id)
    {

        $settings = Setting::findOrFail($id);

        $settings->delete();

        return response()->json('success', 200);
    }
    public function editShippingUnitsInfo()
    {
        $admin_page_title = 'Shipping Units';

        $settings = (object) [
            'weight_unit' => GeneralSetting::where('key', 'weight_unit')->value('value') ?? 'kg',
            'dimension_unit' => GeneralSetting::where('key', 'dimension_unit')->value('value') ?? 'cm',
        ];

        return view('admin.setting.shipping_units_settings', compact('admin_page_title', 'settings'));
    }

    public function updateShippingUnitsInfo(Request $request)
    {
        // Validate inputs
        $validated = $request->validate([
            'weight_unit' => 'required|in:kg,lb,oz',
            'dimension_unit' => 'required|in:cm,in',
        ]);
        // Save weight_unit setting
        GeneralSetting::updateOrCreate(
            ['key' => 'weight_unit'],
            [
                'value' => $validated['weight_unit'],
                'type' => 'admin',  // Type, you can modify based on context
                'description' => 'Controls the default shipping Weight unit'
            ]  // Description
        );

        // Save dimension_unit setting
        GeneralSetting::updateOrCreate(
            ['key' => 'dimension_unit'],
            [
                'value' => $validated['dimension_unit'],
                'type' => 'admin',
                'description' => 'Controls the default shipping Dimension unit'
            ]
        );

        return back()->with('success', 'Shipping Units Information Saved Successful.');
    }
}
