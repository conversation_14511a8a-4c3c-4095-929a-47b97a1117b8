<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
// use App\Models\AccessLabel;
use App\Models\AlertPreference;
// use App\Models\EmailTemplate;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Validator;
use Illuminate\Support\Str;
// use Auth;

class AlertPreferencesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Subscription Types and Preferences';
        $user_groups = $this->userGroups();

        $items = AlertPreference::paginate($this->itemPerPage);
        // $items_data = array();
        foreach($items as $item){
            if($item->user_group_id){
                $groups_id = explode(',', $item->user_group_id);
                $item->group = $this->makeGroupName($groups_id);
            }
            // $item->access = $this->checkAlertPreferenceAccess($item->id);
            // $group_name_array = array();
            // if(count($groups_id) > 0){
            //     foreach($groups_id as $group_id){
            //         $group = UserGroup::find($group_id);
            //         $group_name_array[] = $group->title;
            //     }
            // }
            // if(count($group_name_array) > 0){
            //     $item->group = implode(',', $group_name_array);
            // }
            // $access_label = AccessLabel::find($item->access_label_id);
            // $item->access_label = $access_label ? $access_label->title : 'N/A';
            // $items_data[] = $item;
        }
        // $accessLabels = AccessLabel::whereStatus(true)->where([
        //     ['id', '!=', 1],
        //     ['id', '!=', 5],
        // ])->get(['id', 'title']);
        // $email_templates = EmailTemplate::whereStatus(true)->get(['id', 'subject']);

        // $items = collect($items_data)->where('access',true)->paginate($this->itemPerPage);
        // dd($items);
        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.alert_preference', compact('items', 'sl'));
        }

        return view('admin.setting.alert_preference.index', compact('items','sl', 'admin_page_title', 'user_groups'));
    }

    public function makeGroupName($groups_id){
        foreach($groups_id as $group_id){
            $group = UserGroup::find($group_id);
            $group_name_array[] = $group->title;
        }

        return implode(',', $group_name_array);
    }

    // public function checkAlertPreferenceAccess($id)
    // {
    //     if (!Auth::user()) abort(403);
    //     $alert_preference = AlertPreference::find($id);
    //     $access_label = AccessLabel::find($alert_preference->access_label_id);
    //     $alert_preference_access = false;

    //     if(!empty($access_label)){
    //         $alert_preference_access_labels = explode(',', $access_label->user_groups_id);
    //         foreach ($alert_preference_access_labels as $alert_preference_access_label) {
    //             $alert_preference_access = in_array($alert_preference_access_label, Auth::user()->userGroup());
    //         }
    //     }
    //     if (in_array(6, Auth::user()->userGroup())) {
    //         $alert_preference_access = true;
    //     }
    //     return $alert_preference_access;
    // }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'user_group_id*' => 'required|exists:user_groups,id',
            // 'email_template_id' => 'required|exists:email_templates,id',
            'key' => 'required|string|alpha_dash|unique:alert_preferences,key',
            'email_status' => 'nullable|boolean',
            'email_default' => 'nullable|boolean',
            'sms_status' => 'nullable|boolean',
            'sms_default' => 'nullable|boolean',
            'email_force_checked' => 'nullable|boolean',
            'sms_force_checked' => 'nullable|boolean',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        if ($request->key) {
            $item_key = Str::slug($request->key, '_');
        } else {
            $item_key = Str::slug($request->key, '_');
        }

        $item = new AlertPreference();
        $item->title = $request->get('title');
        $item->key = $item_key;
        $item->user_group_id = implode(',', $request->get('user_group_id'));
        // $item->email_template_id = $request->get('email_template_id');
        $item->status = $request->get('status') ? 1 : 0;

        $item->email = $request->get('email_status') ? 1 : 0;
        $item->email_default = $request->get('email_default') ? 1 : 0;
        if ($request->get('email_force_checked')) {
            $item->email = 1;
            $item->email_default = 1;
        }

        $item->sms = $request->get('sms_status') ? 1 : 0;
        $item->sms_default = $request->get('sms_default') ? 1 : 0;
        if ($request->get('sms_force_checked')) {
            $item->sms = 1;
            $item->sms_default = 1;
        }

        $item->email_force_checked = $request->get('email_force_checked') ? 1 : 0;
        $item->sms_force_checked = $request->get('sms_force_checked') ? 1 : 0;

        $item->save();

        return response()->json([
            'status' => 'success',
            'message' => $item->title . ' Successfully added',
        ], 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AlertPreference  $alertPreference
     * @return \Illuminate\Http\Response
     */
    public function show(AlertPreference $alertPreference)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AlertPreference  $alertPreference
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = AlertPreference::find($id);
        $item->user_group_id = explode(',', $item->user_group_id);
        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AlertPreference  $alertPreference
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($id,$request->all());
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'user_group_id*' => 'required|exists:user_groups,id',
            // 'email_template_id' => 'required|exists:email_templates,id',
            'key' => 'required|string|alpha_dash|unique:alert_preferences,key,' . $id,
            'email_status' => 'nullable|boolean',
            'email_default' => 'nullable|boolean',
            'sms_status' => 'nullable|boolean',
            'sms_default' => 'nullable|boolean',
            'email_force_checked' => 'nullable|boolean',
            'sms_force_checked' => 'nullable|boolean',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        if ($request->key) {
            $item_key = Str::slug($request->key, '_');
        } else {
            $item_key = Str::slug($request->key, '_');
        }

        $item = AlertPreference::findOrFail($id);
        $item->title = $request->get('title');
        $item->key = $item_key;
        $item->user_group_id = implode(',', $request->get('user_group_id'));
        // $item->email_template_id = $request->get('email_template_id');
        // dd($request->get('status'));
        $item->status = $request->get('status') ? 1 : 0;

        $item->email = $request->get('email_status') ? 1 : 0;
        $item->email_default = $request->get('email_default') ? 1 : 0;
        if($request->get('email_force_checked')){
            $item->email = 1;
            $item->email_default = 1;
        }

        $item->sms = $request->get('sms_status') ? 1 : 0;
        $item->sms_default = $request->get('sms_default') ? 1 : 0;
        if ($request->get('sms_force_checked')) {
            $item->sms = 1;
            $item->sms_default = 1;
        }

        $item->email_force_checked = $request->get('email_force_checked') ? 1 : 0;
        $item->sms_force_checked = $request->get('sms_force_checked') ? 1 : 0;

        $item->update();

        return response()->json([
            'status' => 'success',
            'message' => $item->title . ' Successfully added',
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AlertPreference  $alertPreference
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = AlertPreference::findOrFail($id);
        $item->delete();

        return response()->json([
            'status' => 'success',
            'message' => $item->title . ' Successfully deleted',
        ], 200);
    }
}
