<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShippingMethod;
use App\Models\ShippingMethodOption;
use Illuminate\Http\Request;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;
use Illuminate\Support\Facades\DB;

class ShippingMethodController extends Controller
{
    private $folderPath = 'shipping_method_options/';
    // private $optionFolderPath = 'shipping_method_options/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Shipping Methods';
        $items = ShippingMethod::paginate($this->itemPerPage);
        $sl = SLGenerator($items);
        if ($request->view == 'html') {
            return view('admin.jquery_live.shipping_methods', compact('sl', 'items'));
        }
        return view('admin.shipping_method.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Shipping Method';
        $shipping_option_icon_info = Session::get('shipping_option_icon');
        $countries = $this->countries();
        $currencies = $this->currenciesList();

        $collection = collect(Session::get('shipping_method_options'));
        $shipping_method_options = $collection->sortBy('ordering')->sortBy('ordering');

        return view('admin.shipping_method.create', compact('shipping_method_options', 'shipping_option_icon_info', 'countries', 'currencies', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string',
            // 'sub_title' => 'nullable|string',
            'public_key' => 'nullable|string',
            'private_key' => 'nullable|string',
            // 'amount' => 'required|numeric|between:0,9999999999.99',
            'country' => 'required',
            'allowed_currency' => 'required',
            // 'ordering' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        $item = new ShippingMethod;
        $item->title = $request->get('title');
        // $item->sub_title = $request->get('sub_title');
        $item->public_key = $request->get('public_key'); // example - IP-BL-128-678
        $item->private_key = $request->get('private_key');
        // $item->amount = $request->get('amount');
        $item->country = implode(',', $request->get('country'));
        $item->allowed_currency = implode(',', $request->get('allowed_currency'));
        $item->status = $request->get('status') ?? 0;
        // $item->ordering = $request->get('ordering') ?? 0;
        $item->save();

        $collection = collect(Session::get('shipping_method_options'));
        $shipping_method_options = $collection->sortBy('ordering');
        // dd($shipping_method_options);

        if (count($shipping_method_options) > 0) {
            $folderName = $this->folderName() . '/' . $item->id . '/';
            foreach ($shipping_method_options as $value) {
                $shipping_option= new ShippingMethodOption;
                $shipping_option->shipping_method_id = $item->id;
                $shipping_option->title = $value->title;
                $shipping_option->amount = $value->amount;
                $shipping_option->ordering = $value->ordering ?? 0;
                $shipping_option->status = $value->status ?? 0;
                if ($value->extension) {
                    $image_name = 'shipping_option_icon_' . uniqid() . '.' . $value->extension;

                    $shipping_option->shipping_method_option_image = $folderName.'/' . $image_name;

                    // $value->folder = $this->folderName();
                    // $value->file_path = $folderName.'/' . $image_name;
                    Storage::move($value->file_current, $this->folderPath . $folderName.'/' . $image_name);
                    // $value->file_current = $this->folderPath . $folderName.'/' . $image_name;
                }
                $shipping_option->save();
            }
        }

        Session::forget('shipping_method_options');

        // $item = ShippingMethod::findOrFail($item->id);
        // $shipping_method_image_info = Session::get('shipping_method_image');
        // if ($shipping_method_image_info) {
        //     $image_name = 'shipping_method_image_' . rand(99, 999999999) . '.' . $shipping_method_image_info->extension;
        //     $folderName = $this->folderName() . '/' . $item->id . '/';
        //     $item->shipping_method_image = $folderName.'/' . $image_name;
        //     Storage::move($shipping_method_image_info->shipping_method_image, $this->folderPath . $folderName.'/' . $image_name);
        //     Session::forget('shipping_method_image');
        // }
        // $item->update();

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.shipping-methods.index'))->with('success', 'Shipping Method created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function show(ShippingMethod $shippingMethod)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function edit(ShippingMethod $shippingMethod)
    {
        // Session::forget('shipping_method_options');

        $admin_page_title = 'Edit Shipping Method';
        $item = $shippingMethod;
        $item->countries = $this->countries();
        $item->currencies = $this->currenciesList();
        $shipping_option_icon_info = Session::get('shipping_option_icon');

        $shipping_method_options = DB::table('shipping_method_options')->where('shipping_method_id', $item->id)->get();
        foreach($shipping_method_options as $value){
            $value->file_root = asset('storage/' . $this->folderPath);
            $value->file_path = $value->shipping_method_option_image;
        }
        if (Session::has('shipping_method_options')) {
            $collection = collect(Session::get('shipping_method_options'));
            foreach ($collection as $collection_item) {
                // $collection_item->file_path = asset('storage/session_files/' . Auth()->user()->username . '/' . 'shipping_option_icon');
                // dd($collection_item);
                // dd($collection_item,$collection_item->file_current);
            }
            if (count($shipping_method_options) > 0) {
                $new_collection = collect($shipping_method_options->merge($collection));
                $item->shipping_method_options = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('shipping_method_options'));
                $item->shipping_method_options = $collection->sortBy('ordering');
            }
        }else{
            $item->shipping_method_options = $shipping_method_options;
        }
        return view('admin.shipping_method.edit', compact('item', 'shipping_option_icon_info', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ShippingMethod $shippingMethod)
    {
        $this->validate($request, [
            'title' => 'required|string',
            // 'sub_title' => 'nullable|string',
            'public_key' => 'nullable|string',
            'private_key' => 'nullable|string',
            // 'amount' => 'required|numeric|between:0,9999999999.99',
            'country' => 'required',
            'allowed_currency' => 'required',
            // 'ordering' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        $item = $shippingMethod;
        $item->title = $request->get('title');
        // $item->sub_title = $request->get('sub_title');
        $item->public_key = $request->get('public_key');
        $item->private_key = $request->get('private_key');
        // $item->amount = $request->get('amount');
        $item->country = implode(',', $request->get('country'));
        $item->allowed_currency = implode(',', $request->get('allowed_currency'));
        $item->status = $request->get('status') ?? 0;
        // $item->ordering = $request->get('ordering') ?? 0;
        $item->update();

        $collection = collect(Session::get('shipping_method_options'));
        $shipping_method_options = $collection->sortBy('ordering');
        // dd($shipping_method_options);

        if (count($shipping_method_options) > 0) {
            $folderName = $this->folderName() . '/' . $item->id . '/';
            foreach ($shipping_method_options as $value) {
                $shipping_option = new ShippingMethodOption;
                $shipping_option->shipping_method_id = $item->id;
                $shipping_option->title = $value->title;
                $shipping_option->amount = $value->amount;
                $shipping_option->ordering = $value->ordering ?? 0;
                $shipping_option->status = $value->status ?? 0;
                if ($value->extension) {
                    $image_name = 'shipping_option_icon_' . uniqid() . '.' . $value->extension;

                    $shipping_option->shipping_method_option_image = $folderName.'/' . $image_name;

                    // $value->folder = $this->folderName();
                    // $value->file_path = $folderName.'/' . $image_name;
                    Storage::move($value->file_current, $this->folderPath . $folderName.'/' . $image_name);
                    // $value->file_current = $this->folderPath . $folderName.'/' . $image_name;
                }
                $shipping_option->save();
            }
        }

        Session::forget('shipping_method_options');

        $message = 'Shipping Method successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.shipping-methods.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ShippingMethod  $shippingMethod
     * @return \Illuminate\Http\Response
     */
    public function destroy(ShippingMethod $shippingMethod)
    {
        $item = ShippingMethod::findOrFail($shippingMethod->id);
        // $this->deleteFile($this->folderPath, $item->shipping_method_image);
        $options = ShippingMethodOption::where('shipping_method_id', $item->id)->get();
        if(count($options) > 0){
            foreach($options as $value){
                $option = ShippingMethodOption::findOrFail($value->id);
                $option->delete();
            }
        }
        $item->delete();

        // $items = TransactionMethod::paginate($this->itemPerPage);
        // $sl = SLGenerator($items);

        $message = '<strong>' . $item->title . '</strong> delete successful';
        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);

        // return view('admin.jquery_live.transaction_methods', compact('items', 'sl'));
    }

    // public function fileUpload(Request $request, $target, $id)
    // {
    //     $validator = Validator::make($request->all(), [
    //         $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
    //     ]);

    //     if ($validator->fails()) {
    //         foreach ($validator->messages()->all() as $key => $message) {
    //             return response()->json([
    //                 'status' => 'failed',
    //                 'message' =>  $message
    //             ], 200);
    //             break;
    //         }
    //     }

    //     $item = ShippingMethod::findOrFail($id);

    //     $this->deleteFile($this->folderPath, $item->$target);

    //     $imgName = $item->slug . '_' . $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
    //     $folderName = $this->folderName();
    //     $request->$target->storeAs($this->folderPath . '/' . $folderName, $imgName);

    //     $item->$target = $folderName . '/' . $imgName;
    //     $item->update();

    //     $url = url('/storage/shipping_methods/' . $folderName . '/' . $imgName);

    //     return response()->json(
    //         [
    //             'status' => 'success',
    //             'file_url' => $url
    //         ],
    //         200
    //     );
    // }

    // public function fileRemove($target, $id)
    // {
    //     $item = ShippingMethod::findOrFail($id);
    //     $this->deleteFile($this->folderPath, $item->$target);
    //     $item->$target = null;
    //     $item->update();

    //     return response()->json('success', 200);
    // }

    public function options(Request $request, $shipping_method_id = null)
    {
        $new_collection = collect(Session::get('shipping_method_options'));
        // foreach ($new_collection as $new_collection_item) {
        //     $new_collection_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'shipping_option_icon' . '/');
        // }
        // dd($new_collection);
        if ($shipping_method_id) {
            $shipping_options = DB::table('shipping_method_options')->where('shipping_method_id', $shipping_method_id)->get();;
            if ($shipping_options) {
                foreach ($shipping_options as $shipping_option) {
                    $shipping_option->file_root = asset('storage/shipping_method_options/');
                    $shipping_option->file_path = $shipping_option->shipping_method_option_image;
                }
            }

            if (count($shipping_options) > 0) {
                $collection = collect($shipping_options->merge($new_collection));
            } else {
                $collection = $new_collection;
            }
            $items = $collection->sortBy('ordering');
        } else {
            $items = $new_collection->sortBy('ordering');
        }

        // dd($items);

        return view('admin.jquery_live.shipping_options', compact('items', 'shipping_method_id'));
    }

    public function storeOption(Request $request, $shipping_method_id = null) {
        $validator = Validator::make($request->all(), [
            'option_title' => 'required|string|max:100',
            'amount' => 'required|numeric|between:0,9999999999.99',
            'ordering' => 'nullable|numeric',
            'option_status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'failed',
                'messages' => $validator->messages(),
            ], 200);
        }
        $shipping_option_icon = Session::get('shipping_option_icon');

        $item = (object)([
            'id' => uniqid(),
            'shipping_method_id' => $shipping_method_id,

            'file_current' => $shipping_option_icon->shipping_option_icon ?? null,
            'file_path' => $shipping_option_icon->file_path ?? null,
            'extension' => $shipping_option_icon->extension ?? null,
            'file_root' => asset('storage/session_files/' . Auth()->user()->username . '/' . 'shipping_option_icon' . '/'),

            'title' => $request->get('option_title'),
            'amount' => $request->get('amount'),
            'ordering' =>  $request->get('ordering') ?? 0,
            'status' => $request->get('option_status') ?? 0,
        ]);

        if (Session::has('shipping_method_options')) {
            $items = Session::get('shipping_method_options');
            if (count($items) > 10) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  'limit end'
                    ],
                    200
                );
            }
            array_push($items, $item);
        } else {
            $items = array($item);
        }

        Session::put([
            'shipping_method_options' => $items,
        ]);

        Session::forget('shipping_option_icon');

        $message = '<strong>' . $item->title . '</strong> added successful';
        return response()->json([
            'items' => $items,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    public function editOption($id, $shipping_method_id = null)
    {
        $shipping_method = ShippingMethod::find($shipping_method_id);

        if ($shipping_method) {
            $items = DB::table('shipping_method_options')->where('shipping_method_id', $shipping_method->id)->get();
            foreach ($items as $item) {
                $item->file_root = asset('storage/shipping_method_options/');
                $item->file_path = $item->shipping_method_option_image;
                $item->file_img_upload_url = route('admin.shipping_methods.items.file.upload', ['shipping_option_icon', $item->id, $shipping_method_id]);
                $item->file_img_remove_url = route('admin.shipping_methods.items.file.remove', ['shipping_option_icon', $item->id, $shipping_method_id]);
            }
        }
        if (Session::has('shipping_method_options')) {
            $collection = collect(Session::get('shipping_method_options'));
            foreach ($collection as $collection_item) {
                // $collection_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'shipping_option_icon' . '/');
                $collection_item->file_img_upload_url = route('admin.shipping_methods.items.file.upload', ['shipping_option_icon', $collection_item->id, $shipping_method_id]);
                $collection_item->file_img_remove_url = route('admin.shipping_methods.items.file.remove', ['shipping_option_icon', $collection_item->id, $shipping_method_id]);
            }
            if ($shipping_method && count($items) > 0) {
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $items = $collection->sortBy('ordering');
            }
        }
        // dd($items);
        foreach ($items as $item) {
            if ($item->id == $id) {
                if ($item->file_path) {
                    $item->img_url = $item->file_root . '/' . $item->file_path;
                }
                return response()->json($item, 200);
                break;
            }
        }
    }

    public function destroyOption($id, $shipping_method_id = null)
    {
        $shipping_item = ShippingMethod::find($shipping_method_id);
        if ($shipping_item) {
            $items = DB::table('shipping_method_options')->where('shipping_method_id', $shipping_item->id)->get();
            foreach ($items as $key => $item) {
                $item->file_root = asset('storage/shipping_method_options/');
                if ($item->id == $id) {
                    // unset($items[$key]);
                    $item = ShippingMethodOption::findOrFail($item->id);
                    Storage::delete($this->folderPath . $item->shipping_method_option_image);
                    $item->delete();
                    $message = '<strong>' . $item->title . '</strong> deleted successful';
                    break;
                }
            }
            // $shipping_item->attribute_options = serialize($items);
            // $shipping_item->update();
        }

        if (Session::has('shipping_method_options')) {
            $session_items = Session::get('shipping_method_options');
            foreach ($session_items as $key => $session_item) {
                $session_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'shipping_option_icon' . '/');
                if ($session_item->id == $id) {
                    $auth_user = Auth()->user();
                    unset($session_items[$key]);
                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . 'shipping_option_icon/', $session_item->file_path);
                    $message = '<strong>' . $session_item->title . '</strong> deleted successful';
                    break;
                }
            }
            Session::put([
                'shipping_method_options' => $session_items,
            ]);
            if ($shipping_item && count($items) > 0) {
                $collection = collect(Session::get('shipping_method_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('shipping_method_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json([
            'items' => $items,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    public function updateOption(Request $request, $id, $shipping_method_id = null)
    {
        $validator = Validator::make($request->all(), [
            'option_title' => 'required|string|max:100',
            'amount' => 'required|numeric|between:0,9999999999.99',
            'ordering' => 'nullable|numeric',
            'option_status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'failed',
                'messages' => $validator->messages(),
            ], 200);
        }

        $shipping_method = ShippingMethod::find($shipping_method_id);

        if ($shipping_method) {
            $items = DB::table('shipping_method_options')->where('shipping_method_id', $shipping_method->id)->get();
            foreach ($items as $item) {
                if ($item->id == $id) {
                    $item = ShippingMethodOption::findOrFail($item->id);
                    $item->title = $request->option_title;
                    $item->amount = $request->amount;
                    $item->ordering = $request->get('ordering') ?? 0;
                    $item->status = $request->get('option_status') ?? 0;
                    $item->update();
                    $message = '<strong>' . $item->title . '</strong> update successful';
                }
            }
            // $shipping_method->shipping_method_id = $shipping_method->id;
            // $shipping_method->update();
        }

        if (Session::has('shipping_method_options')) {
            foreach (Session::get('shipping_method_options') as $item) {
                if ($item->id == $id) {
                    $item->title = $request->option_title;
                    $item->amount = $request->amount;
                    $item->ordering = $request->get('ordering') ?? 0;
                    $item->status = $request->get('option_status') ?? 0;

                    $message = '<strong>' . $item->title . '</strong> update successful';
                }
            }
            Session::put([
                'shipping_method_options' => Session::get('shipping_method_options'),
            ]);

            if ($shipping_method && count($items) > 0) {
                $collection = collect(Session::get('shipping_method_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('shipping_method_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json([
            'items' => $items,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    public function fileShippingItemUpload(Request $request, $target, $id, $shipping_method_id = null)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);
        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }
        $shipping_item = ShippingMethod::find($shipping_method_id);
        if ($shipping_item) {
            $items = DB::table('shipping_method_options')->where('shipping_method_id', $shipping_item->id)->get();
            foreach ($items as $item) {
                if ($item->id == $id) {

                    $item = ShippingMethodOption::findOrFail($item->id);
                    $imgName = $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
                    $folderName = $this->folderName() . '/' . $shipping_item->id . '/';

                    $this->deleteFile($this->folderPath, $item->shipping_method_option_image);
                    $item->shipping_method_option_image = $folderName  . $imgName;
                    // $item->shipping_method_option_image = $this->folderPath . $folderName. '/'. $imgName;
                    $request->$target->storeAs($this->folderPath . $folderName, $imgName);

                    $item->update();
                    // $item->extension = $request->$target->extension();
                    // $item->file_root = asset('storage/shipping_method_options/');
                    // $item->text_file_root = asset('storage/shipping_method_options/');
                    $url = url('/storage/shipping_method_options/' . $folderName. '/'. $imgName);
                    break;
                }
            }
            // $shipping_item->attribute_options = serialize($items);
            // $shipping_item->update();
        }

        if (Session::has('shipping_method_options')) {
            $session_items = Session::get('shipping_method_options');
            foreach ($session_items as $session_item) {
                if ($session_item->id == $id) {
                    $imgName = $target . "_" . $session_item->id . uniqid() . '.' . $request->$target->extension();
                    $folderName = $this->folderName();
                    $auth_user = Auth()->user();

                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target . '/', $session_item->file_path);
                    $request->$target->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);

                    $session_item->file_path = $folderName  . '/' . $imgName;
                    $session_item->file_current = $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName  . '/' . $imgName;
                    $session_item->extension = $request->$target->extension();

                    $url = url('storage/session_files/' . $auth_user->username . '/' . $target . '/' . $folderName . '/' . $imgName);
                    // dd($session_item);
                    break;
                }
            }
            Session::put([
                'shipping_method_options' => $session_items,
            ]);
            if ($shipping_item && count($items) > 0) {
                $collection = collect(Session::get('shipping_method_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('shipping_method_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileShippingItemRemove(Request $request, $target, $id, $shipping_item_id = null)
    {
        $shipping_item = ShippingMethod::find($shipping_item_id);
        if ($shipping_item) {
            $items = DB::table('shipping_method_options')->where('shipping_method_id', $shipping_method->id)->get();
            foreach ($items as $item) {
                if ($item->id == $id) {
                    $item = ShippingMethodOption::findOrFail($item->id);
                    $this->deleteFile($this->folderPath, $item->shipping_method_option_image);
                    $item->shipping_method_option_image = null;
                    $item->update();
                    // $item->file_path = null;
                    // $item->file_current = null;
                    // $item->extension = null;

                    break;
                }
            }
            // $shipping_item->attribute_options = serialize($items);
            // $shipping_item->update();
        }

        if (Session::has('shipping_method_options')) {
            $session_items = Session::get('shipping_method_options');
            foreach ($session_items as $session_item) {
                if ($session_item->id == $id) {
                    $auth_user = Auth()->user();

                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $session_item->file_path);
                    $session_item->file_path = null;
                    $session_item->file_current = null;
                    $session_item->extension = null;

                    break;
                }
            }

            Session::put([
                'shipping_method_options' => $session_items,
            ]);

            if ($shipping_item && count($items) > 0) {
                $collection = collect(Session::get('shipping_method_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('shipping_method_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json('success', 200);
    }
}
