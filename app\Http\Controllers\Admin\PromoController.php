<?php

namespace App\Http\Controllers\Admin;

use Session;
use App\Models\Promo;
use App\Models\Product;
use App\Models\ProductBrand;
use Illuminate\Http\Request;
use App\Models\ProductCategory;
use App\Http\Controllers\Controller;


use Illuminate\Support\Facades\Storage;

class PromoController extends Controller
{
    private $folderPath = 'session_files/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Promos';
        $items = Promo::paginate($this->itemPerPage);

        $sl = SLGenerator($items);
        return view('admin.promo.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Promo';
        $image_info = Session::get('promo_image');

        $productCategory = ProductCategory::get();
        $products = Product::get();
        $productBrand = ProductBrand::get();

        return view('admin.promo.create', compact('admin_page_title', 'image_info', 'productCategory', 'products', 'productBrand'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            'promo_code' => 'required|string|max:255|unique:promos',
            'from_date' => 'nullable|date_format:"m/d/Y"',
            'to_date' => 'nullable|date_format:"m/d/Y"',
            'description' => 'nullable|string',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
            'never_expire' => 'nullable|boolean',
            'global' => 'nullable|boolean',

        ]);
        $item = new Promo;
        $condition = $request->get('coupon_type_condition');
        $dataMap = [
            'category' => $request->get('productCategory'),
            'products' => $request->get('product'),
            'brand' => $request->get('productBrand'),
            'no_condition' => '',
            'cart_subtotal' => '',
        ];
        if (array_key_exists($condition, $dataMap)) {
            $data = $item->coupon_type_condi_data = json_encode($dataMap[$condition]);
        } else {
            $data = $item->coupon_type_condi_data = null; // Default to null if condition is invalid
        }
        $item->coupon_type_condi_data = $data;

        $item->title = $request->get('title');
        $item->promo_code = $request->get('promo_code');
        $item->from_date = $this->formatDate($request->get('from_date'));
        $item->to_date = $this->formatDate($request->get('to_date'));
        $item->description = $request->get('description');
        $item->discount_type = $request->get('discount_type');
        $item->discount_price = $request->get('discount_price');
        $item->ordering = $request->get('ordering') ?? 0;
        $item->status = $request->get('status') ?? 0;
        $item->never_expire = $request->get('never_expire') ?? 0;
        $item->coupon_value = $request->get('coupon_value', 'discount');
        $item->customer_group = $request->get('customer_group', 'new');
        $item->uses_per_coupon = $request->get('uses_per_coupon', 1);
        $item->uses_per_customer = $request->get('uses_per_customer', 1);
        $item->coupon_type_condition = $request->get('coupon_type_condition', 'no_condition');
        $item->send_mail = $request->get('send_mail');
        $item->global = $request->get('global');
        // Save the record
        $item->save();

        if ($request->input('global') == 1) {
            // Unset all existing global promos except this one (if updating)
            Promo::where('global', 1)
                ->when($promo->id ?? null, function ($query) use ($promo) {
                    return $query->where('id', '!=', $promo->id);
                })
                ->update(['global' => null]);
        }

        $image_info = Session::get('promo_image');
        if ($image_info) {
            $image_name = $item->id . uniqid() . '.' . $image_info->extension;
            $folderName = $this->folderName();
            Storage::move($image_info->promo_image, $this->folderPath . $folderName . '/' . $image_name);
            $item->promo_image = $folderName . '/' . $image_name;
            Session::forget('promo_image');
            $item->save();
        }

        // Check if the file is uploaded directly
        if ($request->hasFile('promo_image')) {
            $image_name = $item->id . time() . '.' . $request->promo_image->extension();
            $storeStatus = $request->promo_image->storeAs($this->folderPath, $image_name);
            if ($storeStatus) {
                $item->promo_image = $this->folderPath . '/' . $image_name;
                $item->save();
            }
        }

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.promos.index'))->with('success', 'Promo Added Successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function show(Promo $promo)
    {
        abort('404');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function edit(Promo $promo)
    {
        $admin_page_title = 'Edit Promo';

        // Format dates for display in the form
        $promo->from_date = $this->undoFormatDate($promo->from_date);
        $promo->to_date = $this->undoFormatDate($promo->to_date);

        // Retrieve related data
        $productCategory = ProductCategory::all(); // Assuming Category model is available
        $products = Product::all();        // Assuming Product model is available
        $productBrand = ProductBrand::all();      // Assuming Brand model is available

        // Decode coupon_type_condi_data for pre-selected values
        $selectedData = json_decode($promo->coupon_type_condi_data, true) ?? [];

        // Pass data to the view
        return view('admin.promo.edit', compact(
            'admin_page_title',
            'promo',
            'productCategory',
            'products',
            'productBrand',
            'selectedData'
        ));
    }



    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Promo $promo)
    {
        // Validation
        $this->validate($request, [
            'title' => 'required|string|max:255',
            'promo_code' => 'nullable|unique:promos,promo_code,' . $promo->id,
            'from_date' => 'nullable|date_format:"m/d/Y"',
            'to_date' => 'nullable|date_format:"m/d/Y"',
            'description' => 'nullable|string',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
            'never_expire' => 'nullable|boolean',
            'global' => 'nullable|boolean',
            'coupon_value' => 'nullable|string',
            'customer_group' => 'nullable|string',
            'guest_checkout' => 'nullable|boolean',
            'uses_per_coupon' => 'nullable|integer',
            'uses_per_customer' => 'nullable|integer',
            'coupon_type_condition' => 'nullable|string|in:no_condition,cart_subtotal,category,products,brand',
        ]);
        $condition = $request->get('coupon_type_condition');
        $dataMap = [
            'category' => $request->get('productCategory'),
            'products' => $request->get('product'),
            'brand' => $request->get('productBrand'),
            'no_condition' => '',
            'cart_subtotal' => '',
        ];
        if (array_key_exists($condition, $dataMap)) {
            $data = $promo->coupon_type_condi_data = json_encode($dataMap[$condition]);
        } else {
            $data = $promo->coupon_type_condi_data = null; // Default to null if condition is invalid
        }
        $promo->coupon_type_condi_data = $data;
        // Update promo details
        $promo->title = $request->get('title');
        $promo->promo_code = $request->get('promo_code');
        $promo->from_date = $this->formatDate($request->get('from_date'));
        $promo->to_date = $this->formatDate($request->get('to_date'));
        $promo->description = $request->get('description');
        $promo->discount_type = $request->get('discount_type');
        $promo->discount_price = $request->get('discount_price');
        $promo->ordering = $request->get('ordering') ?? 0;
        $promo->status = $request->get('status') ?? 0;
        $promo->never_expire = $request->get('never_expire') ?? 0;
        $promo->coupon_value = $request->get('coupon_value');
        $promo->customer_group = $request->get('customer_group');
        $promo->guest_checkout = $request->get('guest_checkout') ?? 0;
        $promo->uses_per_coupon = $request->get('uses_per_coupon') ?? 1;
        $promo->uses_per_customer = $request->get('uses_per_customer') ?? 1;
        $promo->coupon_type_condition = $request->get('coupon_type_condition');
        $promo->send_mail = $request->get('send_mail');
        $promo->global = $request->get('global');

        if ($request->input('global') == 1) {
            // Unset all existing global promos except this one (if updating)
            Promo::where('global', 1)
                ->when($promo->id ?? null, function ($query) use ($promo) {
                    return $query->where('id', '!=', $promo->id);
                })
                ->update(['global' => null]);
        }


        // Save changes
        $promo->update();

        // Redirect based on button action
        return ($request->get('btn') == 'update')
            ? back()->with('success', 'Promo updated successfully.')
            : redirect()->route('admin.promos.index')->with('success', 'Promo updated successfully.');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function destroy(Promo $promo)
    {
        $promo = Promo::findOrFail($promo->id);
        $promo->delete();

        $message = '<strong>' . $promo->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }

    /**
     * Upload File Function For This Component
     */
    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }

        $item = Promo::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target); // deleting old File

        $imgName = $item->id . "_" . $item->id . "_" . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath . $folderName, $imgName);

        $item->$target = $folderName . '/' . $imgName;
        $item->update();

        $url = url('/storage/promos/' . $folderName . '/' . $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    /**
     * Remove File Function For This Component
     *
     * @param string target target key is table key
     * @return boolean
     */
    public function fileRemove($target, $id)
    {
        $item = Promo::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();

        return response()->json('success', 200);
    }
}
