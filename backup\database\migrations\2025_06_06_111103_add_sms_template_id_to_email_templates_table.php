<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSmsTemplateIdToEmailTemplatesTable extends Migration
{
    public function up()
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->unsignedBigInteger('sms_template_id')->nullable()->after('sms_status');

            $table->foreign('sms_template_id')
                ->references('id')
                ->on('whatsapp_template_metas')
                ->onDelete('set null'); // or cascade / restrict depending on your logic
        });
    }

    public function down()
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->dropForeign(['sms_template_id']);
            $table->dropColumn('sms_template_id');
        });
    }
}
