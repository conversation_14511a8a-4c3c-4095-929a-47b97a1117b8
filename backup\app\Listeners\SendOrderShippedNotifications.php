<?php

namespace App\Listeners;

use App\Events\OrderShipped;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendOrderShippedNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(OrderShipped $event)
    {
        $this->notifier->sendCustomerOrderShippedNotification($event->order, $event->cart, $event->message_status);
       // $this->notifier->sendAdminOrderShippedNotifications($event->order, $event->cart);
    }
}
