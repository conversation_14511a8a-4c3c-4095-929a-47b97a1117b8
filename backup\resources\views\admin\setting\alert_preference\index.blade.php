@extends('layouts.admin')
@section('title','Access')
@section('content')
<section id="sub-nav">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-6">
      </div>
      <div class="col-md-6">
        <div class="block sub-nav-utility clearfix">
          <div class="float-right">
            <button data-url="{{ route('admin.alert_preferences.store') }}" data-index="{{ route('admin.alert_preferences.index') }}" data-token="{{ csrf_token() }}" data-toggle="modal" data-target="#AddEditModal" data-title="Create Alert Preferences" class="btn btn-primary">New</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<section>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div id="items" class="block clearfix">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                @if ($items->count() > 0)
                <th class="select">
                  <input type="checkbox" id="check_all">
                </th>
                @endif
                <th class="sl">SL</th>
                <th class="details">Title</th>
                <th class="role">Group</th>
                <th class="role">Email</th>
                <th class="role">SMS</th>
                <th class="verified">Status</th>
                <th class="action">Action</th>
              </tr>
            </thead>
            @if ($items->count() > 0)
            @foreach ($items as $item)
            <tr id="row{{ $item->id }}">
              <td>
                <input type="checkbox" class="check-item" id="check_item{{ $loop->index }}" name="users[{{ $loop->index }}]" value="{{ $item->id }}" form="manage-checked-form">
              </td>
              <td class="sl">{{ $sl }}</td>
              <td class="title">{{ $item->title }}</td>
              <td class="role">{{ $item->group }}</td>
                {{-- <td class="role">{{ $item->access_label }}</td> --}}
                <td class="role">{{ $item->email ? 'Yes' : 'No' }}</td>
                <td class="role">{{ $item->sms ? 'Yes' : 'No' }}</td>
              {{-- <td class="role">{{ $item->default ? 'Yes' : 'No' }}</td> --}}
              <td class="verified">{{ $item->status ? 'Yes' : 'No' }}</td>
              <td class="action">
                <button class="btn btn-primary btn-sm" data-id="{{ $item->id }}" data-url="{{ route('admin.alert_preferences.update', $item->id) }}" data-editurl="{{ route('admin.alert_preferences.edit', $item->id) }}" data-index="{{ route('admin.alert_preferences.index') }}" data-toggle="modal" data-target="#AddEditModal" data-title="Edit {{ $item->title }}">Edit</button>
                <button data-name="{{ $item->title }}" data-url="{{ route('admin.alert_preferences.destroy', $item->id) }}" data-index="{{ route('admin.alert_preferences.index') }}" class="btn btn-danger btn-sm delete">Delete</button>
              </td>
            </tr>
            @php($sl++)
            @endforeach
            @else
            <tr>
              <td class="text-center" colspan="6">
                No access added <a href="#" data-url="{{ route('admin.alert_preferences.store') }}" data-token="{{ csrf_token() }}" data-title="Create Access Label" data-toggle="modal" data-target="#AddEditModal">Add New</a>
              </td>
            </tr>
            @endif
          </table>
          {{ $items->links() }}
        </div>
      </div>
    </div>
  </div>
</section>
<div id="AddEditModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="Add Modal" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
        <div class="loader d-none">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
      <div class="modal-header">
        <h4 id="modal_title" class="modal-title">Create Alert Preferences</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form method="POST" id="submit_form" enctype="multipart/form-data">
        @csrf
        <div class="modal-body">
            <div class="form-row form-group">
                <label for="title">Title</label>
                <input type="text" id="title" name="title" value="{{ old('title') }}" class="form-control {{ $errors->has('title') ? 'in-invalid' : null }}">
                <span class="text-small text-danger">
                    <small id="title_error">{{ $errors->first('title') }}</small>
                </span>
            </div>
            <div class="form-row form-group">
                <label for="key">Key</label>
                <input type="text" id="key" name="key" value="{{ old('key') }}" class="form-control {{ $errors->has('key') ? 'in-invalid' : null }}">
                <span class="text-small text-danger">
                    <small id="key_error">{{ $errors->first('key') }}</small>
                </span>
            </div>
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="user_group_id">User Groups</label>
                    {{-- <select name="access_label_id" id="access_label_id" class="selectpicker" data-width="100%" title="Select">
                        @foreach ($accessLabels as $accessLabel)
                        <option value="{{ $accessLabel->id }}" {{ old('access_label_id') == $accessLabel->id ? 'selected' : null }}>{{ $accessLabel->title }}</option>
                        @endforeach
                    </select> --}}
                    <select name="user_group_id[]" id="user_group_id" class="selectpicker {{ $errors->has('user_group_id') ? 'is-invalid' : '' }}" title="Select User Role" data-width="100%" multiple>
                        @foreach ($user_groups as $user_group)
                        <option value="{{ $user_group->id }}" {{ in_array($user_group->id,old('user_group_id') ?? array()) ? 'selected' : ''  }} data-content="{{ $user_group->path.$user_group->title }}"></option>
                        @if(count($user_group->children))
                            @include('admin.user.options',['items' => $user_group->children, old('user_group_id')])
                        @endif
                        @endforeach
                    </select>
                    {{-- @if($errors->has('access_label_id')) --}}
                    <span class="text-small text-danger">
                        <small id="user_group_id_error">{{ $errors->first('user_group_id') }}</small>
                    </span>
                    {{-- @endif --}}
                </div>
                {{-- <div class="form-group col-md-6">
                    <label for="email_template_id">Email Template</label>
                    <select name="email_template_id" id="email_template_id" class="selectpicker" data-width="100%" title="Select">
                        @foreach ($email_templates as $email_template)
                        <option value="{{ $email_template->id }}" {{ old('email_template_id') == $email_template->id ? 'selected' : null }}>{{ $email_template->subject }}</option>
                        @endforeach
                    </select>
                    @if($errors->has('email_template_id'))
                    <small class="text-danger">{{ $errors->first('email_template_id') }}</small>
                    @endif
                </div> --}}
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="email_status">Email</label>
                    <div class="switch switch-checkbox">
                        <input id="email_status" name="email_status" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="email_status_error">{{ $errors->first('email_status') }}</small>
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="email_default">Default</label>
                    <div class="switch switch-checkbox">
                        <input id="email_default" name="email_default" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="email_default_error">{{ $errors->first('email_default') }}</small>
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="email_force_checked">System Alert</label>
                    <div class="switch switch-checkbox">
                        <input id="email_force_checked" name="email_force_checked" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="email_force_checked_error">{{ $errors->first('email_force_checked') }}</small>
                    </span>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="sms_status">SMS</label>
                    <div class="switch switch-checkbox">
                        <input id="sms_status" name="sms_status" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="sms_status_error">{{ $errors->first('sms_status') }}</small>
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="sms_default">Default</label>
                    <div class="switch switch-checkbox">
                        <input id="sms_default" name="sms_default" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="sms_default_error">{{ $errors->first('sms_default') }}</small>
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="sms_force_checked">System Alert</label>
                    <div class="switch switch-checkbox">
                        <input id="sms_force_checked" name="sms_force_checked" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="sms_force_checked_error">{{ $errors->first('sms_force_checked') }}</small>
                    </span>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label class="d-block text-center" for="status">Status</label>
                    <div class="switch switch-checkbox">
                        <input id="status" name="status" type="checkbox" value="1">
                        <span class="toggle-outside">
                            <span class="status-text">
                                <span class="status-off">NO</span>
                                <span class="status-on">YES</span>
                            </span>
                            <span class="toggle-inside"></span>
                        </span>
                    </div>
                    <span class="text-danger">
                        <small id="status_error">{{ $errors->first('status') }}</small>
                    </span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <div id="save_changes"></div>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection

@section('scripts')
<script>
    $('#AddEditModal').on('show.bs.modal', function (event) {
        // $('#save_changes').addClass('d-none');
        let button = $(event.relatedTarget)
        let id = button.data('id') ?? 0;
        let url = button.data('url');
        let edit_url = button.data('editurl');
        // alert(edit_url);
        let index_url = button.data('index');
        let modal_title = button.data('title');
        let saveButton = '<button type="submit" id="submitButton" data-id="'+ id +'" data-url="'+ url +'" data-index="'+index_url+'"  class="btn btn-primary">Save changes</button>';
        $('#save_changes').html(saveButton);
        $('#modal_title').empty().html(modal_title);
        if (id) {
            $('#title').prop('disabled', true);
            $('#key').prop('disabled', true);
            // $('#default').prop('disabled', true);
            // $('#status').prop('disabled', true);
            // $('#email_status').prop('disabled', true);
            // $('#email_default').prop('disabled', true);
            // $('#sms_status').prop('disabled', true);
            // $('#sms_default').prop('disabled', true);

            $('#email_status').prop('disabled', false);
            $('#email_default').prop('disabled', false);
            $('#sms_status').prop('disabled', false);
            $('#sms_default').prop('disabled', false);

            $('#status').prop('checked', false);
            $('#email_status').prop('checked', false);
            $('#email_default').prop('checked', false);
            $('#sms_status').prop('checked', false);
            $('#sms_default').prop('checked', false);

            $('#modal_title').empty().html(modal_title);
            $('.loader').removeClass('d-none');

            $.get(edit_url, function (data) {
                if (data) {

                    setTimeout(function(){
                        $('.loader').addClass('d-none');
                        $('#title').prop('disabled', false).empty().val(data.title);
                        $('#key').prop('disabled', false).empty().val(data.key);
                        $('#email_template_id').selectpicker('val', data.email_template_id);
                        $('#user_group_id').selectpicker('val', data.user_group_id);
                    }, 700);

                    if(data.status){
                        // status.checked = true;
                        // $('#status').val(1);
                        setTimeout(function(){
                            $("#status").prop("checked", true);
                        }, 1000);
                    }else{
                        // status.checked = false;
                        // $('#status').val(0);
                        setTimeout(function(){
                            $("#status").prop("checked", false);
                        }, 1000);
                    }

                    if(data.email){
                        // email_status.checked = true;
                        // $('#email_status').val(1);
                        setTimeout(function(){
                            $("#email_status").prop("checked", true);
                        }, 1000);
                    }else{
                        // email_status.checked = false;
                        // $('#email_status').val(0);
                        setTimeout(function(){
                            $("#email_status").prop("checked", false);
                        }, 1000);
                    }

                    if(data.email_default){
                        // email_default.checked = true;
                        // $('#email_default').val(1);
                        setTimeout(function(){
                            $("#email_default").prop("checked", true);
                        }, 1000);
                    }else{
                        // email_default.checked = false;
                        // $('#email_default').val(0);
                        setTimeout(function(){
                            $("#email_default").prop("checked", false);
                        }, 1000);
                    }

                    if(data.sms){
                        // sms_status.checked = true;
                        // $('#sms_status').val(1);
                        setTimeout(function(){
                            $("#sms_status").prop("checked", true);
                        }, 1000);
                    }else{
                        // sms_status.checked = false;
                        // $('#sms_status').val(0);
                        setTimeout(function(){
                            $("#sms_status").prop("checked", false);
                        }, 1000);
                    }

                    if(data.sms_default){
                        // sms_default.checked = true;
                        // $('#sms_default').val(1);
                        setTimeout(function(){
                            $("#sms_default").prop("checked", true);
                        }, 1000);
                    }else{
                        // sms_default.checked = false;
                        // $('#sms_default').val(0);
                        setTimeout(function(){
                            $("#sms_default").prop("checked", false);
                        }, 1000);
                    }

                    if(data.email_force_checked){
                        // sms_default.checked = true;
                        // $('#sms_default').val(1);
                        setTimeout(function(){
                            $("#email_force_checked").prop("checked", true);
                            $("#email_status").prop("checked", true).prop("disabled", true);
                            $("#email_default").prop("checked", true).prop("disabled", true);
                        }, 1000);
                    }else{
                        // sms_default.checked = false;
                        // $('#sms_default').val(0);
                        setTimeout(function(){
                            $("#email_force_checked").prop("checked", false);
                        }, 1000);
                        // $("#email_status").prop("checked", false).prop("disabled", false);
                        // $("#email_default").prop("checked", false).prop("disabled", false);
                    }

                    if(data.sms_force_checked){
                        // sms_default.checked = true;
                        // $('#sms_default').val(1);
                        setTimeout(function(){
                            $("#sms_force_checked").prop("checked", true);
                            $("#sms_status").prop("checked", true).prop("disabled", true);
                            $("#sms_default").prop("checked", true).prop("disabled", true);
                        }, 1000);
                    }else{
                        // sms_default.checked = false;
                        // $('#sms_default').val(0);
                        setTimeout(function(){
                            $("#sms_force_checked").prop("checked", false);
                        }, 1000);
                        // $("#sms_status").prop("checked", false).prop("disabled", false);
                        // $("#sms_default").prop("checked", false).prop("disabled", false);
                    }

                    setTimeout(function(){
                        $('.loader').addClass('d-none');
                    }, 1300);




                    // const status = document.getElementById('status');
                    // const email_status = document.getElementById('email_status');
                    // const email_default = document.getElementById('email_default');
                    // const sms_status = document.getElementById('sms_status');
                    // const sms_default = document.getElementById('sms_default');

                    // $('#status').prop('disabled', false);
                    // $('#email_status').prop('disabled', false);
                    // $('#email_default').prop('disabled', false);
                    // $('#sms_status').prop('disabled', false);
                    // $('#sms_default').prop('disabled', false);


                }
            });
        }
    });

    $('#AddEditModal').on('hidden.bs.modal', function () {
        refreshModal();
    });

    function refreshModal() {
        // $('#title').val(null);
        // $('#key').val(null);

        // $('#status').val(null);
        // $('#default').val(null);

        // $('#user_group_id').selectpicker('val','');
        // $('#access_label_id').selectpicker('val','');

        // $('#title_error').empty();
        // $('#key_error').empty();
        // $('#status_error').empty();
        // $('#email_status_error').empty();
        // $('#email_default_error').empty();
        // $('#sms_status_error').empty();
        // $('#sms_default_error').empty();

        // $('#email_template_id_error').empty();
        // $('#user_group_id_error').empty();

        // const status = document.getElementById('status');
        // status.checked = false;
        // const email_status = document.getElementById('email_status');
        // email_status.checked = false;
        // const email_default = document.getElementById('email_default');
        // email_default.checked = false;
        // const sms_status = document.getElementById('sms_status');
        // sms_status.checked = false;
        // const sms_default = document.getElementById('sms_default');
        // sms_default.checked = false;
    }

    $('#email_force_checked').click(function () {
        if (this.checked) {
            $("#email_status").prop("checked", true).prop("disabled", true);
            $("#email_default").prop("checked", true).prop("disabled", true);
            $("#email_force_checked").prop("checked", true);

        } else {
            $("#email_status").prop("checked", false).prop("disabled", false);
            $("#email_default").prop("checked", false).prop("disabled", false);
            $("#email_force_checked").prop("checked", false);
        }
    });

    $('#sms_force_checked').click(function () {
        if (this.checked) {
            $("#sms_status").prop("checked", true).prop("disabled", true);
            $("#sms_default").prop("checked", true).prop("disabled", true);
            $("#sms_force_checked").prop("checked", true);

        } else {
            $("#sms_status").prop("checked", false).prop("disabled", false);
            $("#sms_default").prop("checked", false).prop("disabled", false);
            $("#sms_force_checked").prop("checked", false);
        }
    });

    $('#status').click(function () {
      if (this.checked) {
        //   this.checked = true;
        //   $('#status').val(1);
        $("#status").prop("checked", true);
      } else {
        //   this.checked = false;
        //   $('#status').val(0);
        $("#status").prop("checked", false);
      }
    });

    $('#email_status').click(function () {
      if (this.checked) {
        //   this.checked = true;
        //   $('#email_status').val(1);
        $("#email_status").prop("checked", true);
      } else {
        //   this.checked = false;
        //   $('#email_status').val(0);
        $("#email_status").prop("checked", false);
      }
    });

    $('#email_default').click(function () {
      if (this.checked) {
        //   this.checked = true;
        //   $('#email_default').val(1);
        $("#email_default").prop("checked", true);
      } else {
        //   this.checked = false;
        //   $('#email_default').val(0);
        $("#email_default").prop("checked", false);
      }
    });

    $('#sms_status').click(function () {
      if (this.checked) {
        //   this.checked = true;
        //   $('#sms_status').val(1);
        $("#sms_status").prop("checked", true);
      } else {
        //   this.checked = false;
        //   $('#sms_status').val(0);
         $("#sms_status").prop("checked", false);
      }
    });

    $('#sms_default').click(function () {
      if (this.checked) {
        //   this.checked = true;
        //   $('#sms_default').val(1);
        $("#sms_default").prop("checked", true);
      } else {
        //   this.checked = false;
        //   $('#sms_default').val(0);
        $("#sms_default").prop("checked", false);
      }
    });

    $('#submit_form').on('submit', function (event) {
        event.preventDefault();
        let id = $("#submitButton").attr('data-id');
        let href = $("#submitButton").attr('data-url');
        let index_url = $("#submitButton").attr('data-index');
        let form = new FormData(this);
        let method = 'POST';
        $.ajax({
            url: href,
            method: method,
            data: form,
            dataType: 'json',
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                // console.log(data);
                if (data.status == 'error') {
                    $('.loader').addClass('d-none');
                    $('#title_error').empty().html(data.messages.title);
                    $('#key_error').empty().html(data.messages.key);
                    // $('#email_template_id_error').empty().html(data.messages.email_template_id);
                    $('#user_group_id_error').empty().html(data.messages.access_label_id);
                    $('#status_error').empty().html(data.messages.status);
                    $('#default_error').empty().html(data.messages.default);
                    $('#error_message').empty().html(data.message);
                }else{
                   $('#system-message').empty().html(data.message);
                    $.ajax({
                        url: index_url + '?view=html',
                        success: function (data) {
                            $('.loader').addClass('d-none');
                            $('#AddEditModal').modal('hide');
                            $('#items').empty().append(data);
                            $('#alert').addClass('alert-success').removeClass('d-none alert-danger');
                            $('#alert-session').remove();
                            refreshModal();
                        }
                    });
                }
            }
        });
    });

    $('.delete').on('click', function () {
        let id = $(this).attr('data-toggle');
        let delete_url = $(this).attr('data-url');
        let index = $(this).attr('data-index');
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.confirm({
            title: 'Are you sure to delete?',
            content: 'Confirm to delete this' + ' <strong>' + name + '</strong> ' + 'permanently.',
            type: 'blue',
            buttons: {
                ok: {
                    text: "Confirm",
                    btnClass: 'btn-primary',
                    keys: ['enter'],
                    action: function () {
                        $.ajax({
                            type: "DELETE",
                            url: delete_url,
                            success: function (data) {
                                $('#alert-session').remove();
                                $('#alert').addClass('alert-danger').removeClass('d-none');
                                $('#alert').addClass('alert-danger').removeClass('d-none alert-success').empty().html(data.message);
                                $.ajax({
                                    url: index + '?view=html',
                                    success: function (data) {
                                        $('#items').empty().append(data);
                                    }
                                });
                            },
                            error: function (data) {}
                        });
                    }
                },
                cancel: function () {}
            }
        });
    });
</script>
@endsection
