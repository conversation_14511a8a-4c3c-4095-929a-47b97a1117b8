<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->after('business_hours_description', function ($table) {
                $table->longText('terms_condition')->nullable();
                $table->longText('terms_condition_review')->nullable();
            });
            $table->after('site_logo', function ($table) {
                $table->string('email_template_logo')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn('terms_condition');
            $table->dropColumn('terms_condition_review');
            $table->dropColumn('email_template_logo');
        });
    }
}
