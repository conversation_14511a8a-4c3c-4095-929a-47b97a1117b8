<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCouponValueToPromosTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('promos') && !Schema::hasColumn('promos', 'coupon_value')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->string('coupon_value')->nullable();
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('promos') && Schema::hasColumn('promos', 'coupon_value')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->dropColumn('coupon_value');
            });
        }
    }
}
