<?php

namespace App\Listeners;

use App\Events\LowInventory;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendLowInventoryNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(LowInventory $event)
    {
        $this->notifier->sendLowInventoryNotification($event->user, $event->productItem);
    }
}
