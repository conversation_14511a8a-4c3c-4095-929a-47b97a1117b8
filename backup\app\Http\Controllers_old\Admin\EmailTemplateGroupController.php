<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailTemplateGroup;
use Illuminate\Http\Request;

class EmailTemplateGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Email Template Groups';
        $items = EmailTemplateGroup::orderBy('email_template_groups.id', 'desc')->paginate($this->itemPerPage);
        foreach($items as $value){
            // $value->order_status = $this->orderStatus($value->order_status_id);
            if($value->order_status_id){
                $value->title =  'Order '.$this->orderStatus($value->order_status_id);
            }
        }

        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.email_template_groups', compact('items', 'sl'));
        }

        return view('admin.email_template.group.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Email Template group';
        $email_template_types = $this->email_template_types();
        $system_email_groups = $this->system_email_groups();
        $order_status = $this->orderStatus();
        return view('admin.email_template.group.create', compact('admin_page_title','email_template_types', 'system_email_groups', 'order_status'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'nullable|string|max:255',
            'email_template_type' => 'required|string|max:255',
            'system_group' => 'nullable|string|max:255',
            'order_status_id' => 'nullable|string|max:255',
            'status' => 'nullable|boolean',
        ]);

        $item = new EmailTemplateGroup;
        $item->title = $request->title;
        $item->order_status_id = $request->order_status_id;
        $item->system_group = $request->system_group;
        $item->type = $request->email_template_type;
        $item->status = $request->status ?? 0;
        $item->save();

        $message = 'Group successfully added.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.email_template_groups.index')->with('success', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmailTemplateGroup  $emailTemplateGroup
     * @return \Illuminate\Http\Response
     */
    public function show(EmailTemplateGroup $emailTemplateGroup)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EmailTemplateGroup  $emailTemplateGroup
     * @return \Illuminate\Http\Response
     */
    public function edit(EmailTemplateGroup $emailTemplateGroup)
    {
        $admin_page_title = 'Edit Email Template Group';
        $email_template_types = $this->email_template_types();
        $system_email_groups = $this->system_email_groups();
        $item = $emailTemplateGroup;
        $item->order_status = $this->orderStatus();

        return view('admin.email_template.group.edit', compact('admin_page_title', 'email_template_types', 'system_email_groups', 'item'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EmailTemplateGroup  $emailTemplateGroup
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmailTemplateGroup $emailTemplateGroup)
    {
        $this->validate($request, [
            'title' => 'nullable|string|max:255',
            'email_template_type' => 'required|string|max:255',
            'system_group' => 'nullable|string|max:255',
            'order_status_id' => 'nullable|string|max:255',
            'status' => 'nullable|boolean',
        ]);

        $emailTemplateGroup->order_status_id = $request->order_status_id;
        $emailTemplateGroup->type = $request->email_template_type;
        $emailTemplateGroup->system_group = $request->system_group;
        $emailTemplateGroup->title = $request->title;
        $emailTemplateGroup->status = $request->status ?? 0;
        $emailTemplateGroup->update();

        $message = 'template successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.email_template_groups.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmailTemplateGroup  $emailTemplateGroup
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmailTemplateGroup $emailTemplateGroup)
    {
        $emailTemplateGroup->delete();

        $message = '<strong>' . $emailTemplateGroup->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }
}
