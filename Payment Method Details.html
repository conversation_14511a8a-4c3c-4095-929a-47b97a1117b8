<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1178150984C0" style="width:613px;" class="column-headers-background">A</th></tr></thead><tbody><tr style="height: 20px"><th id="1178150984R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0">Description of Requirements</td></tr><tr style="height: 218px"><th id="1178150984R1" style="height: 218px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 218px">2</div></th><td class="s1">We need payment/transactions methods to be able to complete online order checkout transactions as well as transactions that happen in our physical stores. Payment methods are used charge the customer for whatever transactions they make with us. A single payment or transaction method may offer multiple ways to complete a transaction. These may include, card payments, Mobile wallet payments, ACH payment, Bank transfers, USSD code pyaments, payment through QR codes, etc. There are other payment/transaction methods called Buy Now Pay Later (BNPL). These typically offer to pay on behalf of the customer in exchange for high interest fees. A payment/transaction method will typically accept Fiat (real) Currencies and/or Cryptocurrencies. Some of the traditional methods we will support on our website are: SquadPay, Flutterwave, Stripe payments. PayPal, BTCPay Server. We plan to support more in the future. Some of the BNPLs we want to implement are PayPal Credit, Klarna, Affirm, and Afterpay, More BNPL will be offered in the future if need be</td></tr><tr style="height: 149px"><th id="1178150984R2" style="height: 149px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 149px">3</div></th><td class="s1">A payment/transaction method will have an API which should be configurable from the backend with it features implemented to be as dynamic as possible. This will enable us to add new payment method APIs easily when need. So, we will need to understand all common and advanced features that thse payment methods share and make them configurable from the backend</td></tr><tr style="height: 95px"><th id="1178150984R3" style="height: 95px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 95px">4</div></th><td class="s1">Typically, the providers of these payment/transaction methods state on their websites which countries they can be used for and which currencies are allowed. These should also be configurable in our backend</td></tr><tr style="height: 154px"><th id="1178150984R4" style="height: 154px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 154px">5</div></th><td class="s1" dir="ltr">Each transaction will have a fee, we need to be able to configure from the backend if 1guygadget will pay this fee or if we will pass it on to the customer. Note that BNPLs will charge us higher fees than a traditional method. In all cases, we wil never show the customer fees. Fees will be integrated into SKU pricing and/or Currency exchange rates.</td></tr><tr style="height: 196px"><th id="1178150984R5" style="height: 196px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 196px">6</div></th><td class="s1">There may be occassions where a customer does not have enough money one bank account or credit/debit or mobile money wallet to complete a transaction. The Split pay scheme offers the opportunity to complete the order into 2 transactions instead of 1. Each transaction incurs a transaction fee which if not handle properly of non-configurable, will be passed on to us. Transaction fees eat up profit margins. Our implementation should offer split pay options for such cases when needed. For example, a split pay scheme was  statically implemented for SquadPay only when a customer wants to pay with Nigerian Naira (NGN), but splitpay was not implement for USD or (EUR and POUNDS) payments. It would be nice to extend the split pay implementation to be dynamic and applicable to other currencies and payment methods. The split values are currently also statically implemented. They are 50-50% for 1st  and 2nd transaction, or 60-40%, or 80-20%.  </td></tr><tr style="height: 239px"><th id="1178150984R6" style="height: 239px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 239px">7</div></th><td class="s1">Some payment method providers like Squad pay and Flutterwave have a unique requirement that has been observed  in the test/sandbox env but not sure if it also happens in the production environment. A single transaction cannot exceed 5,000,000 NGN. So we had to implement a work around for this. The work arround was to conduct multiple partial transactions with the limit amount and then the last partial transaction will be whatever is left of order total. This posses a unique situation/problem for us. A transaction which would otherwise had cost a single transaction fee may now cost 2 or more. We may see incresed cart abandonment if we decide to pass all fees to the customer. On the other hand, 1GuyGadget will take a margin hit if it pays all of these fees. How do we handle who pays this? We may do a smart scheme where the burdern is split between us and the customer, or we pay the fees or the customer pays the fees. Another option maybe to prombt the customer to remove excess items from cart. However, this is not a good solution because we loose potential income if customer removes items from cart. I&#39;m open to you suggestions here</td></tr><tr style="height: 93px"><th id="1178150984R7" style="height: 93px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 93px">8</div></th><td class="s1">A payment/transaction method will typically have a provision for Refunds. This needs to be taken into account for our implementation.</td></tr><tr style="height: 176px"><th id="1178150984R8" style="height: 176px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 176px">9</div></th><td class="s1">Occassional Discounts will be offererd depending on the payment method and payment currency selected selected. Generally, discounts are used to discourage customer from paying in their local currencies if they can pay in USD, EURO, POUNDS or toher high-valued currencies. Such payments eliminate transaction conversion fees that a payment processor link Strip for instance will charge to convert from a local currency t o USD/EURO/POUND  or toher high-valued currencies. Even this feature needs to be configurable. Currently, it has a static implementation</td></tr><tr style="height: 272px"><th id="1178150984R9" style="height: 272px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 272px">10</div></th><td class="s1"><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Some of common Backend features:</span><span style="font-size:10pt;font-family:Arial;color:#000000;"> <br>- Payment/Transaction method title <br>- Subtitle<br>- Logo<br>- API Keys<br>- Discount type and discount value<br>- Allowed Countries: Which SHIP TO country locations will this method appear at check out for<br>- Allowed Currencies: What currencies does this method accept<br>- Split pay<br>- Bill Transaction Fee to: options could be Customer or System. We could add a 3rd option which is to split it. Your input is requested.<br>- Let&#39;s discuss any other features that you consider necessary</span></td></tr><tr style="height: 165px"><th id="1178150984R10" style="height: 165px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 165px">11</div></th><td class="s1"><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Frontend Features:<br></span><span style="font-size:10pt;font-family:Arial;color:#000000;">You have almost every component of the UI/UX currently in the code base. I will let you know if any changes are needed. <br>- The payment /transactionmethods are displayed in the frontend inside a custom accordion. <br>- The payment/transaction methods that show inside this accordion whill depend on the SHIP TO location selected by the customer and the &quot;Allowed Countres&quot; configured in the backend.<br>- Each method is displayed is a segment of the accordion. A method will have a logo, sub text, and the payment options configured in the backend<br></span></td></tr></tbody></table></div>