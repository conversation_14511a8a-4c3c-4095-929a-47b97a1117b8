<div id="accordionChildMenu{{ $menu->id }}">
    <div class="accordion child-menu-accordion level_{{ $level }}" data-parent_menu_id="{{ $menu->id }}">
        @foreach ($items as $item)
        <div class="card">
            <div class="card-header" id="heading{{ $item->id }}">
                @if(count($item->children)> 0)
                <a class="child-menu-item child_menu_item_{{ $item->id }} {{ $item->menu_active ?'active ':'' }}{{ count($item->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" {{$item->url_type=='custom_url'?'target="_blank"':''}}><span>{{$item->menu_name?$item->menu_name:$item->title}}</span></a>

                <button class="btn collapsed accordion-child-btn btn_child_menu_item_{{ $menu->id }}" type="button" data-toggle="collapse" data-target="#collapse{{ $item->id }}" aria-expanded="false" aria-controls="collapse{{ $item->id }}" data-acc_item_id="{{ $item->id }}">
                    <i class="fas fa-plus"></i><i class="fas fa-minus"></i>
                </button>
                @else
                <a class="{{ $item->menu_active ?'active ':'' }}{{ count($item->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" {{$item->url_type=='custom_url'?'target="_blank"':''}}><span>{{$item->menu_name?$item->menu_name:$item->title}}</span></a>
                @endif
            </div>

            @if(count($item->children)> 0)
            <div id="collapse{{ $item->id }}" class="collapse" aria-labelledby="heading{{ $item->id }}">
                <div class="card-body">
                    @include('layouts.utility.menu.sub_menu_menu', [
                        'items' => $item->children,
                        'menu' => $item,
                        'level' => $level + 1
                        // 'level' => 3
                    ])
                </div>
            </div>
            {{-- <script type="text/javascript">
            setTimeout(function() {
                let id = "{{ $menu->id }}";
                let accordion_body_height = $('#collapse'+id).attr('data-height');
                $('.accordion.child-menu-accordion .card #collapse'+id +' .card-body').css('height',accordion_body_height+'px');
            }, 300);
            </script> --}}
            @endif
        </div>
        @endforeach
    </div>

    {{-- <script>
        $(document).ready(function() {
            $('.accordion-child-btn').on('click', function() {
                var acc_item_id = $(this).attr('data-acc_item_id');
                if ($(this).hasClass('collapsed')) {
                    $('.child_menu_item_' + acc_item_id).addClass('active');
                } else {
                    $('.child_menu_item_' + acc_item_id).removeClass('active');
                }
            });
        });
    </script> --}}
</div>

{{-- @if(count($items))
<ul class="sub-menu clearfix">
    @foreach ($items as $item)
       @if($item->access)
            <li>
               <a id="menu_id_{{ $item->id }}" class="sub-menu-item menu_id_{{ $item->id }} {{ $item->menu_active ?'active ':'' }}@if(count($item->menuChildren) > 0)have-sub-menu @endif" data-menu_id="{{ $item->id }}" href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" {{$item->url_type=='custom_url'?'target="_blank"':''}}>
                   @if($item->menu_icon)<i class="{{ $item->menu_icon }}"></i>@endif
                   <span>{{$item->menu_name?$item->menu_name:$item->title}}</span>
                   @if(count($item->menuChildren) > 0)
                    <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                        <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                    </svg>
                   @endif
               </a>
               @if($item->menuChildren)
               @include('layouts.utility.menu.sub_menu',[
                   'items' => $item->menuChildren,
                ])
               @endif
           </li>
       @endif
    @endforeach
</ul>
@endif --}}
