<?php

namespace App\Listeners;

use App\Events\OrderPlaced;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendOrderPlacedNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(OrderPlaced $event)
    {
        $this->notifier->sendCustomerNewOrderNotification($event->order, $event->cart);
        //$this->notifier->sendAdminOrderPlacedNotifications($event->order, $event->cart);
    }
}
