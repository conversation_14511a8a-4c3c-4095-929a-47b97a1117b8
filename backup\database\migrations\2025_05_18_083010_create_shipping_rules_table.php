<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingRulesTable extends Migration
{
    public function up()
    {
        Schema::create('shipping_rules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('store_id');         // The store that ships
            $table->string('destination_country', 2);       // ISO 2-letter country code to ship to
            $table->timestamps();

            $table->foreign('store_id')->references('id')->on('settings')->onDelete('cascade');
            $table->unique(['store_id', 'destination_country']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('shipping_rules');
    }
}
