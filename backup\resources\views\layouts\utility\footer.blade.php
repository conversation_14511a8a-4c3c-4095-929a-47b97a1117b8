@php
use App\Http\Controllers\Controller;
use App\Models\Coupon;
$location_data = Session::get('shop_logged_data');
$settings = App\Models\Setting::where('status', true)->get()->unique('country'); // Debugging the output
$countries = (new Controller())->countries();
$promo = Coupon::where('show_in_promo_popup', true)->where('status', true)->first();
$location_data = Session::get('shop_logged_data');
@endphp
<style>
    .modal.discount-modal .modal-content .modal-body h3 {
        font-size: 70px;
    }

    .sticky-alert {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 9999;
        width: 90%;
        max-width: 500px;
        text-align: center;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    }

    @keyframes rotate360 {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    .rotate {
        animation: rotate360 1s ease-in-out;
    }
</style>
<footer class="pt-4">
    <div id="top">
        <div class="container">
            <div class="row">
                <div class=" col-md-7 col-xl-8">
                    <div class="block footer-menu">
                        <div class="row">
                            <div class="col-md-6 col-lg-5">
                                <h3>PHONE<a href="#">(112) 434-32323</a></h3>
                                <ul>
                                    <li><a href="#">History</a></li>
                                    <li><a href="#">Our Team</a></li>
                                    <li><a href="#">Mission Statement</a></li>
                                    <li><a href="#">Terms & Condition</a></li>
                                    <li><a href="#">Privacy Policy</a></li>
                                </ul>
                            </div>
                            <div class="col-md-6 col-lg-5">
                                <h3>EXTRAS</h3>
                                <ul>
                                    <li><a href="{{ $page->view_store_info_url }}">Store Address</a></li>
                                    <li><a href="#">News and stories</a></li>
                                    <li><a href="#">Publications</a></li>
                                    <li><a href="#">Take action</a></li>
                                    <li><a href="#">Recomendations</a></li>
                                    <li><a href="#">Help</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=" col-md-5 col-xl-4 align-self-end">
                    <div class="block subscribe">
                        <p><a href="#"><img src="{{ asset('images/setting/logo_white.svg') }}" alt="1GugGydget"
                                    class="w-100"></a></p>
                        <!-- <h4>Sign Up To Receive<br />Our Newsletter</h4> -->
                        <h4>Join Our <br>Message Subscription List</h4>
                        <form id="subscribe-form" method="POST" action="{{ route('subscribe') }}">
                            @csrf
                            <div class="form-control"
                                style="display:flex; align-items:center; color:white; margin-bottom:15px">
                                <input type="email"
                                    style="background-color: black; width:100%; border:none; outline:none; color:white;"
                                    placeholder="Email" id="email" name="email" />
                            </div>
                            <div class="form-control" style="display:flex; align-items:center; color:white;">
                                <x-phone-input name="phone" />
                            </div>
                            <span id="form-error" class="text-danger small mt-2 d-block " style="display:none;"></span>
                            <span id="form-success" class="text-success small mt-2 d-block " style="display:none;"></span>

                            <p class="terms">You agree to receive automated promotional and personalized text messages from 1GuyGadget. You also agree to our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a><br>This agreement isn’t a condition of any purchase. Msg & Data rates may apply. Msg frequency varies. Reply OUT or STOP to cancel subscription or HELP for help.</p>
                            <button type="submit" class="btn btn-primary btn-design btn-lg btn-sub-lg" id="subscribe-button">
                                <span class="button-text fw-bold text-uppercase">Subscribe</span>
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                            </button>
                            <input type="hidden" name="sub_type" value="normal" />
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="copyright">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="block text-center">
                        <p class="mb-0 text-light"><small>&copy;2019 - {{ date('Y') }} 1GugGydget. All rights
                                reserved.</small></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<div class="social-horizontal-bar position-fixed">
    <ul>
        <li><a href="#"><img src="{{ asset('images/soicals/instagram.png') }}" alt=""></a></li>
        <li><a href="#"><img src="{{ asset('images/soicals/facebook.png') }}" alt=""></a></li>
        <li><a href="#"><img src="{{ asset('images/soicals/twitter.png') }}" alt=""></a></li>
    </ul>
</div>
<div class="modal fade" id="userLoginModal" tabindex="-1" aria-labelledby="userLoginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="system_message_modal_loader loader">
                <div class="grid-loader">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="modal-header">
                <h6 class="modal-title text-uppercase" id="userLoginModalLabel"><strong>Customer Login</strong></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                <div class="system_message_modal alert d-none">
                    <span class="system_message_modal_text"></span>
                    {{-- <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i
                            class="fa-solid fa-xmark"></i></button> --}}
                </div>
                <p class="text-center" id="userLoginModalText"></p>
                <form action="{{ route('login') }}" method="post" enctype="multipart/form-data" id="userLogin">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="custom-floating-label">
                                <input type="text" name="email"
                                    class="form-control  mb-0 {{ $errors->has('email') ? 'is-invalid' : '' }}"
                                    id="email" placeholder=" " value="{{ old('email') }}">
                                <label for="email" class="form-label">Email Address</label>
                            </div>
                            <small id="signin_email_error" class="form-text text-danger"></small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="custom-floating-label">
                                <input type="password" name="password"
                                    class="form-control mb-0 {{ $errors->has('password') ? 'is-invalid' : '' }}"
                                    id="login_password" placeholder=" ">
                                <label for="login_password" class="form-label">Password</label>
                            </div>
                            <small id="signin_password_error" class="form-text text-danger"></small>
                        </div>
                    </div>
                    <div class="mb-2 clearfix">
                        <div class="form-check form-switch float-right">
                            <input class="form-check-input" type="checkbox" role="switch" id="switch">
                            <label class="form-check-label" for="switch">Show Password</label>
                        </div>
                    </div>
                    {{-- <div class="form-group{{ $errors->has('g-recaptcha-response') ? ' has-error' : '' }}">
                    <div class="col-md-12">
                        {!! RecaptchaV3::field('site_login') !!}
                        @if ($errors->has('g-recaptcha-response'))
                        <span class="help-block">
                            <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                        </span>
                        @endif
                        <input type="hidden" name="g-recaptcha-response"
                            value="{{ old('g-recaptcha-response') }}" />
                    </div>
            </div> --}}
            <div class="my-3 my-md-4 text-center">
                <button id="user_login_button" class="btn btn-dark btn-design btn-guygadget text-uppercase"
                    type="submit"><strong>Login</strong></button>
            </div>
            <div class="socials my-3 clearfix">
                <div class="text-divider">OR</div>
                <div class="row">
                    <div class="col-5 col-md-4">
                        <p class="mb-0 text-uppercase social-text text-uppercase">Login with</p>
                    </div>
                    <div class="col-7 col-md-8">
                        <ul class="text-center">
                            <li><a href="#"><i class="fa-brands fa-square-facebook"></i></a></li>
                            <li><a href="#"><i class="fa-brands fa-google"></i></a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <p class="t"><small>By continuing you agree to our Terms and Conditions and our Privacy
                    Policy</small></p>
            <p class="text-center"><a class="btn btn-link" data-toggle="modal"
                    data-target="#forgotPasswordModal" href="javascript:void(0)">Forgot your password?</a></p>
            </p>
            {{-- <p class="text-center"><a class="text-dark" href="{{ route('password.request') }}">Forgot your
            password?</a></p> --}}
            <p class="text-center">Don't have an account? <a class="btn btn-sm btn-outline-dark mt-3"
                    data-toggle="modal" data-target="#createAccountModal" href="javascript:void(0)">CREATE AN
                    ACCOUNT</a></p>
            </form>
        </div>
    </div>
</div>
</div>
<div class="modal fade" id="createAccountModal" tabindex="-1" aria-labelledby="createAccountModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="system_message_modal_loader loader">
                <div class="grid-loader">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="modal-header">
                <h6 class="modal-title text-uppercase" id="createAccountModalLabel"><strong>Create an Account</strong>
                </h6>
                <h6 class="modal-title text-uppercase continue_shopping d-none"><strong>YOUR ACCOUNT HAS BEEN
                        CREATED</strong></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                {{-- <div class="system_message_modal alert d-none"> --}}
                <p class="system_message_modal_text text-center"></p>
                {{-- <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i
                            class="fa-solid fa-xmark"></i></button> --}}
                {{--
                </div> --}}
                <p class="text-center"><a class="text-uppercase btn btn-design btn-dark continue_shopping d-none"
                        href="{{ $page->view_products_url }}"><strong>Continue shopping</strong></a></p>
                <form method="post" enctype="multipart/form-data" action="{{ route('register') }}" id="createAccount">
                    @csrf
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="text" name="username" class="form-control  mb-0" id="username" placeholder=" "
                                value="">
                            <label for="email" class="form-label">Username</label>
                        </div>
                        <small id="username_error" class="form-text text-danger"></small>
                    </div>
                    {{-- <div class="mb-2">
                        <input type="text" class="form-control" id="username" name="username" placeholder="username">
                        <small id="username_error" class="form-text text-danger"></small>
                    </div> --}}
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="text" class="form-control" id="first_name" name="first_name" placeholder=" ">
                            <label for="first_name" class="form-label">First Name</label>
                        </div>
                        <small id="first_name_error" class="form-text text-danger"></small>
                    </div>
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="text" class="form-control" id="last_name" name="last_name"
                                placeholder="Last Name">
                            <label for="last_name" class="form-label">Last Name</label>
                        </div>
                        <small id="last_name_error" class="form-text text-danger"></small>
                    </div>
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="register_email" class="form-control" id="register_email" name="email"
                                placeholder="Email Address">
                            <label for="register_email" class="form-label">Email Address</label>
                        </div>
                        <small id="email_error" class="form-text text-danger"></small>
                    </div>
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="password" class="form-control" id="password" name="password"
                                placeholder="Password">
                            <label for="password" class="form-label">Password</label>
                        </div>
                        <small id="password_error" class="form-text text-danger"></small>
                    </div>
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="password" class="form-control" id="password_confirmation"
                                name="password_confirmation" placeholder="Confirm New Password">
                            <label for="password_confirmation" class="form-label">Confirm New Password</label>
                        </div>
                        <small id="password_confirmation_error" class="form-text text-danger"></small>
                    </div>
                    <div class="mb-2 clearfix">
                        <div class="form-check form-switch float-right">
                            <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                            <label class="form-check-label" for="flexSwitchCheckDefault">Show Password</label>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="input-group">
                            <input name="tel_cell" type="tel" id="tel" value="" />
                            <small id="valid-msg" class="d-none form-text text-success"></small>
                            <small id="error-msg" class="d-none form-text text-danger"></small>
                        </div>
                        <small id="tel_cell_error" class="form-text text-danger"></small>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md-12 mb-md-0 mb-2 country-select">
                            @php($country = $page->location_data ? $page->location_data->country : '')
                            <select class="selectpicker form-control custom-floating-label " name="country" id="country"
                                data-live-search="true">
                                @foreach ($page->countries as $key => $value)
                                <option value="{{ $key }}" {{ $key==$country ? 'selected' : null }}>
                                    {{ $value }}
                                </option>
                                @endforeach
                            </select>
                            {{-- <div class="custom-floating-label">
                                <input type="text" class="form-control" id="country" name="country" placeholder=" ">
                                <label for="country" class="form-label">Country</label>
                            </div> --}}
                            <small id="country_error" class="form-text text-danger"></small>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="text" class="form-control" id="address" name="address" placeholder="Address"
                                v-model="searchKey" @keyup.enter="setDeliveryAddress">
                            <label for="address" class="form-label">Address</label>
                        </div>
                        <small id="address_error" class="form-text text-danger"></small>
                    </div>
                    <div class="mb-2">
                        <div class="custom-floating-label">
                            <input type="text" class="form-control" id="address_2" name="address_2"
                                placeholder="Address 2">
                            <label for="address_2" class="form-label">Address 2</label>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md-12 mb-md-0 mb-2 country-select">
                            <select id="state" name="state" class="form-control custom-floating-label ">
                                <option>Select a State</option>
                            </select>
                            <small id="state_error" class="form-text text-danger"></small>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md-6 mb-md-0 mb-2">
                            <div class="custom-floating-label">
                                <input type="text" class="form-control" id="city" name="city" placeholder=" ">
                                <label for="city" class="form-label">City</label>
                            </div>
                            <small id="city_error" class="form-text text-danger"></small>
                        </div>
                        <div class="col-md-6">
                            <div class="custom-floating-label">
                                <input type="number" min='1' class="form-control" id="postal_code" name="zip_code"
                                    placeholder=" ">
                                <label for="zip_code" id="postal_code_label" class="form-label">Zip Code</label>
                            </div>
                            <small id="zip_code_error" class="form-text text-danger"></small>
                        </div>
                    </div>
                    <div class="form-group mb-0">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="email_notifications"
                                name="email_notifications" value="1">
                            <label class="custom-control-label" for="email_notifications">Send me newsletters &
                                offers</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="sms_notifications"
                                name="sms_notifications" value="1">
                            <label class="custom-control-label" for="sms_notifications">Send me important SMS
                                notifications</label>
                        </div>
                    </div>
                    <div class="form-group{{ $errors->has('g-recaptcha-response') ? ' has-error' : '' }}">
                        <div class="col-md-12">
                            {{-- {!! RecaptchaV3::field('site_register') !!}
                            @if ($errors->has('g-recaptcha-response'))
                            <span class="help-block">
                                <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                            </span>
                            @endif --}}
                            <input type="hidden" name="g-recaptcha-response" />
                        </div>
                    </div>
                    <div class="my-5 text-center">
                        <button class="btn btn-dark btn-lg btn-design btn-guygadget text-uppercase px-4"
                            type="submit"><strong>Create Account</strong></button>
                    </div>
                    <div class="socials mb-1 clearfix">
                        <div class="text-divider">OR</div>
                        <div class="row">
                            <div class="col-5 col-md-4">
                                <p class="mb-0 text-uppercase social-text text-uppercase">Login with</p>
                            </div>
                            <div class="col-7 col-md-8">
                                <ul class="text-center">
                                    <li><a href="#"><i class="fa-brands fa-square-facebook"></i></a></li>
                                    <li><a href="#"><i class="fa-brands fa-google"></i></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <p><small>By continuing you agree to our Terms and Conditions and our Privacy Policy</small></p>
                    <p class="text-center">Already have an account? <a class="btn btn-sm btn-outline-dark"
                            data-toggle="modal" data-target="#userLoginModal" data-page_url="{{ url()->current() }}"
                            href="javascript:void(0)">LOGIN</a></p>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="system_message_modal_loader loader">
                <div class="grid-loader">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="modal-header">
                <h6 class="modal-title text-uppercase" id="userLoginModalLabel">
                    <strong>{{ __('Reset Password') }}</strong>
                </h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                <div class="system_message_modal alert d-none">
                    <span class="system_message_modal_text"></span>
                    {{-- <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i
                            class="fa-solid fa-xmark"></i></button> --}}
                </div>
                <form action="{{ route('password.email') }}" method="post" enctype="multipart/form-data"
                    id="forgotPassword">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="custom-floating-label">
                                <input id="forgot_email" type="forgot_email"
                                    class="form-control @error('forgot_email') is-invalid @enderror" name="email"
                                    value="{{ old('forgot_email') }}" placeholder=" " autocomplete="" autofocus>
                                <label for="forgot_email" class="form-label">Email Address</label>
                            </div>
                            <small id="forgot_password_email_error" class="form-text text-danger"></small>
                        </div>
                        <div class="form-group{{ $errors->has('g-recaptcha-response') ? ' has-error' : '' }}">
                            <div class="col-md-12">
                                {{-- {!! RecaptchaV3::field('forgot_password') !!}
                                @if ($errors->has('g-recaptcha-response'))
                                <span class="help-block">
                                    <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                                </span>
                                @endif --}}
                                <input type="hidden" name="g-recaptcha-response" />
                            </div>
                        </div>
                    </div>
                    <div class="my-3 text-center">
                        <button class="btn btn-dark btn-design text-uppercase" type="submit"><strong>{{ __('Send
                                Password Reset Link') }}</strong></button>
                    </div>
                    <p class="text-center">Don't have an account? <a class="btn btn-sm btn-outline-dark "
                            data-toggle="modal" data-target="#createAccountModal" href="javascript:void(0)">CREATE AN
                            ACCOUNT</a></p>
                    <p class="text-center">Already have an account? <a class="btn btn-sm btn-outline-dark"
                            data-toggle="modal" data-target="#userLoginModal" data-page_url="{{ url()->current() }}"
                            href="javascript:void(0)">LOGIN</a></p>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="system_message_modal_loader loader">
                <div class="grid-loader">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="modal-header">
                <h5 class="modal-title" id="thankReviewModalLabel"><strong>RESET PASSWORD</strong></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                <div class="system_message_modal alert d-none">
                    <span class="system_message_modal_text"></span>
                    {{-- <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i
                            class="fa-solid fa-xmark"></i></button> --}}
                </div>
                <form action="{{ route('password.update') }}" method="post" enctype="multipart/form-data"
                    id="resetPassword">
                    @csrf
                    <input type="hidden" name="token" value="{{ request()->token }}">
                    {{-- <div class="form-group">
                        <div class="custom-floating-label">
                            <input id="reset_email" type="email"
                                class="form-control {{ $errors->has('email')? 'is-invalid' : '' }}" name="email"
                    value="{{ old('email') }}" id="email" placeholder="Your E-mail" autofocus>
                    <label for="reset_email" class="form-label">Email Address</label>
            </div>
            <small id="email_error" class="form-text text-danger"></small>
        </div> --}}
        <div class="form-group">
            <div class="custom-floating-label">
                <input id="reset_password" type="password"
                    class="form-control {{ $errors->has('password') ? 'is-invalid' : '' }}" name="password"
                    id="password" placeholder=" ">
                <label for="reset_password" class="form-label">New Password</label>
            </div>
            <small id="password_error" class="form-text text-danger"></small>
        </div>
        <div class="form-group">
            <div class="custom-floating-label">
                <input id="reset_password_confirmation" type="password"
                    class="form-control {{ $errors->has('password_confirmation') ? 'is-invalid' : '' }}"
                    name="password_confirmation" id="password-confirmation" placeholder=" ">
                <label for="reset_password_confirmation" class="form-label">Confirm Password</label>
            </div>
            <small id="new_password_error" class="form-text text-danger"></small>
        </div>
        <div class="form-group{{ $errors->has('g-recaptcha-response') ? ' has-error' : '' }}">
            <div class="col-md-12">
                {{-- {!! RecaptchaV3::field('reset_password') !!}
                            @if ($errors->has('g-recaptcha-response'))
                            <span class="help-block">
                                <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                </span>
                @endif --}}
                <input type="hidden" name="g-recaptcha-response" />
            </div>
        </div>
        <div class="form-action my-3 text-center">
            <button type="submit"
                class="btn btn-dark btn-design text-uppercase"><strong>Submit</strong></button>
        </div>
        <p class="text-center">Don't have an account? <a class="btn btn-sm btn-outline-dark"
                data-toggle="modal" data-target="#createAccountModal" href="javascript:void(0)">CREATE AN
                ACCOUNT</a></p>
        <p class="text-center">Already have an account? <a class="btn btn-sm btn-outline-dark"
                data-toggle="modal" data-target="#userLoginModal" data-page_url="{{ url()->current() }}"
                href="javascript:void(0)">LOGIN</a></p>
        </form>
    </div>
</div>
</div>
</div>
@auth
<div class="modal fade" id="leaveReviewModal" tabindex="-1" aria-labelledby="leaveReviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-uppercase" id="leaveReviewModalLabel">Write Review</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                <h6>Hi {{ auth()->user()->first_name . ' ' . auth()->user()->last_name }}! you will receive 10 reward
                    points for this review!</h6>
                <strong>Not you?</strong>&nbsp;&nbsp;&nbsp;<a href="{{ route('logout') }}"
                    class="btn btn-sm btn-outline-dark"
                    onclick="event.preventDefault(); document.getElementById('review_logout_form').submit();">
                    Logout
                    <form id="review_logout_form" action="{{ route('logout') }}" method="POST" style="display: none;">
                        {{ csrf_field() }}
                    </form>
                </a>
                <form class="form" id="submitLeaveReview" action="{{ route('post.product_review') }}" method="post"
                    enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="rating" id="write_review_rating" value="0">
                    <input type="hidden" name="product_id" id="product_id" value="">
                    <input type="hidden" name="product_sku_s" id="product_sku_s" value="">
                    <div class="row mb-4 mt-1">
                        <div class="col-md-12">
                            <select data-width="100%" name="product_item_id" id="product_item_id"
                                class="form-select selectpicker" title="What Products you are reviewing?">
                                @foreach ($page->products as $value)
                                <!-- <option value="{{ $value->product_item_id }}" data-sku="{{ $value->sku_details }}"
                                    data-content="<div><strong>{{ $value->product_title . ' #' . $value->order_no }}</strong><br /><small>{{ $value->title . ' ' . $value->sub_title . ' (' . $value->condition . ')' }} SKU: {{ $value->sku_details }}</small></div>">
                                    {{ $value->product_title . ' #' . $value->order_no }}
                                </option> -->
                                <option value="{{ $value->product_item_id }}" data-sku="{{ $value->sku_details }}"
                                    data-content="<div><strong>{{ $value->product_title . ' #' . $value->order_no }}</strong><br /><small>{{ $value->title . ' ' . $value->sub_title . ' (' . $value->condition . ')' }}</small></div>">
                                    {{ $value->product_title . ' #' . $value->order_no }}
                                </option>
                                @endforeach
                                {{-- <div>{{ $value->title.' '$value->sub_title.' ('.$value->condition.')' }}
                        </div> --}}
                        </select>
                        {{-- <select data-width="100%" name="product_id" id="review_product_id"
                                class="form-select selectpicker" title="What Products you are reviewing?">
                                @foreach ($page->products as $value)
                                <option value="{{ $value->id }}" {{ old('product_id')==$value->id ? 'selected' : ''
                                    }}>{{ $value->title }}</option>
                        @endforeach
                        </select>
                        <small id="product_id_error" class="form-text text-danger"></small> --}}
                    </div>
                    {{-- <div class="col-md-12">
                            <select data-width="100%" name="product_item_id" id="review_product_item_id"
                                class="form-select selectpicker" title="Select"></select>
                            <small id="product_item_id_error" class="form-text text-danger"></small>
                        </div> --}}
            </div>
            <div class="row mb-2 mt-1">
                <div class="col-md-12">
                    <p class="mb-2">Your Ratings</p>
                    <div class="rating-stars">
                        <ul class="write-stars">
                            <li class='write-star' title='Poor' data-value='1'>
                                <i class='fa fa-star fa-fw'></i>
                            </li>
                            <li class='write-star' title='Fair' data-value='2'>
                                <i class='fa fa-star fa-fw'></i>
                            </li>
                            <li class='write-star' title='Good' data-value='3'>
                                <i class='fa fa-star fa-fw'></i>
                            </li>
                            <li class='write-star' title='Excellent' data-value='4'>
                                <i class='fa fa-star fa-fw'></i>
                            </li>
                            <li class='write-star' title='WOW!!!' data-value='5'>
                                <i class='fa fa-star fa-fw'></i>
                            </li>
                        </ul>
                    </div>
                    <div class='success-box'>
                        <div class='text-message'></div>
                    </div>
                    <small id="rating_error" class="form-text text-danger"></small>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md-12">
                    <div class="row pb-2">
                        <div class="col-md-6">
                            <label for="last_name" class="form-label mb-0">Your Message</label>
                        </div>
                        <div class="col-md-6 text-right">
                            <a class="text-dark" href="#">Review Guidelines</a>
                        </div>
                    </div>
                    <textarea type="text" rows="6" class="form-control"
                        name="review_message">{{ old('review_message') }}</textarea>
                    <small id="review_message_error" class="form-text text-danger"></small>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md-12">
                    <p class="info-text type-1">Add Pictures and Video (optional)</p>
                    <p class="info-text type-1"><i><small>A Maximum of 4 photos and 1 video allowed.<br />All
                                files must be less that 20 MB in size.</small></i></p>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="add-items clearfix">
                            <div class="custom-file">
                                <input class="custom-file-input file-uploads" name="review_images" type="file"
                                    data-type="image" data-icon="far fa-image" id="review_images"
                                    data-url="{{ route('web_auth.images.files.upload.temp', ['review_images', 'view' => 'review']) }}">
                                <label class="custom-file-label" for="review_images">
                                    <i class="fa-solid fa-plus"></i>
                                </label>
                            </div>
                            <small class="form-text text-danger review_images_error">{{
                                        $errors->first('review_images') }}</small>
                            <div class="progress review_images_progress mt-2 d-none">
                                <div class="progress-bar progress-bar-success"></div>
                            </div>
                        </div>
                        <div class="images clearfix" id="files_review_images">
                            @if (count($page->review_images) > 0)
                            @foreach ($page->review_images as $image)
                            <div class="image {{ $image->file_id }}_image clearfix">
                                <div class="row">
                                    <div class="col-md-4">
                                        <a href="{{ $image->file_path ? asset('storage/session_files/' . Auth()->user()->username . '/' . 'review_images' . '/' . $image->file_path) : '#' }}"
                                            data-fancybox="products" data-caption>
                                            <img class="review_images_{{ $image->file_id }}"
                                                src="{{ asset('storage/session_files/' . Auth()->user()->username . '/' . 'review_images' . '/' . $image->file_path) }}">
                                            <i
                                                class="review_images_icon_{{ $image->file_id }} far fa-image d-none"></i>
                                        </a>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <button type="button" class="btn btn-danger remove-file btn-sm"
                                            data-icon="far fa-images" data-name="User image"
                                            data-id="{{ $image->file_id }}" data-toggle="review_images"
                                            data-url="{{ route('web_auth.images.files.remove.temp', ['review_images', $image->file_id]) }}"><i
                                                class="fas fa-trash-alt"></i></button>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-5 mb-2">
                <div class="col-md-12">
                    <h6 class="terms-title"><strong>Terms and Conditions</strong></h6>
                    <ul class="terms">
                        <li><span>1.</span>1GuyGadget account members will get at least 10 points for each
                            published review when a member provides a valid My Best Buy member number. Receive
                            points for up to eight 25-point reviews per calendar year. Some point offers expire,
                            so act quickly. Please allow up to 30 days for points to appear in your account.
                            Subject to My 1GuyGadget <a href="#">Program Terms</a>. Subject to change
                            without notice. 1GuyGadget may withdraw offer prior to expiration</li>
                        <li><span>2.</span>You will receive an email once your review has been posted and if
                            someone replies to your review.</li>
                    </ul>
                </div>
            </div>
            <div class="my-5 text-center">
                <button class="btn btn-dark btn-lg btn-design btn-guygadget text-uppercase"
                    type="submit"><strong>Submit</strong></button>
            </div>
            </form>
        </div>
    </div>
</div>
</div>
@endauth
<div class="modal fade" id="thankReviewModal" tabindex="-1" aria-labelledby="thankReviewModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="thankReviewModalLabel">Thanks You!</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                <p class="text-center px-5">Your review and rewards are being processed. Rewards will be assigned in
                    about 30 days, and your review will be published if it complies with our <a class="text-dark"
                        href="#">review guidance.</a></p>
                <p class="text-center mb-0">Want to review another product?</p>
                <p class="text-center mb-0"><a href="#" class="btn btn-dark btn-lg btn-design btn-guygadget">WRITE
                        REVIEW</a></p>
                <p class="text-center mb-0"><a class="text-dark" href="#">Edit Previous Review(s)</a></p>
            </div>
        </div>
    </div>
</div>
<div class="modal discount-modal fade {{ session('discount_modal_closed') || session('discount_fully_dismissed') ? '' : '' }}" id="discountModal" data-backdrop="static" data-keyboard="false" tabindex="-1"
    aria-labelledby="discountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <button type="button" class="close close-discount" data-dismiss="modal" aria-label="Close"><i
                    class="fa-solid fa-xmark"></i></button>
            <button type="button" class="btn btn-back promo_regi_popup_back_btn d-none position-absolute top-0 start-50 translate-middle-x mt-2" style="z-index:999; width: fit-content;"
                style="left: 10px;right: unset;"><i class="fas fa-arrow-circle-left text-white"></i></button>
            <div class="modal-body">
                <p class="text-center"><img class="standalone-logo-icon" src="{{ url('images/icon/gold_logo.svg') }}"
                        alt=""></p>
                <div id="promo_regi_sys_msg" class="d-none text-center" style="color: rgb(255, 255, 255);">Your subscription
                    successfully saved. Please check text message on your phone &amp; reply START to subscribe.</div>
                <div id="promo-form" class="">
                    @if ($promo)
                    <h3>{{ $promo->name }}</h3>
                    <h4>When you sign up for<br />emails and texts</h4>
                    @endif
                    <div id="mainPromoForm">

                        <form method="post">
                            @csrf
                            <div class="promo_regi_email_field">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="custom-floating-label">
                                            <input type="text" class="form-control form-control-lg mb-0 promo_regi_email_input" id="offer_email"
                                                name="discount_email" placeholder=" ">
                                            <label for="offer_email" class="form-label">Email Address</label>
                                        </div>
                                        <div id="promo_email_error"></div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <button type="button"
                                            class="btn btn-block btn-design btn-light text-uppercase btn-lg promo_regi_email_btn"><strong>CONTINUE <span id="promo_regi_email_submit_btn_spining_icon" class="d-none">
                                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                </span></strong></button>
                                    </div>
                                </div>
                            </div>
                            <div class="promo_regi_phone_field d-none">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="custom-floating-label">
                                            <input type="text" class="form-control form-control-lg mb-0 phone-fcs"
                                                id="promo_regi_phone" name="discount_phone"
                                                autocomplete="off"
                                                data-intl-tel-input-id="1">

                                        </div>
                                        <div id="promo_phone_error"></div>

                                        <div class="py-3">
                                            <p class="mb-0 text-center text-12 lh-16">You agree to receive automated promotional and personalized text messages from <a href="/"><strong class="text-white">1GuyGadget</strong></a>. You also agree to our <a href="#"><strong class="text-white"> Terms of Service</strong></a> and <a href="#"><strong class="text-white">Privacy Policy</strong></a>. This agreement isn’t a condition of any purchase. Msg & Data rates may apply. Msg frequency varies. Reply OUT or STOP to cancel subscription or HELP for help.
                                            </p>
                                        </div>
                                        <div id="promo_regi_phone_error_msg"
                                            class="invalid-feedback-msg-white m_validations_showhide" style="display:none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <button type="button"
                                            class="btn btn-block btn-design btn-light text-uppercase btn-lg promo_regi_submit_btn"><strong>SUBSCRIBE
                                                &nbsp; <span id="promo_regi_submit_btn_spining_icon" class="d-none">
                                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                </span></strong></button>
                                    </div>
                                </div>
                            </div>
                            <div id="promo_errors" class="mt-2"></div>
                            <input type="hidden" name="sub_type" value="promo" />
                            @if ($promo)
                            <input type="hidden" name="promo_id" value="{{$promo->id}}" />
                            @endif

                        </form>
                    </div>
                    <div id="verifyOTP" class="d-none">
                        <div id="otp-message" class="alert alert-success"></div>
                        <form class="">
                            @csrf
                            <input type="number" id="promo-otp" class="form-control form-control-lg" placeholder="Enter OTP" />
                            <div id="promo_otp_error"></div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <button type="button"
                                        class="btn btn-block btn-design btn-light text-uppercase btn-lg promo_otp_submit_btn "><strong>SUBMIT
                                            &nbsp; <span id="promo_otp_submit_btn_spining_icon" class="d-none">
                                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            </span></strong></button>
                                </div>
                            </div>
                            <div id="promo_otp_errors" class="mt-2"></div>

                        </form>
                    </div>
                </div>
                <p class="text-center mt-3">Discount valid on full-price items only - cannot be combined with other
                    offers or discounts.</p>
            </div>
        </div>
    </div>
</div>
{{-- @if (!empty($page->location_data->location_popup)) --}}
<div class="modal fade" id="locationModal" tabindex="-1" aria-labelledby="locationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="system_message_modal_loader loader">
                <div class="grid-loader">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="modal-header">
                {{-- <h6 class="modal-title text-uppercase" id="userLoginModalLabel"><strong></strong></h6> --}}
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body text-center">
                <p><img class="shop_country_image" style="width: 100px; height: auto;"
                        src="{{ asset('images/flags/' . strtolower($page->location_data->country) . '.png') }}" alt="">
                </p>
                {{-- Your location is set to --}}
                @if (!Session::has('shop_logged_data'))
                <h4 id="choose_country_name"><strong>Choose your country</strong></h4>
                @endif
                <h4><strong id="shop_country_name">{{ $page->location_data->country_name }}</strong></h4>
                <p>Change Country/Region</p>
                <form action="{{ route('post.site_location_data') }}" method="post" enctype="multipart/form-data"
                    id="site_location_data">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <select class="selectpicker form-control" data-width="100%" name="location_country"
                                id="location_country" data-live-search="true" {{ $errors->has('country') ? 'is-invalid'
                                : '' }}" title="Country">
                                @foreach ($page->counties_currency as $key => $value)
                                <option value="{{ $key }}" {{ $page->location_data->country == $key ? 'selected' : ''
                                    }}>
                                    {{ $value }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <p id="shop_currency">{!! $page->location_data->currency !!}</p>
                        </div>
                    </div>
                    {{-- <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="custom-floating-label">
                                <input id="forgot_email" type="forgot_email"
                                    class="form-control @error('forgot_email') is-invalid @enderror" name="email"
                                    value="{{ old('forgot_email') }}" placeholder=" " autocomplete="" autofocus>
                    <label for="forgot_email" class="form-label">Email Address</label>
            </div>
            <small id="forgot_password_email_error" class="form-text text-danger"></small>
        </div>
        <div class="form-group{{ $errors->has('g-recaptcha-response') ? ' has-error' : '' }}">
            <div class="col-md-12">
                <input type="hidden" name="g-recaptcha-response" />
            </div>
        </div>
    </div> --}}
    <div class="my-3 text-center">
        <button class="btn btn-dark btn-design text-uppercase"
            type="submit"><strong>Continue</strong></button>
    </div>
    {{-- <p class="text-center"><a class="btn btn-link" data-toggle="modal"
                            data-target="#chnageCountryModal" href="javascript:void(0)">Change country</a></p> --}}
    {{-- <p class="text-center">Already have an account? <a class="btn btn-sm btn-outline-dark"
                            data-toggle="modal" data-target="#userLoginModal" href="javascript:void(0)">LOGIN</a></p>
                    --}}
    </form>
</div>
</div>
</div>
</div>
<div class="modal fade" id="currencyWarningModal" tabindex="-1" aria-labelledby="currencyWarningModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="thankReviewModalLabel">Thanks You!</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i
                        class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="modal-body">
                <p class="text-center px-5">Your payment exceeds the transaction limit.</p>
            </div>
        </div>
    </div>
</div>
@if ($promo)
@if (!session('discount_fully_dismissed'))

<div class="get-discount-tag">
    <p class="mb-0">
        <a class="btn pe-0 btn-off" href="#"><strong>{{ $promo->name }}</strong></a>
        <a class="pe-3 close-discount-tag" href="#"><i class="fa-solid fa-xmark"></i></a>
    </p>
</div>
@endif
@endif
<style>
    .pac-container {
        z-index: 1200 !important;
    }
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/css/intlTelInput.css">
<!--<div class="back-to-top back_to_top"><i class="fas fa-arrow-alt-circle-up"></   i></div>-->
<script src="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/intlTelInput.min.js"></script>
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script> --}}
<script type="text/javascript" src="{{ asset('js/script.js') }}"></script>
<script src="{{ asset('js/file-upload/vendor/jquery.ui.widget.js') }}"></script>
<script src="{{ asset('js/file-upload/jquery.fileupload.js') }}"></script>
<script src="{{ asset('js/country-states.js') }}"></script>
<script src="https://maps.googleapis.com/maps/api/js?libraries=places&key=AIzaSyBX6xSl7_lNgUUuO4Pkhse9NnTS9GJrm5M">
</script>
<script>
    //if (typeof locationData === 'undefined') {
    const locationData = @json($location_data ?? []);
    const promoId = @json(isset($promo) ? $promo->id : null);
    let otpMode = false;
    let promoEmailVisible = true;
    let promoPhoneVisible = false;

    //}
</script>
<script>
    var country = document.getElementById('country');
    var postal_code_label = document.getElementById('postal_code_label');
    country.addEventListener("change", () => {
        if (!(country.value == "US")) {
            postal_code_label.textContent = "Postal Code";
        } else postal_code_label.textContent = "Zip Code";
    });
    document.addEventListener("DOMContentLoaded", function() {
        // Simulate Vuex store commits with plain JS (replace with actual logic)
        const store = {
            commit: function(mutation, payload) {
                console.log(`Committed mutation: ${mutation}`, payload);
                // Implement your own logic here for handling state
            }
        };
        store.commit("getAllStores", []);
        store.commit("getLoading", true);
        $("#country").change(function() {
            $("#address").val("");
            $("#city").val("");
            $("#postal_code").val("");
            const input = document.getElementById("address");
            if (input) {
                var options = {
                    types: ["address"],
                    componentRestrictions: {
                        country: $("#country").val().toUpperCase()
                    },
                };
                console.log($("#country").val().toUpperCase());
                const autocomplete = new google.maps.places.Autocomplete(input, options);
                autocomplete.addListener('place_changed', () => {
                    getAddressDetails();
                    // You can now access place.address_components, place.geometry, etc.
                });
            }
        });
        const input = document.getElementById("address");
        const autocomplete = new google.maps.places.Autocomplete(input);
        autocomplete.addListener('place_changed', () => {
            getAddressDetails();
            // You can now access place.address_components, place.geometry, etc.
        });
        setTimeout(() => {
            store.commit("getLoading", false);
        }, 700);
    });
</script>
<script>
    async function getAddressDetails() {
        const addressInput = document.getElementById('address').value;
        console.log(addressInput);
        const apiKey = 'AIzaSyBX6xSl7_lNgUUuO4Pkhse9NnTS9GJrm5M';
        const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(addressInput)}&key=${apiKey}`;
        try {
            const response = await fetch(url);
            const data = await response.json();
            if (data.results && data.results.length > 0) {
                const addressComponents = data.results[0].address_components;
                let address = "",
                    city = "",
                    state = "",
                    country = "",
                    postal_code = "";
                console.log(data.results[0].address_components);
                addressComponents.forEach(component => {
                    if (component.types.includes("street_number")) {
                        address = component.long_name + " ";
                    }
                    if (component.types.includes("route")) {
                        address += component.long_name;
                    }
                    if (component.types.includes("locality")) {
                        city = component.long_name;
                    }
                    if (component.types.includes("administrative_area_level_1")) {
                        state = component.short_name;
                    }
                    if (component.types.includes("country")) {
                        country = component.short_name;
                    }
                    if (component.types.includes("postal_code")) {
                        postal_code = component.long_name;
                    }
                });
                document.getElementById("city").value = city;
                document.getElementById("state").value = state;
                document.getElementById("address").value = address;
                document.getElementById("postal_code").value = postal_code;
                console.log(state);
                console.log(city);
                let select_state = document.getElementById("state");
                for (let i = 0; i < select_state.options.length; i++) {
                    if (select_state.options[i].value === state) {
                        select_state.selectedIndex = i;
                        break;
                    }
                }
                let select_country = document.getElementById("country");
                for (let i = 0; i < select_country.options.length; i++) {
                    if (select_country.options[i].value === state) {
                        select_country.selectedIndex = i;
                        break;
                    }
                }
            } else {
                alert('Address not found');
            }
        } catch (error) {
            console.error('Error fetching address details:', error);
        }
    }
</script>
<script>
    // user country code for selected option
    var user_country_code = "US";
    (() => {
        // script https://www.html-code-generator.com/html/drop-down/state-name
        // Get the country name and state name from the imported script.
        const country_list = country_and_states.country;
        const state_list = country_and_states.states;
        const id_state_option = document.getElementById("state");
        const id_country_option = document.getElementById("country");
        const create_states_selection = () => {
            // selected country code
            let selected_country_code = id_country_option.value;
            // get state names by selected country-code
            let state_names = state_list[selected_country_code];
            // if invalid country code
            if (!state_names) {
                id_state_option.innerHTML = '<option>Select a State</option>';
                return;
            }
            // create option
            let option = '';
            option += '<option>Select a State</option>';
            state_names.forEach(state => {
                option += '<option value="' + state.code + '">' + state.name + '</option>';
            });
            id_state_option.innerHTML = option;
        };
        // country select change event update state code
        id_country_option.addEventListener('change', create_states_selection);
        create_states_selection();
    })();
</script>
<script>
    const input = document.querySelector("#tel");
    // get the country data from the plugin
    const countryData = window.intlTelInput.getCountryData();
    const addressDropdown = document.querySelector("#country");
    // // populate the country dropdown
    // for (let i = 0; i < countryData.length; i++) {
    // const country = countryData[i];
    // const optionNode = document.createElement("option");
    // optionNode.value = country.iso2;
    // const textNode = document.createTextNode(country.name);
    // optionNode.appendChild(textNode);
    // addressDropdown.appendChild(optionNode);
    // }
    // init plugin
    const iti = window.intlTelInput(input, {
        initialCountry: locationData.country ?? 'US',
        separateDialCode: true,
        hiddenInput: (telInputName) => ({
            phone: "tel_cell"
        }),
        loadUtils: () => import("https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js") // for formatting/placeholders etc
    });
    // set address dropdown's initial value
    addressDropdown.value = iti.getSelectedCountryData().iso2;
    // listen to the telephone input for changes
    input.addEventListener('countrychange', () => {
        addressDropdown.value = iti.getSelectedCountryData().iso2;
    });
    // listen to the address dropdown for changes
    addressDropdown.addEventListener('change', () => {
        iti.setCountry(addressDropdown.value);
    });
</script>
<script>
    if (document.getElementById("guest_address_line_1")) {
        document.addEventListener("DOMContentLoaded", function() {
            // Simulate Vuex store commits with plain JS (replace with actual logic)
            const store = {
                commit: function(mutation, payload) {
                    console.log(`Committed mutation: ${mutation}`, payload);
                    // Implement your own logic here for handling state
                },
            };
            store.commit("getAllStores", []);
            store.commit("getLoading", true);
            $("#guest_country").change(function() {
                $("#guest_address").val("");
                $("#guest_city").val("");
                $("#guest_postal_code").val("");
                const guest_input = document.getElementById("guest_address_line_1");
                if (guest_input) {
                    var options = {
                        types: ["address"],
                        componentRestrictions: {
                            country: $("#guest_country").val().toUpperCase()
                        },
                    };
                    console.log($("#guest_country").val().toUpperCase());
                    const autocomplete = new google.maps.places.Autocomplete(
                        guest_input,
                        options
                    );
                    autocomplete.addListener("place_changed", () => {
                        guest_getAddressDetails();
                        // You can now access place.address_components, place.geometry, etc.
                    });
                }
            });
            const guest_input = document.getElementById("guest_address_line_1");
            const autocomplete = new google.maps.places.Autocomplete(guest_input);
            autocomplete.addListener("place_changed", () => {
                guest_getAddressDetails();
                // You can now access place.address_components, place.geometry, etc.
            });
            setTimeout(() => {
                store.commit("getLoading", false);
            }, 700);
        });
    }
</script>
<script>
    if (document.getElementById("guest_address_line_1")) {
        async function guest_getAddressDetails() {
            const addressInput = document.getElementById("guest_address_line_1").value;
            console.log(addressInput);
            const apiKey = "AIzaSyBX6xSl7_lNgUUuO4Pkhse9NnTS9GJrm5M";
            const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
        addressInput
      )}&key=${apiKey}`;
            try {
                const response = await fetch(url);
                const data = await response.json();
                if (data.results && data.results.length > 0) {
                    const addressComponents = data.results[0].address_components;
                    let address = "",
                        city = "",
                        state = "",
                        country = "",
                        postal_code = "";
                    console.log(data.results[0].address_components);
                    addressComponents.forEach((component) => {
                        if (component.types.includes("street_number")) {
                            address = component.long_name + " ";
                        }
                        if (component.types.includes("route")) {
                            address += component.long_name;
                        }
                        if (component.types.includes("locality")) {
                            city = component.long_name;
                        }
                        if (component.types.includes("administrative_area_level_1")) {
                            state = component.short_name;
                        }
                        if (component.types.includes("country")) {
                            country = component.short_name;
                        }
                        if (component.types.includes("postal_code")) {
                            postal_code = component.long_name;
                        }
                    });
                    //   document.getElementById("guest_address_line_1").value = country;
                    document.getElementById("guest_city").value = city;
                    document.getElementById("guest_state").value = state;
                    document.getElementById("guest_address").value = address;
                    document.getElementById("guest_postal_code").value = postal_code;
                    console.log(state);
                    console.log(city);
                    let select_state = document.getElementById("guest_state");
                    for (let i = 0; i < select_state.options.length; i++) {
                        if (select_state.options[i].value === state) {
                            select_state.selectedIndex = i;
                            break;
                        }
                    }
                    let select_country = document.getElementById("guest_country");
                    for (let i = 0; i < select_country.options.length; i++) {
                        if (select_country.options[i].value === state) {
                            select_country.selectedIndex = i;
                            break;
                        }
                    }
                } else {
                    alert("Address not found");
                }
            } catch (error) {
                console.error("Error fetching address details:", error);
            }
        }
    }
</script>
<script>
    if (document.getElementById("guest_country")) {
        // user country code for selected option
        var user_country_code = "US";
        (() => {
            // script https://www.html-code-generator.com/html/drop-down/state-name
            // Get the country name and state name from the imported script.
            const country_list = country_and_states.country;
            const guest_state_list = country_and_states.states;
            const guest_id_state_option = document.getElementById("guest_state");
            const guest_id_country_option = document.getElementById("guest_country");
            const create_states_selection = () => {
                // selected country code
                let guest_selected_country_code = guest_id_country_option.value;
                // get state names by selected country-code
                let guest_state_names = guest_state_list[guest_selected_country_code];
                // if invalid country code
                if (!guest_state_names) {
                    guest_id_state_option.innerHTML = "<option>Select a State</option>";
                    return;
                }
                // create option
                let option = "";
                option += "<option>Select a State</option>";
                guest_state_names.forEach((state) => {
                    option +=
                        '<option value="' + state.code + '">' + state.name + "</option>";
                });
                guest_id_state_option.innerHTML = option;
            };
            // country select change event update state code
            guest_id_country_option.addEventListener("change", create_states_selection);
            create_states_selection();
        })();
    }
</script>
<script>
    // const guest_input = document.querySelector("#guest_tel");
    // if (guest_input) {
    //     const guestcountry_addressDropdown = document.querySelector("#guest_country");
    //     // populate the country dropdown
    //     for (let i = 0; i < countryData.length; i++) {
    //         const country = countryData[i];
    //         const optionNode = document.createElement("option");
    //         optionNode.value = country.iso2;
    //         const textNode = document.createTextNode(country.name);
    //         optionNode.appendChild(textNode);
    //         guestcountry_addressDropdown.appendChild(optionNode);
    //     }
    //     var initialCountry = $("#guest_country").val().toUpperCase();
    //     // init plugin
    //     const guest_iti = window.intlTelInput(guest_input, {
    //         initialCountry: initialCountry,
    //         separateDialCode: true,
    //         hiddenInput: (telInputName) => ({
    //             phone: "tel_cell"
    //         }),
    //         loadUtils: () =>
    //             import(
    //                 "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
    //             ), // for formatting/placeholders etc
    //     });
    //     // set address dropdown's initial value
    //     guestcountry_addressDropdown.value = guest_iti.getSelectedCountryData().iso2;
    //     // listen to the telephone input for changes
    //     guest_input.addEventListener("countrychange", () => {
    //         guestcountry_addressDropdown.value = guest_iti.getSelectedCountryData().iso2;
    //     });
    //     // listen to the address dropdown for changes
    //     guestcountry_addressDropdown.addEventListener("change", () => {
    //         var country = $("#guest_country").val().toUpperCase();
    //         guest_iti.setCountry(country);
    //         // console.log("hey changed" + addressDropdown.value);
    //     });
    // }
    // window.addEventListener('load', () => {
    //     // const phone_inputs = document.querySelectorAll(".tel_cell");
    //     // const currentCountry = locationData.country ?? 'US';

    //     // phone_inputs.forEach((input, index) => {
    //     //     window.intlTelInput(input, {
    //     //         initialCountry: currentCountry,
    //     //         separateDialCode: true,
    //     //         loadUtils: () =>
    //     //             import(
    //     //                 "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
    //     //             )
    //     //     });
    //     // });
    // });
    // // get the country data from the plugin
    // // const countryData = window.intlTelInput.getCountryData();
</script>
<script>
    $(document).ready(function() {
        const shipping_tel = document.querySelector("#shipping_tel_cell");
        const shipping_addressDropdown = document.querySelector("#shipping_country");

        const init_country = locationData?.country?.toLowerCase() ?? 'us';
        const subphone = document.querySelector("#phone");

        const subphone_iti = window.intlTelInput(subphone, {
            initialCountry: init_country,
            separateDialCode: true,
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
        });


        // Populate country dropdown using intl-tel-input data
        const shipping_countryData = window.intlTelInput.getCountryData();
        for (let i = 0; i < shipping_countryData.length; i++) {
            const country = shipping_countryData[i];
            const optionNode = document.createElement("option");
            optionNode.value = country.iso2;
            optionNode.textContent = country.name;
            shipping_addressDropdown.appendChild(optionNode);
        }

        // Initialize intlTelInput on the phone field
        const shipping_iti = window.intlTelInput(shipping_tel, {
            initialCountry: init_country,
            separateDialCode: true,
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
        });

        // Set initial country in the dropdown
        if (window.jQuery && typeof jQuery.fn.selectpicker === "function") {
            $(shipping_addressDropdown).val(init_country).selectpicker('refresh');
        } else {
            shipping_addressDropdown.value = init_country;
        }

        // Update dropdown when phone input changes
        shipping_tel.addEventListener("countrychange", () => {
            const selectedIso2 = shipping_iti.getSelectedCountryData().iso2;
            if (window.jQuery && typeof jQuery.fn.selectpicker === "function") {
                $(shipping_addressDropdown).val(selectedIso2).selectpicker('refresh');
            } else {
                shipping_addressDropdown.value = selectedIso2;
            }
        });

        // Update phone input when dropdown changes
        shipping_addressDropdown.addEventListener("change", () => {
            const selected = shipping_addressDropdown.value;
            if (selected) {
                shipping_iti.setCountry(selected);
            }
        });

        const guest_tel = document.querySelector("#guest_tel");
        const guest_country_addressDropdown = document.querySelector("#guest_country");


        // Populate country dropdown using intl-tel-input data
        const guest_countryData = window.intlTelInput.getCountryData();
        for (let i = 0; i < guest_countryData.length; i++) {
            const country = guest_countryData[i];
            const optionNode = document.createElement("option");
            optionNode.value = country.iso2;
            optionNode.textContent = country.name;
            guest_country_addressDropdown.appendChild(optionNode);
        }

        // Initialize intlTelInput on the phone field
        const guest_iti = window.intlTelInput(guest_tel, {
            initialCountry: init_country,
            separateDialCode: true,
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
        });

        // Set initial country in the dropdown
        if (window.jQuery && typeof jQuery.fn.selectpicker === "function") {
            $(guest_country_addressDropdown).val(init_country).selectpicker('refresh');
        } else {
            guest_country_addressDropdown.value = init_country;
        }

        // Update dropdown when phone input changes
        guest_tel.addEventListener("countrychange", () => {
            const selectedIso2 = guest_iti.getSelectedCountryData().iso2;
            if (window.jQuery && typeof jQuery.fn.selectpicker === "function") {
                $(guest_country_addressDropdown).val(selectedIso2).selectpicker('refresh');
            } else {
                guest_country_addressDropdown.value = selectedIso2;
            }
        });

        // Update phone input when dropdown changes
        guest_country_addressDropdown.addEventListener("change", () => {
            const selected = guest_country_addressDropdown.value;
            if (selected) {
                guest_iti.setCountry(selected);
            }
        });

    });
    $(function() {
        $('#shipping_country').selectpicker('refresh');
    });
    $(function() {
        $('#guest_country').selectpicker('refresh');
    });
</script>
@if (request()->is('/'))
<!-- <script>
    $(document).ready(function() {
        setTimeout(function() {
            if (!$("#locationModal").hasClass("show")) {
                console.log("Location modal is not visible.");
                const secondModal = $("#discountModal");
                secondModal.removeClass('d-none').modal('show');
            }
        }, 500)
    });
</script> -->
@endif
<script>
    $('.promo_regi_email_btn').on('click', function() {
        const email = $('.promo_regi_email_input').val(); // Adjust selector to your input

        $('#promo_errors').html(''); // Clear any previous messages
        $('#promo_email_error').html(''); // Clear any previous messages

        $('#promo_regi_email_submit_btn_spining_icon').removeClass('d-none');

        if (!email) {
            $('#promo_email_error').html(`
            <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>Please enter an email.</small>
        `);
            $('#promo_regi_email_submit_btn_spining_icon').addClass('d-none');

            return;
        }

        $.ajax({
            url: "{{ route('check-subscriber') }}",
            type: 'GET',
            dataType: 'json',
            data: {
                field: 'email',
                value: email
            },
            success: function(response) {
                if (!response.exists) {
                    $('.promo_regi_email_field').addClass('d-none');
                    $('.promo_regi_phone_field').removeClass('d-none');
                    $('.promo_regi_popup_back_btn').removeClass('d-none');
                    promoPhoneVisible = true;
                    promoEmailVisible = false;
                } else {

                    $('#promo_email_error').html(`
            <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>This email is already taken.</small>
        `);
                }
                $('#promo_regi_email_submit_btn_spining_icon').addClass('d-none');

            },
            error: function() {

                $('#promo_email_error').html(`
            <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>Something went wrong. Please try again.</small>
        `);
                $('#promo_regi_email_submit_btn_spining_icon').addClass('d-none');

            }
        });

    });
</script>
<script>
    $('.promo_regi_popup_back_btn').on('click', function() {
        $('#promo_errors').html(''); // Clear any previous messages
        $('#promo_email_error').html('');


        if (otpMode) {
            $('#mainPromoForm').removeClass('d-none');
            $('#verifyOTP').addClass('d-none');
            $('#otp-message').text('');
            $('#promo_errors').text('');
            promoPhoneVisible = false;
            promoEmailVisible = false;
            otpMode = false;
        } else if (!promoPhoneVisible && !promoEmailVisible) {
            promoPhoneVisible = true;
            $('.promo_regi_email_field').addClass('d-none');
            $('.promo_regi_phone_field').removeClass('d-none');
            // $('.promo_regi_popup_back_btn').addClass('d-none');
        } else if (promoPhoneVisible) {
            promoPhoneVisible = false;
            promoEmailVisible = true;
            $('.promo_regi_email_field').removeClass('d-none');
            $('.promo_regi_phone_field').addClass('d-none');
            $('.promo_regi_popup_back_btn').addClass('d-none');
        }
    })
</script>
<!-- <script>
    $('.close-discount-tag').on('click', function(event) {
        event.preventDefault();
        $('.get-discount-tag').addClass('d-none');
    });
</script>
<script>
    $('.get-discount-tag').on('click', function(event) {
        $('#discountModal').removeClass('d-none').modal('show');
    });
</script> -->
<script>
    $(document).ready(function() {
        const init_country = locationData?.country?.toLowerCase() ?? 'us';

        const promoPhone = document.querySelector("#promo_regi_phone");

        const promoPhone_iti = window.intlTelInput(promoPhone, {
            initialCountry: init_country,
            separateDialCode: true,
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
        });
        $('.promo_regi_submit_btn').on('click', function() {
            $("#promo_regi_submit_btn_spining_icon").removeClass("d-none");
            let formData = {
                email: $('#offer_email').val(),
                phone: promoPhone_iti.getNumber(),
                type: $('#promo-form input[name="sub_type"]').val(),
                promo_id: promoId,
                _token: '{{ csrf_token() }}'
            };

            $.ajax({
                url: "{{ route('subscribe') }}",
                type: "POST",
                data: formData,
                success: function(response) {
                    if (response.mode == 'otp') {
                        otpMode = true;
                        $('#mainPromoForm').addClass('d-none')
                        $('#verifyOTP').removeClass('d-none')
                        $('#otp-message').text(response.message);


                    } else if (response.success) {
                        $('#promo-form').addClass('d-none');
                        $('#promo_regi_sys_msg').removeClass('d-none');
                        $('#promo_regi_sys_msg').html(`
            ${response.message}
            <img id="animated-logo" class="standalone-logo-icon" 
                 src="{{ url('images/icon/gold_logo.svg') }}" 
                 alt="logo" style="width: 40px; display:inline-block; vertical-align: middle; margin-left: 10px;">
        `);
                        const logoAnimation = setInterval(() => {
                            $('#animated-logo').addClass('rotate');
                            setTimeout(() => {
                                $('#animated-logo').removeClass('rotate');
                            }, 1000); // Match duration of animation
                        }, 3000);
                        const checkUserReply = setInterval(function() {
                            $.ajax({
                                url: "{{ route('is_user_replied') }}",
                                type: "POST",
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    phone: promoPhone_iti.getNumber(),

                                },
                                success: function(response) {
                                    if (response === true || response.replied === true) {
                                        clearInterval(checkUserReply); // stop polling
                                        clearInterval(logoAnimation);
                                        $('#promo_regi_sys_msg').html(response.message);
                                        $('#promo_regi_sys_msg').removeClass('d-none');
                                        $('.promo_regi_popup_back_btn').addClass("d-none");

                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error("Error checking user reply:", error);
                                }
                            });
                        }, 2000); // every 2 seconds

                    } else {
                        if (response.errors) {
                            // alert('Validation errors: ' + JSON.stringify(response.errors));
                            const errors = JSON.stringify(response.errors);
                            const parsedErrors = JSON.parse(errors);
                            const errorMessages = Object.values(parsedErrors).flat().join(
                                '<br>');
                            // alert(errorMessages);

                            $('#promo_phone_error').html(`
            <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>${errorMessages}</small>
        `);
                        } else {
                            // alert(response.message);

                            $('#promo_phone_error').html(`
            <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>${response.message}</small>
        `);
                        }
                    }
                    $("#promo_regi_submit_btn_spining_icon").addClass("d-none");
                    $('.promo_regi_popup_back_btn').addClass('d-none');

                },
                error: function(xhr) {
                    let errorMessages = 'Something went wrong. Please try again.';

                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        const errors = xhr.responseJSON.errors;
                        errorMessages = Object.values(errors).map(msgArray => msgArray.join('<br>')).join('<br>');
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessages = xhr.responseJSON.message;
                    }


                    $('#promo_phone_error').html(`
            <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>${errorMessages}</small>
        `);
                    $("#promo_regi_submit_btn_spining_icon").addClass("d-none");
                }
            });
        });
        $('.promo_otp_submit_btn').on('click', function() {
            $('.promo_regi_popup_back_btn').addClass("d-none");
            $('#promo_otp_error').html('');
            $("#promo_otp_submit_btn_spining_icon").removeClass("d-none");
            let formData = {
                otp: $('#promo-otp').val(),
                promo_id: promoId,
                phone: promoPhone_iti.getNumber(),
                email: $('#offer_email').val(),

                _token: '{{ csrf_token() }}'
            };

            $.ajax({
                url: "{{ route('verify_otp') }}",
                type: "POST",
                data: formData,
                success: function(response) {
                    $('#verifyOTP').addClass('d-none');
                    $('#promo-form').addClass('d-none');

                    $('#promo_regi_sys_msg').html(response.message);

                    $('#promo_regi_sys_msg').removeClass('d-none');
                    $("#promo_otp_submit_btn_spining_icon").addClass("d-none");
                    $('.promo_regi_popup_back_btn').addClass("d-none");
                },
                error: function(xhr) {
                    console.log(xhr.responseJSON);
                    let errorMessages = 'Something went wrong. Please try again.';

                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        const errors = xhr.responseJSON.errors;
                        errorMessages = Object.values(errors).map(msgArray => msgArray.join('<br>')).join('<br>');
                    } else if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessages = xhr.responseJSON.error;
                    }

                    $('#promo_otp_error').html(`
            
             <small  class="form-text text-white d-flex align-items-center gap-1"><i class="fas fa-exclamation-triangle"></i>${errorMessages}</small>
        `);
                    $("#promo_otp_submit_btn_spining_icon").addClass("d-none");
                }
            });
        });
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('switch').addEventListener('change', function() {
            const passwordInput = document.getElementById('login_password');
            passwordInput.type = this.checked ? 'text' : 'password';
        });
        document.getElementById('flexSwitchCheckDefault').addEventListener('change', function() {
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('password_confirmation');
            const inputType = this.checked ? 'text' : 'password';
            passwordInput.type = inputType;
            confirmPasswordInput.type = inputType;
        });
    });
</script>
@if (!Session::has('cart'))
<script type="text/javascript">
    $('.cart_count').addClass('d-none');
    $('.shopping-cart-click').removeClass('have-item');
    $('#cart_block').empty();
</script>
@endif
<script type="text/javascript">
    let pay_cart_update_status = true;
    let current_page = "{{ Request::url() }}";
    if (window.performance) {
        if (window.performance.navigation.type == 2) {
            setTimeout(function() {
                cartRefresh();
            }, 300);
        }
    }
    window.onpageshow = function(event) {
        if (event.persisted) {
            setTimeout(function() {
                cartRefresh();
            }, 300);
        }
    };
    $(document).ready(function() {
        setTimeout(function() {
            cartRefresh();
        }, 300);
        $('.file-uploads').fileupload({
            headers: {
                'X-CSRF-Token': $('input[name="_token"]').val()
            },
            progressall: function(e, data) {
                let input_name = $(this).attr('name');
                let progress = parseInt(data.loaded / data.total * 100, 10);
                $('.' + input_name + '_progress').removeClass('d-none').fadeIn();
                $('.' + input_name + '_progress .progress-bar').css(
                    'width', progress + '%'
                );
                if (progress == 100) {
                    $('.' + input_name + '_progress').fadeOut();
                }
                $('#' + input_name).prop('disabled', true);
            },
            add: function(e, data) {
                // Validate file type (optional but recommended)
                let file = data.files[0];
                let allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml',
                    'video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv'
                ];
                let allowedExtensions = ['mp4', 'avi', 'mov', 'wmv', 'jpg', 'jpeg', 'png', 'gif',
                    'svg'
                ]; // Add video extensions here as well
                let fileExtension = file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(
                        fileExtension)) {
                    alert('Invalid file type. Please upload an image or video.');
                    return false;
                }
                data.submit();
                let input_name = $(this).attr('name');
                let icon_name = $(this).attr('data-icon');
                $('.' + input_name + '_progress .progress-bar').css(
                    'width', 0 + '%'
                );
                $('.' + input_name + '_icon').removeClass(icon_name).addClass(
                    'fas fa-spinner fa-spin');
            },
            done: function(e, data) {
                console.log(data);
                let input_name = $(this).attr('name');
                let icon_name = $(this).attr('data-icon');
                if (data.result.status == 'failed') {
                    $('.' + input_name + '_error').empty().append(data.result.message).removeClass(
                        'd-none');
                    $('.' + input_name + '_icon').removeClass('fas fa-spinner fa-spin').addClass(
                        icon_name);
                    $('#' + input_name).prop('disabled', false);
                } else {
                    $('.' + input_name + '_error').addClass('d-none');
                    $('.' + input_name + '_icon').removeClass('fas fa-spinner fa-spin').addClass(
                        icon_name);
                    $('#files_' + input_name).empty().append(data.result);
                    $('#' + input_name).prop('disabled', false);
                    // Attach remove event for newly added files
                    $('.file-remove').on('click', function() {
                        let fileName = $(this).attr('data-name');
                        let input_name = $(this).attr('data-toggle');
                        let icon_name = $(this).attr('data-icon');
                        let url = $(this).attr('data-url');
                        let fileId = $(this).attr('data-id');
                        $.confirm({
                            title: 'Are you sure to remove?',
                            content: 'Confirm to delete this' + ' <strong>' +
                                fileName + '</strong> ' + 'permanently.',
                            type: 'blue',
                            buttons: {
                                ok: {
                                    text: "Confirm",
                                    btnClass: 'btn-primary',
                                    keys: ['enter'],
                                    action: function() {
                                        if (fileId) {
                                            $('.' + input_name + '_' + fileId)
                                                .addClass('d-none');
                                            $('.' + input_name + '_icon_' +
                                                fileId).removeClass(
                                                'd-none').removeClass(
                                                icon_name).addClass(
                                                'fas fa-spinner fa-spin');
                                        } else {
                                            $('.' + input_name + '_icon')
                                                .removeClass('d-none');
                                            $('.' + input_name + '_icon')
                                                .removeClass(icon_name)
                                                .addClass(
                                                    'fas fa-spinner fa-spin');
                                        }
                                        $.get(url, function(data) {
                                            if (data == 'success') {
                                                $('.' + input_name +
                                                        '_preview')
                                                    .addClass('d-none')
                                                    .attr('src', null);
                                                $('.' + input_name +
                                                        '_hide')
                                                    .addClass('d-none');
                                                $('.' + input_name +
                                                        '_icon')
                                                    .removeClass(
                                                        'd-none');
                                                $('.' + input_name +
                                                        '_icon')
                                                    .removeClass(
                                                        'fas fa-spinner fa-spin'
                                                    ).addClass(
                                                        icon_name);
                                            }
                                            if (data ==
                                                'files_success') {
                                                $('.' + fileId +
                                                        '_image')
                                                    .addClass('d-none');
                                                $('.' + fileId +
                                                        '_file')
                                                    .addClass('d-none');
                                                $('.' + input_name +
                                                        '_icon')
                                                    .removeClass(
                                                        'd-none');
                                                $('.' + input_name +
                                                        '_icon')
                                                    .removeClass(
                                                        'fas fa-spinner fa-spin'
                                                    ).addClass(
                                                        icon_name);
                                            }
                                        });
                                    }
                                },
                                cancel: function() {}
                            }
                        });
                    });
                }
            },
        });
        $('.remove-file').on('click', function() {
            let fileName = $(this).attr('data-name');
            let input_name = $(this).attr('data-toggle');
            let icon_name = $(this).attr('data-icon');
            let url = $(this).attr('data-url');
            let fileId = $(this).attr('data-id');
            $.confirm({
                title: 'Are you sure to remove?',
                content: 'Confirm to delete this' + ' <strong>' + fileName + '</strong> ' +
                    'permanently.',
                type: 'blue',
                buttons: {
                    ok: {
                        text: "Confirm",
                        btnClass: 'btn-primary',
                        keys: ['enter'],
                        action: function() {
                            if (fileId) {
                                $('.' + input_name + '_' + fileId).addClass('d-none');
                                $('.' + input_name + '_icon_' + fileId).removeClass(
                                    'd-none').removeClass(icon_name).addClass(
                                    'fas fa-spinner fa-spin');
                            } else {
                                $('.' + input_name + '_icon').removeClass(icon_name)
                                    .addClass('fas fa-spinner fa-spin');
                                $('.' + input_name + '_icon').removeClass('d-none');
                            }
                            $.get(url, function(data) {
                                if (data == 'success') {
                                    $('.' + input_name + '_preview').addClass(
                                        'd-none').attr('src', null);
                                    $('.' + input_name + '_hide').addClass(
                                        'd-none');
                                    $('.' + input_name + '_icon').removeClass(
                                        'd-none');
                                    $('.' + input_name + '_icon').removeClass(
                                        'fas fa-spinner fa-spin').addClass(
                                        icon_name);
                                }
                                if (data == 'files_success') {
                                    $('.' + fileId + '_image').addClass('d-none');
                                    $('.' + fileId + '_file').addClass('d-none');

                                    $('.' + input_name + '_icon').removeClass(
                                        'd-none');
                                    $('.' + input_name + '_icon').removeClass(
                                        'fas fa-spinner fa-spin').addClass(
                                        icon_name);
                                }
                            })
                        }
                    },
                    cancel: function() {}
                }
            });
        });
        $('.stars li').on('mouseover', function() {
            var onStar = parseInt($(this).data('value'), 10);
            $(this).parent().children('li.star').each(function(e) {
                if (e < onStar) {
                    $(this).addClass('hover');
                } else {
                    $(this).removeClass('hover');
                }
            });
        }).on('mouseout', function() {
            $(this).parent().children('li.star').each(function(e) {
                $(this).removeClass('hover');
            });
        });
        $('.stars li').on('click', function() {
            var onStar = parseInt($(this).data('value'), 10); // The star currently selected
            var stars = $(this).parent().children('li.star');
            for (i = 0; i < stars.length; i++) {
                $(stars[i]).removeClass('selected');
            }
            for (i = 0; i < onStar; i++) {
                $(stars[i]).addClass('selected');
            }
            // JUST RESPONSE (Not needed)
            var ratingValue = parseInt($('.stars li.selected').last().data('value'), 10);
            var msg = "";
            if (ratingValue > 1) {
                msg = "Thanks! You rated this " + ratingValue + " stars.";
            } else {
                msg = "We will improve ourselves. You rated this " + ratingValue + " stars.";
            }
            responseMessage(msg);
            $('.review_rating').val(ratingValue);
        });
    });

    function responseMessage(msg) {
        $('.success-box').fadeIn(200);
        $('.success-box div.text-message').html("<span>" + msg + "</span>");
    }

    function cartRefresh() {
        let site_cart_data_url = "{{ route('site_cart_data') }}";
        let timestamp = new Date().getTime();
        let get_site_cart_json_data_url = site_cart_data_url + '?shopping_cart_data=1&timestamp=' + timestamp;
        $.get(get_site_cart_json_data_url, function(response) {
            // console.log(response);
            let total = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(response.grand_total - response.paid_grand_total);
            let total_local = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(response.grand_total_local - response.paid_grand_total_local);
            let local_amount_paid = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(response.grand_total_local - response.paid_grand_total_local);
            // console.log(total, total_local, local_amount_paid);
            // $('.cart_amount').empty().append(total);
            // $('.cart_local_amount').empty().append(total_local);
            // $('.local_amount_paid').empty().append(local_amount_paid);
            if (response.quantity == 0) {
                $('.cart_count').addClass('d-none');
                // window.location.replace(redirect_page);
            }
            if (response.quantity > 0) {
                $('.cart_count').html(response.quantity).removeClass('d-none');
                $('.shopping-cart-click').addClass('have-item');
            }
            if (response.grand_total_local > 5000000) {
                $('.cart_button_text').empty().append('PAY');
            } else {
                $('.cart_button_text').empty().append('PLACE ORDER');
            }
            setTimeout(function() {
                if (response.quantity == 0) {
                    $('.cart_count').addClass('d-none');
                    $('.shopping-cart-click').removeClass('have-item');
                    // window.location.replace(redirect_page);
                }
                if (response.quantity > 0) {
                    $('.cart_count').html(response.quantity).removeClass('d-none');
                    $('.shopping-cart-click').addClass('have-item');
                }
            }, 300);
        });
        let get_site_cart_item_data_url = site_cart_data_url + '?shopping_cart_block=1&current_page=' + current_page +
            '&timestamp=' + timestamp;
        $.get(get_site_cart_item_data_url, function(response) {
            $('#cart_block').empty().append(response);
            $('.custom-scrollbar').scrollbar({
                ignoreMobile: true,
            });
        });
        // let get_site_cart_data_url = site_cart_data_url +'?shopping_cart_items=1&timestamp=' + timestamp;
        // $.get(get_site_cart_data_url, function (response) {
        //     if(response){
        //         $('.shopping-cart-click').addClass('have-item');
        //         $('.shopping_cart_items').html(response);
        //         $('.custom-scrollbar').scrollbar({
        //             ignoreMobile:true,
        //         });
        //         setTimeout(function() {
        //             $('.shopping-cart-click').addClass('have-item');
        //             $('.shopping_cart_items').html(response);
        //             $('.custom-scrollbar').scrollbar({
        //                 ignoreMobile:true,
        //             });
        //         }, 300);
        //     }
        // });
    }
    $('.autocomplete').devbridgeAutocomplete({
        lookup: function(query, done) {
            var search_url = "{{ route('search.live', ':query') }}";
            search_url = search_url.replace(':query', query);
            $.get(search_url, function(response) {
                var result = {
                    suggestions: response
                }
                done(result);
            });
        },
        onSelect: function(suggestion) {
            window.location.href = suggestion.url;
        }
    });
    $('#userLoginModal').on('show.bs.modal', function(event) {
        let button = $(event.relatedTarget);
        let page_url = button.data('page_url');
        let modal_title = button.data('title');
        let modal_text = button.data('text');
        $('#userLoginModalLabel strong').empty().append(modal_title);
        $('#userLoginModalText').empty().append(modal_text);
        let modal_location = button.data('modal_location');
        if (modal_location) {
            $('#user_login_button').attr('data-location', modal_location);
        }
        if ($('#createAccountModal').hasClass('show')) {
            $('#createAccountModal').modal('toggle');
        }
        if ($('#forgotPasswordModal').hasClass('show')) {
            $('#forgotPasswordModal').modal('toggle');
        }
        setTimeout(function() {
            $('body').addClass('modal-open');
        }, 700);
        if (page_url) {
            $('#user_login_button').attr('data-redirect_url', page_url);
        }
    });
    $('#createAccountModal').on('show.bs.modal', function(event) {
        if ($('#userLoginModal').hasClass('show')) {
            $('#userLoginModal').modal('toggle');
        }
        if ($('#forgotPasswordModal').hasClass('show')) {
            $('#forgotPasswordModal').modal('toggle');
        }
        setTimeout(function() {
            $('body').addClass('modal-open');
        }, 700);
    });
    $('#forgotPasswordModal').on('show.bs.modal', function(event) {
        if ($('#createAccountModal').hasClass('show')) {
            $('#createAccountModal').modal('toggle');
        }
        if ($('#userLoginModal').hasClass('show')) {
            $('#userLoginModal').modal('toggle');
        }
        setTimeout(function() {
            $('body').addClass('modal-open');
        }, 700);
    });
    $('#userLogin').on('submit', function(event) {
        event.preventDefault();
        grecaptcha.execute("{{ Config::get('recaptchav3.sitekey') }}", {
            action: 'site_login'
        }).then(function(token) {
            $('#userLogin').find("[name='g-recaptcha-response']").val(token);
            let redirect_url = $('#user_login_button').attr('data-redirect_url');
            let location = $('#user_login_button').attr('data-location');
            let formData = $('form#userLogin').serialize();
            let url = $('form#userLogin').attr('action');
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            //  $.post(url,formData, function (response) {
            //     if(response.status == 'error'){
            //         $('#signin_email_error').empty().append(response.messages.email);
            //         $('#signin_password_error').empty().append(response.messages.password);
            //     }else{
            //        window.location.replace(response.url);
            //     }
            // });
            $('#signin_email_error').empty();
            $('#signin_password_error').empty();
            $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                success: function(response) {
                    // console.log(response);
                    if (redirect_url) {
                        if (location == 'leave_review') {
                            window.location.replace(redirect_url + '?location=' + location);
                        } else {
                            window.location.replace(redirect_url);
                        }
                    } else {
                        window.location.replace(response.url);
                    }
                },
                error: function(request, status, error) {
                    let response = request.responseJSON;
                    // console.log(request.responseJSON);
                    if (response.errors) {
                        $('#signin_email_error').empty().append(response.errors.email);
                        $('#signin_password_error').empty().append(response.errors
                            .password);
                    }
                }
            });
        });
    });
    var url = window.location.href;
    if (/leave_review/g.test(url)) {
        setTimeout(function() {
            $('#leaveReviewModal').modal('show');
        }, 300);
    }
    $('#createAccount').submit(function(event) {
        event.preventDefault();
        grecaptcha.execute("{{ Config::get('recaptchav3.sitekey') }}", {
            action: 'site_register'
        }).then(function(token) {
            $('#createAccount').find("[name='g-recaptcha-response']").val(token);
            let formData = $('form#createAccount').serialize();
            let url = $('form#createAccount').attr('action');
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            // $('.system_message_modal').addClass('d-none');
            $('#username_error').empty();
            $('#first_name_error').empty();
            $('#last_name_error').empty();
            $('#email_error').empty();
            $('#password_error').empty();
            $('#tel_cell_error').empty();
            $('#address_error').empty();
            $('#country_error').empty();
            $('#state_error').empty();
            $('#city_error').empty();
            $('#zip_code_error').empty();
            $('.system_message_modal_text').empty();
            $('.system_message_modal_loader').removeClass('d-none').addClass('active');
            $('.modal-content').addClass('loading');
            $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                success: function(response) {
                    // console.log(response);
                    // window.location.replace(response.url);
                    $('.system_message_modal_text').append(response.success);
                    $('.system_message_modal_loader').addClass('d-none').removeClass(
                        'active');
                    // $('.system_message_modal').addClass('alert-success').removeClass('d-none');
                    $('.modal-content').removeClass('loading');
                    $('form#createAccount').hide();
                    $('#createAccountModalLabel').hide();
                    $('.continue_shopping').removeClass('d-none');
                },
                error: function(request, status, error) {
                    let response = request.responseJSON;
                    if (response.errors) {
                        $('#username_error').empty().append(response.errors.username);
                        $('#first_name_error').empty().append(response.errors.first_name);
                        $('#last_name_error').empty().append(response.errors.last_name);
                        $('#email_error').empty().append(response.errors.email);
                        $('#password_error').empty().append(response.errors.password);
                        $('#tel_cell_error').empty().append(response.errors.tel_cell);
                        $('#address_error').empty().append(response.errors.address);
                        $('#country_error').empty().append(response.errors.country);
                        $('#state_error').empty().append(response.errors.state);
                        $('#city_error').empty().append(response.errors.city);
                        $('#zip_code_error').empty().append(response.errors.zip_code);
                        $('.system_message_modal_loader').addClass('d-none').removeClass(
                            'active');
                        $('.modal-content').removeClass('loading');
                    }
                }
            });
        });
        // $.post(url,formData, function (response) {
        //     if(response.status == 'error'){
        //         $('#username_error').empty().append(response.messages.username);
        //         $('#first_name_error').empty().append(response.messages.first_name);
        //         $('#last_name_error').empty().append(response.messages.last_name);
        //         $('#email_error').empty().append(response.messages.email);
        //         $('#password_error').empty().append(response.messages.password);
        //         $('#tel_cell_error').empty().append(response.messages.tel_cell);
        //         $('#address_error').empty().append(response.messages.address);
        //         $('#country_error').empty().append(response.messages.country);
        //         $('#state_error').empty().append(response.messages.state);
        //         $('#city_error').empty().append(response.messages.city);
        //         $('#zip_code_error').empty().append(response.messages.zip_code);
        //     }else{
        //        window.location.replace(response.url);
        //     }
        // });
    });
    $('#forgotPassword').on('submit', function(event) {
        event.preventDefault();
        grecaptcha.execute("{{ Config::get('recaptchav3.sitekey') }}", {
            action: 'forgot_password'
        }).then(function(token) {
            $('#forgotPassword').find("[name='g-recaptcha-response']").val(token);
            let formData = $('form#forgotPassword').serialize();
            let url = $('form#forgotPassword').attr('action');
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $('.system_message_modal').addClass('d-none');
            $('#forgot_password_email_error').empty();
            $('.system_message_modal_text').empty();
            $('.system_message_modal_loader').removeClass('d-none').addClass('active');
            $('.modal-content').addClass('loading');
            $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                success: function(response) {
                    // console.log(response);
                    // window.location.replace(response.url);
                    $('.system_message_modal_text').append(response.success);
                    $('.system_message_modal_loader').addClass('d-none').removeClass(
                        'active');
                    $('.system_message_modal').addClass('alert-success').removeClass(
                        'd-none');
                    $('.modal-content').removeClass('loading');
                },
                error: function(request, status, error) {
                    let response = request.responseJSON;
                    if (response.errors) {
                        $('#forgot_password_email_error').append(response.errors.email);
                        $('.system_message_modal_loader').addClass('d-none').removeClass(
                            'active');
                        $('.modal-content').removeClass('loading');
                    }
                }
            });
        });
        // $.post(url,formData, function (response) {
        //     console.log(response.success);
        //     if(response.errors){
        //         $('#forgot_password_email_error').append(response.errors.email);
        //         $('.system_message_modal_loader').addClass('d-none').removeClass('active');
        //         $('.modal-content').removeClass('loading');
        //         //  window.location.replace(response.url);
        //         //  $('#system_message').empty().append(response.message);
        //     }
        //     if(response.success){
        //         $('.system_message_modal_text').append(response.success);
        //         $('.system_message_modal_loader').addClass('d-none').removeClass('active');
        //         $('.system_message_modal').addClass('alert-success').removeClass('d-none');
        //         $('.modal-content').removeClass('loading');
        //     }
        // });
    });
    $('#resetPasswordModal').on('submit', function(event) {
        event.preventDefault();
        grecaptcha.execute("{{ Config::get('recaptchav3.sitekey') }}", {
            action: 'reset_password'
        }).then(function(token) {
            $('#resetPasswordModal').find("[name='g-recaptcha-response']").val(token);
            let formData = $('form#resetPassword').serialize();
            let url = $('form#resetPassword').attr('action');
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $('.system_message_modal').addClass('d-none');
            $('#email_error').empty();
            $('#password_error').empty();
            $('#new_password_error').empty();
            $('.system_message_modal_text').empty();
            $('.system_message_modal_loader').removeClass('d-none').addClass('active');
            $('.modal-content').addClass('loading');
            $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                success: function(response) {
                    // console.log(response);
                    // window.location.replace(response.url);
                    $('.system_message_modal_text').append(response.success);
                    $('.system_message_modal_loader').addClass('d-none').removeClass(
                        'active');
                    $('.system_message_modal').addClass('alert-success').removeClass(
                        'd-none');
                    $('.modal-content').removeClass('loading');
                },
                error: function(request, status, error) {
                    let response = request.responseJSON;
                    if (response.errors) {
                        $('#email_error').append(response.errors.email);
                        $('#password_error').append(response.errors.password);
                        $('#new_password_error').append(response.errors.password);
                        $('.system_message_modal_loader').addClass('d-none').removeClass(
                            'active');
                        $('.modal-content').removeClass('loading');
                    }
                }
            });
        });
    });
    $(document).on('changed.bs.select', '#location_country', function(e) {
        let location_id = $(this).val();
        url = "{{ route('live.get.location_data', [':location_id']) }}";
        url = url.replace(':location_id', location_id);
        $('.system_message_modal_loader').removeClass('d-none').addClass('active');
        $('.modal-content').addClass('loading');
        $.get(url, function(data) {
            // console.log(data);
            $('.shop_country_image').empty().attr('src', data.image_url);
            $('#shop_country_name').empty().append(data.country_name);
            $('#choose_country_name').addClass('d-none');
            $('#shop_currency').empty().append(data.currency);
            $('.system_message_modal_loader').addClass('d-none').removeClass('active');
            $('.modal-content').removeClass('loading');
        });
    });
    $(document).on('click', '.add_cart', function(e) {
        e.preventDefault();
        let url = $(this).attr('data-url');
        let page_url = $(this).attr('data-page_url');
        let cart_url = $(this).attr('data-cart_url');
        let id = $(this).attr('data-sku_id');
        console.log(id, 'skup');
        let btn_add_cart = $(this).attr('data-incart') == 'true' ? 'yes' : 'no';
        console.log('URL:', $(this).attr('data-url'));
        console.log('Page URL:', $(this).attr('data-page_url'));
        console.log('Cart URL:', $(this).attr('data-cart_url'));
        console.log('SKU ID:', $(this).attr('data-sku_id'));
        console.log('Add Cart:', $(this).attr('data-add_cart'));
        $('.global-loader').addClass('active');
        $('.shopping-cart-click img').addClass('shake');
        setTimeout(function() {
            $('.shopping-cart-click img').removeClass('shake');
        }, 700);
        if (id) {
            if (!$(this).hasClass('buy_now_btn')) {

                $(this).empty().append('Add to Cart');
            }
            $(this).removeClass('active-item');
            $(this).removeClass('active');
            $(this).removeClass('in_cart');

            var cart_button = null;
            if ($(this).hasClass('buy_now_btn')) {
                cart_button = document.getElementById('buy_cart_item' + id);
            } else {
                cart_button = document.getElementById('add_cart' + id);
            }
        }
        $.ajax({
            url: url + '?add_to_cart=1&added_cart=' + btn_add_cart,
            method: 'GET',
            success: function(response) {
                setTimeout(function() {
                    $('#shopping-cart-item').html(response);
                    $('.custom-scrollbar').scrollbar({
                        ignoreMobile: true,
                    });
                }, 300);
                //location.reload();
                var currentPath = window.location.pathname;
                // if (id) {
                //     var message = cart_button.classList.contains('in_cart') ?
                //         "Item added successfully!" : "Item removed successfully!";
                // }
                // Remove leading and trailing slashes and get the slug
                // var slug = currentPath.replace(/^\/|\/$/g, '').toLowerCase();
//                 $('#alert-cart').html(`
//     <div class="alert alert-secondary sticky-alert" style="    background: black;
//     color: white;
//     font-weight: bold;
//     border: none;
// " role="alert">
//      ${message }
//     </div>
// `);
                setTimeout(function() {
                    $('#alert-cart').html('');
                }, 5000);
                if (slug == 'cart' || slug == 'checkout') {
                    // Refresh the page
                    location.reload();
                }
            },
            error: function(request, status, error) {
                let response = request.responseJSON;
                // console.log(request.responseJSON,request, status, error);
            }
        });
        $.ajax({
            url: url + '?added_cart=' + btn_add_cart,
            method: 'GET',
            success: function(response) {
                let total = new Intl.NumberFormat('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(response.grand_total - response.paid_grand_total);
                let total_local = new Intl.NumberFormat('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(response.grand_total_local - response.paid_grand_total_local);
                let local_amount_paid = new Intl.NumberFormat('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(response.grand_total_local - response.paid_grand_total_local);
                // console.log(total, total_local, local_amount_paid);
                // $('.cart_amount').empty().append(total);
                // $('.cart_local_amount').empty().append(total_local);
                // $('.local_amount_paid').empty().append(local_amount_paid);
                $.each(response.items, function(index, item) {
                    $.each(item.cart_items, function(cart_item_index, cart_item) {
                        if (item.in_cart) {
                            // console.log(cart_item.id);
                            $('.cart_item' + cart_item.id).empty().append(
                                'Remove form Cart');
                            $('.cart_item' + cart_item.id).addClass('active-item');
                            $('.cart_item' + cart_item.id).addClass('active');
                            $('.cart_item' + cart_item.id).addClass('in_cart');

                            $('.buy_cart_item' + cart_item.id).attr('data-add_cart',
                                'yes');
                        }
                        if (page_url && item.in_cart) {
                            window.location.replace(page_url);
                        }
                    });
                });
                setTimeout(function() {
                    if (response.quantity > 0) {
                        $('.shopping-cart-click').addClass('have-item');
                        $('.cart_count').html(response.quantity).removeClass('d-none');
                    } else {
                        $('.shopping-cart-click').removeClass('have-item');
                        $('.cart_count').addClass('d-none');
                    }
                    if (response.grand_total_local > 5000000) {
                        $('.cart_button_text').empty().append('PAY');
                    } else {
                        $('.cart_button_text').empty().append('PLACE ORDER');
                    }
                }, 700);
                if (cart_url && response.quantity > 0) {
                    setTimeout(function() {
                        $.get(cart_url + '?current_page=' + current_page, function(data) {
                            $('#cart_block').empty().append(data);
                            $('.custom-scrollbar').scrollbar({
                                ignoreMobile: true,
                            });
                        });
                    }, 300);
                }
                let checkout_form_url = $('form#checkout_form').attr('action');
                if (checkout_form_url) {
                    $('.custom-scrollbar').scrollbar({
                        ignoreMobile: true,
                    });
                    $.ajaxSetup({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    });
                    var payment_method = $("input[name=payment_method]:checked").val();
                    // let dialCodeShipping = $("#shipping_tel_cell").intlTelInput("getSelectedCountryData").dialCode;
                    // $("#shipping_tel_cell_country_code").val(dialCodeShipping);
                    // var shipping_tel_cell = $("#shipping_tel_cell").intlTelInput("getNumber");
                    // $("#shipping_tel_cell").val(shipping_tel_cell);
                    let formData = $('form#checkout_form').serialize();
                    $.ajax({
                        url: checkout_form_url,
                        method: 'POST',
                        data: formData,
                        // data: formData + '&continue_payment=1',
                        success: function(data) {
                            // console.log(data);
                            $('#cartAccordion').empty().append(data);
                            $('#continue_payment').addClass('d-none');
                            // $('#payment_tel_cell').intlTelInput({
                            //     initialCountry: "auto",
                            //     separateDialCode: true,
                            //     formatOnDisplay: true,
                            //     geoIpLookup: function (success, failure) {
                            //         $.get("https://ipapi.co", function () {}, "jsonp").always(function (resp) {
                            //             var countryCode = (resp && resp.country) ? resp.country : "";
                            //             success(countryCode);
                            //         });
                            //     }
                            // });
                        }
                    });
                }
            },
            error: function(request, status, error) {
                let response = request.responseJSON;
                // console.log(request.responseJSON,request, status, error);
            }
        });
        setTimeout(function() {
            $('.global-loader').removeClass('active');
            if (id) {
                cart_button.disabled = false;
            }
        }, 700);
    });
    $(document).on('click', '#use_reward_points', function(event) {
        $('.global-loader').addClass('active');
        let url = "{{ route('get.use_reward_points', ':checked_status') }}";
        url = url.replace(':checked_status', this.checked ? 1 : 0);
        $.get(url + '?current_page=' + current_page, function(response) {
            $('#cart_block').empty().append(response);
            $('.global-loader').removeClass('active');
        });
    });
    $(document).on('click', '#use_cash_value', function(event) {
        $('.global-loader').addClass('active');
        let url = "{{ route('get.use_cash_value', ':checked_status') }}";
        url = url.replace(':checked_status', this.checked ? 1 : 0);
        $.get(url + '?current_page=' + current_page, function(response) {
            $('#cart_block').empty().append(response);
            $('.global-loader').removeClass('active');
        });
    });
    $(document).on('click', '.remove-item', function(event) {
        event.preventDefault
        $('.global-loader').addClass('active');
        let url = $(this).attr('data-url');
        let product_id = $(this).attr('data-product_id');
        if (product_id) {
            $('#product_items_sku .product_id' + product_id).empty().append('Add to Cart');
            $('#product_items_sku .product_id' + product_id).removeClass('active-item');
            $('#product_items_sku .product_id' + product_id).removeClass('active');
            $('#product_items_sku .product_id' + product_id).removeClass('in_cart');

            $('.buy_now_btn.product_id' + product_id).attr('data-add_cart', 'yes');
        }
        $('.shopping-cart-click img').addClass('shake');
        setTimeout(function() {
            $('.shopping-cart-click img').removeClass('shake');
        }, 700);
        $.get(url + '?add_to_cart=1', function(response) {
            $('.shopping_cart_items').html(response);
            $('.custom-scrollbar').scrollbar({
                ignoreMobile: true,
            });
        });
        $.get(url + '?checkout=1&current_page=' + current_page, function(response) {
            $('#cart_block').empty().append(response);
            $('.custom-scrollbar').scrollbar({
                ignoreMobile: true,
            });
        });
        $.get(url + '?cart_data=1&current_page=' + current_page, function(response) {
            let total = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(response.grand_total - response.paid_grand_total);
            let total_local = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(response.grand_total_local - response.paid_grand_total_local);
            let local_amount_paid = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(response.grand_total_local - response.paid_grand_total_local);
            // $('.cart_amount').empty().append(total);
            // $('.cart_local_amount').empty().append(total_local);
            // $('.local_amount_paid').empty().append(local_amount_paid);
            $.each(response.items, function(index, item) {
                $.each(item.cart_items, function(cart_item_index, cart_item) {
                    if (item.in_cart) {
                        $('.cart_item' + cart_item.id).empty().append(
                            'Remove form Cart');
                        $('.cart_item' + cart_item.id).addClass('active-item');
                        $('.cart_item' + cart_item.id).addClass('active');
                        $('.cart_item' + cart_item.id).addClass('in_cart');

                        $('.buy_cart_item' + cart_item.id).attr('data-add_cart', 'yes');
                    }
                });
            });
            if (response.grand_total_local > 5000000) {
                $('.cart_button_text').empty().append('PAY');
            } else {
                $('.cart_button_text').empty().append('PLACE ORDER');
            }
            if (response.quantity == 0) {
                $('.shopping-cart-click').removeClass('have-item').removeClass('active');
                $('.cart_count').addClass('d-none');
                if (response.redirect_page_status) {
                    window.location.replace(response.redirect_page);
                }
            }
            if (response.quantity > 0) {
                $('.cart_count').html(response.quantity).removeClass('d-none');
            }
            let checkout_form_url = $('form#checkout_form').attr('action');
            if (checkout_form_url) {
                $('.custom-scrollbar').scrollbar({
                    ignoreMobile: true,
                });
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                var payment_method = $("input[name=payment_method]:checked").val();
                // let dialCodeShipping = $("#shipping_tel_cell").intlTelInput("getSelectedCountryData").dialCode;
                // $("#shipping_tel_cell_country_code").val(dialCodeShipping);
                // var shipping_tel_cell = $("#shipping_tel_cell").intlTelInput("getNumber");
                // $("#shipping_tel_cell").val(shipping_tel_cell);
                let formData = $('form#checkout_form').serialize();
                $.ajax({
                    url: checkout_form_url,
                    method: 'POST',
                    data: formData,
                    // data: formData + '&continue_payment=1',
                    success: function(data) {
                        // console.log(data);
                        $('#cartAccordion').empty().append(data);
                        $('#continue_payment').addClass('d-none');
                        // $('#payment_tel_cell').intlTelInput({
                        //     initialCountry: "auto",
                        //     separateDialCode: true,
                        //     formatOnDisplay: true,
                        //     geoIpLookup: function (success, failure) {
                        //         $.get("https://ipapi.co", function () {}, "jsonp").always(function (resp) {
                        //             var countryCode = (resp && resp.country) ? resp.country : "";
                        //             success(countryCode);
                        //         });
                        //     }
                        // });
                    }
                });
            }
            $('.global-loader').removeClass('active');
            location.reload();
        });
    });
    $(document).on('click', '.remove_cart_sku_item', function(e) {
        e.preventDefault();
        $('.global-loader').addClass('active');
        $('.shopping-cart-click img').addClass('shake');
        setTimeout(function() {
            $('.shopping-cart-click img').removeClass('shake');
        }, 700);
        let url = $(this).attr('data-url');
        $.get(url + '?checkout=1&current_page=' + current_page, function(response) {
            $('#cart_block').empty().append(response);
            $('.custom-scrollbar').scrollbar({
                ignoreMobile: true,
            });
        });
        $.ajax({
            url: url + '?add_to_cart=1',
            method: 'GET',
            success: function(response) {
                setTimeout(function() {
                    $('.shopping_cart_items').html(response);
                    $('.custom-scrollbar').scrollbar({
                        ignoreMobile: true,
                    });
                    let site_cart_data_url = "{{ route('site_cart_data') }}";
                    let timestamp = new Date().getTime();
                    let get_site_cart_json_data_url = site_cart_data_url +
                        '?shopping_cart_data=1&timestamp=' + timestamp;
                    $.get(get_site_cart_json_data_url, function(response) {
                        let total = new Intl.NumberFormat('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(response.grand_total);
                        let total_local = new Intl.NumberFormat('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(response.grand_total_local);
                        let local_amount_paid = new Intl.NumberFormat('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(response.cart_total_local - response
                            .grand_total_local);
                        // $('.cart_amount').empty().append(total);
                        // $('.cart_local_amount').empty().append(total_local);
                        // $('.local_amount_paid').empty().append(local_amount_paid);
                        if (response.quantity == 0) {
                            $('.cart_count').addClass('d-none');
                            if (response.redirect_page_status) {
                                window.location.replace(response.redirect_page);
                            }
                        }
                        if (response.quantity > 0) {
                            $('.cart_count').html(response.quantity).removeClass(
                                'd-none');
                        }
                        if (response.grand_total_local > 5000000) {
                            $('.cart_button_text').empty().append('PAY');
                        } else {
                            $('.cart_button_text').empty().append('PLACE ORDER');
                        }
                    });
                    let checkout_form_url = $('form#checkout_form').attr('action');
                    if (checkout_form_url) {
                        $('.custom-scrollbar').scrollbar({
                            ignoreMobile: true,
                        });
                        $.ajaxSetup({
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                                    'content')
                            }
                        });
                        var payment_method = $("input[name=payment_method]:checked").val();
                        // let dialCodeShipping = $("#shipping_tel_cell").intlTelInput("getSelectedCountryData").dialCode;
                        // $("#shipping_tel_cell_country_code").val(dialCodeShipping);
                        // var shipping_tel_cell = $("#temp_shipping_tel_cell").intlTelInput("getNumber");
                        // $("#shipping_tel_cell").val(shipping_tel_cell);
                        let formData = $('form#checkout_form').serialize();
                        $.ajax({
                            url: checkout_form_url,
                            method: 'POST',
                            data: formData,
                            // data: formData + '&continue_payment=1',
                            success: function(data) {
                                // console.log(data);
                                $('#cartAccordion').empty().append(data);
                                $('#continue_payment').addClass('d-none');
                                // $('#payment_tel_cell').intlTelInput({
                                //     initialCountry: "auto",
                                //     separateDialCode: true,
                                //     formatOnDisplay: true,
                                //     geoIpLookup: function (success, failure) {
                                //         $.get("https://ipapi.co", function () {}, "jsonp").always(function (resp) {
                                //             var countryCode = (resp && resp.country) ? resp.country : "";
                                //             success(countryCode);
                                //         });
                                //     }
                                // });
                            }
                        });
                    }
                    location.reload();
                }, 300);
            },
            error: function(request, status, error) {
                let response = request.responseJSON;
                // console.log(request.responseJSON,request, status, error);
            }
        });
    });
    $('#submitLeaveReview').on('submit', function(event) {
        event.preventDefault();
        let url = $(this).attr('action');
        let form = new FormData(this);
        let method = 'POST';
        $.ajax({
            url: url,
            method: method,
            data: form,
            dataType: 'json',
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                // console.log(data);
                let message = data.message;
                $('#alert').addClass('alert-success').removeClass('d-none alert-danger').empty()
                    .html(message);
                $('#leaveReviewModal').modal('hide');
            },
            error: function(request, status, error) {
                let response = request.responseJSON;
                // console.log(response);
                $('#product_id_error').empty().append(response.messages.product_id);
                $('#rating_error').empty().append(response.messages.rating);
                $('#review_message_error').empty().append(response.messages.review_message);
                $('#product_item_id_error').empty().append(response.messages.product_item_id);
            }
        });
    });
    $(document).on('changed.bs.select', '#product_item_id', function(e) {
        let product_item_id = $(this).val();
        let url = "{{ route('live.get.review_product', [':product_item_id']) }}";
        url = url.replace(':product_item_id', product_item_id);
        let data_sku = $('option:selected', this).attr("data-sku");
        $.get(url, function(data) {
            $('#product_id').val(data.product_id);
            $('#product_sku_s').val(data_sku);
        });
    });
    const pages = document.querySelectorAll(".page");
    const translateAmount = 100;
    let translate = 0;
    slide = (direction) => {
        direction === "next" ? translate -= translateAmount : translate += translateAmount;
        pages.forEach(
            pages => (pages.style.transform = `translateX(${translate}%)`)
        );
    }
    $(document).on('click', '.page .have-sub-menu', function(event) {
        event.preventDefault();
        let menu_id = $(this).data('menu_id');
        $('#pages').append('<div  class="page child-page" id="page_' + menu_id + '"></div>');
        let child_page = $('#page_' + menu_id).html();
        let divCount = $('#pages').find('.page').length;
        $('#pages').css('width', (divCount * 100) + '%');
        setTimeout(function() {
            $('#page_' + menu_id + '.page').html(child_page);
            $('#pages .page').css('transform', `translateX(${-(divCount -1) * 100}%)`);
            $('.mobile-col').removeClass('main-mobile-menu').addClass('child-mobile-menu-area');
            $('.menu-accordion-mobile.sub-page-menu').removeClass('d-none');
            $('.menu-accordion-mobile').addClass('secondary-area');
            $('.child-mobile-menu-area').addClass('active');
            $('.bottom-block').addClass('d-none');
            $('.main-mobile-menu').addClass('d-none');
            $('.child-page .block.menu-accordion-mobile ul li a').each(function(index) {
                let delay = 5 * index;
                $(this).css({
                    'animation': `rotateX 300ms ${delay}ms ease forwards`
                });
            });
        }, 100)
    });
    $('.write-stars li').on('click', function() {
        var onWriteStar = parseInt($(this).data('value'), 10); // The star currently selected
        var stars = $(this).parent().children('li.write-star');
        for (i = 0; i < stars.length; i++) {
            $(stars[i]).removeClass('selected');
        }
        for (i = 0; i < onWriteStar; i++) {
            $(stars[i]).addClass('selected');
        }
        // JUST RESPONSE (Not needed)
        var ratingWriteValue = parseInt($('.write-stars li.selected').last().data('value'), 10);
        var msg = "";
        if (ratingWriteValue > 1) {
            msg = "Thanks! You rated this " + ratingWriteValue + " stars.";
        } else {
            msg = "We will improve ourselves. You rated this " + ratingWriteValue + " stars.";
        }
        responseMessage(msg);
        $('#write_review_rating').val(ratingWriteValue);
    });
    $(document).on('click', '#child_parent_title', function() {
        let menu_id = $(this).data('menu_id');
        // var mobile_parent_menu = $("#child_parent_active").val();
        $('#pages').children().last().remove();
        let divCount = $('#pages').find('.page').length;
        // alert(divCount);
        if (divCount > 0) {
            $('#pages').css('width', (divCount * 100) + '%');
            $('#pages .page').css('transform', `translateX(${-(divCount -1) * 100}%)`);
            $('.child-page .block.menu-accordion-mobile ul li a').each(function(index) {
                console.log(index);
                let delay = 20 * index;
                $(this).css({
                    'animation': `rotateX 300ms ${delay}ms ease forwards`
                });
            });
        }
        if (divCount == 1) {
            // slide();
            // let divCount = $('#pages').find('.page').length;
            $('#pages').css('width', (divCount * 100) + '%');
            $('.mobile-col').addClass('main-mobile-menu').removeClass('child-mobile-menu-area');
            $('.menu-accordion-mobile').removeClass('secondary-area');
            $('.menu-accordion-mobile.sub-page-menu').addClass('d-none');
            $('.child-mobile-menu-area').removeClass('active');
            $('.bottom-block').removeClass('d-none');
            $('.main-mobile-menu').removeClass('d-none')
        }
        // if(mobile_parent_menu == 0){
        // }
    });
    // $(document).on('changed.bs.select','#review_product_id', function(e) {
    //     let review_product_id = $(this).val();
    //     let url = "{{ route('live.get.review_products', [':review_product_id']) }}";
    //     url = url.replace(':review_product_id', review_product_id);
    //     $.get(url, function (data) {
    //         $('#review_product_item_id').empty().selectpicker('refresh').append(data).selectpicker('refresh');
    //     });
    // });
    setTimeout(function() {
        $('.collapse').each(function(index) {
            let accordion_body_height = $(this).attr('data-height');
            let accordion_id = $(this).attr('data-id');
            if (accordion_body_height) {
                $('.block.menu-accordion .card #collapse' + accordion_id + ' .card-body').css({
                    'height': accordion_body_height
                });
            }
        });
    }, 300);
    $(".sub-menu li > a").mouseenter(function() {
        let menu_id = $(this).attr('data-menu_id');
        // let url = "{{ route('live.submenu', [':menu_id']) }}";
        // url = url.replace(':menu_id', menu_id);
        $('.child-menu-col').removeClass('active');
        $('#child_items').empty();
        let child_pc_page = $('#accordionChildMenu' + menu_id).html();
        if (child_pc_page) {
            $('#child_items').empty().append(child_pc_page);
            $('#child_items .card').each(function(index) {
                let delay = 60 * index;
                $(this).css({
                    'animation': `rotateX 300ms ${delay}ms ease forwards`
                });
            });
            $('.child-menu-col').addClass('active');
            var parent_menu_id = $('#child_items .accordion.child-menu-accordion').attr('data-parent_menu_id');
            $('.menu_id_' + parent_menu_id).addClass('active');
            // console.log(parent_menu_id);
        }
    });
    $('#accordionMainMenu').on('show.bs.collapse', function(e) {
        $(e.target).closest('.card').addClass('active');
    });
    $('#accordionMainMenu').on('hide.bs.collapse', function(e) {
        $(e.target).closest('.card').removeClass('active');
    });
    $(".sub-menu li > a").hover(function() {
        setTimeout(function() {
            var parent_menu_id = $('#child_items .accordion.child-menu-accordion').attr(
                'data-parent_menu_id');
            $('.sub-menu-item').removeClass('active');
            $('.menu_id_' + parent_menu_id).addClass('active');
        }, 3);
    }, function() {
        setTimeout(function() {
            var parent_menu_id = $('#child_items .accordion.child-menu-accordion').attr(
                'data-parent_menu_id');
            $('.sub-menu-item').removeClass('active');
            $('.menu_id_' + parent_menu_id).addClass('active');
        }, 3);
    });
    $(".sub-menu-item").hover(
        function() {
            $(this).addClass('active');
        },
        function() {
            $(this).removeClass('active');
        }
    );
    $(document).on('click', '.accordion-child-btn', function() {
        var acc_item_id = $(this).attr('data-acc_item_id');
        $(this).toggleClass('active');
        if ($(this).hasClass('collapsed')) {
            $('.child_menu_item_' + acc_item_id).removeClass('active');
        } else {
            $('.child_menu_item_' + acc_item_id).addClass('active');
        }
    });
</script>
@if ($page->location_data->location_popup)
<script type="text/javascript">
    $(document).ready(function() {
        $('#locationModal').modal('show');
    })
</script>
@endif
@if (request()->token && empty(request()->paymentId))
<script type="text/javascript">
    setTimeout(function() {
        $('#resetPasswordModal').modal('show');
    }, 300);
</script>
@endif
<script>
    $("form").on("submit", function(e) {
        var tell_cell = $("#tell_cell").val();
        $("#tell_cell").val(tell_cell);
        var guest_tell_cell = $("#guest_tell_cell").val()
        $("#guest_tell_cell").val(guest_tell_cell);
        var shipping_tell_cell = $("#shipping_tell_cell").val();
        $("#shipping_tell_cell").val(shipping_tell_cell);
        var payment_tell_cell = $("#payment_tel_cell").val();
        $("#payment_tell_cell").val(payment_tell_cell);
        var contact_tell_cell = $("#contact_tell_cell").val();
        $("#contact_tell_cell").val(contact_tell_cell);
    });
</script>
<script>
    $(document).ready(function() {
        $(".review-toggle").click(function() {
            let isExpanded = $(this).attr("aria-expanded") === "true";
            // Toggle images only for the specific button with class .review-toggle
            $(this).find(".ratings-total-count img").attr("src", isExpanded ?
                "{{ asset('images/icon/star-black.png') }}" :
                "{{ asset('images/icon/star-black.png') }}");
            $(this).find('.review-count').css("display", isExpanded ? "inline-block" : "none");
        });
    });
</script>
<script>
    $(document).ready(function() {
        $('.media-popup').on('click', function() {
            // Make sure you call .data('media-type') NOT .data('data-media-type')
            var mediaType = $(this).data('media-type');
            var mediaUrl = $(this).data('media-url');
            console.log(mediaType);
            console.log(mediaUrl);
            // If either is empty, the modal will appear blank
            if (!mediaUrl) {
                return; // No file to show; you can prevent the modal from opening if needed
            }
            if (mediaType === 'video') {
                $('#modalContentWrapper').html(`
                <video controls autoplay style="width:100%; height:auto;border-radius:21px">
                    <source src="${mediaUrl}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            `);
            } else {
                $('#modalContentWrapper').html(`
                <img src="${mediaUrl}" alt="Review Image" style="width:100%; height:auto;border-radius:21px">
            `);
            }
        });
        // Clear modal content when hidden
        $('#mediaModal').on('hidden.bs.modal', function() {
            $('#modalContentWrapper').html('');
        });

    });
    $(document).ready(function() {
        $('#subscribe-form').on('submit', function(e) {
            e.preventDefault();

            var $form = $(this);
            var formData = $form.serialize();
            $('#form-error').hide().text('');
            subscribeButtonLoading(true);
            $.ajax({
                url: $form.attr('action'),
                method: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                success: function(response) {


                    $('#form-success').text('Thank you for subscribing!').show();

                    $form.trigger('reset');
                    subscribeButtonLoading(false);

                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;

                        if (errors.email) {
                            $('#form-error').text(errors.email[0]).show();
                        }

                        if (errors.phone) {
                            $('#form-error').text(errors.phone[0]).show();
                        }
                        if (errors.contact) {
                            $('#form-error').text(errors.contact[0]).show();
                        }
                        subscribeButtonLoading(false);

                    } else {
                        $('#form-error').text('Something went wrong. Try again later').show();
                        subscribeButtonLoading(false);

                    }
                }
            });
        });

        function subscribeButtonLoading(status) {
            if (status) {
                $('#subscribe-button .button-text').addClass('d-none');
                $('#subscribe-button .spinner-border').removeClass('d-none');
                $('#subscribe-button').prop('disabled', true);
            } else {
                $('#subscribe-button .button-text').removeClass('d-none');
                $('#subscribe-button .spinner-border').addClass('d-none');
                $('#subscribe-button').prop('disabled', false);
            }
        }
    });

    function sendPromoCode(phone, couponId, button) {
        const originalHtml = button.html();

        // Show loading spinner and message
        button.html(
            `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...`
        ).prop('disabled', true);
        $.ajax({
            url: "{{ route('sent-promo-code-sms') }}", // replace with your actual API endpoint
            method: 'POST',
            data: {
                phone: phone,
                coupon_id: couponId
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Only if it's a web route
            },
            success: function(response) {
                if (response.success) {
                    // Replace button content with success message
                    button.html(response.success || 'Promo code sent!');
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    button.html(xhr.responseJSON.error || 'An unexpected error occurred.');
                } else {
                    button.html('An unexpected error occurred.');
                }
            }
        });
    }
    $(document).on('click', '#sent_promo_code_sms', function() {
        const phone = $(this).data('mobile');
        const couponId = $(this).data('promo');
        const button = $(this);

        sendPromoCode(phone, couponId, button);
    });
</script>
<script>
    $(document).ready(function() {
        let discountModalClosed = @json(session('discount_modal_closed'));
        console.log('discountModalClosed - ' + discountModalClosed);
        @if(!session('discount_modal_closed') && !session('discount_fully_dismissed'))
        setTimeout(function() {
            if (!$('#locationModal').hasClass('show')) {
                $('#discountModal').modal('show');
                 $('.get-discount-tag').fadeOut(200);
            }
        }, 500);
        @endif

        // When user closes the main modal
        $('.close-discount').on('click', function(event) {
            $('#discountModal').modal('hide');
$('.get-discount-tag').fadeIn(200); // 500ms fade

            // Send AJAX to mark modal as closed
            $.post('{{ route("discount.markClosed") }}', {
                _token: '{{ csrf_token() }}'
            }).done(function() {

            });
        });

        // When user clicks minimized tag button
        $('.btn-off').on('click', function(event) {

            $('#discountModal').modal('show');
            $('.get-discount-tag').fadeOut(200); // 500ms fade

        });

        // When user dismisses the minimized tag
        $('.close-discount-tag').on('click', function(event) {


            $('.get-discount-tag').hide();

            $.post('{{ route("discount.markDismissed") }}', {
                _token: '{{ csrf_token() }}'
            });
        });
    });
</script>

@yield('scripts')