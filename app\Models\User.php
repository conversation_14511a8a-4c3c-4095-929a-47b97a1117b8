<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Auth;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        // 'tel_cell_country_code',
        'tel_cell',
        'username',
        'country',
        'postal_code',
        'city',
        'state',
        'address',
        'address_2',
        'agree_terms',
        'password',
        'ip',
        'email',
        'password',
        'email_notifications',
        'sms_notifications',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function userGroup()
    {
        $user_group_maps = UserGroupMap::where('user_id',  Auth::user()->id)->get();
        foreach ($user_group_maps as $user_group_map) {
            $user_groups[] = $user_group_map->user_group_id;
        }
        return Auth::check() ? $user_groups : false;
    }

    public function permissions($component, $key = null)
    {
        if (!Auth::check()) return false;

        if ($this->access('master_login')) return true;

        $component = Component::where('key', $component)->first();
        if (!$component) return false; // 'Component not found!'
        $permissions = UserPermission::whereIn('user_group_id', $this->getAuthUserChildArray())->where('component_id', $component->id)->get();
        if ($permissions->count() <= 0) return false;
        if (is_null($key)) return true;

        $keyPermission = UserAccessKey::where('key', $key)->first();
        if (is_null($keyPermission)) return false; // 'Unknown Key'
        foreach ($permissions as $permission) {
            if ($permission->user_permission_components_id == $keyPermission->id) return true;
        }
    }

    public function getAuthUserChildArray()
    {
        $user = Auth::user();
        $user_groups_array = UserGroupMap::where('user_id', $user->id)->get(['user_group_id']);
        $user_groups = array();
        foreach ($user_groups_array as $user_group_array) {
            $user_group = UserGroup::where('id', $user_group_array->user_group_id)->first();
            $user_groups[] = $user_group;
        }
        foreach ($user_groups as $user_group) {
            $user_group->user_group = $user_group->id;
            $user_group->child = $this->childUserGroup($user_group->children);
            $user_group->user_groups = $this->getUserGroups($user_group->children);
            $get_user_groups[] = implode(',', $user_group->user_groups) . ',' . $user_group->id;
        }

        foreach ($get_user_groups as $get_user_group) {
            if ($get_user_group) {
                $get_user_group_data[] = $get_user_group;
            }
        }

        $user_get_groups = implode(',', $get_user_group_data);
        $user_get_groups_array = explode(',', $user_get_groups);
        $user_groups_array = array_unique($user_get_groups_array);

        return $user_groups_array;
    }

    public function childUserGroup($items)
    {
        foreach ($items as $item) {
            $item->user_group = $item->id;
            $item->child = $this->childUserGroup($item->children);
            $item->user_groups = $this->getUserGroups($item->children);
        }
        return $items;
    }

    public function getUserGroups($items)
    {
        $user_items = array();
        $old_user_group = null;
        foreach ($items as $item) {
            $user_items[] = $item->id;
            if (count($item->user_groups) > 0) {
                $old_user_group[] = implode(',', $item->user_groups);
            }
        }
        if ($old_user_group) {
            $user_items = array_merge($user_items, $old_user_group);
        }
        return $user_items;
    }


    public function isAdmin()
    {
        return ($this->access('master_login') || $this->access('admin_login')) ? true : false;
    }

    public function access($key)
    {
        if (!Auth::check()) return false;
        $user_groups = UserGroupMap::where('user_id', auth()->user()->id)->get(['user_group_id']);
        $user_access_permission_array = array();
        foreach ($user_groups as $user_group) {
            $user_group = UserGroup::findOrFail($user_group->user_group_id);
            $user_access_permission_array[] = $user_group->user_permission_accesses_id;
        }

        $user_access_permission_data = array_unique(explode(',', implode(',', $user_access_permission_array)));
        if (!is_null($key)) {
            $keyPermission = UserAccessKey::where('key', $key)->first();
            return in_array($keyPermission->id, $user_access_permission_data) ? true : false;
        }

        $user_access_permissions = UserAccessKey::whereType('access')->get();
        foreach ($user_access_permissions as $user_access_permission) {
            if (in_array($user_access_permission->id, $user_access_permission_data)) {
                $user_access_data[$user_access_permission->key] = true;
            } else {
                $user_access_data[$user_access_permission->key] = false;
            }
        }

        return (object)$user_access_data;
    }
}
