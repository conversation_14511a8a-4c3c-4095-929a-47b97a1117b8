<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Order;
use App\Mail\SiteEmail;
use EasyPost\EasyPostClient;
use App\Models\EmailTemplate;
use App\Mail\OrderShippedMail;
use App\Models\AlertPreference;
use Illuminate\Console\Command;
use App\Models\EmailTemplateGroup;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class CheckShippingStatus extends Command
{
    protected $signature = 'shipping:check-status';
    protected $description = 'Check shipping status via EasyPost API and update orders';

    public function handle()
    {
        $controller = new Controller();
        $orders = Order::where('status', '!=', 5)
            ->whereHas('user', function ($query) {
                $query->where('country', 'US');
            })
            ->with('user')
            ->get();
        foreach ($orders as $order) {
            $user_email_templates_group = EmailTemplateGroup::where('title', 'Order_Shipped_API')->first();
            $user_email_templates = EmailTemplate::where('email_template_group_id', $user_email_templates_group->id)->get();
            $tracking_code = $order->tracking_code;
            if ($tracking_code) {
                $client = new EasyPostClient(config('app.easypost_api'));
                $tracker = $client->tracker->create([
                    'tracking_code' => $order->tracking_code,
                    // 'tracking_code' => '1ZRK72890702403229',
                ]);
            

                foreach ($user_email_templates as $user_email_template) {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $order->user,
                        'order' => $order,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                        'tracking_code' => $tracker->public_url
                    ];
                }
                if ($tracker->status === 'in_transit') {
                    $order->shipped_at = now();
                    $order->status = 5;
                    $order->update();
                    $this->info("Order ID {$order->id} marked as Shipped.");
                    $mail_data->subject = $controller->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $controller->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $controller->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $controller->sendEmailData($mail_data)->sms_body;
                    Mail::to($order->user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $this->info('Shipping status check completed.');
    }
}
