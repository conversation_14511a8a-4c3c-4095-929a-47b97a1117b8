<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Mail\SiteEmail;
use App\Models\Tracking;
use Illuminate\Http\Request;
use App\Models\EmailTemplate;
use App\Models\RedeemCashReward;
use App\Models\EmailTemplateGroup;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Events\RewardsAlert;

class RedeemCashRewardController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Rewards';
        $items = RedeemCashReward::orderBy('id', 'desc')->paginate($this->itemPerPage);
        
        foreach ($items as $value) {
            $customer = User::find($value->user_id);
            // dd($customer);
            $value->date_time = Carbon::parse($value->created_at)->format('M, d Y g:i A');;
            $value->full_name = $customer->first_name . ' ' . $customer->last_name;
            $value->customer_email = $customer->email;
            $value->customer_phone = $customer->tel_cell_country_code . $customer->tel_cell;
            $value->transfer_status = $this->rewardTransferStatus($value->transfer_status);
        }
        // dd($items);
        $sl = SLGenerator($items);
        return view('admin.reward.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        abort(404);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        abort(404);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\RedeemCashReward  $redeemCashReward
     * @return \Illuminate\Http\Response
     */
    public function show(RedeemCashReward $redeemCashReward)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\RedeemCashReward  $redeemCashReward
     * @return \Illuminate\Http\Response
     */
    public function edit(RedeemCashReward $redeemCashReward)
    {
        $admin_page_title = 'Update Reward';
        $item = $redeemCashReward;
        $item->rewardTransferStatus = $this->rewardTransferStatus();
        $item->transfer_meta = unserialize($item->transfer_meta);
        if ($item->transfer_type == 'bank_account') {
            $item->transfer_type = 'Bank Account';
            $item->transfer_details = $item->transfer_meta->bank . '<br />' . $item->transfer_meta->name_on_account . ' <br />' . $item->transfer_meta->account_number;
        } elseif ($item->transfer_type == 'zelle_payments') {
            $item->transfer_type = 'Zelle';
            $item->transfer_details = $item->transfer_meta->email_phone_number;
        } elseif ($item->transfer_type == 'check') {
            $item->transfer_type = 'Check';
            $item->transfer_details = $item->transfer_meta->full_name;
        } elseif ($item->transfer_type == 'cash_app_payment') {
            $item->transfer_type = 'Cash';
            $item->transfer_details = $item->transfer_meta->email_phone_number;
        }
        $item->email_templates = EmailTemplate::where('status', true)->get();
        $item->email_constants = $this->email_constants();
        $send_sms_emails = Tracking::where('cash_reward_id', $redeemCashReward->id)->get();
        foreach ($send_sms_emails as $value) {
            $value->date_time = Carbon::parse($value->created_at)->format('F j, Y g.iA');
        }
        $item->send_sms_emails = $send_sms_emails;
        $item->email_template_groups = EmailTemplateGroup::where('system_group', 'reward')->get();
        // dd($item);
        return view('admin.reward.edit', compact('item', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\RedeemCashReward  $redeemCashReward
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, RedeemCashReward $redeemCashReward)
    {
        $this->validate($request, [
            'transfer_status' => 'required|numeric',
        ]);
        $redeemCashReward->approve_user_id = auth()->user()->id;
        $redeemCashReward->transfer_status = $request->transfer_status;
        $redeemCashReward->status = $request->transfer_status == 3 || $request->transfer_status == 4 ? 1 : 0;
        $redeemCashReward->update();

        if ($request->action == 'send_email_sms') {
            $this->validate($request, [
                'subject' => 'required|string',
                'pre_header' => 'required|string',
                'email_body' => 'required|string',
                'sms_body' => 'nullable|string',
            ]);

            $user = User::find($redeemCashReward->user_id);

            $mail_data = (object)[
                'user' => $user,
                'cash_reward' => $redeemCashReward,
                'subject' => $request->subject,
                'pre_header' => $request->pre_header,
                'email_body' => $request->email_body,
                'sms_status' => $request->sms_status ? true : false,
                'sms_body' => $request->sms_body,
            ];
            $mail_data->subject = $this->sendEmailData($mail_data)->subject;
            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
            $mail_data->content = $this->sendEmailData($mail_data)->content;
            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;


            $lead_source = (object)[
                'admin_id' => auth()->user()->id,
                'admin_email' => auth()->user()->email,
                'admin_phone' => auth()->user()->tel_cell_country_code . auth()->user()->tel_cell,
                'source' => 'admin',
            ];

            // $tracking = new Tracking();
            // $tracking->cash_reward_id = $redeemCashReward->id;
            // $tracking->content = serialize($this->sendEmailData($mail_data));
            // $tracking->from_email = config('mail.from.address');
            // $tracking->to_email = $user->email;
            // $tracking->subject = $request->subject;
            // $tracking->lead_source = serialize($lead_source);
            // $tracking->ip = $this->getClientIpAddress();
            // $tracking->save();

            event(new RewardsAlert($redeemCashReward, $user, $request->sms_status, $request->email_template_id));

            // Mail::to($user->email)->send(new SiteEmail($mail_data));

            // if ($request->sms_status && $user->tel_cell) {
            //     $sms_tracking = Tracking::findOrFail($tracking->id);
            //     $sms_tracking->sms_content = serialize($mail_data->sms_content);
            //     $sms_tracking->from_phone = config('app.twilio_number');
            //     $sms_tracking->to_phone = $user->tel_cell;
            //     $sms_tracking->update();
            // }

            return back()->with('success', 'Reward update Successful.');
        }

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.redeem_cash_rewards.index'))->with('success', 'Reward update Successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\RedeemCashReward  $redeemCashReward
     * @return \Illuminate\Http\Response
     */
    public function destroy(RedeemCashReward $redeemCashReward)
    {
        abort(404);
    }
}
