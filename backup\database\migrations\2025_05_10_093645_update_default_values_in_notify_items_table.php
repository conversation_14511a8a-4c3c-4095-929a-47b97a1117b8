<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateDefaultValuesInNotifyItemsTable extends Migration
{
    public function up(): void
    {
        Schema::table('notify_items', function (Blueprint $table) {
            $table->boolean('status_phone')->default(false)->change();
            $table->boolean('status_email')->default(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('notify_items', function (Blueprint $table) {
            // Optional: revert back to whatever the old default was, if you know it.
            $table->boolean('status_phone')->default(true)->change();
            $table->boolean('status_email')->default(true)->change();
        });
    }
}
