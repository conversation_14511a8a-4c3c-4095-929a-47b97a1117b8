<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('access_label_id')->constrained('access_labels');
            $table->string('title');
            $table->string('page_type')->nullable();
            $table->string('page_key')->nullable();
            $table->string('slug');
            $table->string('url_type')->nullable();
            $table->mediumText('content')->nullable();
            $table->string('header_image')->nullable();
            $table->string('article_image')->nullable();
            $table->string('meta_title')->nullable();
            $table->string('meta_keyword')->nullable();
            $table->string('meta_description')->nullable();
            $table->boolean('is_menu')->default(false);
            $table->string('menu_name')->nullable();
            $table->string('menu_icon')->nullable();
            $table->integer('ordering')->default(0);
            $table->foreignId('parent_menu_id')->nullable()->constrained('pages');
            $table->string('menu_location')->nullable();
            $table->longText('page_meta')->nullable();
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pages');
    }
}
