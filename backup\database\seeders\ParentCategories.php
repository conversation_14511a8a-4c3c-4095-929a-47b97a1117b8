<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use Illuminate\Database\Seeder;
use App\Models\ProdcutCategoriesParent;

class ParentCategories extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $parentCategory = ProdcutCategoriesParent::find(1);
        ProductCategory::query()->update(['parent_category_id' => $parentCategory->id]);
    }
}
