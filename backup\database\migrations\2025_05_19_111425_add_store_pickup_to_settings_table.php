<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStorePickupToSettingsTable extends Migration
{
   public function up()
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->boolean('store_pickup')->default(false)->after('status'); 
        });
    }

    public function down()
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn('store_pickup');
        });
    }
}
