<ul class="sub-menu level_{{ $level }} clearfix">
    @foreach ($items as $item)
    <li class="{{ count($item->children) > 0 ? 'parent' : '' }}">
        <a id="menu_id_{{ $item->id }}" class="sub-menu-item menu_id_{{ $item->id }} {{ $item->menu_active ?'active ':'' }}@if(count($item->menuChildren) > 0)have-sub-menu @endif" data-menu_id="{{ $item->id }}" href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" {{$item->url_type=='custom_url'?'target="_blank"':''}}>
            <span>{{ $item->menu_name ? $item->menu_name : $item->title }}</span>
            @if(count($item->menuChildren) > 0)
            <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
            </svg>
            @endif
        </a>

        @if (count($item->children) > 0)
            @if ($level == 2)
                @include('layouts.utility.menu.sub_menu_menu', [
                    'items' => $item->children,
                    'menu' => $item,
                    'level' => $level + 1
                    // 'level' => 3
                ])
            {{-- <button class="btn btn-link collapsed accordion-btn" data-toggle="collapse" data-target="#collapseChild{{ $item->id }}" aria-expanded="false" aria-controls="collapseChild{{ $item->id }}">
                <i class="fas fa-plus"></i><i class="fas fa-minus"></i>
            </button> --}}

            {{-- <div id="collapseChild{{ $item->id }}" class="collapse" aria-labelledby="headingChild{{ $item->id }}">
                <ul>
                    @include('layouts.utility.menu.sub_menu_menu', [
                        'items' => $item->children,
                        'level' => 3
                    ])
                </ul>
            </div> --}}
            @else
                {{-- <ul>
                    @include('layouts.utility.menu.sub_menu', [
                        'items' => $item->children,
                        'level' => $level + 1
                    ])
                </ul> --}}
            @endif
        @endif
    </li>
    @endforeach
</ul>

{{-- @if(count($items))
<ul class="sub-menu clearfix">
    @foreach ($items as $item)
       @if($item->access)
            <li>
               <a id="menu_id_{{ $item->id }}" class="sub-menu-item menu_id_{{ $item->id }} {{ $item->menu_active ?'active ':'' }}@if(count($item->menuChildren) > 0)have-sub-menu @endif" data-menu_id="{{ $item->id }}" href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" {{$item->url_type=='custom_url'?'target="_blank"':''}}>
                   @if($item->menu_icon)<i class="{{ $item->menu_icon }}"></i>@endif
                   <span>{{$item->menu_name?$item->menu_name:$item->title}}</span>
                   @if(count($item->menuChildren) > 0)
                    <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                        <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                    </svg>
                   @endif
               </a>
               @if($item->menuChildren)
               @include('layouts.utility.menu.sub_menu',[
                   'items' => $item->menuChildren,
                ])
               @endif
           </li>
       @endif
    @endforeach
</ul>
@endif --}}
