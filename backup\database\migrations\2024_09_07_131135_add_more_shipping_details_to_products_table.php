<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMoreShippingDetailsToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('shipping_weight', 'open_box_shipping_weight');
            $table->renameColumn('shipping_length', 'open_box_shipping_length');
            $table->renameColumn('shipping_height', 'open_box_shipping_height');
            $table->renameColumn('shipping_width', 'open_box_shipping_width');

            $table->after('product_attribute_id', function ($table) {
                $table->string('box_with_shipping_weight')->nullable();
                $table->string('box_with_shipping_length')->nullable();
                $table->string('box_with_shipping_height')->nullable();
                $table->string('box_with_shipping_width')->nullable();
            });
            $table->dropColumn('box_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('open_box_shipping_weight', 'shipping_weight');
            $table->renameColumn('open_box_shipping_length', 'shipping_length');
            $table->renameColumn('open_box_shipping_height', 'shipping_height');
            $table->renameColumn('open_box_shipping_width', 'shipping_width');

            $table->dropColumn('box_with_shipping_weight');
            $table->dropColumn('box_with_shipping_length');
            $table->dropColumn('box_with_shipping_height');
            $table->dropColumn('box_with_shipping_width');
        });
    }
}
