<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhatsappTemplateMetasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('whatsapp_template_metas', function (Blueprint $table) {
    $table->id();
    $table->string('template_id')->unique(); // like "order_received"
    $table->string('title'); // like "Order Received"
    $table->text('body'); // the message body with {{1}}, {{2}}, etc.
    $table->json('variables'); // array of variable descriptions
    $table->timestamps();
});
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('whatsapp_template_metas');
    }
}
