<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EstimatedDeliveryDate;
use Illuminate\Http\Request;
use Validator;

class EstimatedDeliveryDateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Estimated Delivery Dates';
        $items = EstimatedDeliveryDate::paginate($this->itemPerPage);
        foreach($items as $value){
            $value->sku_location_id = $this->countries($value->sku_location_id);
            $value->ship_location_id = $this->countries($value->ship_location_id);
        }
        $sl = SLGenerator($items);
        $countries = $this->countries();

        if ($request->view == 'html') {
            return view('admin.jquery_live.estimate_delivery_date', compact('items', 'sl'));
        }

        return view('admin.setting.estimate_delivery_date', compact('items', 'sl', 'admin_page_title', 'countries'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        abort(404);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sku_location_id' => 'required|string|max:3',
            'ship_location_id' => 'required|string|max:3',
            'min_duration' => 'required|integer|max:90',
            'max_duration' => 'required|integer|max:90',
            'status' => 'nullable|boolean',
        ],[
            'sku_location_id.required' => 'The product item location is required.',
            'ship_location_id.required' => 'The customer ship to location is required.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 400);
        }

        $item = new EstimatedDeliveryDate();
        $item->sku_location_id = $request->get('sku_location_id');
        $item->ship_location_id = $request->get('ship_location_id');
        $item->min_duration = $request->get('min_duration');
        $item->max_duration = $request->get('max_duration');
        $item->status = $request->get('status') ? 1 : 0;
        $item->save();

        return response()->json([
            'status' => 'success',
            'message' => $item->title . ' Successfully added',
        ], 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EstimatedDeliveryDate  $estimatedDeliveryDate
     * @return \Illuminate\Http\Response
     */
    public function show(EstimatedDeliveryDate $estimatedDeliveryDate)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EstimatedDeliveryDate  $estimatedDeliveryDate
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = EstimatedDeliveryDate::find($id);
        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EstimatedDeliveryDate  $estimatedDeliveryDate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,$id)
    {
        $validator = Validator::make($request->all(), [
            'sku_location_id' => 'required|string|max:3',
            'ship_location_id' => 'required|string|max:3',
            'min_duration' => 'required|integer|max:90',
            'max_duration' => 'required|integer|max:90',
            'status' => 'nullable|boolean',
        ], [
            'sku_location_id.required' => 'The product item location is required.',
            'ship_location_id.required' => 'The customer ship to location is required.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 400);
        }

        $item = EstimatedDeliveryDate::findOrFail($id);
        $item->sku_location_id = $request->get('sku_location_id');
        $item->ship_location_id = $request->get('ship_location_id');
        $item->min_duration = $request->get('min_duration');
        $item->max_duration = $request->get('max_duration');
        $item->status = $request->get('status') ? 1 : 0;
        $item->update();

        return response()->json([
            'status' => 'success',
            'message' => $item->title . ' Successfully updated',
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EstimatedDeliveryDate  $estimatedDeliveryDate
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = EstimatedDeliveryDate::findOrFail($id);
        $item->delete();

        return response()->json([
            'status' => 'success',
            'message' => $item->title . ' Successfully deleted',
        ], 200);
    }
}
