<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\URL;

class SiteEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $pre_header;
    public $subject;
    public $content;
    public $store_location;
    public $user_id;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($mail_data)
    {
        $this->subject = $mail_data->subject;
        $this->pre_header = $mail_data->pre_header;
        $this->content = $mail_data->content;
        $this->store_location = $mail_data->store_location ?? null;
        $this->user_id = $mail_data->user->id ?? null;

        // $this->sms_content = $mail_data->sms_content;
        // if ($mail_data->sms_status) {
        //     $message =  $mail_data->sms_content;

        //     if ($mail_data->user->tel_cell)
        //     {
        //         app('App\Http\Controllers\Controller')->sendMessage($message, $mail_data->user->tel_cell);
        //     }
        // }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $signedUrl = URL::signedRoute('unsubscribe.promotional', ['id' => $this->user_id]);

        return $this->subject($this->subject)
            ->view('email_templates.site_email')
            ->with(['store_location' => $this->store_location, 'signedUrl' => $signedUrl]);
    }
}
