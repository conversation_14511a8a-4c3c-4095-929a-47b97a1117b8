<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShippingRule extends Model
{
    protected $table = 'shipping_rules';

    protected $fillable = [
        'store_id',
        'destination_country',
    ];

    // If you want to disable timestamps, uncomment below:
    // public $timestamps = false;
    public function store()
{
    return $this->belongsTo(Setting::class, 'store_id');
}
}
