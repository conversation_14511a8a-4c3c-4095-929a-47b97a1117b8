<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class PaymentMethodController extends Controller
{
    /**
     * Get available payment methods for checkout
     */
    public function getAvailableForCheckout(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'country' => 'required|string|size:2',
            'currency' => 'required|string|size:3',
            'amount' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $country = $request->get('country');
        $currency = $request->get('currency');
        $amount = (float) $request->get('amount');

        try {
            $paymentMethods = PaymentMethod::getAvailableForCheckout($country, $currency)
                ->filter(function ($method) use ($country, $currency, $amount) {
                    return $method->isAvailable($country, $currency, $amount);
                })
                ->map(function ($method) use ($amount, $currency) {
                    return $this->formatPaymentMethodForCheckout($method, $amount, $currency);
                });

            return response()->json([
                'success' => true,
                'payment_methods' => $paymentMethods->values(),
                'total_count' => $paymentMethods->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payment methods for checkout: ' . $e->getMessage(), [
                'country' => $country,
                'currency' => $currency,
                'amount' => $amount
            ]);

            return response()->json(['error' => 'Unable to load payment methods'], 500);
        }
    }

    /**
     * Get payment method details including fees and split options
     */
    public function getPaymentMethodDetails(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $paymentMethod = PaymentMethod::findOrFail($id);
        $amount = (float) $request->get('amount');
        $currency = $request->get('currency');

        try {
            $details = [
                'id' => $paymentMethod->id,
                'name' => $paymentMethod->name,
                'title' => $paymentMethod->title,
                'subtitle' => $paymentMethod->subtitle,
                'type' => $paymentMethod->type,
                'payment_options' => $paymentMethod->payment_options,
                'supports_split_payment' => $paymentMethod->supports_split_payment,
                'fee_breakdown' => $paymentMethod->getFeeBreakdown($amount),
                'discount_amount' => $paymentMethod->getDiscountAmount($amount, $currency),
                'requires_partial_transactions' => $paymentMethod->exceedsSingleTransactionLimit($amount),
            ];

            // Add split payment options if supported
            if ($paymentMethod->supports_split_payment) {
                $details['split_payment_options'] = $paymentMethod->getSplitPaymentConfigurations();
                
                // Calculate split amounts for each option
                $details['split_calculations'] = [];
                foreach ($paymentMethod->split_payment_options as $option) {
                    try {
                        $details['split_calculations'][$option] = $paymentMethod->calculateSplitPayment($amount, $option);
                    } catch (\Exception $e) {
                        // Skip this option if calculation fails
                        continue;
                    }
                }
            }

            // Add partial transaction details if needed
            if ($paymentMethod->exceedsSingleTransactionLimit($amount)) {
                $details['partial_transactions'] = $paymentMethod->calculatePartialTransactions($amount);
                $details['transaction_fees'] = $paymentMethod->calculateFees($amount);
            }

            return response()->json([
                'success' => true,
                'payment_method' => $details
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payment method details: ' . $e->getMessage(), [
                'payment_method_id' => $id,
                'amount' => $amount,
                'currency' => $currency
            ]);

            return response()->json(['error' => 'Unable to load payment method details'], 500);
        }
    }

    /**
     * Admin: List all payment methods
     */
    public function index(Request $request)
    {
        $query = PaymentMethod::query();

        // Filter by type if provided
        if ($request->has('type')) {
            $query->byType($request->get('type'));
        }

        // Filter by status if provided
        if ($request->has('status')) {
            if ($request->get('status') === 'active') {
                $query->active();
            } elseif ($request->get('status') === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $paymentMethods = $query->orderBy('sort_order')->orderBy('title')->get();

        return response()->json([
            'success' => true,
            'payment_methods' => $paymentMethods
        ]);
    }

    /**
     * Admin: Create new payment method
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:payment_methods,name|max:255',
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'type' => 'required|in:traditional,bnpl,crypto',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'api_config' => 'nullable|array',
            'payment_options' => 'nullable|array',
            'allowed_countries' => 'nullable|array',
            'allowed_currencies' => 'nullable|array',
            'discount_type' => 'in:none,percentage,fixed',
            'discount_value' => 'nullable|numeric|min:0',
            'fee_bearer' => 'in:customer,system,split',
            'fee_percentage' => 'nullable|numeric|min:0|max:100',
            'fee_fixed' => 'nullable|numeric|min:0',
            'split_ratio' => 'nullable|numeric|min:0|max:1',
            'supports_split_payment' => 'boolean',
            'split_payment_options' => 'nullable|array',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'max_single_transaction' => 'nullable|numeric|min:0',
            'supports_partial_transactions' => 'boolean',
            'supports_refunds' => 'boolean',
            'sandbox_mode' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        try {
            $data = $request->except(['logo']);
            
            // Handle logo upload
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('payment-methods', 'public');
                $data['logo_url'] = Storage::url($logoPath);
            }

            // Set default values
            $data['is_active'] = $request->get('is_active', true);
            $data['sort_order'] = $request->get('sort_order', 0);
            $data['discount_type'] = $request->get('discount_type', 'none');
            $data['fee_bearer'] = $request->get('fee_bearer', 'customer');
            $data['supports_split_payment'] = $request->get('supports_split_payment', false);
            $data['supports_partial_transactions'] = $request->get('supports_partial_transactions', false);
            $data['supports_refunds'] = $request->get('supports_refunds', true);
            $data['sandbox_mode'] = $request->get('sandbox_mode', false);

            $paymentMethod = PaymentMethod::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Payment method created successfully',
                'payment_method' => $paymentMethod
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error creating payment method: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create payment method'], 500);
        }
    }

    /**
     * Admin: Show specific payment method
     */
    public function show($id)
    {
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'payment_method' => $paymentMethod
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Payment method not found'], 404);
        }
    }

    /**
     * Admin: Update payment method
     */
    public function update(Request $request, $id)
    {
        $paymentMethod = PaymentMethod::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:payment_methods,name,' . $id,
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'type' => 'required|in:traditional,bnpl,crypto',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'api_config' => 'nullable|array',
            'payment_options' => 'nullable|array',
            'allowed_countries' => 'nullable|array',
            'allowed_currencies' => 'nullable|array',
            'discount_type' => 'in:none,percentage,fixed',
            'discount_value' => 'nullable|numeric|min:0',
            'fee_bearer' => 'in:customer,system,split',
            'fee_percentage' => 'nullable|numeric|min:0|max:100',
            'fee_fixed' => 'nullable|numeric|min:0',
            'split_ratio' => 'nullable|numeric|min:0|max:1',
            'supports_split_payment' => 'boolean',
            'split_payment_options' => 'nullable|array',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'max_single_transaction' => 'nullable|numeric|min:0',
            'supports_partial_transactions' => 'boolean',
            'supports_refunds' => 'boolean',
            'sandbox_mode' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        try {
            $data = $request->except(['logo']);

            // Handle logo upload
            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                if ($paymentMethod->logo_url) {
                    $oldLogoPath = str_replace('/storage/', '', $paymentMethod->logo_url);
                    Storage::disk('public')->delete($oldLogoPath);
                }
                
                $logoPath = $request->file('logo')->store('payment-methods', 'public');
                $data['logo_url'] = Storage::url($logoPath);
            }

            $paymentMethod->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Payment method updated successfully',
                'payment_method' => $paymentMethod->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating payment method: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update payment method'], 500);
        }
    }

    /**
     * Admin: Delete payment method
     */
    public function destroy($id)
    {
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            
            // Delete logo file if exists
            if ($paymentMethod->logo_url) {
                $logoPath = str_replace('/storage/', '', $paymentMethod->logo_url);
                Storage::disk('public')->delete($logoPath);
            }
            
            $paymentMethod->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting payment method: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete payment method'], 500);
        }
    }

    /**
     * Admin: Toggle payment method status
     */
    public function toggleStatus($id)
    {
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            $paymentMethod->update(['is_active' => !$paymentMethod->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Payment method status updated successfully',
                'is_active' => $paymentMethod->is_active
            ]);

        } catch (\Exception $e) {
            Log::error('Error toggling payment method status: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update status'], 500);
        }
    }

    /**
     * Format payment method for checkout display
     */
    private function formatPaymentMethodForCheckout(PaymentMethod $method, float $amount, string $currency): array
    {
        $feeBreakdown = $method->getFeeBreakdown($amount);
        $discountAmount = $method->getDiscountAmount($amount, $currency);
                $discountValue = $method->discount_value;

        return [
            'id' => $method->id,
            'name' => $method->name,
            'title' => $method->title,
            'subtitle' => $method->subtitle,
            'logo_url' => $method->logo_url,
            'type' => $method->type,
            'discount_type' => $method->discount_type,
            'fee_percentage' => $method->fee_percentage,
            'fee_fixed' => $method->fee_fixed,
            'payment_options' => $method->payment_options,
            'supports_split_payment' => $method->supports_split_payment,
            'split_payment_options' => $method->supports_split_payment ? $method->getSplitPaymentConfigurations() : [],
            'fee_info' => [
                'customer_fees' => $feeBreakdown['customer_fees'],
                'fee_bearer' => $feeBreakdown['fee_bearer'],
                'transaction_count' => $feeBreakdown['transaction_count']
            ],
            'discount_amount' => $discountAmount,
            'discount_value' => $discountValue,
            'requires_partial_transactions' => $method->exceedsSingleTransactionLimit($amount),
            'partial_transaction_info' => $method->exceedsSingleTransactionLimit($amount) ? [
                'max_single_amount' => $method->max_single_transaction,
                'transaction_count' => count($method->calculatePartialTransactions($amount))
            ] : null
        ];
    }

    /**
     * Calculate split payment preview
     */
    public function calculateSplitPayment(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'split_option' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            $amount = (float) $request->get('amount');
            $splitOption = $request->get('split_option');

            $calculation = $paymentMethod->calculateSplitPayment($amount, $splitOption);

            return response()->json([
                'success' => true,
                'split_calculation' => $calculation
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }
}