<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Session;

class AdminController extends Controller
{
    public function index()
    {
        $admin_page_title = 'Dashboard';
        return view('admin.dashboard', compact('admin_page_title'));
    }

    public function refreshData(){

        Session::forget('product_option_icon');
        Session::forget('product_option_text_icon');

        return response()->json('success', 200);
    }
}
