<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeToAlertPreferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('alert_preferences', function (Blueprint $table) {
    $table->string('type')->default('default'); // 'default' or 'custom'
});
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('alert_preferences', function (Blueprint $table) {
                        $table->dropColumn('type'); // remove column if rolled back
        });
    }
}
