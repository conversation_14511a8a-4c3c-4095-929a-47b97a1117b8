<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#ffffff;text-align:center;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1538481480C0" style="width:867px;" class="column-headers-background">A</th><th id="1538481480C1" style="width:525px;" class="column-headers-background">B</th></tr></thead><tbody><tr style="height: 20px"><th id="1538481480R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0"></td><td class="s0"></td></tr><tr style="height: 43px"><th id="1538481480R1" style="height: 43px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 43px">2</div></th><td class="s1">Description of Requirements (Let&#39;s discuss before you start!)</td><td class="s0"></td></tr><tr style="height: 94px"><th id="1538481480R2" style="height: 94px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 94px">3</div></th><td class="s2">The website offers:<br>- Domestic shipping (that is shipping within the same country i.e. city to city, state to state)<br>- International Shipping (shipping from country to country)<br>- Pick up of orders at a local store that offers Store Pickups</td><td class="s0"></td></tr><tr style="height: 80px"><th id="1538481480R3" style="height: 80px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 80px">4</div></th><td class="s2">The website Uses 3 kinds of shipping APIs:<br>- EasyPost API (partially implemented)<br>- DHL Express API (partially implement)<br>- 3rd Party shipping API (Not sure if the previous Dev started it)</td><td class="s2"></td></tr><tr style="height: 227px"><th id="1538481480R4" style="height: 227px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 227px">5</div></th><td class="s2" dir="ltr">Shipping of cart items can be aggregated or split into multuiple sub-orders inside an order. The decision to split or aggregate shipping of order item is carried out by the system and this is transparent to the customer. If aggregation is selected, we need to take into account that the order items can be in multiple store locations and items may need to be shipped down to an aggregation point (usually the main store location). From this aggregating store, the items will now be shipped. It makes sense cost-wise to only aggregate order SKU items in thesame country/location. Would love to hear your opinion on this.<br><br>If the system deem split shipment as the optimal solution, then the orders will ship separately from one another. In the case where all items in an order are located at a particular store, the items will be shipped together.<br><br>The time it will take to aggregate the items for shipping need to be taken into account as well as the time required to prepare a split/or non slipt order for shipment. This will be included in the final estimated delivery times show in the &quot;Shipping/delivery&quot; section at checkout. This estimated delivery time = shipping transient time from shipping APIs if available (or worst case estimated delivery times set in the backend for a Ship To and SKU location relationship for 3PL that normally don&#39;t have APIs) + Order aggregation time if any + Order preparation time. The order preparation time could be a backend setting.<br><br>Store pickup option will also showed a &quot;Ready by&quot; date and the store information where to pickup to.</td><td class="s2"></td></tr><tr style="height: 83px"><th id="1538481480R5" style="height: 83px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 83px">6</div></th><td class="s2">EasyPost is one of the largest shipping logistic provider.They provide an API that integrates the APIs of multiple shipping and logistics providers/carriers. In some cases, their shipping costs are cheaper than going directly to buy from any of the shipping providers they offer. For instance, the EasyPost API also integrates the DHL API. Sometimes the price coming from DHL easypost is cheaper than the price coming from DHL&#39;s API</td><td class="s2"></td></tr><tr style="height: 374px"><th id="1538481480R6" style="height: 374px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 374px">7</div></th><td class="s2" dir="ltr">The plan is to offer DHL Express International service for all international shipments, and DHL Express Domestic for non-United states local shipments ( i.e. city to city and state to state shipments).<br>Then, use EasyPost API for all shipments within the US. Please, do not make your static. I still want a dynamic code that allows for adding new shipping API and for setting which countries are allowed to use them. However, this chould change any time soon. We could decide to even add seperate FedEx and UPS APIs. So, think scalability.<br><br>Both DHL API and EasyPost API provide shipment tracking. The previous devs have implemented this to some extent. Needs to be finished or completely re-written. Your choice!<br><br>Examples:<br>- For united States to Ghana, or from China to Nigeria: DHL Express international will be used.<br>- For States  or Cities inside Nigeria or Ghana: DHL Express Domestic will be used<br>- For states and Cities inside the United States, We will use EasyPost API to obtain rates and then provide the best options to the customer. <br><br>So, you will need to create a backend configuration that allows us to show either 1 or 2 or 3 or more shipping options based on some criteria like the Country allowed, Name of shipper, number of business days it will take to ship, the name of the shipping option/service and/or cost limits. As an example, DHL API will typically provide more than 1 option, but we will be using only the DHL Express option/service because I have a contract with DHL for this option. So, we need to be able to configure this option from the backend. EasyPost will usually provide a lot of services/options from multiple carriers like DHL, FEDEX, UPS, USPS, etc. So we need to able to configure which once we want to use. <span style="font-weight:bold;">Let&#39;s discuss the part until you understand what I want. Your opinions are also valued!</span></td><td class="s0"></td></tr><tr style="height: 151px"><th id="1538481480R7" style="height: 151px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 151px">8</div></th><td class="s2"><span style="font-size:10pt;font-family:Arial;color:#000000;">An international shipment will need to go through customs. Customs would normally determine how much duties and taxes need to be paid before the shipment is allowed to continue it journey to the final destination. DHL Express or any international service offers the option for the Shipment duties and taxes to be paid before shipment or during/after delivery. If paid before shipment, the term for it is </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">DDP (Delivered Duties Paid)</span><span style="font-size:10pt;font-family:Arial;color:#000000;"> and if paid later the term for it is </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">DDU (Delivered Duties Unpaid). </span><span style="font-size:10pt;font-family:Arial;color:#000000;">This is important because in the DHL API 9also for EasyPost I believe), there is a field called </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">incoterms. </span><span style="font-size:10pt;font-family:Arial;color:#000000;"> There are multiple possible values for incoterms, but the ones we care about are DDP and DDU.<br><br>Also, every international shipment needs to include a custom invoice describing the shipment and the value. You need to generate this automatically in your code for every international shipment.</span></td><td class="s0"></td></tr><tr style="height: 126px"><th id="1538481480R8" style="height: 126px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 126px">9</div></th><td class="s2" dir="ltr"><span style="text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#4285f4;">Duties and Taxes can be very expensive depending on the country and goods shipment. The kind of goods 1GuyGadget sells attracts higher duties and taxes. Also, the amount can fluctuate. So, even if DDP is selected, the customer may still owe duties and taxes when they receive the shipment. To avoid this, we will use</span><span style="font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#4285f4;"> </span><span style="font-weight:bold;color:#4285f4;">DHL Duties and Taxes</span><span style="font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#4285f4;"> API</span><span style="text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#4285f4;"> to calculate and collect duties and taxes.</span></td><td class="s0"></td></tr><tr style="height: 206px"><th id="1538481480R9" style="height: 206px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 206px">10</div></th><td class="s2"><span style="font-size:10pt;font-family:Arial;color:#000000;">In other to make shipping and duties and taxes more affordable, 1GuyGadget tries to partner with 3rd Party shipping services. These are companies that offer LTL (Less Than container Load) shipments. The issue with these kinds of services is that they are much more slower than DHL. We are talking 3 to 4 weeks to delivery. But, there may be some customers who don&#39;t care about this and would pay for a service like this. So, we will offer it. 3rd party shipping services provide a tracking URL. This url may not be as stable as DHL or EasyPost. We will use this 3rd shipping to bundle all order going from an origin store/warehouse to a destination store/warehouse. From the destination store/warehouse, the orders are then repacked individually and then shipped to the final destination using DHL express domestic service. So, there are two trips made: from origin store to destination store (1st trip) and from destination store to customer (2nd trip). Each trip takes time. The 1st trip&#39;s estimated time should be configured in the backend (there is already some code for this). It is estimated because it fluctuates a lot! This will be a setting you need to check that it works well. This first trip is also charged based on the weight per unit pound or kg depending on the origin country. for example, If from the united states, it is LBS but if from China, it is KGS. So, we need to add a setting in the backend for p</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">rice per unit weight.</span><span style="font-size:10pt;font-family:Arial;color:#000000;"> The 2nd trip time is more stable and should come from an API pfferince domestic services like DHL domestic API. Both trips need to provide the customer with tracking details so that they always know where their shipment is.</span></td><td class="s0"></td></tr><tr style="height: 101px"><th id="1538481480R10" style="height: 101px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 101px">11</div></th><td class="s2">Just like every other thing with pricing on the website, shipping can be discounted or be completely free due to a promo. This means that promo affects shipping. <br><br>All API (DHL or EasyPost) Shipping services bought/selected by the customer will generate shipping label(s) when posible. These shipping label needs to be attached to the order in the backend for easy access by Admin. The 3rd party shipping 1st trp will very likely not have a shipping label. The 2nd trip that uses DHL for instance will have a shipping label that needs to be saved to the order in the back end as well.</td><td class="s0"></td></tr><tr style="height: 212px"><th id="1538481480R11" style="height: 212px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 212px">12</div></th><td class="s2" dir="ltr"><span style="font-weight:bold;">NOTE:</span><br>The current shipping UI interface and functionality need to be cleaned up. There are a bunch of unnecessary information on it. It needs match the existing website UI/UX and be straight to the point. For instance, The current implementation shows item shipping costs many times, has UI elements that take up alot of space for no good reason. Also, this implementation of the aggregated shipping will result in abandoned carts because it will charge shipping cost per item. Rather the system should be implemented such that if it decides to do aggreagated shipping, the system notes what it will cost to aggregated the order item a the main store location and then also calculates the cost for shipping all items down to the customer assuming the will all ship together from a main store to a final destination. Adds both costs together and shows it as one. We would then have setting in the backend to decide if the system will eat up the aggregated shipping costs or pass it onto the customer. This is very important especially for international shipments where additional shipping cost could mean additional duties, taxes, and fees. For domestic shipments, the shipping cost still needs to be calculated as one  cost, but the order items could ship individually or be picked up individually store offers pick ups. This whole syste needs to be dynamic with configurable settings.</td><td class="s0"></td></tr><tr style="height: 20px"><th id="1538481480R12" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">13</div></th><td class="s0"></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1538481480R13" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">14</div></th><td class="s3">Requirements</td><td class="s0"></td></tr><tr style="height: 372px"><th id="1538481480R14" style="height: 372px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 372px">15</div></th><td class="s4">General Shipping Settings:</td><td class="s5"><a target="_blank" href="https://docs.easypost.com/carriers.">- Dimensions unit (L, W,  &amp; H):  Can &quot;cm&quot; for centimeter or &quot;in&quot; for inches<br>- Weight unit: Can be &quot;kg&quot; for kilogram or  &#39;&#39;lb&quot; for pound<br>- Shipping Boxes: This will have an &quot;Add Shipping Box&quot; buttons.<br>    It will be used to create and save custom shipping boxes that will be used by the 3D packing API at checkout. These boxes setting will be Length, Width, Height, and Weight. These boxes have physically locations for where the orders are  shipping from. So for instance, box 1 with dimensions 6 x 8 x 9 inches could be located in our US stores only and box 2 with dimensions 4 x 4 x 4 could be located at our Nigerian warehouse only. Box 3 of dimension 21 x 6 x 8 could be located in Nigeria and China.<br>- Shipping Carriers: Its interface will have: <br>  - &quot;Add Shipping Carrier&quot; button: This is a  button that gives the ability to add shipping carriers like USPS, UPS, DHL Express, DHL E-Commerce, FedEx, etc. <br>  - &quot;Add Shipping Service&quot;:  Each shipping carrier offers multiple shipping services. We need to use this option to add which of the shiping services we want to use in our system. See the shipping services here: https://docs.easypost.com/carriers. Click on individual carriers to see their services<br>  - For each shipping carrier service, we need a slider for &quot;Get live Rates&quot; with values &quot;Yes or no&quot;. This means that the shipping rates or cost will come from API or be set manually by admin.<br>  - Also, there should be checkbox fields to &quot;Allow Domestic Shipping&quot; and  &quot;Allow International Shipping&quot;.<br></a></td></tr><tr style="height: 106px"><th id="1538481480R15" style="height: 106px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 106px">16</div></th><td class="s2"><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Shipping Methods</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: The system shipping methods need to be design to be dynamic such that we can easily integrate new shipping api in the future. In the backend settings, you will find a setting called &quot;Shipping Method&quot;. It was created to allow flexible integration of shipping methods/APIs. You will need to update its implementation with the features below:</span></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Shipping API Name</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: A name field or the shipping API</span></td></tr><tr style="height: 71px"><th id="1538481480R16" style="height: 71px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 71px">17</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">API Type</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: API type can be &quot;Single Carrier&quot; or &quot;Multiple carriers&quot;. </span></td></tr><tr style="height: 73px"><th id="1538481480R17" style="height: 73px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 73px">18</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- 3</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">rd Party?</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: Yes or No. We also need to be able indicate if API is for is 3rd party or not. </span></td></tr><tr style="height: 121px"><th id="1538481480R18" style="height: 121px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 121px">19</div></th><td class="s7">-</td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Live Tracking: </span><span style="font-size:10pt;font-family:Arial;color:#000000;">Yes or No. Most APIs typically offer automatic live tracking for shipments. Some 3rd party API don&#39;t. Some will provide a manual tracking link per shipment. Tracking will be automatically used for shipments when available. I will explain in order section another how Automatic and manual tracking will work.</span></td></tr><tr style="height: 123px"><th id="1538481480R19" style="height: 123px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 123px">20</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Carriers</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: For Single carriers can be DHL, UPS, and Multi-Carriers can be EasyPost, ShipStation, EasyShip. For Multiple carrier APIs like EasyPost that have multiple shipping carriers, we can multi select. For single carrier, single select. The carrier names should come from the carriers in the general shipping settings</span></td></tr><tr style="height: 143px"><th id="1538481480R20" style="height: 143px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 143px">21</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">API keys:</span><span style="font-size:10pt;font-family:Arial;color:#000000;"> Some APIs will have keys and others like 3rd Party may not</span></td></tr><tr style="height: 264px"><th id="1538481480R21" style="height: 264px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 264px">22</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Account numbers (Add Button)</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: Shipping APIs use account numbers. DHL has both import and export account numbers. EasyPost has the option to use their own account numbers for the various shipping carriers like DHL, UPS and Fedex. You can have multple shipping accounts. For instance, You can have an account with DHL USA, DHL Nigeria, etc and each account will come with 2 accounts numbers (import and export). You can use these account  numbers direectly with shipping carrier, or through their logistic partner like EasyPost. 3rd party accounts may or may not have account numbers.<br>Here&#39;s how account numbers works: If a shipment is going from the USA to Nigeria through DHL, it is consider and export from USA to NIgeria or and Import to Nigeria from USA. So, you can either use the DHL export account for the USA or the DHL import account for Nigeria to talk to the API. Another example, let&#39;s imaging a shipment going from USA to Ghana. In this case you can only use the US export account number to talk to the DHL shipping API. Currently, 1GuyGadget only has 2 account with DHL,in the USA and in Nigeria. We are planning more in future<br></span></td></tr><tr style="height: 144px"><th id="1538481480R22" style="height: 144px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 144px">23</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Generate label</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: (Values can be Yes or No, or yes a sliding button, or check box). Some shipping can generate shipping labels and when they can, we want to use them. These label will be generated automaticall at checkout and then saved to the order to be used for shipping the order.  </span></td></tr><tr style="height: 119px"><th id="1538481480R23" style="height: 119px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 119px">24</div></th><td></td><td class="s6"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Label format</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: If label is to be generated, the options can be PDF or PNG. PDF is default<br>- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Request Pickup</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: This will enable 1GuyGadget to schedule parcel pickup by shipping carriers from 1GuyGadget Store locations </span></td></tr><tr style="height: 154px"><th id="1538481480R24" style="height: 154px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 154px">25</div></th><td></td><td class="s2"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Convert dimension unit</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: values could be &quot;to in&quot; for inches, &quot;to cm&quot; for centimeter<br>- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Convert weight unit</span><span style="font-size:10pt;font-family:Arial;color:#000000;">:  values could be &quot;to kg&quot; for kilogram, &#39;&quot;to g&quot; for gram, &quot;to lbs&quot; for pounds, and &quot;oz&quot; for ounces. <br><br>I should add that when your code is evaluating these options at checkout, if the general settings unit for dimension or weight matches these local shipping api sttings values, not need to convert. For instance, no need to convert kg to kg or lbs to lbs, or in to in</span></td></tr><tr style="height: 671px"><th id="1538481480R25" style="height: 671px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 671px">26</div></th><td class="s0"></td><td class="s2"><span style="font-size:10pt;font-family:Arial;color:#000000;">- </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">Add Options</span><span style="font-size:10pt;font-family:Arial;color:#000000;">: This button is used to added options from the shipping API. This will depend on whether the shipping API is for Single Carrier, Multple Carrier, or 3rd Party such as :<br> </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;"> Single Carrier:</span><span style="font-size:10pt;font-family:Arial;color:#000000;"><br>  -  Service Name</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">: </span><span style="font-size:10pt;font-family:Arial;color:#000000;">A name field that will appear at the frontend shipping module. Examples are &quot;1GG Express worldwide&quot;<br>  -  Service Type: Select a service from the list of available services configured for that shipping carrier in the general settings.<br>  -  Rate Option: Based on the service selected, the Rate option is pulled in from general shipping settings. If rate was set to get live rating: yes. Then rate comes from the shipping api. If No, then we need a Rate field. at this leave, we can overide this copy of rate option if desired. Example, if rate option was live rate: yes in general setting. We can set this local copy to live rate: no if desired<br>  -  Rate type: Can be a &quot;flat shipping rate&quot; or &quot;rate per unit weight&quot;. Flat shipping rate will show as is at checkout. Rate per unit weight will be used to multiply the total shipment weight at checkout<br>  -  Rate: This is the manual rate amount enter<br>  -  Rate adder type: A rate adder is a extra amount that we add to  or subtract from the shipping rate. The type can be &quot;fixed&quot; or &quot;percent&quot;<br>  -  Rate adder: The amount fix or percent that will be added to or subtracted from the rate.<br><br> </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;"> Multiple Carrier</span><span style="font-size:10pt;font-family:Arial;color:#000000;">:<br>  -  Service Carrier: Select the carrier for the service that you want configure or set. The list of carriers comes from the prevously multi carriers selected.<br>  - Then we include everything else from single carrier<br><br>  </span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#000000;">If API 3rd Party option is yes</span><span style="font-size:10pt;font-family:Arial;color:#000000;">:<br>  -  Service Name: A name field that will appear at the frontend shipping module. Examples are &quot;1GG Budget&quot;<br>  -  Rate Option: Default is is &quot;No&quot;. Most 3rd party API don&#39;t give live rates<br>  -  Rate type: Can be a &quot;flat shipping rate&quot; or &quot;rate per unit weight&quot;. Flat shipping rate will show as is at checkout. Rate per unit weight will be used to multiply the total shipment weight at checkout<br>  -  Rate: This is the manual rate amount enter<br>  -  Rate adder type: A rate adder is a extra amount that we add to  or subtract from the shipping rate. The type can be &quot;fixed&quot; or &quot;percent&quot;<br>  -  Rate adder: The amount fix or percent that will be added to or subtracted from the rate.</span></td></tr><tr style="height: 20px"><th id="1538481480R26" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">27</div></th><td class="s0"></td><td class="s0">Add any other features you see as necessary. Let&#39;s discuss.</td></tr></tbody></table></div>