<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateProductCategories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('product_categories') && !Schema::hasColumn('product_categories', 'parent_category_id')) {
            Schema::table('product_categories', function (Blueprint $table) {
                $table->unsignedBigInteger('parent_category_id')->nullable()->after('id'); // or place it after a relevant column
                $table->foreign('parent_category_id')
                      ->references('id')
                      ->on('prodcut_categories_parents') // typo? Should it be `product_categories_parents`?
                      ->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('product_categories') && Schema::hasColumn('product_categories', 'parent_category_id')) {
            Schema::table('product_categories', function (Blueprint $table) {
                $table->dropForeign(['parent_category_id']);
                $table->dropColumn('parent_category_id');
            });
        }
    }
}
