<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerPromo extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'promo_id',
    ];

    // Relationship with the User model
    public function customer()
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    // Relationship with the Promo model
    public function promo()
    {
        return $this->belongsTo(Promo::class, 'promo_id');
    }
}
