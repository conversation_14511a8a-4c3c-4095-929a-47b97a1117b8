<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRedeemCashRewardsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('redeem_cash_rewards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->foreignId('approve_user_id')->nullable()->constrained('users');
            $table->integer('points');
            $table->decimal('amount', 20, 2);
            $table->decimal('currency_rate', 20, 2)->nullable();
            $table->string('country')->nullable();
            $table->string('transfer_type');
            $table->longText('transfer_meta');
            $table->integer('transfer_status')->default(1);
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('redeem_cash_rewards');
    }
}
