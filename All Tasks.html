<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{background-color:#ffffff;text-align:left;color:#34a853;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1009409523C0" style="width:260px;" class="column-headers-background">A</th><th id="1009409523C1" style="width:625px;" class="column-headers-background">B</th><th id="1009409523C2" style="width:820px;" class="column-headers-background">C</th><th id="1009409523C3" style="width:474px;" class="column-headers-background">D</th></tr></thead><tbody><tr style="height: 20px"><th id="1009409523R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0"></td><td class="s1">Frontend</td><td class="s1">Backend</td><td class="s2">Notes and Comments</td></tr><tr style="height: 348px"><th id="1009409523R1" style="height: 348px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 348px">2</div></th><td class="s3">User &amp; Address fields, configs and settings</td><td class="s3" dir="ltr">- Ensure that the Create Account and Sign-in fields (including Facebook and Google Sign-in) all work well<br><br>- Guest and user checkout fields (Includes shipping and billing fields) should work perfectly<br><br>- User Dashboard shipping and billing details fields should work perfetly<br><br>- Test the Sign-in and account creation validation process, This includes the new user email validationneeds to be working<br><br>- Check WhatsApp and Twilio Integration. Add WhatsApp contact button (Sign-in should be required to prevent spamming)<br></td><td class="s3" dir="ltr">- Contact, shipping, and billing fields all need to work perfectly<br><br>- Indicate a user phone number that uses WhatsApp. This is a visual aid to help Admins when they need to contact a customer or guest customer. The plan is to use WhatsApp more often and Twilio as back because of WhatsApp is cheaper<br><br>- Complete the User group accounts permissions in the backend. This is for things like who can View, Edit, Delete, etc. for user in the Superadmin, Admin, Customer, groups. It needs to be configurable from the backend.</td><td class="s3">- Emmanuel to Fix the grammar errors in frontend create account flow.</td></tr><tr style="height: 832px"><th id="1009409523R2" style="height: 832px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 832px">3</div></th><td class="s3">Product, SKU/Inventory, ShipTo, Store Locations, Product Settings, and Menu</td><td class="s4">- The product listing page had a nice view from Nov 2024 before the last developers worked on it. It was a staggered product-list view with uneven product cards that loaded more products when the page is scrolled. The last developer implemented a different version of the product list page that had center-aligned feature and even product cards. I want to keep both views and make it selecteble from the backend. This should be applicable to the views that show 2 and 3 columns which I believe are desktop and tablet responsive views..<br><br>- Product discount tags doesn&#39;t always show the discount information when enabled. It used to work well before the previous developers.<br><br>- Show Product SKU videos as well, not just images.<br><br>- You will notice in the backend than there is a setting for whether an SKU is returnable or not. This feature has not been completely integrated in the frontend. First we need a &quot;Non-Returnable&quot; badge or text for non-returnable items. This way the customer is aware before buying. This should show on the &quot;Product details&quot;, &quot;View Cart&quot;, &quot;CheckOut&quot; pages, as well as in the front end Dashboard and the Request for return form. Such a product should not be selectable from a list of SKUs in the request to return form. If it is the only item purchased, the request to return button should not be available.<br><br>- Product list Filtering system needs to work 100%<br><br>- Show the secondary currency for every country selected by the &quot;Ship To&quot; button. This means that the wise.com currency converter API sould be integrated. For Nigeria, we implemented a simple currecy converter by scraping Abokiforex.app. implement a logic tha guarantees wise.com converter as the secondary source when Abokiforex.ap is down or not responding or returning any value.<br><br>- Fix the &quot;Notify Me&quot; logic for out-of-stock inventory. Out-of-stock inventory should not show up in available inventory list. If a condition does not have at least one SKU available, hide the &quot;Availble *conditon* options&quot; tab. That condition button should show &#39;&#39;Sold Out&quot; message as well as the &quot;Notify Me&quot; message.<br><br>- Product Reviews logic and features need to work 100% - see the review section tasks<br><br>- ShipTo should control al country, state and Telphone flag and country code fields. Whenever shipto is set to a particular country, the country field value should be the same as shipto country value. The country&#39;s state values options should be loaded in the dropdown list as well as the telephone country code and country flag is set to match the shipto countrt<br><br></td><td class="s3"><span style="font-size:10pt;font-family:Arial;color:#34a853;">- Create a button configuration that allows for switching between even and uneven product cards view listing. This is for desktop and tablet responsive views<br><br>- Ship To and SKU country/location relationship configuration. Recall that we were trying to come up with a simple but effect algorithm that give admins the least amount of work to do.  i asked chat GPT and it suggested the follow info to the right of this cell =====&gt;&gt;&gt;<br>We already have some of the structure that Chat GPT suggested. We just need to figure out what is the least complicated why to create the system.<br><br><br><br>- Admins are mostly going to interact with the backend from phones and tablets. We need them to be able to create new SKUs and snap and upload picture of them as quick as possible. The current back doesn&#39;t not allow to take/and upload pictures directly. It only allows for uploads.<br><br>- Currently, we can only upload images of SKU. I want to be able to also upload videos and show them in the frontend since we are now hosting images and videos on Amazon S3. The previous developers started the porting to S3 but never finished.<br><br>- From the Product items (which is also SKU) View section, we need to modify the following:<br>   - Remove &quot;Product Status&quot;<br>   - Change the options/values of the &quot;Stock&quot;. &quot;Yes&quot; becomes &quot;Available&quot; and &quot;In Sold&quot; becomes &quot;Sold&quot;<br>   <br>- From the Product items (which is also SKU) summary View section, we need to modify the following:<br>   - Remove the &quot;Product Status&quot; column. This will move up to the &quot;Product&quot; level and changed to &quot;Inventory Status&quot; as explained.<br>   - Remove the &quot;Inventory Status&quot; and &quot;Notify Me&quot; buttons and functions. These  will have to be re-implemented at the &quot;Product&quot; level<br><br>- At the &quot;Product&quot; level, add the following features and function:<br>   - Add a column for &quot;Inventory Status&quot; to the &quot;Products&quot; Summary View table<br>   - Implement the &quot;Inventory Status&quot; and &quot;Notify Me&quot;edit button functions<br>    There are four inventory status and they are as follows (Note that these inventory status are for backend only. They are not supposed to show up in the frontend, but they affect how and if SKUs show up in the frontend):<br>        - I</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#34a853;">n Stock:</span><span style="font-size:10pt;font-family:Arial;color:#34a853;"> means that SKUs are available in our stores for that product<br>        - I</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#34a853;">n Stock OTM:</span><span style="font-size:10pt;font-family:Arial;color:#34a853;"> means that SKUs are not available in our stores for that product but we can get them within an estimated number of days. This is the only status that needs estimated time to arrival. This is the time it will take to arrive at a particular store from its current location. The time of arrival depends of its location. So we need to add  this time to delivery as a feature in &quot;Estimated Delivery Days&quot; setting to take into account the additional time it will take to delivery the item. That is, for products that have &quot;Inventory Status&quot; as &quot;In Stock OTM&quot;, the estimated delivery days for their SKUs  needs to have an additional field let&#39;s call it &quot;Days to Receive OTM Stock.&quot; When this field is set or greathan than zero, its value is added to the min and max estimated SKU shipping dates.<br>        - O</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#34a853;">ut Of Stock:</span><span style="font-size:10pt;font-family:Arial;color:#34a853;">  means that SKUs are not available in our stores for that product or anywhere else anytime soon.<br>        - D</span><span style="font-size:10pt;font-family:Arial;font-weight:bold;color:#34a853;">iscontinued:</span><span style="font-size:10pt;font-family:Arial;color:#34a853;">  means that SKUs  for that product are no longer being made<br>    <br>- We need to add settable feature for &quot;Store Pickup&quot; to existing and new stores. Recall that I said not all stores offer store pickup. If it doesn&#39;t offer store pickup, then walk-ins are not allowed. Customer can only receive their orders by buying and shipping from the e-Commerce website<br><br>- Add a variable that controls the general website currency (ex. USD is the main currency but should be changable if needed).<br><br>- The currency converter API is not always accurate, therefore we need settings to increase or decrease the API conversion values.<br><br><br>-  The Shipping tabs for Product doesn&#39;t have units. This will confuse the Admins and also our system. For dimension such as length, height and width, tthere are two possible units, Centimeters (cm) used by the rest of the world and inches (in) used by US only. For Weight, it is kilogram (kg) used by the rest of the world and (lbs) used by the Us. EasyPost API uses ounces (oz)  for weight. We need a general setings that configure the dimension and weight for shipping. I&#39;m open to how you chose to implemet it. Just explain it to me.</span></td><td class="s4"> Here’s a high-level approach to designing such a system:<br><br>1. Database Design<br>Stores Table: Store ID, Country, Inventory Details.<br>Shipping Rules Table: Rule ID, Source Country, Destination Countries (can be a list or a separate table for many-to-many relationships).<br>Inventory Table: Inventory ID, Store ID, Product Details, Quantity.<br><br>2. Backend Logic<br>Admin Interface: Create an interface for admins to configure shipping rules. This interface should allow:<br>Adding/editing stores and their inventory.<br>Defining shipping rules (e.g., from Ghana Store Location to Nigeria and neighboring countries).<br>Setting global rules (e.g., from the U.S Store Location. to all countries).<br><br>3. Shipping Rules Engine<br>Rule Matching: When an order is placed, the system should:<br>Identify the inventory location.<br>Match the inventory location with the shipping rules.<br>Determine the eligible shipping destinations based on the rules.<br><br>4. Example Workflow<br>Admin Configuration:<br><br>Admin logs in and adds a new store in Ghana.<br>Admin sets a shipping rule: Ghana can ship to Nigeria and neighboring countries.<br>Admin adds inventory to the Ghana store.<br>Order Processing:<br><br>Customer places an order.<br>System checks the inventory location (e.g., Ghana).<br>System applies the shipping rule (Ghana can ship to Nigeria).<br>Order is processed and shipped accordingly.</td></tr><tr style="height: 588px"><th id="1009409523R3" style="height: 588px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 588px">4</div></th><td class="s3">Cart System, Payment APIs, SMS System, Email Templates &amp; Email System, and Promo system</td><td class="s3" dir="ltr"><br><span style="color:#34a853;">- Optimize and fix cart code and cart editing process. Note that Cart content can be modified in the &quot;Product Details&quot;, &quot;View Cart&quot; and &quot;Checkout&quot; Pages. Some of the notable issues include:<br>  - Add To and Remove From cart doesn&#39;t work consistently. Sometimes when you press the add to cart button, it doesn&#39;t work. Understand and solve this issue.<br>  - On the checkout page, the delete SKUs button doesn&#39;t work well. Also, the delete Product group card doesn&#39;t work well.<br>  - The &quot;+&quot; sign cart items at checkout should give the user the option to add SKUs with similar feature to the SKU product group in cart.This needs to work 100%. All this + button is currently slow to load. It needs to be optimized.<br>  - While on the checkout page, if you empty the cart, it redirects to home page. This is wrong. It should redirect to the product lists page.<br><br>- &quot;GET XX% OFF&quot; and &quot;Phone footer&quot; promo &amp; new subscriber systems need to work well. I have made a PPT file that has some UI flow improvement. Please, ask me for it when you get to this point.<br><br>- Need to add Flutterwave payment API.<br><br>- Need to finish checkout  &quot;Order Received&quot; email template CSS Formatting and all other email templates formatting required. <br><br></span>Restore the Payment flow/Section from Nov 2024 and hookup the latest backend functionality to. It is less distracting than the current Payment flow.<br><br>The right panel (that contains contact detail, shippin and payment) needs to be scrollable.<br><span style="color:#34a853;"><br></span>Integrate BNPL in the the system flow. This includes advertising them on the Product list Page and Product details page. Then, integrating their frontend components.</td><td class="s3" dir="ltr"><br><br><span style="color:#34a853;">- The promo system was implemented by last dev. Check that it works well and that it implements all the feature that I have listed in the &quot;Promo System Details&quot; tab.<br></span><br><span style="color:#34a853;">- SMS systems need to be implemented such that WhatsApp messaging is the first choice and the phone number is not on WhatsApp,the system will send SMS via Twilio<br></span><br><span style="color:#34a853;">- All payment APIs need to be configureable in the backend to work for specific countries. This feature has been 80% implemented. You need to make sure it works well.<br></span><br><span style="color:#34a853;">- Email templates are configurable from the backend. The idea we are using is a template header and template footer with custom body. In the Templete header, we are currently displaying the company&#39;s logo and were are also supposed to display the phone number(s) of the country where the user created the account with the email address that we are emailing. On the footer, we are also supposed to display socials, Store address(s)  for the country where the user created the account, If we do not have a store in the user account location or te system did not store a country location for the user (i.e. guest user), default to using the headquarters information. Then, we can create 2 or 3 body templates that admins can reuse each time to keep things simple. I can help with this if you tell me what to do :-).<br><br><br><br></span>E-Commerce websites offer BNPL like Affirm, Afterpay, Klarna etc. These BNPL are country based. Some work in the US and EU, While others works in the US and Canada only. African countries have their own BNPLs.These BNPL options help with marketing and improving sales. However, it comes at a great cost. The business has to give up to 7% profit margin (i..e. these BNPL will take up to 7% of checkout total). we need to somehow pass this burden to the customer without them noticing and then causing them to abandon cart. <br>(1) One way of going about doing this is to extend the SKU pricing scheme. Currently we have a flat SKU price and discount scheme (i.e.one price for every SHIP To location). We could still keep this base price as is, but the add fixed/and or percentage base price variation depending on SHIP TO location. So assuming an SKU base price is $200. If i add a based price variation of 10% for the united states, this means that shoppers in the US with see a base price of $210 will other countries will see $200. At checkout, We will offer the customer an additional option to pay with BNPL for full cost, but a discount of the BNPL transaction costs if they chose to pay with other options like Stripe and other non-BNPL options. This option also gives us the possiblity to add all payment processor transaction costs as upfront hidden costs to the SKU price.<br>(2) the second less prefered way is to flat out charge the customer for transcation fees. This way I thing will lead to alot of abandoned cart.<br>I need to have your thoughts on this<br></td><td class="s0"></td></tr><tr style="height: 324px"><th id="1009409523R4" style="height: 324px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 324px">5</div></th><td class="s3">Core Checkout &amp; payment gateway backend</td><td class="s3" dir="ltr">Guest and User checkout needs to work 100%. No bugs!<br><br>Discount / Promo code should work 100%. Also, Reward points and/or Cash Value should work at checkout.<br><br>At the end of Checkout (i.e.Thank you page), The option to Create account from guest needs to work 100%. We also need to advertise on the thank you page to the user that they don&#39;t get points from their order if they don&#39;t create an account. I.e Guest accounts don&#39;t get points.<br><br>- Currently, the &quot;Works With&quot; Network/Carrier list doesn&#39;t change with &quot;Ship To&quot; change. You will first need to make the network&#39;s attribute in the backend to be country &quot;Ship To&quot; dependent. Then, Make sure the correct networks set for particular countries show up in the frontend. Also, given the size and the amount of work required for this website, admins will not always be able to load network/carrier icons in the backend. Most of the times, they will leave it blank (no Icon, no Text Icon). In this case, we want to show text as icons in the frontend when icon images are not available. I have created UI example for this. Ask me about it.<br><br>Need to finish the implementation of BTC Server. It is currently hosted on a shared spae. We need to host it on our own VPS. Ask me for login in details.</td><td class="s3" dir="ltr">Payment Gateway backend  setup<br><br><br>I&#39;m not sure if the abandoned cart logic and messaging (email and text) is working. You need to check and fix any bugs<br><br><br></td><td class="s0"></td></tr><tr style="height: 538px"><th id="1009409523R5" style="height: 538px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 538px">6</div></th><td class="s3" dir="ltr">Order Management I</td><td class="s5"></td><td class="s3" dir="ltr">The &quot;Create Order&quot; from backend feature needs to work 100% just like the checkout in the frontend. This feature will be used as our POS system in the physical stores. It should be possible to the payment APIs already implemented as well as the shipping system. Adding and removing SKU items should be easy and work well. Also, SKU numbers as well as pictures should be visible and viewable to help the Admins identify which items the customer is interested in. There also needs to be an option for indication whether the order is &quot;online&quot; or &quot;Instore&quot;.                                                                    <br>- For Orders summary:<br>   - The  Shipping info panel must contain the following field:<br>      - Shipping Method (Title): The title of the shipping method<br>      - Shipment ID: This ID can come from API or entered manual by admin<br>      - Shipment Tracking Code/WayBill No: This can come from API or entered manually by Admin<br>      - Est. Delivery Date: This can come from API or entered manually by Admin<br>      - Download Shipment Label: Comes from API if exists or can be uploaded by admin<br>      - Tracking Details<br>      - Shipment status: Comes from API if exists or updated manually by admin<br>      - Tracking Url: Usually will come from API but if API doesn&#39;t have it, this value can be empty<br>      - Shipment  tracking updates / History: Shipping updates can be added by admin.<br>      - Shipping Label (button): To be us to downland and print a saved shipping label if available.<br>Create an order fulfilment interface for non-physical goods such as gift cards. We need the back end interface such that codes for non-physical good can either be manually enter or automatically generated by calling an API endpoint (API to be integrated after we launch the website, for now we will use the manual entrys, but the system needs to be designed to scale easily). The system needs to track when (date and time) the gift card was created, if a gift card has been redeemed or not and when (date and ime) it was redeemed. I would also what to grab the IP address and location of the device that redeemed it (i.e the device that completed the 2FA/MFA). The system also needs to semd an acknowledgement when a gift card has been redeemed to teh customer that purchased the gift card.<br><br>PDF copy of invoice/receipt and custom invoices needs to be generated and attached to all orders with payment confirmed.<br><br>Order Type: Orders can be &quot;Purchased Online&quot; or &quot;Purchased InStore&quot;. When order are checkout out from the website frontend, they are &quot;Purchased Online.&quot; When an order is crreated from the backend, it can be &quot;Purchased Online&quot; or &quot;Purchased InStore&quot; depending on whether the Admin toggled the online or instore button in the &quot;Create Order&quot; interface.<br><br>Complete the Order automated emails</td><td class="s0"></td></tr><tr style="height: 583px"><th id="1009409523R6" style="height: 583px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 583px">7</div></th><td class="s3" dir="ltr">Shippping/Delivery and Taxes</td><td class="s3" dir="ltr">Ensure that the new &quot;&quot;Estimated Delivery Date&quot;&quot; function works like before after you have made changes to the backend to function similar to the &quot;Shipping Rule&quot; <br><br><br><br><br>The 3D packing algorithm should be fully integrated and working at checkout. It should be implemented such taht it doesn&#39;t slow dow the system.<br><br><br><br><br><br><br><br>Shipping/Delivery: Complete the multishipping flow at checkout. See the &quot;Shipping System Details&quot; tab.<br><br>Taxes: There a domestic and international taxes. Domestic taxes needs to be collected for a shipments that within the same state where 1GuyGadget is physically located in the United states. For example, 1guygadget is currently in Texas only. All shipments to customers in Texas needs to pay Texas State taxes. Shipiments from Texas to California or Michigan do not need to pay taxes because 1GuyGadget doesn&#39;t have physical locations in the destination states yet. All shipments going from country to country need to pay international duties and taxes. For international taxes, we will use DHL Duties and Taxes API to calculate them at checkout. Please, refer to the duties and taxes description in the &quot;Shipping System Details.&quot; We will also use the DHL Duties and Taxes API to also obtain the Harmonized (HS) code all items in the cart. This is needed to compute the duties and taxes. Also, discount on duties and taxes but local and international needs to reflect at checkout. You will also need to make sure that whatever UI/UX  you choose will  with existing look and functionality<br><br></td><td class="s3" dir="ltr">The &quot;Estimated Delivery Date&quot; fuction shows the customer an estimated timeframe for delivery of a purchase SKU. It also takes into account that the SKU may not be physically available at 1GuyGagdet Store and may require some time to be shipped there.<br>Currently, the estimated delivery dates setting in the backend is such that there is just a one to one connection that can be made between &quot;SKU location&quot; and &quot;Ship TO&quot;. Implement a functionality similar to that seen in the &quot;Shipping Rules&quot; function for the Estimated Delivery Date so that we can make a one to one, one to many, and well as one to all connection between &quot;SKU&quot; location and &quot;Ship To&quot;<br><br>We need a &quot;Custom Boxes&quot; settings that is country based. For instance, we can have series of box sizes/dimensions and types for the U.S.China could have its own various custom boxes dimensions. These dimensions and other boxes setting are to be set by admins<br><br>Finish the integration of the 3D box packing algorithm. This includes creating a backend config for 1GuyGadget custom boxes.Each store location have custom boxes that we will use for shipping. The sizes of these boxes vary depending on the country location of the stores. Examples, U.S. stores may use different customer boxes compare to the Nigeria or China stores. The 3d box packing algorithm takes as inputs the cart SKU items dimensions and weights, plus the available 1guygadget custom boxes for a particular country where at least 1 1guygadget store exists. It then gives as outputs the overall dimensions and weight (dim weight) and the best custom box that can be used to pack the cart items. The overall dimensions and weight then fed to the shipping APIs (DHL or EasyPost as the case maybe for now, but we will expand in the near future to include the separate FedEx and UPS APIs ).The final box dimension(s) computed at checkout is then sent to the shipping API to obtain shipping rates. This must take into account if we are splitting the order for shipping or aggregating it. Final boxe information are saved to the order to enable admin see at a galnce how to pack the order.<br><br>Admins should have an interface for updating order shipping labels and adding new shipment to an order. The backend shipping interface will be use to check if cheaper shipping rates are available for shipping the order items. It will also come in handy when we need to ship items between stores for order aggregation purposes. Additional costs and are cost savings are saved to the order details and these are separate from the original order cost at checkout and do not afte the original paid order total. These additional cost and/or cost saving are used for tax and accounting purposes.<br><br><br>Full Shipping details: Please, refer to &quot;Shipping/Delivery System Details.&quot;<br><br><br>Taxes: Create backend system/config for both domestic and International taxes. Taxes are calculated for each SKU item in the order and stored. For International duties and taxes, we will use the DHL Duties and Taxes API. I need your suggestion for free Domestic taxes API. If we can&#39;t find one, the most important for now is to collect taxes on each item sold to shipping or delivery address in the state of Texas in the United States.  Just like for payment methods, we needs a configurble discount setting for taxes (international Duties and  Taxes and Domestic taxes)</td><td class="s0"></td></tr><tr style="height: 1143px"><th id="1009409523R7" style="height: 1143px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 1143px">8</div></th><td class="s3" dir="ltr">SKU Configuration ||</td><td class="s3" dir="ltr"></td><td class="s3" dir="ltr"><span style="font-weight:bold;">Flexible SKU pricing interface:<br></span>SKUs have a price field. Admins currently perform a set of computations externally before setting the SKU price. It is desirable to bring this into the system such that Admins can still set the SKU price manually based on what they have calculated or they feed the system inputs and it will give them a suggested SKU price.<br>Such inputs includes: <br>(a) Cost Price of SKU item. <br>(b) Expected gross margin percentage<br>(c) whether or not to account for payment API fees. Which fees of the currently integrated API fees to use for the computation? Keep in mind that we plan to offset additional cost due to payment fes by offering payment API based discount. This is just a psycological game we are playing here. For instance if we added 4% extra cost to SKU to account for merchant fees, we can deduct that 4% at check based on what Payment method user selects.For instance, if the customer choose to pay with crypto currency, we give dem the 4% discount and if they choose Stripe, we don&#39;t.<br>(d) how much shipping cost are we willing to bear? 100%, 50% 0%<br>(e) How much duties and taxes do we want to bear? <br>FYI, we also have a Currency exchange settings in the backend which we plan to use to compensate for some cross border costs like duties and taxes. For instance, we could set the exchange rates higher than market price for Nigeria and then offer Nigeria X% discount in Duties and taxes<br><br>Note This interface will NOT be displayed in the frontend.<br><br>A great deal of SKU inventory will come from 1guygadget.com paid order database. I have a nice summary on that website&#39;s backend where I can see at a glance the Items that we bought.  We need to be able to use this data to automate creating partial SKU enteries on buy.1guygadget.com. So, the system will pull available SKU features (like SKU number, Titles, etc) and field and then use it to create a partial SKU entry on buy.1guygadget.com. This partial entries can then be later modified by admins to become full entry SKUs.<br><br>Inventory physical storage binning scheme: All Inventory with &quot;In Stock&quot; Status will be stored pysically bins. The bins will have a QR code and labels for identifying what they hold. Since a bin can fill up, there should be provision for multiple bin per product. A bin will have sections/mini-bins. These sections will then hold a equal number of SKUs. Let&#39;s say we have a bin for iPhone 16 pro. This bin can hold 9 sections/min-bins, and each bin can hold a finite number iPhone 16 Pro SKU. The bin will have a label for bin number, Product name, and QR-Code. This QR code will store information which min-bin/section holds which SKU. The mini-bin or section will have a number that identifies it. The idea here is that when an order is made, we can check the backend quickly to see how to retrieve the SKUs from the inventory for quick packing and shipping. A shop manager or Admin should be able to scan bins to see if it hold an SKU number that he/she is looking for. Our system should already know which Bin and Mini-bin holds the SKU of interest.<br>For SKU items that we have available on handed in in each bin/mini-bin, we need to be able to store them with labels attached to their storage bags or sleeves. This label will also have a QR code that store the SKU feature. I have an example that I implemented for the SKU on 1guygadgadet.com but it doesn&#39;t use QR code system<br><br>There will be cases where certain SKUs are only supposed to ship Domestically or to certain countries only because the selling price of the SKU is much lower than the shipping cost. For instance, and SKU that costs $20 and located in the U.S. (for instance an iPhone charger) does not make sense to ship to Nigeria because the shipping cost is $40+ plus duties and taxes, But, it could ship to Canada and/or Mexico. We need to give admin a way to configure SKUs in the SKU settings. You could have a button that is &quot;Allow international shipping?&quot; with values &quot;Yes/No&quot; and then when the value is &quot;Yes&quot;, there will be another field with dropdrop down values of  &quot;All Countries&quot; or &quot;Select Countries&quot;. When &quot;Select Countries&quot; is chosen, Admin can then multi-select which countries the SKU can Ship TO. This way we can have the defaults as Allow International Shipping&quot; : Yes , &quot;Ship To Countries&quot;: All Countries. Then, Admin can change the setting as required when needed. This still alignes with the global SKU location and SHIP to Countries relationship implemented. It is just an extra refinement.<br><br>With the Ship TO and SKU location relationship configuration defined above, an SKU will show in the frontend to a user shopping from a location if the above relationship allows it. In addition, a button or switch located in the Add/Edit SKU (product items) settings is required to hide or show SKUs located in a particular location from being shown to users in that location. For example, if an SKU is located in the United States and this hide button is active, that SKU will not show to users with Ship TO location set in the United States.<br><br>- An SKU&#39;s Network/Carrier attribute needs to have a country feature. This will allow us to display only phone networks/carriers that are operational in a customer&#39;s country &quot;Ship To&quot; location to the customer. For instance, AT&amp;T and Verizon U.S. network carriers. They are not available in Ghana or Nigeria. So, showing a customer living in Ghana that an iPhone &quot;Works with AT&amp;T and T-mobile&quot; makes no sense. The customer needs to see that the iPhone he will be buying will work with networks in his country. To do this, you could use the an API that lists countries and carriers if you find one.  In that case, you will add some extra  code for creating &quot;Network&quot; attributes using API such that we will have a country field for selecting a country that applies to a set of networks/carriers. The process should be:<br>    - Admin tries to Edit/Add Attribute Items<br>    - If TYPE:&quot;Carrier&quot; is selected, a list of countries appears. If unslected, list of country goes away.<br>    - After selecting a country, an Admin willl need to click on a &quot;Get Networks/Carriers&quot; Button. <br>    - When clicked, this button will make a call to the API and get the Network names for the selected country. It will then upload these automatically as the network options to the database. This will save admin time of using the &quot;Add Option&quot; button to load carrier/network icon and text icon every entry manually, they can still use this button if they want to.<br>    - After this, the admin can finish creating the network attribute. <br> The idea here is to only call the API when we need it and then save the data locally. So that if the API is not available, we can still create network attributes manually (that the way the code was originally written).<br>If there is no such API for free, then all you need do is to only add the country option as stated above and the admins will take care of adding option for networks. <br></td><td class="s0"></td></tr><tr style="height: 544px"><th id="1009409523R8" style="height: 544px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 544px">9</div></th><td class="s3" dir="ltr">Rewards System</td><td class="s3" dir="ltr">The rewards accrual and redemption system can accessed in the Dashboard after login. A user accrues point for their orders and they can decide to convert the points to cash value. Both point and cash value can also be redeemed at checkout. A user can also decide to cashout their cashvalue. The cash out process/options are location dependent. Ensure that the process of redemption works well from the frontend. Also, The UI for the redemption request interface needs to to be improved.</td><td class="s3" dir="ltr">- Review points config/setting should be country-based. Review points systems need to work 100%. Review points differ for Text only, Text+ Image, and Text+ Video and/or Image. Note that only user who are logged in can leave reviews. This is to reduce spam.<br><br><br>We have a backend tab for Rewards. - This simple Rewards Redemption needs to be country-based. Also, the redemption methods/options are country-dependent. For instance, Amazon gifts cards and CashApp payments maybe an option for the United states but they may not work for China, whereas Alipay Will work for China.These location-based rewards redemption options should be configurable from the backend. These configurable backend location-based fields will enable us collect the customer info, and show them a options for redeeming their cash value. For instance, such interface for Nigeria could show redemption options like Bank Transfer, Mobile Money Wallet, Cryptocurrency. Each othese option will require the client to input different kinds of information. Bank transfer requires a Bank name, the account name and number. Could also require a routing number. Redemption through Zelle requires a Zelle phone number and/or Email. Redemption via Gift card would require just an email address. Essentially, we need need to collect information for how the customer wants to receive their cash value and then an Admin will ensure that process is completed.<br><br>We will need to either create a seperate tab for Referrals or find a way to add it to Rewards. The referral program is way that existing users can refer new customers to us using their uniquely assign links. They will earn reward points each time an new customer uses their links. This is a common feature for e-Commerce websites. <br>Here are some things you need to know about our point system:<br>  - Points can be redeemed at check out<br>  - Points can also be converted to cash value. This cash value can also be used at checkout or can be redeemed as a gift from customer account page in backend. Depending on the country where the customer lives, we can sett how they will cash out their money. For instance, in Nigeria, it can be via banks. For the US, it can be via CashApp, Zelle,etc, For Vietnam, it can be via a gift card. These should be configurable from the bank to may to make it dynamic and future-proof<br>   - Need to track and show total of Cash (USD which is main currency) each customer has redeemed (either via checkout or Cash)<br>   - Admin can award points<br>   - Rewards Status<br>      - Pending:  Default state when use clicks the &quot;Redeem Rewards&quot; button<br>      - Processing: Used to indicate When Admin is working on the redeem rewards request <br>      - Completed: When the rewards has been sent out or paid by Admin<br>      - Completed (Checkout):  When rewards is used at checkout used at Checkout<br><br>User Referral links point system and accrual display system</td><td class="s0"></td></tr><tr style="height: 375px"><th id="1009409523R9" style="height: 375px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 375px">10</div></th><td class="s3" dir="ltr">Order Management II, Product pairing</td><td class="s3">Add &quot;&quot;Pairs well with&quot;&quot; section to product details and Checkout pages. This section shows product list items of products that are usually bought together with the item that the user is currently looking at or items that are in the cart. You will find the HTML of this commented out in the code. Ask me if you don&#39;t find it.</td><td class="s3" dir="ltr">For Orders summary: - The Shipping info panel must contain the following field: - Shipping Method (Title): The title of the shipping method - Shipment ID: This ID can come from API or entered manual by admin - Shipment Tracking Code/WayBill No: This can come from API or entered manually by Admin - Est. Delivery Date: This can come from API or entered manually by Admin - Download Shipment Label: Comes from API if exists or can be uploaded by admin - Tracking Details - Shipment status: Comes from API if exists or updated manually by admin - Tracking Url: Usually will come from API but if API doesn&#39;t have it, this value can be empty - Shipment tracking updates / History: Shipping updates can be added by admin. - Shipping Label (button): To be us to downland and print a saved shipping label if available.<br><br>Order Shipment Tracking: All orders need to have shipment tracking details. As you will see in the &quot;Shipping System Details&quot; tab, we will have 3rd party and non-3rd party shipping carriers &amp; options. 3rd party shipping carrier will not not always have tracking details. For this case, there needs to be a small interface for Admins to manually enter tracking information. This can be done for a single order or a batch of orders. Admins need to be able to manually add tracking info to an order or a batch of orders at once. When tracking info is available automatically, it will usually be available via a webhook or URL. Sometimes, the contents of the URL may need to be post-proceesed to obtain tracking details only. This is true especially for 3rd party APIs that normally include too much info in the tracking link like the cost of shipment. We just need tracking info, so we will post process the link.<br><br>For the &quot;Pair Well with&quot; section, it may be best to allow the Admins to determine in the backend which products pair well with each other. This way the frontend code to show the products isn&#39;t complex. For instance, for an iPhone, the Admin can set that it pairs well with AirPods, AirPods Max, Phone Case, Chargers. This way, the front end code can then randomize these based on brands and series or other features.<br><br>Finish all cron Job set up tasks required by the system to function correctly.<br></td><td class="s0"></td></tr><tr style="height: 387px"><th id="1009409523R10" style="height: 387px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 387px">11</div></th><td class="s3" dir="ltr">Returns and Refund</td><td class="s3" dir="ltr">Make sure a refund status of an item/SKU or the order shows in the orders section of frontend dashboard. If a refund is denied, we need to display to a notification to the customer that we have sent an email to them on why their refund was denied. The notification could look like this: &quot;&quot;We have sent an <NAME_EMAIL> the reason why the refund request was denied&quot;&quot;<br><br>After a refund is approved and the RMA process is complete (i.e customer has shipped back the item that needed to be retuned), we need to give the customer options to choose from on how their refund should be proceeded. These option should appear in the Dashboard for the SKU(s)with approved refund. The options are Receive Cash Refund (x% Restocking Fee will be deducted) or Recieve 1GuyGadget Cash value for full refund amount. The x% restocking fee depends on how much processing fees we were charged for the transaction.<br></td><td class="s3" dir="ltr">Order refunds: Currently, we have only developed a system that collects the request to return or refund from from the customer, but we have not completed the refund system. The existing system needs to be checked to make sure it works. A complete refund system should: - Should show item refund status for the order section as well. - A button to refund the separate items in an order or the entire order. - When the refund button is clicked, the admin should be able to select which items to refund. If an item in non returnable, then it should not be refundable. - Taxes are calculated per item in the order. If the order is international, and the order has already shipped, then Custom duties and taxes are not refundable. If not yet shipped, it can be refunded. For domestic taxes, United States only, taxes are always refundeable even after it has shipped. - In general, If items have shipped, then shipping in not refundable. If the items have not shipped, shipping can be refunded. if some items in the order are approved for refund, and order hasn&#39;t shipped, then the order needs to be recalculated with non-refunded item(s), new shipping option needs to be added to the order (because total weight and dimension of shipping box has changed) and if the new shipping rate is less, then we can refund the rest of the shipping, if more, we need to deduct the extra from the total refud amount. - Our system should connect to the payment APIs refund system and request it to refund the correct amount to the customer. - Show/save the refunded amount in the Order and the breakdown of the refund amount. For instance, show how much was refunded for shipping, taxes, etc<br><br>Generate return shipping labels: Used to create return shipping labels from shipping APIs for individual SKU items in an order or for the whole order. Note that 3rd party shipping APIs may not have this feature<br><br>Refunds/Returns are expensive to the business, therefore we need to charge a Restocking fee in the case a refund is approved, or offer the customer the option of receiving their refund as 1GuyGadget Cash Value to save the restocking fee.If Returns /Refunds get approved by admin, we need to notify the customer that refund has been approved and present them a call to action with received the full/or partial refund amount with Restocking Fee or Get the 1GuyGadget Cash Value</td><td class="s0"></td></tr><tr style="height: 179px"><th id="1009409523R11" style="height: 179px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 179px">12</div></th><td class="s3" dir="ltr">Sell/Trade-in System</td><td class="s3" dir="ltr">1GuyGadget used 1guygadget.com to buy the devices that it will sell on the e-Commerce website that you are working on. The Sell | Tradin Button is supposed to send a user to this trade-in/buyback website. The issue here is that the structure of the buyback website only makes it possible to buy devices in the United states. However, we will like to start a buyback program for the countries where we have physical store locations. This can be automated on the website to display as a form fully configurable from the backend. This form will be used to collect information about the device(s) a seller is wants to sell. The difference between this form-method and the fully automated U.S. method is that form method doesn&#39;t make instant offers. It only collects information about the device and the seller. Then an Admin will reach out to the user to complete the transction. This form for example should have name, date, number, upload picture and video fields. Ask me questions when you get here</td><td class="s3" dir="ltr">A fully configurable Sell | Trade in system that shows up in the front. <br></td><td class="s0"></td></tr><tr style="height: 443px"><th id="1009409523R12" style="height: 443px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 443px">13</div></th><td class="s5" dir="ltr">Misc.</td><td class="s3" dir="ltr">&quot;Recommended for you&quot; section. This code currently implemented to randomized the displayed product. Not sure if this is the best way to do it. I&#39;m open to your suggestions. Its UI and functionality needs to be checked and fixed.<br><br>The landing page texts are currently static. I need this to like a slideshow with texts fully editable from the backend with different types of animations settings for exit and intro. The idea here is that I would love to change the text often and make it a little more fun and interesting. The images portion of the landing page is currently a slide show. Looks to be working fine, but it has just has one kind of intro and outro which is the slide.<br><br>There are two status bars above and below the menu whose HTML are currently hidden. These bars can both be programmed to be visible at the same time or individually, and this should fully configurable from the backend. I plan to use it to show plain text, animated texts, emojis, images, gifs. An example of what I plan to display on these status bars is &quot;*Fire emoji* Free shipping on all orders $350&quot;<br><br>Currently,&quot;Success&quot; and &quot;Failure&quot; status messges show up under the menu as a either green or red bars with text. Let&#39;s make this  and all other kinds of alerts configurable from the backend just like the status bars described above<br></td><td class="s3" dir="ltr">A mini accounting interface: This interface will help show the Revenue health of the business (To be viewed by a Super Admin only). We should be able to use this interface to see the Total Sales generated for a given period of time, the total taxes collected by country (domestic or international), our total spending on shipping, on transaction fees, a calculation of gross margins for a particular period, etc.<br><br>A backend dashboard that gives some vital summary information. We can Number of Pending orders, Number of orders shipped, Number of Pending returns, Total Sales $ a day, and many more. I think we can ask ChatGPT to suggest which features we can show here<br><br>Make the landing page elements (Text and images) fully configurable from the back end<br><br>Make the status bars fully configurable from the backend. The backend settings control when  to show or hide them, animates the text contents, shows emojis, images, gifs etc on the status bars. This also inclides all status &quot;Success&quot; and &quot;Failure&quot; alerts and other kinds of alerts<br><br>All Website&#39;s footer elements should work and be configurable from the backend. There are some that we need to remove. Ask me questions when we get to this point<br><br>A configurable Blog. I really don&#39;t have ideas for this one other than Blog features and posts needs to be country dependent. I will take any ideas or templates that you have. Here&#39;s a very good example: https://www.growmilkweedplants.com/blog<br><br>Country-Based settings for Terms of service and Privacy Policy fully configurable from back end. We will have a default terms and condition and privacy policy, and then any country-based terms &amp; privacy policy set will overwrite the default for the country in question.&quot;<br><br>- I would love to have an email interface like an inbox system were we can have a collection of emails from customers and guests. Admins can then use this interface to write a new email and reply to emails received. Without a system like this, we can currently only send emails, but when the email is replied to, we will need to go check our emails through an email client (like webmail) to read and reply to such email.We want to be able to do this from the back end. Keep in mind that emails can have attachements like images and videos</td><td class="s0"></td></tr><tr style="height: 74px"><th id="1009409523R13" style="height: 74px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 74px">14</div></th><td class="s1">Frontend/Backend Final review</td><td class="s3">- Make sure responsive views work well (PC/Laptop/Tablet/Mobile)<br>- Fix any little remaining issues with the frontend views (may need to resize icons, fix image widths, etc)</td><td class="s3">- We will to make the backend as responsive as possible to enable ease of use on Tablets and Mobile phones</td><td class="s0"></td></tr></tbody></table></div>