<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDaysToReceiveOtmStockToEstimatedDeliveryDatesTable extends Migration
{
    public function up(): void
    {
        Schema::table('estimated_delivery_dates', function (Blueprint $table) {
            $table->integer('days_to_receive_otm_stock')->nullable()->after('max_duration'); // replace 'some_existing_column' with the actual column after which you want to add this
        });
    }

    public function down(): void
    {
        Schema::table('estimated_delivery_dates', function (Blueprint $table) {
            $table->dropColumn('days_to_receive_otm_stock');
        });
    }
}
