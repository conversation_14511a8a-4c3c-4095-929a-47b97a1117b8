<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddGuestCheckoutToPromosTable extends Migration
{
    public function up()
    {
        Schema::table('promos', function (Blueprint $table) {
            if (!Schema::hasColumn('promos', 'guest_checkout')) {
                $table->boolean('guest_checkout')->default(0);
            }
        });
    }

    public function down()
    {
        Schema::table('promos', function (Blueprint $table) {
            if (Schema::hasColumn('promos', 'guest_checkout')) {
                $table->dropColumn('guest_checkout');
            }
        });
    }
}
