<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShippingMethodToUserShippingsTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('user_shippings') && !Schema::hasColumn('user_shippings', 'shipping_method')) {
            Schema::table('user_shippings', function (Blueprint $table) {
                $table->string('shipping_method')->nullable()->after('address_2');
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('user_shippings') && Schema::hasColumn('user_shippings', 'shipping_method')) {
            Schema::table('user_shippings', function (Blueprint $table) {
                $table->dropColumn('shipping_method');
            });
        }
    }
}
