<?php

namespace App\Listeners;

use App\Events\CouponUsed;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendCouponUsedNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(CouponUsed $event)
    {
        $this->notifier->sendCouponUsedNotification($event->user, $event->coupon);
    }
}
