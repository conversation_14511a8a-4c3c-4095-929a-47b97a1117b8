<?php namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GeneralSetting extends Model
{
    protected $table = 'general_settings';

    protected $fillable = ['key', 'value', 'type', 'description'];

    public $timestamps = true;

    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }

    public static function set($key, $value, $type = 'general', $description = null)
    {
        return static::updateOrCreate(
            ['key' => $key],
            ['value' => $value, 'type' => $type, 'description' => $description]
        );
    }
    public static function getWeightUnits()
    {
        return [
            'kg' => 'Kilogram (kg)',
            'lb' => 'Pound (lb)',
            'oz' => 'Ounce (oz)',
        ];
    }

    public static function getDimensionUnits()
    {
        return [
            'cm' => 'Centimeter (cm)',
            'in' => 'Inch (in)',
        ];
    }

    public static function getUnitLabel($type, $unit)
    {
        $map = [
            'weight' => self::getWeightUnits(),
            'dimension' => self::getDimensionUnits(),
        ];

        return $map[$type][$unit] ?? $unit;
    }
    public static function general_setting($key, $default = null)
    {
        // You can cache this for better performance
        $setting = GeneralSetting::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }
}
