<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReviewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->foreignId('approve_user_id')->nullable()->constrained('users');
            $table->foreignId('product_id')->constrained('products');
            $table->foreignId('product_item_id')->constrained('product_items');
            $table->text('product_sku_s')->nullable();
            $table->foreignId('parent_review_id')->nullable()->constrained('reviews');
            $table->longText("message")->nullable();
            $table->integer('rating')->default(0);
            $table->longText('review_images')->nullable();
            $table->ipAddress('ip')->nullable();
            $table->boolean("status")->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('reviews');
    }
}
