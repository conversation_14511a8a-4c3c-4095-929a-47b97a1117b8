4mCWEBP24m(1)                    General Commands Manual                   4mCWEBP24m(1)

1mNAME0m
       cwebp - compress an image file to a WebP file

1mSYNOPSIS0m
       1mcwebp 22m[4moptions24m] 4minput_file24m 4m-o24m 4moutput_file.webp0m

1mDESCRIPTION0m
       This manual page documents the 1mcwebp 22mcommand.

       1mcwebp  22mcompresses  an image using the WebP format.  Input format can be
       either PNG, JPEG, TIFF, WebP or raw Y'CbCr samples.  Note: Animated PNG
       and WebP files are not supported.

1mOPTIONS0m
       The basic options are:

       1m-o 4m22mstring0m
              Specify the name of the output WebP file. If omitted, 1mcwebp 22mwill
              perform compression but only report statistics.   Using  "-"  as
              output name will direct output to 'stdout'.

       1m-- 4m22mstring0m
              Explicitly  specify the input file. This option is useful if the
              input file starts with a '-' for instance. This option must  ap-
              pear 1mlast22m.  Any other options afterward will be ignored.

       1m-h, -help0m
              A short usage summary.

       1m-H, -longhelp0m
              A summary of all the possible options.

       1m-version0m
              Print the version number (as major.minor.revision) and exit.

       1m-lossless0m
              Encode  the image without any loss. For images with fully trans-
              parent area, the invisible pixel values (R/G/B or Y/U/V) will be
              preserved only if the -exact option is used.

       1m-near_lossless 4m22mint0m
              Specify the level of near-lossless image preprocessing. This op-
              tion adjusts pixel values to help compressibility, but has mini-
              mal impact on the visual quality. It triggers lossless  compres-
              sion  mode automatically. The range is 0 (maximum preprocessing)
              to 100 (no preprocessing, the default).  The  typical  value  is
              around 60. Note that lossy with 1m-q 100 22mcan at times yield better
              results.

       1m-q 4m22mfloat0m
              Specify  the  compression  factor for RGB channels between 0 and
              100. The default is 75.
              In case of lossy compression (default), a small factor  produces
              a  smaller  file with lower quality. Best quality is achieved by
              using a value of 100.
              In case of lossless compression (specified by the 1m-lossless  22mop-
              tion), a small factor enables faster compression speed, but pro-
              duces a larger file.  Maximum compression is achieved by using a
              value of 100.

       1m-z 4m22mint24m Switch on 1mlossless 22mcompression mode with the specified level be-
              tween 0 and 9, with level 0 being the fastest, 9 being the slow-
              est.  Fast  mode  produces  larger file size than slower ones. A
              good default is 1m-z 622m.  This option is actually  a  shortcut  for
              some  predefined  settings for quality and method. If options 1m-q0m
              or 1m-m 22mare subsequently used, they will invalidate the effect  of
              this option.

       1m-alpha_q 4m22mint0m
              Specify  the  compression factor for alpha compression between 0
              and 100.  Lossless compression of  alpha  is  achieved  using  a
              value  of 100, while the lower values result in a lossy compres-
              sion. The default is 100.

       1m-preset 4m22mstring0m
              Specify a set of pre-defined parameters  to  suit  a  particular
              type  of  source material. Possible values are:  1mdefault22m, 1mphoto22m,
              1mpicture22m, 1mdrawing22m, 1micon22m, 1mtext22m. Since 1m-preset 22moverwrites the other
              parameters' values (except  the  1m-q  22mone),  this  option  should
              preferably appear first in the order of the arguments.

       1m-m 4m22mint24m Specify  the  compression method to use. This parameter controls
              the trade off between encoding speed  and  the  compressed  file
              size  and  quality.   Possible values range from 0 to 6. Default
              value is 4.  When higher values are used, the encoder will spend
              more time inspecting additional encoding possibilities  and  de-
              cide on the quality gain.  Lower value can result in faster pro-
              cessing  time  at the expense of larger file size and lower com-
              pression quality.

       1m-crop 4m22mx_position24m 4my_position24m 4mwidth24m 4mheight0m
              Crop the source to a rectangle with top-left corner  at  coordi-
              nates  (1mx_position22m,  1my_position22m)  and size 1mwidth 22mx 1mheight22m.  This
              cropping area must be fully contained within the source  rectan-
              gle.  Note: the cropping is applied 4mbefore24m any scaling.

       1m-resize 4m22mwidth24m 4mheight0m
              Resize  the  source to a rectangle with size 1mwidth 22mx 1mheight22m.  If
              either (but not both) of the 1mwidth 22mor 1mheight  22mparameters  is  0,
              the  value will be calculated preserving the aspect-ratio. Note:
              scaling is applied 4mafter24m cropping.

       1m-mt    22mUse multi-threading for encoding, if possible.

       1m-low_memory0m
              Reduce memory usage of lossy encoding by saving four  times  the
              compressed  size (typically). This will make the encoding slower
              and the output slightly different in size and  distortion.  This
              flag  is  only effective for methods 3 and up, and is off by de-
              fault. Note that leaving this flag off will have some  side  ef-
              fects  on  the  bitstream:  it forces certain bitstream features
              like number of partitions (forced to 1). Note that  a  more  de-
              tailed  report  of bitstream size is printed by 1mcwebp 22mwhen using
              this option.


   1mLOSSY OPTIONS0m
       These options are only effective when doing  lossy  encoding  (the  de-
       fault, with or without alpha).


       1m-size 4m22mint0m
              Specify  a  target size (in bytes) to try and reach for the com-
              pressed output.  The compressor will make several passes of par-
              tial encoding in order to get as close as possible to this  tar-
              get. If both 1m-size 22mand 1m-psnr 22mare used, 1m-size 22mvalue will prevail.

       1m-psnr 4m22mfloat0m
              Specify  a  target  PSNR  (in  dB) to try and reach for the com-
              pressed output.  The compressor will make several passes of par-
              tial encoding in order to get as close as possible to this  tar-
              get. If both 1m-size 22mand 1m-psnr 22mare used, 1m-size 22mvalue will prevail.

       1m-pass 4m22mint0m
              Set  a maximum number of passes to use during the dichotomy used
              by options 1m-size 22mor 1m-psnr22m. Maximum value is 10,  default  is  1.
              If options 1m-size 22mor 1m-psnr 22mwere used, but 1m-pass 22mwasn't specified,
              a  default  value of '6' passes will be used. If 1m-pass 22mis speci-
              fied, but neither 1m-size 22mnor 1m-psnr 22mare, a  target  PSNR  of  40dB
              will be used.

       1m-qrange 4m22mint24m 4mint0m
              Specifies  the permissible interval for the quality factor. This
              is particularly useful when using multi-pass (1m-size 22mor 1m-psnr 22mop-
              tions).  Default is 0 100.  If the  quality  factor  is  outside
              this  range,  it  will be clamped.  If the minimum value must be
              less or equal to the maximum one.

       1m-af    22mTurns auto-filter on. This algorithm will spend additional  time
              optimizing the filtering strength to reach a well-balanced qual-
              ity.

       1m-jpeg_like0m
              Change  the  internal  parameter mapping to better match the ex-
              pected size of JPEG compression. This flag will  generally  pro-
              duce  an output file of similar size to its JPEG equivalent (for
              the same 1m-q 22msetting), but with less visual distortion.


       Advanced options:


       1m-f 4m22mint24m Specify the strength of the deblocking  filter,  between  0  (no
              filtering)  and  100 (maximum filtering). A value of 0 will turn
              off any filtering.  Higher value will increase the  strength  of
              the  filtering  process  applied after decoding the picture. The
              higher the value the smoother the picture will  appear.  Typical
              values are usually in the range of 20 to 50.

       1m-sharpness 4m22mint0m
              Specify  the  sharpness  of the filtering (if used).  Range is 0
              (sharpest) to 7 (least sharp). Default is 0.

       1m-strong0m
              Use strong filtering (if filtering is being used thanks  to  the
              1m-f 22moption). Strong filtering is on by default.

       1m-nostrong0m
              Disable  strong  filtering (if filtering is being used thanks to
              the 1m-f 22moption) and use simple filtering instead.

       1m-sharp_yuv0m
              Use more accurate and sharper  RGB->YUV  conversion  if  needed.
              Note  that  this  process  is  slower  than  the  default 'fast'
              RGB->YUV conversion.

       1m-sns 4m22mint0m
              Specify the amplitude of  the  spatial  noise  shaping.  Spatial
              noise  shaping (or 1msns 22mfor short) refers to a general collection
              of built-in algorithms used to decide which area of the  picture
              should use relatively less bits, and where else to better trans-
              fer  these  bits.  The  possible range goes from 0 (algorithm is
              off) to 100 (the maximal effect). The default value is 50.

       1m-segments 4m22mint0m
              Change the number of partitions to use during  the  segmentation
              of  the  sns  algorithm. Segments should be in range 1 to 4. De-
              fault value is 4.  This option has no effect for methods  3  and
              up, unless 1m-low_memory 22mis used.

       1m-partition_limit 4m22mint0m
              Degrade quality by limiting the number of bits used by some mac-
              roblocks.  Range is 0 (no degradation, the default) to 100 (full
              degradation).  Useful values are usually around 30-70 for moder-
              ately  large  images.   In the VP8 format, the so-called control
              partition has a limit of 512k and is used to store the following
              information: whether the macroblock is skipped, which segment it
              belongs to, whether it is coded as  intra  4x4  or  intra  16x16
              mode,  and  finally  the prediction modes to use for each of the
              sub-blocks.  For a very large image, 512k only leaves room for a
              few bits per 16x16 macroblock.  The absolute minimum is  4  bits
              per  macroblock.  Skip, segment, and mode information can use up
              almost all these 4 bits (although the case is  unlikely),  which
              is problematic for very large images. The partition_limit factor
              controls  how  frequently  the  most bit-costly mode (intra 4x4)
              will be used. This is useful in case the 512k limit  is  reached
              and  the  following  message is displayed: 4mError24m 4mcode:24m 4m624m 4m(PARTI-0m
              4mTION0_OVERFLOW:24m 4mPartition24m 4m#024m 4mis24m 4mtoo24m 4mbig24m 4mto24m 4mfit24m 4m512k)24m.  If  using
              1m-partition_limit  22mis not enough to meet the 512k constraint, one
              should use less segments in order to save more header  bits  per
              macroblock.   See  the  1m-segments 22moption. Note the 1m-m 22mand 1m-q 22mop-
              tions also influence the encoder's decisions and ability to  hit
              this limit.


   1mLOGGING OPTIONS0m
       These options control the level of output:

       1m-v     22mPrint extra information (encoding time in particular).

       1m-print_psnr0m
              Compute and report average PSNR (Peak-Signal-To-Noise ratio).

       1m-print_ssim0m
              Compute  and  report average SSIM (structural similarity metric,
              see https://en.wikipedia.org/wiki/SSIM for additional details).

       1m-print_lsim0m
              Compute and report local similarity metric (sum of lowest  error
              amongst the collocated pixel neighbors).

       1m-progress0m
              Report encoding progress in percent.

       1m-quiet 22mDo not print anything.

       1m-short 22mOnly  print  brief  information  (output file size and PSNR) for
              testing purposes.

       1m-map 4m22mint0m
              Output additional ASCII-map of  encoding  information.  Possible
              map  values range from 1 to 6. This is only meant to help debug-
              ging.


   1mADDITIONAL OPTIONS0m
       More advanced options are:

       1m-s 4m22mwidth24m 4mheight0m
              Specify that the input file actually consists of raw Y'CbCr sam-
              ples following the ITU-R BT.601 recommendation, in 4:2:0  linear
              format.  The luma plane has size 1mwidth 22mx 1mheight22m.

       1m-pre 4m22mint0m
              Specify  some  preprocessing  steps.  Using  a value of '2' will
              trigger   quality-dependent   pseudo-random   dithering   during
              RGBA->YUVA conversion (lossy compression only).

       1m-alpha_filter 4m22mstring0m
              Specify the predictive filtering method for the alpha plane. One
              of  'none', 'fast' or 'best', in increasing complexity and slow-
              ness order. Default is 'fast'. Internally,  alpha  filtering  is
              performed  using  four  possible  predictions (none, horizontal,
              vertical, gradient). The 'best' mode will try each mode in  turn
              and  pick  the one which gives the smaller size. The 'fast' mode
              will just try to form an a  priori  guess  without  testing  all
              modes.

       1m-alpha_method 4m22mint0m
              Specify  the algorithm used for alpha compression: 0 or 1. Algo-
              rithm 0 denotes no compression, 1 uses WebP lossless format  for
              compression. The default is 1.

       1m-exact 22mPreserve  RGB values in transparent area. The default is off, to
              help compressibility.

       1m-blend_alpha 4m22mint0m
              This option blends the  alpha  channel  (if  present)  with  the
              source  using  the  background color specified in hexadecimal as
              0xrrggbb. The alpha channel is afterward  reset  to  the  opaque
              value 255.

       1m-noalpha0m
              Using this option will discard the alpha channel.

       1m-hint 4m22mstring0m
              Specify  the  hint  about input image type. Possible values are:
              1mphoto22m, 1mpicture 22mor 1mgraph22m.

       1m-metadata 4m22mstring0m
              A comma separated list of metadata to copy from the input to the
              output if present.  Valid values: 1mall22m,  1mnone22m,  1mexif22m,  1micc22m,  1mxmp22m.
              The default is 1mnone22m.

              Note: each input format may not support all combinations.

       1m-noasm 22mDisable all assembly optimizations.


1mBUGS0m
       Please     report     all     bugs     to     the     issue    tracker:
       https://bugs.chromium.org/p/webp
       Patches welcome! See this page  to  get  started:  https://www.webmpro-
       ject.org/code/contribute/submitting-patches/


1mEXAMPLES0m
       cwebp -q 50 -lossless picture.png -o picture_lossless.webp
       cwebp -q 70 picture_with_alpha.png -o picture_with_alpha.webp
       cwebp -sns 70 -f 50 -size 60000 picture.png -o picture.webp
       cwebp -o picture.webp -- ---picture.png


1mAUTHORS0m
       1mcwebp 22mis a part of libwebp and was written by the WebP team.
       The   latest  source  tree  is  available  at  https://chromium.google-
       source.com/webm/libwebp

       This  manual  page  was  written  by  Pascal   Massimino   <pascal.mas-
       <EMAIL>>, for the Debian project (and may be used by others).


1mSEE ALSO0m
       1mdwebp22m(1), 1mgif2webp22m(1)
       Please  refer  to  https://developers.google.com/speed/webp/  for addi-
       tional information.

                                March 26, 2024                        4mCWEBP24m(1)
