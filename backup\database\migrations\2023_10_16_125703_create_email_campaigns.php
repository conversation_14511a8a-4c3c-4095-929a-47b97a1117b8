<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailCampaigns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('title', 100);
            $table->enum('frequency_type', ['date', 'days', 'daily']);
            $table->text('frequency_time');
            $table->string('frequency_days')->nullable();
            $table->date('frequency_date')->nullable();
            $table->date('expire_date')->nullable();
            $table->string('subject');
            $table->string('pre_header');
            $table->longText('email_body');
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_campaigns');
    }
}
