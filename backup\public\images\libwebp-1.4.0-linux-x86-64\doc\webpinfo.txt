4mWEBPINFO24m(1)                 General Commands Manual                4mWEBPINFO24m(1)

1mNAME0m
       webpinfo - print out the chunk level structure of WebP files along with
       basic integrity checks.

1mSYNOPSIS0m
       1mwebpinfo 4m22mOPTIONS24m 4mINPUT0m
       1mwebpinfo [-h|-help|-H|-longhelp]0m


1mDESCRIPTION0m
       This manual page documents the 1mwebpinfo 22mcommand.

       1mwebpinfo  22mcan  be  used to print out the chunk level structure and bit-
       stream header information of WebP files. It can also check if the files
       are of valid WebP format.


1mOPTIONS0m
       1m-version0m
              Print the version number (as major.minor.revision) and exit.

       1m-quiet 22mDo not show chunk parsing information.

       1m-diag  22mShow parsing error diagnosis.

       1m-summary0m
              Show chunk stats summary.

       1m-bitstream_info0m
              Parse bitstream header.

       1m-h, -help0m
              A short usage summary.

       1m-H, -longhelp0m
              Detailed usage instructions.


1mINPUT0m
       Input files in WebP format. Input files must come last,  following  op-
       tions (if any). There can be multiple input files.


1mBUGS0m
       Please     report     all     bugs     to     the     issue    tracker:
       https://bugs.chromium.org/p/webp
       Patches welcome! See this page  to  get  started:  https://www.webmpro-
       ject.org/code/contribute/submitting-patches/


1mEXAMPLES0m
       webpinfo -h
       webpinfo -diag -summary input_file.webp
       webpinfo -bitstream_info input_file_1.webp input_file_2.webp
       webpinfo *.webp


1mAUTHORS0m
       1mwebpinfo 22mis a part of libwebp and was written by the WebP team.
       The   latest  source  tree  is  available  at  https://chromium.google-
       source.com/webm/libwebp

       This manual page was written by Hui Su <<EMAIL>>, for the  De-
       bian project (and may be used by others).


1mSEE ALSO0m
       1mwebpmux22m(1)
       Please  refer  to  https://developers.google.com/speed/webp/  for addi-
       tional information.

                               November 17, 2021                   4mWEBPINFO24m(1)
