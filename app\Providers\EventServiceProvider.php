<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Order Events
        \App\Events\OrderPlaced::class => [
            \App\Listeners\SendOrderPlacedNotifications::class,
        ],
        \App\Events\OrderShipped::class => [
            \App\Listeners\SendOrderShippedNotifications::class,
        ],
        \App\Events\OrderDelivered::class => [
            \App\Listeners\SendOrderDeliveredNotifications::class,
        ],
        \App\Events\OrderCancelled::class => [
            \App\Listeners\SendOrderCancelledNotifications::class,
        ],
        \App\Events\OrderRefunded::class => [
            \App\Listeners\SendOrderRefundedNotifications::class,
        ],

        // Payment Events
        \App\Events\PaymentSuccess::class => [
            \App\Listeners\SendPaymentSuccessNotifications::class,
        ],
        \App\Events\PaymentFailed::class => [
            \App\Listeners\SendPaymentFailedNotifications::class,
        ],

        // User/Account Events
        \App\Events\AccountRegistered::class => [
            \App\Listeners\SendAccountRegisteredNotifications::class,
        ],
        \App\Events\PasswordReset::class => [
            \App\Listeners\SendPasswordResetNotifications::class,
        ],

        // Contact & Marketing Events
        \App\Events\ContactFormSubmitted::class => [
            \App\Listeners\SendContactFormNotifications::class,
        ],
        \App\Events\NewsletterFormSubmitted::class => [
            \App\Listeners\SendNewsletterFormNotifications::class,
        ],
        \App\Events\ReviewFormSubmitted::class => [
            \App\Listeners\SendReviewFormNotifications::class,
        ],

        // Promotions & Rewards
        \App\Events\CouponUsed::class => [
            \App\Listeners\SendCouponUsedNotifications::class,
        ],
        \App\Events\RedeemRewards::class => [
            \App\Listeners\SendRedeemRewardsNotifications::class,
        ],

        // Inventory & Wishlist
        \App\Events\LowInventory::class => [
            \App\Listeners\SendLowInventoryNotifications::class,
        ],
        \App\Events\NotifyMeOrder::class => [
            \App\Listeners\SendNotifyMeOrderNotifications::class,
        ],
        \App\Events\WishlistReminder::class => [
            \App\Listeners\SendWishlistReminderNotifications::class,
        ],
        \App\Events\ReviewNotification::class => [
            \App\Listeners\SendReviewNotification::class,
        ],
        \App\Events\OrderStatusChange::class => [
            \App\Listeners\SendOrderStatusChangeNotifications::class,
        ],
         \App\Events\RewardsAlert::class => [
            \App\Listeners\SendRewardsAlertChangeNotifications::class,
         ],
         \App\Events\PromotionalAlerts::class => [
            \App\Listeners\SendPromotionalAlertsNotifications::class,
        ]
    ];


    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
