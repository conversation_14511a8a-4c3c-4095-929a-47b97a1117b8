<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePromosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('promos', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('promo_code')->unique();
            $table->date('from_date')->nullable();
            $table->date('to_date')->nullable();
            $table->longText('description')->nullable();
            $table->string('promo_image')->nullable();
            $table->integer('discount_type')->nullable();
            $table->decimal('discount_price', 20, 2)->nullable();
            $table->integer('ordering')->default(0);
            $table->boolean("never_expire")->default(false);
            $table->boolean("status")->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('promos');
    }
}
