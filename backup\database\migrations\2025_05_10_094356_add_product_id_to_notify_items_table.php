<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProductIdToNotifyItemsTable extends Migration
{
   
    public function up(): void
    {
        Schema::table('notify_items', function (Blueprint $table) {
            $table->unsignedBigInteger('product_id')->nullable()->after('id');

            // Optional: Add a foreign key constraint if `products` table exists
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('notify_items', function (Blueprint $table) {
            // Drop the foreign key first if it was added
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
        });
    }
}
