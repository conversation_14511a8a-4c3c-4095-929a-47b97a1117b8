<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAlertPreferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('alert_preferences', function (Blueprint $table) {
            $table->id();
            // $table->foreignId('email_template_id')->nullable()->constrained('email_templates');
            // $table->foreignId('access_label_id')->nullable()->constrained('access_labels');
            $table->string('user_group_id')->nullable();
            // $table->foreignId('user_group_id')->nullable()->constrained('user_groups');
            $table->string('title');
            $table->string('key');
            $table->boolean('email')->default(false);
            $table->boolean('email_default')->default(false);
            $table->boolean('sms')->default(false);
            $table->boolean('sms_default')->default(false);
            $table->boolean('status')->default(true);
            $table->timestamps();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('newsletters_offers_promo');
            $table->dropColumn('sms_notifications');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('alert_preferences');
    }
}
