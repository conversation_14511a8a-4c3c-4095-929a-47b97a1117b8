<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTellCellToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('tel_cell')->change();
            $table->dropColumn('tel_cell_country_code');
        });
        Schema::table('user_shippings', function (Blueprint $table) {
            $table->string('tel_cell')->change();
            $table->dropColumn('tel_cell_country_code');
        });
        Schema::table('user_billings', function (Blueprint $table) {
            $table->string('tel_cell')->change();
            $table->dropColumn('tel_cell_country_code');
        });
        Schema::table('settings', function (Blueprint $table) {
            $table->string('tel_cell')->change();
            $table->dropColumn('tel_cell_country_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->bigInteger('tel_cell')->change();
            $table->string('tel_cell_country_code')->nullable();
        });
        Schema::table('user_shippings', function (Blueprint $table) {
            $table->bigInteger('tel_cell')->change();
            $table->string('tel_cell_country_code')->nullable();
        });
        Schema::table('user_billings', function (Blueprint $table) {
            $table->bigInteger('tel_cell')->change();
            $table->string('tel_cell_country_code')->nullable();
        });
        Schema::table('settings', function (Blueprint $table) {
            $table->bigInteger('tel_cell')->change();
            $table->string('tel_cell_country_code')->nullable();
        });
    }
}
