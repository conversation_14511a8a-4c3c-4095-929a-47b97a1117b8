<?php

namespace App\Http\Controllers;

use App\Models\Subscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Twilio\Rest\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\SentDiscount;
use Illuminate\Support\Facades\Session;
use App\Models\Setting;
use App\Models\Promo;
use App\Models\Coupon;
use App\Mail\SiteEmail;
use Illuminate\Support\Facades\Mail;
use App\Mail\PromoCodeEmail;

class NewsletterController extends Controller
{
    public function index()
    {
        $subcriber = Subscriber::all();
        $admin_page_title = "User Suscriber";
        return view('admin.user_subcriber.index', compact('subcriber', 'admin_page_title'));
    }
    public function subscribe(Request $request)
    {
        if (empty($request->email) && empty($request->phone)) {
            return response()->json([
                'errors' => [
                    'contact' => ['Either phone or email is required.']
                ]
            ], 422);
        }
        $validator = Validator::make($request->all(), [
            'email' => [
                'nullable',
                'email',
                'unique:users,email',
            ],
            'phone' => [
                'max:20',
                'regex:/^\+?[0-9\s\-()]*$/',
                'unique:users,tel_cell',
            ],
        ], [
            'phone.unique' => 'This phone number is already taken.',
        ]);



        $phone = preg_replace("/[^+0-9]/", "", $request->get('phone'));

        // Check if at least one field (email or phone) is provided
        // if (!$request->filled('email') && !$request->filled('phone')) {
        //     return back()->with('error', 'Please provide an email address or phone number.');
        // }

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $mode = \App\Models\CountryMessageSetting::where('country_code', $selectedCountry)->value('mode') ?? 'otp';
        $twilioSenderId = \App\Models\CountryMessageSetting::where('country_code', $selectedCountry)->value('twilio_sms_from') ?? env('TWILIO_SID');

        if ($request->type == 'promo') {
            $this->createSentDiscount($request);

            if ($mode == 'otp') {
                $generatedOTP = $this->generateOtpSession();
                $otpMode = $this->sendConfirmationMessage($phone,  'Use this OTP to verify your number and claim your discount offer: ' . $generatedOTP, $mode);
                if ($otpMode == 'otp') {
                    return response()->json(['message' => 'An OTP has been send to your mobile number', 'mode' => 'otp'], 200);
                } else {
                    $successMessage = "<div>
            <h4 class=' font-7 mt-4'>
            Check your phone for a text message from <span class='yellow-text'>1GuyGadget</span> and reply with <span class='yellow-text'>START</span> to subscribe.</h4>
            </div>";
                    return response()->json(['message' => $successMessage, 'success' => true, 'mode' => 'reply'], 200);
                }
            } else {
                $status = $this->sendConfirmationMessage($phone, 'Hi, reply with START to receive your PROMO code.', $mode);
                $successMessage = "<div> <h4>Subscription saved!</h4>
            <h4 class=' font-7 mt-4'> Please check your phone for a text message from <span class='yellow-text'>1GuyGadget</span> and reply with <span class='yellow-text'>START</span> to receive your PROMO code.</h4>
            </div>";
                return response()->json(['message' => $successMessage, 'success' => true, 'mode' => 'reply'], 200);
            }
        }



        // session()->flash('success', 'Subscribed successfully!');

        session()->flash('success', 'Subscribed successfully!');
        return back();
    }
    function createSentDiscount($request)
    {
        $email = $request->get('email');
        $phone = $request->get('phone');
        $promoId = $request->get('promo_id');

        // Delete any existing record with same email or phone
        SentDiscount::where('email', $email)
            ->orWhere('phone', $phone)
            ->delete();

        // Create new entry
        $discount = SentDiscount::create([
            'email' => $email,
            'phone' => $phone,
            'promo_id' => $promoId,
        ]);
    }


    function sendConfirmationMessage($phone, $text = null, $mode = 'otp', $twilioSenderId = null)
    {
        $whatsappSent = $this->sendViaMetaWhatsApp($phone, [], 'verify_user', 'en');
        Log::error('Meta WhatsApp status: ' . $whatsappSent);
        if (empty($twilioSenderId)) {
            $twilioSenderId = env('TWILIO_SID');
        }

        if (!$whatsappSent && $mode == 'otp') {
            $this->sendViaTwilioSms($phone, $text, $mode, $twilioSenderId);
            return 'otp';
        } elseif ($whatsappSent) {
            $this->sendViaTwilioSms($phone, $text, $mode, $twilioSenderId);

            return 'otp';
        } elseif (!$whatsappSent && $mode == 'reply') {
            $this->sendViaTwilioSms($phone, $text, $mode, $twilioSenderId);
            return 'reply';
        }
    }

    public function verifyOtpSession(Request $request)
    {
        $request->validate([
            'otp' => 'required|string',
        ]);
        $promoId = $request->promo_id;
        $promo = Coupon::find($promoId);
        $promoCode = $promo->code;
        $phone = $request->phone;
        $email = $request->email;

        $storedOtp = session('otp_code');
        $expiresAt = session('otp_expires_at');

        if (!$storedOtp) {
            return response()->json(['error' => 'No OTP found in session'], 422);
        }

        // if (now()->greaterThan($expiresAt)) {
        //     session()->forget(['otp_code', 'otp_expires_at']);
        //     return response()->json(['error' => 'OTP expired'], 422);
        // }

        if ($request->otp !== (string) $storedOtp) {
            return response()->json(['error' => 'Invalid OTP'], 422);
        }


        // OTP valid - clear from session
        session()->forget(['otp_code', 'otp_expires_at']);
        //$this->sendConfirmationMessage($phone,  'Verification successful! Use your promo code below: ' . $promoCode, 'otp');
        $details = (object)[
            'pre_header' => 'Here is your exclusive promo code!',
            'subject' => 'Here is your promo code!',
            'content' => '<p>Use promo code <strong>' . $promoCode . '</strong> at checkout to get your offer.</p>',
        ];

        Mail::to($email)->send(new SiteEmail($details));
        $sentDiscount = SentDiscount::where('phone', $phone)->first();

        // if ($sentDiscount) {
        //     Subscriber::create([
        //         'email' => $sentDiscount->email,
        //         'phone' => $phone,
        //     ]);
        // }

        $this->addGuestUser($email, $phone);

        return response()->json([
            'message' => '
        <div class="" role="alert">
            <h4><strong>Subscription saved!!</strong></h4>
            <h4 class="text-center mt-5"> We have emailed your PROMO code.</h4>
            <h5 class="mt-5 mb-5 text-center"><a href="#" id="sent_promo_code_sms" data-mobile="' . $phone . '" data-promo="' . $promoId . '">Text me a copy of my code
 </a></h5>

        </div>
    '
        ]);
    }
    public function sentPromoSMS(Request $request)
    {
        $phone = $request->phone;
        $couponId = $request->coupon_id;
        $promoCode = Coupon::where('id', $couponId)->first();
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $twilioSenderId =  env('TWILIO_SID');
        $successFull = false;
        $whatsappSent = $this->sendViaMetaWhatsApp($phone, [$promoCode->code], 'offer_code');
        if (!$whatsappSent) {
            Log::error('manualsend whatsapp error');

            $twSent = $this->sendViaTwilioSms($phone, 'Here is your promo code: ' . $promoCode->code, 'reply', $twilioSenderId);
            $successFull = true;
        } else {
            $successFull = true;
        }
        $sentDiscount = SentDiscount::where('phone', $phone)->first();

        if ($sentDiscount) {
            Subscriber::firstOrCreate(
                ['email' => $sentDiscount->email, 'phone' => $phone]
            );
        }
        if ($successFull) {
            return response()->json(['success' => 'Your promo code has been successfully sent to your mobile number.'], 200);
        } else {
            return response()->json(['error' => 'We encountered an issue while sending your promo code.'], 422);
        }
    }

    function isUserRepliedtoMessage(Request $request)
    {
        $phone = $request->phone;
        $discount = SentDiscount::where('phone', $phone)->first();
        $promoId = $discount->promo_id;
        return response()->json([
            'replied' => (bool) $discount->user_replied,
            'message' => '
        <div class="" role="alert">
            <h4><strong>Subscription saved!!</strong></h4>
            <h4 class="text-center mt-5"> We have emailed your PROMO code.</h4>
            <h5 class="mt-5 mb-5 text-center"><a href="#" id="sent_promo_code_sms" data-mobile="' . $phone . '" data-promo="' . $promoId . '">Text me a copy of my code
 </a></h5>

        </div>
    '
        ]);
    }
}
