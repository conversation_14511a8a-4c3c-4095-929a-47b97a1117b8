<?php

namespace App\Http\Controllers;

use App\Models\Subscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NewsletterController extends Controller
{
    public function subscribe(Request $request)
    {
        // dd("hi");
        // Validate the input
        $validator = Validator::make($request->all(), [
            'email' => 'nullable',
            'phone' => 'nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }
        

       Subscriber::create($request->only('email', 'phone'));

        // session()->flash('success', 'Subscribed successfully!');

      session()->flash('success', 'Subscribed successfully!');
return back();
    }
}
