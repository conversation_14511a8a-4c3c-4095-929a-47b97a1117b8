<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCarrierOptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
         Schema::create('carrier_options', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('attribute_id'); // optional if linked to product_attributes
        $table->string('title');
        $table->text('description')->nullable();

        $table->string('file_path')->nullable();
        $table->string('text_file_path')->nullable();
        $table->string('extension')->nullable();
        $table->string('text_extension')->nullable();

        $table->string('img_url')->nullable();
        $table->string('text_img_url')->nullable();
        $table->string('folder')->nullable();

        $table->boolean('status')->default(true);
        $table->integer('ordering')->default(0);
        $table->timestamps();
    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('carrier_options');
    }
}
