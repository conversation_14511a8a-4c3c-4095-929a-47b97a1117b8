<?php

namespace App\Listeners;

use App\Events\NotifyMeOrder;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendNotifyMeOrderNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(NotifyMeOrder $event)
    {
        $this->notifier->sendNotifyMeOrderNotification($event->email, $event->phone, $event->productItem, $event->message_status);
    }
}
