<?php

namespace App\Http\Controllers\Auth;

use Validator;
use App\Models\User;
use App\Mail\SiteEmail;
use App\Models\Tracking;
use App\Models\UserGroupMap;
use Illuminate\Http\Request;
use App\Models\EmailTemplate;
use F9Web\ApiResponseHelpers;
use App\Models\AlertPreference;
use Illuminate\Http\JsonResponse;
use App\Mail\UserVerificationToken;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Auth\Events\Registered;
use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Password;
use Illuminate\Foundation\Auth\RegistersUsers;

class RegisterController extends Controller
{
    use ApiResponseHelpers;
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'username' => 'required|string|max:255|unique:users|alpha_dash',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            "tel_cell" => "required|string|max:20",
            'city' => 'required|string',
            'state' => 'required|string',
            'zip_code' => 'required|integer',
            'country' => 'required|string',
            'address' => 'required|string',
            'address_2' => 'nullable|string',
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required',
        ]);
    }

    public function register(Request $request): JsonResponse
    {

        


        $this->validator($request->all())->validate();


        $user = $this->create($request->all(), $this->getClientIpAddress());

        $user_groups = array(3);
        foreach ($user_groups as  $value) {
            $group_map = new UserGroupMap();
            $group_map->user_id = $user->id;
            $group_map->user_group_id = $value;
            $group_map->save();
        }
        $user_group_map = UserGroupMap::where('user_id', $user->id)->first();
        $user_alert_preferences = AlertPreference::where('user_group_id', 'LIKE', '%' . $user_group_map->user_group_id . '%')->whereStatus(true)->get();

        $value_key_array = array();
        foreach ($user_alert_preferences as $value) {
            $value_key_array[$value->key] = [
                'email' => $value->email_force_checked ? 1 :  $user->email_notifications,
                'sms' => $value->sms_force_checked ? 1 :  $user->sms_notifications,
            ];
        }

        $user = User::findOrFail($user->id);
        $user->alert_preferences = serialize($value_key_array);
        $user->update();

        $token = Password::getRepository()->createNewToken();

        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();

            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'create_account'
                ) {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);

                    $mail_data = (object)[
                        'user' => $user,
                        'account_verification_link' => route('verify.mail', ['email' => $user->email, 'token' => $token]),
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];

                    $tracking = new Tracking();
                    $tracking->user_id = $user->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();

                    // if ($user_email_template->sms_status && $user->tel_cell)
                    if ($user_email_template->sms_status && $user->tel_cell)
                    {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        // $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }

        $email_templates = EmailTemplate::where('system_type', 'create_account')
        ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
        ->select('email_templates.*', 'alert_preferences.user_group_id')
        ->get();

        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });

        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
        ->join('users', 'users.id', 'user_group_maps.user_id')
        ->select(
            'user_group_maps.*',
            'users.email as user_email',
            'users.alert_preferences as user_alert_preferences',
        )
        ->where('users.verified', true)
        ->get();

        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();

                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'create_account'
                            ) {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);

                                $mail_data = (object)[
                                    'user' => $user,
                                    'account_verification_link' => route('verify.mail', ['email' => $user->email, 'token' => $token]),
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                            $mail_data->content = $this->sendEmailData($mail_data)->content;
                                            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];

                                $tracking = new Tracking();
                                $tracking->user_id = $user->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();

                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell)
                                {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }

                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }

        event(new Registered($user));

        $expiresAt = now()->addMinutes(180);
        Cache::put($user->email, $token, $expiresAt);


        $message = 'Thank you for creating an account with us. Your account details and instructions on how to proceed has been emailed to <strong>'. $user->email. '</strong>';
        return $this->respondOk($message);
    }

    public function sendVerificationMail($user)
    {
        $user->token = Password::getRepository()->createNewToken();
        $expiresAt = now()->addMinutes(180);
        Cache::put($user->email, $user->token, $expiresAt);
        Mail::to($user->email)->send(new UserVerificationToken($user));
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data, $get_ip)
    {


        return User::create([
            'username' => $data['username'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'tel_cell' => preg_replace('/[^\d+]/', '', $data['tel_cell']),
           
            'city' => $data['city'],
            'state' => $data['state'],
            'postal_code' => $data['zip_code'],
            'country' => $data['country'],
            'address' => $data['address'],
            'address_2' => $data['address_2'],
            'email_notifications' => $data['email_notifications'] ?? 0,
            'sms_notifications' => $data['sms_notifications'] ?? 0,
            'password' => Hash::make($data['password']),
            'ip' => $get_ip,
        ]);
    }

    public function verifyingMail($email, $token)
    {
        $user = User::where('email', $email)->first();
        $appToken = Cache::get($email);

        if ($user->verified) {
            return redirect()->route('home')->with('success', 'Don\'t Worry You Are Already Verified.');
        } else if (!$user) {
            return redirect()->route('register')->with('warning', 'We Don\'t Found Your Account. Please! Create An Account First.');
        } else if (!$appToken) {
            return redirect()->route('verify.mail.resend')->with('warning', 'Your Email Expired. Try For New Verification Mail Email.');
        } else if ($appToken != $token) {
            return redirect()->route('verify.mail.resend')->with('error', 'Invalid Request or Token Mismatch. Try For New Verification Mail.');
        } else if ($appToken == $token) {
            Cache::forget($email);
            $user->verified = true;
            $user->update();

            return redirect()->route('home')->with('success', 'Verification successful. You can login now.');
        } else {
            abort(404);
        }
    }

    public function showResendVerificationMailForm()
    {
        return view('auth.verify');
    }

    public function resendVerificationMail(Request $request)
    {
        $email = $request->get('email');

        $user = User::where('email', $email)->first();

        if (!$user) {
            return redirect()->route('register')->with('warning', 'We Don\'t Found Your Account. Please! Create An Account First.');
        } else if ($user->verified) {
            return redirect()->route('login')->with('success', 'Don\'t Worry Your Account Already Verified. You Can Login Now.');
        } else {
            $this->sendVerificationMail($user);
        }

        return redirect()->route('home')->with('success', 'Check Your E-mail. Please! Confirm Your E-mail To Active Account.');
    }
}