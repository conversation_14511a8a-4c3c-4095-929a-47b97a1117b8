<?php

namespace App\Listeners;

use App\Events\OrderRefunded;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendOrderRefundedNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(OrderRefunded $event)
    {
        $this->notifier->sendCustomerOrderRefundedNotification($event->order, $event->user, $event->message_status);
    }
}
