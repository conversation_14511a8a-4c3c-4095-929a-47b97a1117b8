<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class OrderStatusChange
{
    use SerializesModels;

    public $order;
    public $user;
    public $message_status;
    public $template;

    public function __construct($order, $user, $message_status, $template)
    {
        $this->order = $order;
        $this->user = $user;
        $this->message_status = $message_status;
        $this->template = $template;
    }
}
