<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemoveUniqueIndexFromUserShippingsEmail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('user_shippings') && Schema::hasColumn('user_shippings', 'email')) {
            // Check if the unique index exists before attempting to drop
            $indexExists = collect(DB::select("SHOW INDEX FROM user_shippings WHERE Key_name = 'user_shippings_email_unique'"))->isNotEmpty();

            if ($indexExists) {
                Schema::table('user_shippings', function (Blueprint $table) {
                    $table->dropUnique('user_shippings_email_unique');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('user_shippings') && Schema::hasColumn('user_shippings', 'email')) {
            Schema::table('user_shippings', function (Blueprint $table) {
                $table->unique('email', 'user_shippings_email_unique');
            });
        }
    }
}
