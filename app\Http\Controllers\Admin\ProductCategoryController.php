<?php

namespace App\Http\Controllers\Admin;

use Session;
use Response;
use Validator;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ProductCategory;
use App\Http\Controllers\Controller;
use App\Models\ProdcutCategoriesParent;
use Illuminate\Support\Facades\Storage;

class ProductCategoryController extends Controller
{
    private $folderPath = 'products/categories/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Categories';
        $items = ProductCategory::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        return view('admin.product.category.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Category';
        $category_image_info = Session::get('category_image');
        $category_image_hover_info = Session::get('category_image_hover');
        $productCategories = ProdcutCategoriesParent::all();
        return view('admin.product.category.create', compact('category_image_info', 'category_image_hover_info', 'admin_page_title','productCategories'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string',
            // 'slug' => 'required|string|alpha_dash|unique:product_categories,slug',
            'ordering' => 'nullable|string',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean',
            'parent_category_id'=>'required'
            
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|unique:product_categories,slug'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        $item = new ProductCategory;
        $item->title = $request->title;
        $item->slug = $item_slug;
        $item->description = $request->description;
        $item->featured = $request->get('featured') ?? 0;
        $item->ordering = $request->ordering ?? 0;
        $item->status = $request->status ?? 0;
        $item->parent_category_id = $request->parent_category_id;
        $category_image_info = Session::get('category_image');
        if ($category_image_info) {
            $image_name = $item->slug . '_category_' . rand(99, 999999999). '.' . $category_image_info->extension;
            $folderName = $this->folderName();
            Storage::move($category_image_info->category_image, $this->folderPath . $folderName . '/' . $image_name);
            $item->category_image = $folderName . '/' . $image_name;
            Session::forget('category_image');
        }
        $category_image_hover_info = Session::get('category_image_hover');
        if ($category_image_hover_info) {
            $image_name = $item->slug . '_category_' . rand(99, 999999999) . '.' . $category_image_hover_info->extension;
            $folderName = $this->folderName();
            Storage::move($category_image_hover_info->category_image_hover, $this->folderPath . $folderName . '/' . $image_name);
            $item->category_image_hover = $folderName . '/' . $image_name;
            Session::forget('category_image_hover');
        }
        if ($request->hasFile('category_image')) {
            $folderName = $this->folderName();
            $imgName = $item->slug . '_category_image_' . uniqid() . '.' . $request->category_image->extension();
            $request->category_image->storeAs($this->folderPath . $folderName, $imgName);
            $item->category_image = $folderName. '/'. $imgName;
        }
        if ($request->hasFile('category_image_hover')) {
            $folderName = $this->folderName();
            $imgName = $item->slug . '_category_image_hover_' . uniqid() . '.' . $request->category_image_hover->extension();
            $request->category_image_hover->storeAs($this->folderPath . $folderName, $imgName);
            $item->category_image_hover = $folderName. '/'. $imgName;
        }
        $item->save();

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.product-categories.index'))->with('success', 'Product category created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProductCategory  $productCategory
     * @return \Illuminate\Http\Response
     */
    public function show(ProductCategory $productCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductCategory  $productCategory
     * @return \Illuminate\Http\Response
     */
    public function edit(ProductCategory $productCategory)
    {
        $admin_page_title = 'Edit Category';
        $item = $productCategory;
        return view('admin.product.category.edit', compact('item', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductCategory  $productCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductCategory $productCategory)
    {
        $this->validate($request, [
            'title' => 'required|string',
            'slug' => 'required|string|alpha_dash|unique:product_categories,slug,' . $productCategory->id,
            'ordering' => 'nullable|string',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        $item = ProductCategory::findOrFail($productCategory->id);
        $item->title = $request->title;
        $item->slug = $request->slug;
        $item->description = $request->description;
        $item->featured = $request->get('featured') ?? 0;
        $item->ordering = $request->ordering ?? 0;
        $item->status = $request->status ?? 0;
        $item->save();

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.product-categories.index'))->with('success', 'Product category created successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductCategory  $productCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProductCategory $productCategory)
    {
        $item = ProductCategory::findOrFail($productCategory->id);
        $this->deleteFile($this->folderPath, $item->brand_image);
        $item->delete();

        $items = ProductCategory::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        return view('admin.jquery_live.product-categories', compact('items', 'sl'));
    }

    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }

        $item = ProductCategory::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target);

        $imgName = $item->slug . '_' . $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath. '/' . $folderName, $imgName);

        $item->$target = $folderName . '/' . $imgName;
        $item->update();

        $url = url('/storage/products/categories/' . $folderName. '/' . $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileRemove($target, $id)
    {
        $item = ProductCategory::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();

        return response()->json('success', 200);
    }
}
