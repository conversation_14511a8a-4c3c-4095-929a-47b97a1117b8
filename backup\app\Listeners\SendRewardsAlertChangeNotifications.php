<?php

namespace App\Listeners;

use App\Events\RewardsAlert;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendRewardsAlertChangeNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(RewardsAlert $event)
    {
        $this->notifier->sendRewardNotifications($event->reward, $event->user, $event->message_status,  $event->template);
        //$this->notifier->sendAdminOrderDeliveredNotifications($event->order, $event->cart);
    }
}
