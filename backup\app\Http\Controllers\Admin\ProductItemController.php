<?php
namespace App\Http\Controllers\Admin;
use Session;
use Validator;
use Carbon\Carbon;
use App\Models\Product;
use App\Models\ProductItem;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use App\Http\Controllers\Controller;
use App\Models\ProductAttributeItem;
use Illuminate\Support\Facades\Storage;
class ProductItemController extends Controller
{
    private $folderPath = 'product_item_images/';
    private $folderPathVideos = 'product_item_videos/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $product_id = null)
    {
        $products = Product::all();
        $admin_page_title = 'Items';
        if ($request->action == 'search') {
            if (empty($request->product_id)) {
                $this->validate($request, [
                    'query' => 'required|min:3',
                ]);
            }
            $keywords = explode(' ', $request->input('query'));
            $search_fields = ['title', 'sub_title', 'product_item_sku'];
            $query = ProductItem::query();
            foreach ($keywords as $keyword) {
                $query->orWhere(function ($query) use ($search_fields, $keyword) {
                    foreach ($search_fields as $field) {
                        $query->orWhere($field, 'LIKE', '%' . $keyword . '%');
                    }
                });
            }
            if ($request->product_id) {
                $query->where('product_items.product_id', $request->product_id);
            }
            $query->orderBy('product_items.status', 'asc');
            $query->orderBy('product_items.id', 'desc');
            $items = $query->get();
            $collection  = new Collection($items);
            $items =  $collection->sortBy('id')->paginate($this->itemPerPage);
            $product = Product::find($request->product_id);
            if ($product) {
                $admin_page_title = $product->title;
            }
        } else {
            if (!empty($product_id)) {
                $product = Product::find($product_id);
                $admin_page_title = $product->title;
                $items = ProductItem::where('product_id', $product_id)->orderBy('id', 'desc')->paginate($this->itemPerPage);
            } else {
                $items = ProductItem::orderBy('id', 'desc')->paginate($this->itemPerPage);
            }
        }
        if ($request->action == 'clear') {
            return redirect()->route('admin.product.items.index');
        }
        foreach ($items as $item) {
            if ($item->discount_price && $item->discount_type) {
                if ($item->discount_type == 1) {
                    $discount_amt = ($item->sale_price * $item->discount_price) / 100;
                } else {
                    $discount_amt = $item->discount_price;
                }
                $item->price = number_format($item->sale_price - $discount_amt, 2);
            } else {
                $item->price = $item->sale_price;
            }
            if (!empty($item->item_attributes)) {
                $item->item_attributes = $this->getAttribute(collect(unserialize($item->item_attributes))->sortBy('ordering'), $item);
            } else {
                $item->item_attributes = array();
            }
        }
        // dd($items);
        $sl = SLGenerator($items);
        if ($request->view == 'html') {
            return view('admin.jquery_live.product_items', compact('sl', 'items', 'admin_page_title', 'products', 'product_id'));
        }
        return view('admin.product.item.index', compact('sl', 'items', 'admin_page_title', 'products', 'product_id'));
    }
    public function cloneItem($id, $product_id = null)
    {
        $item = ProductItem::find($id);
        $new_item = $item->replicate();
        $new_item->product_item_sku = rand(99, 999999999);
        $new_item->status = 0;
        $new_item->discount_type = $item->discount_type ?? null;
        $new_item->discount_price = $item->discount_price ?? null;
        $new_item->created_at = Carbon::now();
        $new_item->updated_at = Carbon::now();
        // $attributes = unserialize($item->item_attributes);
        // foreach($attributes as $value){
        //     $value->id = uniqid();
        //     if($value->data){
        //         if($value->input_type_id == 2){
        //             foreach ($value->data as $data_item) {
        //                 $data_item->id = uniqid();
        //             }
        //         }
        //         if ($value->input_type_id == 1) {
        //             $value->data->id = uniqid();
        //         }
        //     }
        // }
        // $new_item->item_attributes = serialize($attributes);
        $new_item->save();
        $new_item = ProductItem::findOrFail($new_item->id);
        $product_item_image_info = $item->product_item_image;
        if ($product_item_image_info) {
            $image_name = 'product_item_image_' . rand(99, 999999999) . '.' . pathinfo($product_item_image_info, PATHINFO_EXTENSION);
            $folderName = $this->folderName() . '/' . $new_item->id . '/';
            Storage::copy($this->folderPath . $product_item_image_info, $this->folderPath . $folderName . '/' . $image_name);
            $new_item->product_item_image = $folderName . '/' . $image_name;
        }
        $product_item_images_info = unserialize($item->product_item_images);
        if ($product_item_images_info) {
            $folderName = $this->folderName() . '/' . $new_item->id . '/';
            foreach ($product_item_images_info as $product_item_image_info) {
                $image_name = 'product_item_image_' . uniqid() . '.' . pathinfo($product_item_image_info->file_path, PATHINFO_EXTENSION);
                Storage::copy($this->folderPath . $product_item_image_info->file_path, $this->folderPath . $folderName . '/' . $image_name);
                $product_item_image_info->folder = $this->folderName();
                $product_item_image_info->file_path = $folderName . '/' . $image_name;
            }
            $new_item->product_item_images = serialize($product_item_images_info);
        }
        $new_item->update();
        if (empty($product_id)) {
            return redirect()->route('admin.product.items.index')->with('success', 'Product Clone  successful.');
        }
        return redirect()->route('admin.product.items.index', $product_id)->with('success', 'Product Clone  successful.');
    }
    public function getAttribute($items, $item)
    {
        foreach ($items as $value) {
            if ($value->input_type_id == 1) {
                $value->options = $value->data->title;
            } else {
                $value->options = $this->getOption($value->data);
            }
            if ($value->attribute_type_id == 12) {
                $item->color_image_path = $value->data->file_path;
            }
        }
        // dd($items);
        return $items;
    }
    public function getOption($items)
    {
        foreach ($items as $item) {
            $titles[] = $item->title;
        }
        return implode(',', $titles);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($product_id = null)
    {
        $products = Product::all();
        $countries = $this->countries();
        $stores = \App\Models\Setting::all();
        $admin_page_title = 'Create Item';
        $attributes = array();
        if (!empty($product_id)) {
            $product = Product::find($product_id);
            $admin_page_title = 'Add Item in ' . $product->title;
            if ($product->product_attribute_id) {
                $attributes = ProductAttributeItem::where('product_attribute_id', $product->product_attribute_id)->get();
                if (count($attributes) > 0) {
                    foreach ($attributes as $attribute) {
                        $attribute->attribute_options = $this->optionMeta(unserialize($attribute->attribute_options));
                        $attribute->key = Str::slug($attribute->title, '_') . $attribute->id;
                    }
                }
            }
            if ($product->product_attributes) {
                $attributes = collect(unserialize($product->product_attributes))->sortBy('ordering');
                if (count($attributes) > 0) {
                    foreach ($attributes as $attribute) {
                        $attribute->attribute_options = $this->optionMeta($attribute->attribute_options);
                        $attribute->key = Str::slug($attribute->title, '_') . $attribute->id;
                    }
                }
            }
        }
        $product_item_image_info = Session::get('product_item_image');
        $imagesCollection = collect(Session::get('product_item_images'));
        $videosCollection = collect(Session::get('product_item_videos'));
        $images = $imagesCollection->sortBy('file_order');
        $videos = $videosCollection->sortBy('file_order');
        // $countries = $this->countries();
        $product_box_types = $this->product_box_types();
        // dd($product = Product::find($product_id));
        return view('admin.product.item.create', compact('product_item_image_info', 'attributes', 'images','videos', 'admin_page_title', 'product_id', 'products', 'countries', 'product_box_types', 'countries', 'stores'));
    }
    public function optionMeta($items)
    {
        if ($items) {
            foreach ($items as $item) {
                $item->input_value = $item->id;
            }
        }
        return $items;
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $product_id = null)
    {
        $product_id = $product_id ?? $request->product_id;
        $product = Product::find($product_id);
        if ($product->product_attribute_id) {
            $attributes = ProductAttributeItem::where('product_attribute_id', $product->product_attribute_id)->get();
            foreach ($attributes as $attribute) {
                $attribute->attribute_options = unserialize($attribute->attribute_options);
                $attribute_type_key = Str::slug($attribute->title, '_') . $attribute->id;
                $attribute->attribute_type_key = $attribute_type_key;
                $show_on_title_key = 'show_on_title_' . $attribute->id;
                $show_on_sub_title_key = 'show_on_sub_title_' . $attribute->id;
                if (!empty($request->$attribute_type_key)) {
                    $attributes_request[] = (object)[
                        'id' => $attribute->id,
                        'title' => $attribute->title,
                        'input_type_id' => $attribute->input_type_id,
                        'attribute_type_id' => $attribute->attribute_type_id,
                        'data' => $request->$attribute_type_key,
                        'ordering' => $attribute->ordering,
                        'show_on_title' => $request->$show_on_title_key ? true : false,
                        'show_on_sub_title' => $request->$show_on_sub_title_key ? true : false
                    ];
                }
            }
        }
        if ($product->product_attributes) {
            $attributes = unserialize($product->product_attributes);
            foreach ($attributes as $attribute) {
                $attribute->attribute_options = $attribute->attribute_options;
                $attribute_type_key = Str::slug($attribute->title, '_') . $attribute->id;
                $attribute->attribute_type_key = $attribute_type_key;
                $show_on_title_key = 'show_on_title_' . $attribute->id;
                $show_on_sub_title_key = 'show_on_sub_title_' . $attribute->id;
                if (!empty($request->$attribute_type_key)) {
                    $attributes_request[] = (object)[
                        'id' => $attribute->id,
                        'title' => $attribute->title,
                        'input_type_id' => $attribute->input_type_id,
                        'attribute_type_id' => $attribute->attribute_type_id,
                        'data' => $request->$attribute_type_key,
                        'ordering' => $attribute->ordering,
                        'show_on_title' => $request->$show_on_title_key ? true : false,
                        'show_on_sub_title' => $request->$show_on_sub_title_key ? true : false
                    ];
                }
            }
        }
        $this->validate($request, [
            'title' => 'nullable|string',
            'sub_title' => 'nullable|string',
            'product_item_sku' => 'required|string|unique:product_items,product_item_sku',
            'sale_price' => 'required|numeric|between:0,9999999999.99',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'description' => 'nullable|string',
            'remark' => 'nullable|string',
            'stock_status' => 'nullable',
            'ordering' => 'nullable|numeric',
            'store_id' => 'nullable',
            'quantity' => 'required'
        ]);
        if (empty($attributes_request)) {
            $message = 'Please select attributes';
            return redirect()->route('admin.product.item.create', $product_id)->withInput($request->input())->with('warning', $message);
        }
        if (!empty($attributes_request)) {
            $collection = collect($attributes);
            foreach ($attributes_request as $attribute_data) {
                $attributes_data = $collection->where('id', $attribute_data->id)->first();
                if ($attribute_data->input_type_id == 1) {
                    foreach ($attributes_data->attribute_options as $attribute_option_data) {
                        if ($attribute_option_data->id == $attribute_data->data) {
                            $attribute_data->data = $attribute_option_data;
                            break;
                        }
                    }
                    $attribute_array[] = $attribute_data;
                }
                if ($attribute_data->input_type_id == 2) {
                    $attributes_collection = collect($attributes_data->attribute_options);
                    foreach ($attribute_data->data as $key => $attribute_data_id) {
                        $attribute_data->data[$key++] = $attributes_collection->where('id', $attribute_data_id)->first();
                    }
                    $attribute_array[] = $attribute_data;
                }
            }
            $collection = collect($attribute_array);
        }
        if (!empty($product_id)) {
            $product_validator = Validator::make(['product_id' => $product_id], [
                'product_id' => 'required|exists:products,id',
            ]);
        } else {
            $product_validator = Validator::make(['product_id' => $request->product_id], [
                'product_id' => 'required|exists:products,id',
            ]);
        }
        if ($product_validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($product_validator)->with('warning', 'product not exist.');
        }
        $item = new ProductItem;
        $item->product_id = $request->get('product_id') ?? $product_id;
        $item->title = $request->get('title');
        $item->sub_title = $request->get('sub_title');
        $item->product_item_sku = $request->get('product_item_sku'); // example - IP-BL-128-678
        $item->location_id = $request->get('location_id');
        $item->sale_price = $request->get('sale_price');
        $item->discount_type = $request->get('discount_type');
        $item->discount_price = $request->get('discount_price');
        $item->item_attributes = serialize($collection);
        $item->description = $request->get('description');
        $item->box_type = $request->get('box_type');
        $item->stock_status = $request->get('stock_status');
        $item->status = $request->get('status') ?? 0;
        $item->ordering = $request->get('ordering') ?? 0;
        $item->store_id = $request->get('store_id') ?? 0;
        $item->returnable = $request->get('returnable') ?? 1;
                $item->quantity = $request->get('quantity');

        $item->save();
        $item = ProductItem::findOrFail($item->id);
        $product_item_image_info = Session::get('product_item_image');
        if ($product_item_image_info) {
            $image_name = 'product_item_image_' . rand(99, 999999999) . '.' . $product_item_image_info->extension;
            $folderName = $this->folderName() . '/' . $item->id . '/';
            $item->product_item_image = $folderName . '/' . $image_name;
            Storage::move($product_item_image_info->product_item_image, $this->folderPath . $folderName . '/' . $image_name);
            Session::forget('product_item_image');
        }
        $product_item_images_info = Session::get('product_item_images');
        if ($product_item_images_info) {
            $folderName = $this->folderName() . '/' . $item->id . '/';
            foreach ($product_item_images_info as $product_item_image_info) {
                $image_name = 'product_item_image_' . uniqid() . '.' . $product_item_image_info->extension;
                $product_item_image_info->folder = $this->folderName();
                $product_item_image_info->file_path = $folderName . '/' . $image_name;
                Storage::move($product_item_image_info->product_item_images, $this->folderPath . $folderName . '/' . $image_name);
                Session::forget('product_item_images');
            }
            $item->product_item_images = serialize($product_item_images_info);
        }
        $product_item_videos_info = Session::get('product_item_videos');
        if ($product_item_videos_info) {
            $folderName = $this->folderName() . '/' . $item->id . '/';
            foreach ($product_item_videos_info as $product_item_video_info) {
                $image_name = 'product_item_video_' . uniqid() . '.' . $product_item_video_info->extension;
                $product_item_video_info->folder = $this->folderName();
                $product_item_video_info->file_path = $folderName . '/' . $image_name;
                Storage::move($product_item_video_info->product_item_videos, $this->folderPathVideos . $folderName . '/' . $image_name);
                Session::forget('product_item_videos');
            }
            $item->product_item_videos = serialize($product_item_videos_info);
        }
        // if ($request->hasFile('product_item_image')) {
        //     $folderName = $this->folderName() . $item_slug . '/';
        //     $imgName = 'product_item_image_' . uniqid() . '.' . $request->product_item_image->extension();
        //     $request->product_item_image->storeAs($this->folderPath . $folderName, $imgName);
        //     $item->product_item_image = $folderName. '/'. $imgName;
        // }
        $item->update();
        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.product.items.index', $product_id))->with('success', 'Product Item created successful.');
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProductItem  $productItem
     * @return \Illuminate\Http\Response
     */
    public function show(ProductItem $productItem)
    {
        //
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductItem  $productItem
     * @return \Illuminate\Http\Response
     */
    public function edit($id, $product_id = null)
    {
        $countries = $this->countries();
        $products = Product::all();
        $admin_page_title = 'Create Item';
        $item = ProductItem::find($id);
        $item->attributes = array();
        $item->countries = $this->countries();
        $item->product_box_types = $this->product_box_types();
        $stores = \App\Models\Setting::all();
        if (!empty($product_id)) {
            $product = Product::find($product_id);
            $admin_page_title = $product->title;
            if ($product->product_attribute_id) {
                if ($product->product_attribute_id) {
                    $attributes = ProductAttributeItem::where('product_attribute_id', $product->product_attribute_id)->get();
                } else {
                    $attributes = unserialize($attribute->attribute_options);
                }
                if (count($attributes) > 0) {
                    foreach ($attributes as $attribute) {
                        if ($product->product_attribute_id) {
                            $attribute->attribute_options = $this->optionMeta(unserialize($attribute->attribute_options));
                        } else {
                            $attribute->attribute_options = $this->optionMeta($attribute->attribute_options);
                        }
                        $attribute->key = Str::slug($attribute->title, '_') . $attribute->id;
                    }
                }
            }
            if ($product->product_attributes) {
                $attributes = collect(unserialize($product->product_attributes))->sortBy('ordering');
                if (count($attributes) > 0) {
                    foreach ($attributes as $attribute) {
                        $attribute->attribute_options = $this->optionMeta($attribute->attribute_options);
                        $attribute->key = Str::slug($attribute->title, '_') . $attribute->id;
                    }
                }
            }
            $item->attributes = $attributes;
        }
        if ($item->product_item_images) {
            $imagesCollection = collect(unserialize($item->product_item_images));
            $item->product_item_images = $imagesCollection->sortBy('file_order');
        }
        if ($item->product_item_videos) {
            $videosCollection = collect(unserialize($item->product_item_videos));
            $item->product_item_videos = $videosCollection->sortBy('file_order');
        }
        $item_attributes = unserialize($item->item_attributes);
        if (!empty($item_attributes)) {
            $selected_show_on_title = array();
            $selected_show_on_sub_title = array();
            foreach ($item_attributes as $item_attribute) {
                if ($item_attribute->show_on_title) {
                    $selected_show_on_title[] = 'show_on_title_' . $item_attribute->id;
                }
                if (!empty($item_attribute->show_on_sub_title)) {
                    $selected_show_on_sub_title[] = 'show_on_sub_title_' . $item_attribute->id;
                }
                if ($item_attribute->input_type_id == 1) {
                    $selected_id[] = $item_attribute->data->id;
                }
                if ($item_attribute->input_type_id == 2) {
                    $selected_id[] = $this->getSelectedID($item_attribute->data);
                }
            }
            $item->selected_id_array = explode(',', implode(',', $selected_id));
            $item->selected_show_on_title_array = $selected_show_on_title;
            $item->selected_show_on_sub_title_array = $selected_show_on_sub_title;
        } else {
            $item->selected_id_array = array();
            $item->selected_show_on_title_array = array();
            $item->selected_show_on_sub_title_array = array();
        }
        return view('admin.product.item.edit', compact('item', 'admin_page_title', 'product_id', 'products', 'countries', 'stores'));
    }
    public function getSelectedID($items)
    {
        foreach ($items as $item) {
            $selected_id[] = $item->id;
        }
        return implode(',', $selected_id);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductItem  $productItem
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id, $product_id = null)
    {
        //dd($request->all());
        $product_id = $product_id ?? $request->product_id;
        $product = Product::find($product_id);
        if ($product->product_attribute_id) {
            $attributes = ProductAttributeItem::where('product_attribute_id', $product->product_attribute_id)->get();
            foreach ($attributes as $attribute) {
                $attribute->attribute_options = unserialize($attribute->attribute_options);
                $attribute_type_key = Str::slug($attribute->title, '_') . $attribute->id;
                $attribute->attribute_type_key = $attribute_type_key;
                $show_on_title_key = 'show_on_title_' . $attribute->id;
                $show_on_sub_title_key = 'show_on_sub_title_' . $attribute->id;
                if (!empty($request->$attribute_type_key)) {
                    $attributes_request[] = (object)[
                        'id' => $attribute->id,
                        'title' => $attribute->title,
                        'input_type_id' => $attribute->input_type_id,
                        'attribute_type_id' => $attribute->attribute_type_id,
                        'data' => $request->$attribute_type_key,
                        'ordering' => $attribute->ordering,
                        'show_on_title' => $request->$show_on_title_key ? true : false,
                        'show_on_sub_title' => $request->$show_on_sub_title_key ? true : false
                    ];
                }
            }
        }
        if ($product->product_attributes) {
            $attributes = unserialize($product->product_attributes);
            foreach ($attributes as $attribute) {
                $attribute->attribute_options = $attribute->attribute_options;
                $attribute_type_key = Str::slug($attribute->title, '_') . $attribute->id;
                $attribute->attribute_type_key = $attribute_type_key;
                $show_on_title_key = 'show_on_title_' . $attribute->id;
                $show_on_sub_title_key = 'show_on_sub_title_' . $attribute->id;
                if (!empty($request->$attribute_type_key)) {
                    $attributes_request[] = (object)[
                        'id' => $attribute->id,
                        'title' => $attribute->title,
                        'input_type_id' => $attribute->input_type_id,
                        'attribute_type_id' => $attribute->attribute_type_id,
                        'data' => $request->$attribute_type_key,
                        'ordering' => $attribute->ordering,
                        'show_on_title' => $request->$show_on_title_key ? true : false,
                        'show_on_sub_title' => $request->$show_on_sub_title_key ? true : false
                    ];
                }
            }
        }
        $this->validate($request, [
            'title' => 'nullable|string',
            'sub_title' => 'nullable|string',
            'product_item_sku' => 'nullable|string',
            'sale_price' => 'required|numeric|between:0,99999999.99',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,99999999.99',
            'description' => 'nullable|string',
            'remark' => 'nullable|string',
            'stock_status' => 'nullable',
            'status' => 'nullable|boolean',
            'ordering' => 'nullable|numeric',
            'store_id' => 'nullable',
            'returnable' => 'required|boolean',
            'quantity' => 'required'
        ]);
        if (empty($attributes_request)) {
            $message = 'Please select attributes';
            return redirect()->route('admin.product.item.create', $product_id)->with('warning', $message);
        }
        if (!empty($attributes_request)) {
            $collection = collect($attributes);
            foreach ($attributes_request as $attribute_data) {
                $attributes_data = $collection->where('id', $attribute_data->id)->first();
                if ($attribute_data->input_type_id == 1) {
                    foreach ($attributes_data->attribute_options as $attribute_option_data) {
                        if ($attribute_option_data->id == $attribute_data->data) {
                            $attribute_data->data = $attribute_option_data;
                            break;
                        }
                    }
                    $attribute_array[] = $attribute_data;
                }
                if ($attribute_data->input_type_id == 2) {
                    $attributes_collection = collect($attributes_data->attribute_options);
                    foreach ($attribute_data->data as $key => $attribute_data_id) {
                        $attribute_data->data[$key++] = $attributes_collection->where('id', $attribute_data_id)->first();
                    }
                    $attribute_array[] = $attribute_data;
                }
            }
            $collection = collect($attribute_array);
        }
        if (!empty($product_id)) {
            $product_validator = Validator::make(['product_id' => $product_id], [
                'product_id' => 'required|exists:products,id',
            ]);
        } else {
            $product_validator = Validator::make(['product_id' => $request->product_id], [
                'product_id' => 'required|exists:products,id',
            ]);
        }
        if ($product_validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($product_validator)->with('warning', 'product not exist.');
        }
        $item = ProductItem::findOrFail($id);
        $item->product_id = $request->get('product_id') ?? $product_id;
        $item->title = $request->get('title');
        $item->sub_title = $request->get('sub_title');
        $item->product_item_sku = $request->get('product_item_sku');
        $item->location_id = $request->get('location_id');
        $item->sale_price = $request->get('sale_price');
        $item->discount_type = $request->get('discount_type');
        $item->discount_price = $request->get('discount_price');
        $item->description = $request->get('description');
        $item->item_attributes = serialize($collection);
        $item->box_type = $request->get('box_type');
        $item->stock_status = $request->get('stock_status');
        $item->status = $request->get('status') ?? 0;
        $item->ordering = $request->get('ordering') ?? 0;
        $item->store_id = $request->get('store_id') ?? 0;
        $item->returnable = $request->get('returnable') ?? 1;
        $item->quantity = $request->get('quantity');

        
        $item->update();
        $message = 'Product Item successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.product.items.index', $product_id)->with('success', $message);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductItem  $productItem
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = ProductItem::findOrFail($id);
        if ($item->product_item_image) {
            $get_folder_name = explode('/', $item->product_item_image);
            Storage::deleteDirectory($this->folderPath . $get_folder_name[0] . '/' . $get_folder_name[1] . '/' . $item->id);
        }
        if ($item->product_item_images) {
            $images = unserialize($item->product_item_images);
            foreach ($images as $image) {
                $get_folder_name = explode('/', $image->product_item_images);
                Storage::deleteDirectory($this->folderPath . $get_folder_name[0] . '/' . $get_folder_name[1] . '/' . $item->id);
                break;
            }
        }
        $item->delete();
        $message = '<strong>' . $item->product_item_sku . '</strong> delete successful';
        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);
    }
    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'file|mimes:jpg,jpeg,gif,png,svg,webp,mp4,avi,mov,wmv'
        ]);
        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }
        $item = ProductItem::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $imgName = $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName() . '/' . $item->id . '/';
        $request->$target->storeAs($this->folderPath . $folderName, $imgName);
        $item->$target = $folderName . '/' . $imgName;
        $item->update();
        $url = url('/storage/product_item_images/' . $folderName . '/' . $imgName);
        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }
    public function fileRemove($target, $id)
    {
        $item = ProductItem::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();
        return response()->json('success', 200);
    }
    public function filesUpload(Request $request, $target, $id)
    {
        $store_folder = $target;
        $file = $request->file($target);
        $file = $request->file($target);
        $maxSize = $this->maxFileSize;
if ($file ) {
    $mimeType = $file->getMimeType();
    // Define image and video MIME types
    $imageMimeTypes = ['image/jpeg', 'image/jpg', 'image/gif', 'image/png', 'image/svg+xml', 'image/webp'];
    $videoMimeTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska'];
    
    if (in_array($mimeType, $videoMimeTypes)) {
        $maxSize = $this->maxFileSizeVideo;
        $folderPath = $this->folderPathVideos;
    } else {
        $maxSize = $this->maxFileSize;
        $folderPath = $this->folderPath;
    }
    
}
$validator = Validator::make($request->all(), [
    $target => 'file|mimes:jpg,jpeg,gif,png,svg,webp,mp4,mov,avi,mkv|max:' . $maxSize,
]);
        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }
        $item = ProductItem::findOrFail($id);
        $imgName = $target . '_' . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName() . '/' . $item->id;
        $request->$target->storeAs($folderPath . $folderName, $imgName);
        $file_path = $folderName . '/' . $imgName;
        $image_info = (object)([
            'id' => uniqid(),
            'file_id' => uniqid(),
            $target => $file_path,
            'folder' => $this->folderName(),
            'file_path' => $file_path,
            'extension' => $file->extension(),
            'file_caption' => null,
            'file_order' => time(),
        ]);
        if ($item->$target) {
            $images = unserialize($item->$target);
            if (count($images) > 10) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  'upload limit end'
                    ],
                    200
                );
            }
            array_push($images, $image_info);
        } else {
            $images = array($image_info);
        }
        $item->$target = serialize($images);
        $item->update();
        $imagesCollection = collect(unserialize($item->$target));
        $images = $imagesCollection->sortBy('file_order');
        $route_image_info = route('admin.product.item.files.upload.image.info', [$target, $item->id]);
        foreach ($images as $image) {
            $image->route_image_remove = route('admin.product.item.files.remove', [$target, $image->file_id, $item->id]);
        }
        return view('admin.jquery_live.images', compact('images', 'item', 'store_folder', 'route_image_info'));
    }
    public function filesRemove($target, $image_id, $id)
    {
        $item = ProductItem::findOrFail($id);
      
        $images = unserialize($item->$target);
       
        foreach ($images as $key => $image) {
            if ($image->file_id == $image_id) {
               // dd($target . '/' . $image->file_path);
                Storage::delete('' . $target . '/' . $image->file_path);
                unset($images[$key]);
                break;
            }
        }
        if (count($images) < 0) {
            $item->$target = null;
        }
        $item->$target = serialize($images);
        $item->update();
        return response()->json('files_success', 200);
    }
    public function updateImageInfo(Request $request, $target, $id)
    {
        $store_folder = $target;
        $item = ProductItem::findOrFail($id);
        if ($request->action == 'get_info') {
            $images = unserialize($item->$target);
            foreach ($images as $image) {
                if ($image->file_id == $request->imageId) {
                    return response()->json($image);
                }
            }
        }
        $validator = Validator::make($request->all(), [
            'caption' => 'required|string',
            'file_ordering' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return response()->json(
                [
                    'status' => 'failed',
                    'message' =>  $validator->messages()
                ],
                200
            );
        }
        $images = unserialize($item->$target);
        foreach ($images as $image) {
            if ($image->file_id == $request->imageId) {
                $image->file_caption = $request->caption;
                $image->file_order = $request->file_ordering ?? 0;
            }
        }
        $item->$target = serialize($images);
        $item->update();
        $imagesCollection = collect(unserialize($item->product_item_images));
        $images = $imagesCollection->sortBy('file_order');
        $route_image_info = route('admin.product.item.files.upload.image.info', [$target, $item->id]);
        foreach ($images as $image) {
            $image->route_image_remove = route('admin.product.item.files.remove', [$target, $image->file_id, $item->id]);
        }
        return view('admin.jquery_live.images', compact('images', 'item', 'store_folder', 'route_image_info'));
    }
    public function getAttributes(Request $request, $attributes_id = null)
    {
        $product_id = $request->product_id ?? null;
        $attributes_id ?? Session::forget('product_attribute_options');
        $attributes_id = explode(',', $attributes_id);
        $items = ProductAttributeItem::whereIn('product_attribute_id', $attributes_id)->get();
        foreach ($items as $item) {
            $item->attribute_types = $this->attribute_types();
            $item->input_types = $this->input_types();
            $item->attribute_options = collect(unserialize($item->attribute_options))->sortBy('ordering');
        }
        // dd($items);
        Session::put([
            'product_attribute_options' => $items,
        ]);
        $attributes = array();
        if ($request->product_id) {
            $product = Product::find($request->product_id);
            $product_attributes = unserialize($product->product_attributes);
            if ($product_attributes) {
                $session_attributes = Session::get('product_attribute_options');
                $attributes = collect($product_attributes->merge($session_attributes))->paginate($this->itemPerPage);
            } else {
                $session_attributes = Session::get('product_attribute_options');
                $attributes = collect($session_attributes)->paginate($this->itemPerPage);
            }
        } else {
            $items = Session::get('product_attribute_options');
            $attributes = collect($items)->paginate($this->itemPerPage);
        }
        $sl = SLGenerator($attributes);
        $tab_sl = SLGenerator($attributes);
        if (count($attributes) > 0) {
            return view('admin.jquery_live.product_attribute_items', compact('attributes', 'sl', 'tab_sl', 'product_id'));
        }
    }
    public function getProductAttributes($product_id, $item_id = null)
    {
        if ($item_id == null) {
            $product = Product::find($product_id);
            if ($product->product_attribute_id) {
                $attributes = ProductAttributeItem::where('product_attribute_id', $product->product_attribute_id)->get();
            } else {
                $attributes = unserialize($product->product_attributes);
            }
            if (count($attributes) > 0) {
                foreach ($attributes as $attribute) {
                    if ($product->product_attribute_id) {
                        $attribute->attribute_options = $this->optionMeta(unserialize($attribute->attribute_options));
                    } else {
                        $attribute->attribute_options = $this->optionMeta($attribute->attribute_options);
                    }
                    $attribute->key = Str::slug($attribute->title, '_') . $attribute->id;
                }
            }
            return view('admin.jquery_live.product_attribute_options', compact('attributes', 'item_id'));
        }
        $item = ProductItem::find($item_id);
        $product = Product::find($product_id);
        $attributes = array();
        if ($product->product_attribute_id) {
            $attributes = ProductAttributeItem::where('product_attribute_id', $product->product_attribute_id)->get();
        } else {
            $attributes = unserialize($product->product_attributes);
        }
        if (count($attributes) > 0) {
            foreach ($attributes as $attribute) {
                if ($product->product_attribute_id) {
                    $attribute->attribute_options = $this->optionMeta(unserialize($attribute->attribute_options));
                } else {
                    $attribute->attribute_options = $this->optionMeta($attribute->attribute_options);
                }
                $attribute->key = Str::slug($attribute->title, '_') . $attribute->id;
            }
        }
        $item->attributes = collect($attributes)->sortBy('ordering');
        $item_attributes = unserialize($item->item_attributes);
        foreach ($item_attributes as $item_attribute) {
            if ($item_attribute->input_type_id == 1) {
                $selected_id[] = $item_attribute->data->id;
            }
            if ($item_attribute->input_type_id == 2) {
                $selected_id[] = $this->getSelectedID($item_attribute->data);
            }
        }
        $item->selected_id_array = explode(',', implode(',', $selected_id));
        return view('admin.jquery_live.product_attribute_options', compact('item', 'item_id'));
    }
    public function inventory_status($id, Request $request)
    {
        $ProductItem = ProductItem::find($id);
        $request->validate([
            'inventory_status' => 'required',
            'estimated_shipping_weeks' => 'nullable',
        ]);
        // dd($ProductItem);
        if ($request->inventory_status == 'unavailable') {
            $ProductItem->status = 0;
        } else {
            $ProductItem->status = 1;
        }
        $ProductItem->inventory_status = $request->inventory_status;
        $ProductItem->estimated_shipping_weeks = $request->estimated_shipping_weeks ?? 0;
        $ProductItem->save();
        return redirect('/admin/product/items')->with('message', 'Product Item updated successfully!');
    }
    
}
