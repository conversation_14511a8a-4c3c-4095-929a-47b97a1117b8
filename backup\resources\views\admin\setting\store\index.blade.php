@extends('layouts.admin')
@section('title',$admin_page_title)
@section('content')
<section id="sub-nav">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="block block-search clearfix">

                </div>
            </div>
            <div class="col-md-4">
                <div class="block sub-nav-utility clearfix">
                    <div class="float-right">
                        @if (permissions('promo','add'))
                        <a class="float-left btn btn-primary" href="{{ route('admin.stores.create') }}">New</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div id="items" class="block mt-0 pt-0 clearfix">
                    <table class="table table-bordered table-hover">
                        <tr>
                            @if ($items->count() > 0)
                            <th class="select">
                                <input type="checkbox" id="check_all">
                            </th>
                            @endif
                            <th class="sl">SL</th>
                            <th>Details</th>
                            <th class="order">Order</th>
                            <th class="verified">Status</th>
                            <th class="country">Location</th>
                            <th class="action">Action</th>
                        </tr>
                        @if ($items->count() > 0)
                            @foreach ($items as $value)
                                <tr id="row{{$value->id }}" class="{{$value->status ? '' : 'gray' }}">
                                    <td class="select">
                                        <input type="checkbox" class="check-item" id="check_item{{ $loop->index }}" name="pages[{{ $loop->index }}]" value="{{$value->id }}" form="manage-checked-form">
                                    </td>
                                    {{-- <td class="sl">{{ $sl }}</td> --}}
                                    <td class="sl">{{ $value->index_no }}</td>
                                    <td>{!! $value->details !!}</td>
                                    <td class="order">{{$value->ordering }}</td>
                                    <td class="verified">{{$value->status ? 'Enable' : 'Disable' }}</td>
                                    <td class="order">{{$value->country }}</td>
                                    <td class="action">
                                        @if (permissions('store','edit'))
                                        <a href="{{ route('admin.stores.edit',$value->id) }}" class="btn btn-primary btn-sm">Edit</a>
                                        @endif
                                        @if (permissions('store','delete'))
                                        <button  data-name="{{$value->title }}" data-url="{{ route('admin.stores.destroy',$value->id) }}" data-index="{{ route('admin.stores.index') }}" class="btn btn-danger btn-sm delete store-delete">Delete</button>
                                        @endif
                                    </td>
                                </tr>
                                @if(count($value->childStores) > 0)
                                    @foreach ( $value->childStores as $child_value)
                                    <tr>
                                        <td class="select">
                                            <input type="checkbox" class="check-item" id="check_item{{ $loop->index }}" name="pages[{{ $loop->index }}]" value="{{$child_value->id }}" form="manage-checked-form">
                                        </td>
                                        <td class="sl">{{ $child_value->index_no }}</td>
                                        <td>{!! $child_value->details !!}</td>
                                        <td class="order">{{$child_value->ordering }}</td>
                                        <td class="verified">{{$child_value->status ? 'Enable' : 'Disable' }}</td>
                                        <td class="order">{{$value->country }}</td>
                                        <td class="action">
                                            @if (permissions('store','edit'))
                                            <a href="{{ route('admin.stores.edit',$child_value->id) }}" class="btn btn-primary btn-sm">Edit</a>
                                            @endif
                                            @if (permissions('store','delete'))
                                            <button  data-name="{{$child_value->title }}" data-url="{{ route('admin.stores.destroy',$child_value->id) }}" data-index="{{ route('admin.stores.index') }}" class="btn btn-danger btn-sm delete store-delete">Delete</button>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                @endif
                                {{-- @php($sl++) --}}
                            @endforeach
                        @else
                        <tr>
                            <td class="text-center" colspan="6">
                                No {{ $admin_page_title }} added <a class="btn btn-primary btn-sm" href="{{ route('admin.stores.create') }}">Add New</a>
                            </td>
                        </tr>
                        @endif
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script>
    $(document).ready(function() {

    $(document).on('click', '.store-delete', function(e) {
        e.preventDefault();


        var storeName = $(this).data('name');
        var deleteUrl = $(this).data('url');
        var indexUrl = $(this).data('index');



            $.ajax({
                url: deleteUrl,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                        $(this).closest('tr').remove();
                        window.location.href = indexUrl;
                },
                error: function(xhr, status, error) {
                    // Handle the error
                    alert('There was an error with the request.');
                }
            });

    });
});
</script>
@endsection
