<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta name="csrf-token" content="{{ csrf_token() }}">
@if(site_info('favicon'))<link rel="icon" href="{{ asset('storage/setting/' . site_info('favicon')) }}">@endif
<title>@if(isset($page) && $page->meta_title){{ $page->meta_title }}@elseif(isset($page) && $page->title){{ $page->title }}@else @yield("title")@endif | {{ site_info('site_name') }}</title>
<meta name="title" content="{{@$page->meta_title}}" />
<meta name="description" content="{{ @$page->meta_description}}" />
<meta name="keywords" content="{{@$page->meta_keyword}}" />
<meta name="csrf-token" content="{{ csrf_token() }}">
{{-- <link type="text/css" href="{{ asset('css/style.css') }}" rel="stylesheet" media="screen"> --}}
<link type="text/css" rel="stylesheet" href="{{ asset('css/style.css') }}" media="screen">

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<script src="https://www.google.com/recaptcha/api.js?render=6LfG82gqAAAAAPeUFoeWx8ecEoze_KD3XKms-U5s"></script>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
{!! RecaptchaV3::initJs() !!}
@yield('heads')
