<?php

namespace App\Http\Controllers;

use App\Models\NotifyItem;
use App\Mail\EmailSchedule;
use App\Models\ProductItem;
use App\Mail\NotifyUserMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class NotifyItemController extends Controller
{
    public function store(Request $request)
    {

        // Validate the incoming request
        $validatedData = $request->validate([
            'notify_email' => 'required|email|max:255',
            'notify_phone' => 'required|string|max:15',
            'product_item_id'=>'required',
        ]);

        // Create a new NotifyItem record
        NotifyItem::create($validatedData);

        // Return a success response
        return response()->json(['message' => 'NotifyItem created successfully!'], 201);
    }
    public function getData(Request $request, $id)
    {

        $query = NotifyItem::where('product_item_id', $id);


        $notifyItems = $query->skip($request->start)
                             ->take($request->length)
                             ->get();

        $totalFiltered = $query->count();


        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalFiltered,
            'recordsFiltered' => $totalFiltered,
            'data' => $notifyItems,
        ]);
    }


    public function notifyByEmail(Request $request, $id){

       


        $notifyItem = NotifyItem::find($id);


        if (!$notifyItem) {
            return response()->json(['error' => 'NotifyItem not found'], 404);
        }
        switch (true) {
            case $request->status_email:
                $notifyItem->status_email=1;
                $productItem = ProductItem::find($notifyItem->product_item_id);
                if ($notifyItem->update()) {
                    Mail::to($notifyItem->notify_email)->send(new NotifyUserMail($productItem));
                    return response()->json(['sucess' => 'Email sent sucessfully'],200);
                } else {
                    return response()->json(['error' => 'Product Item not found for email'], 404);
                }
                break;
        
            case $request->status_phone:
                $notifyItem->status_phone=1;
                $productItem = ProductItem::find($notifyItem->product_item_id);
                if ($notifyItem->update()) {
                    $message = 'This Item is restocked: ' . $productItem->title;
                   
                    return app('App\Http\Controllers\Controller')->sendMessage($message, $notifyItem->notify_phone);
                    
                } else {
                    return response()->json(['error' => 'Product Item not found for SMS'], 404);
                }
                break;
        
            default:
                return response()->json(['error' => 'No valid notification type provided'], 400);
        }
        
      
     
        



}
}