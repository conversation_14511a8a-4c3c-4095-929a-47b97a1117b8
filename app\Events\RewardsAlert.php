<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class RewardsAlert
{
    use SerializesModels;

    public $reward;
    public $user;
    public $message_status;
    public $template;

    public function __construct($reward, $user, $message_status, $template)
    {
        $this->reward = $reward;
        $this->user = $user;
        $this->message_status = $message_status;
        $this->template = $template;
    }
}
