<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionMethodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transaction_methods', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('method_key');
            $table->string('sub_title')->nullable();
            $table->string('public_key');
            $table->string('private_key');
            $table->text('transaction_method_image')->nullable();
            $table->integer('discount_type')->nullable();
            $table->decimal('discount_price', 20, 2)->nullable();
            $table->string('country')->nullable();
            $table->string('allowed_currency')->nullable();
            $table->integer('ordering')->default(0);
            $table->boolean("status")->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transaction_methods');
    }
}
