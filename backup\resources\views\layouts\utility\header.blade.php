@php
    
    $setting = App\Models\Setting::where('country', $page->location_data->country??'US')->where('status',true)->first();
    //dd($setting);
@endphp

<div class="global-loader">
    <div class="loader active">
        <div class="grid-loader">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
</div>
@include('web.utility.header_content')

<section id="mobile_menu_area" class="mobile-menu-area">
    <div class="mobile-menu-close-area"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-9 mobile-col main-mobile-menu">

                <div class="pages" id="pages">
                    @include('layouts.utility.menu.menu_mobile')
                    {{-- <div class="page">
                        <div class="block menu-accordion-mobile">
                            <div class="menu-accordion-mobile-inner">
                                <ul class="main-mobile-menu">
                                @include('layouts.utility.menu.menu_mobile')
                                </ul>
                            </div>
                        </div>
                        <div class="block bottom-block p-0 m-0 d-xxl-none">
                            <ul class="user-area-mobile-list">
                                <li><a data-bs-toggle="modal" data-bs-target="#userLoginModal" href="javascript:void(0)"><img src="media/images/icon/login.svg" alt=""><span>Login</span></a></li>
                                <li><a href="http://www.1guygadget.com"><i class="fa-solid fa-right-left"></i><span>Sell | Trade-in</span></a></li>
                                <li>
                                    <form action="#">
                                        <div class="input-group mb-3">
                                            <input type="text" class="form-control" id="floatingInputGroup1" placeholder="Search">
                                            <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                                        </div>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div> --}}
                    {{-- <div class="page" >
                        <div class="block menu-accordion-mobile sub-page-menu">
                            <input type="hidden" id="child_parent_active" value="">
                            <h3 id="child_parent_title">
                                <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                                    <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                                </svg>
                                <span></span>
                            </h3>
                            <div class="menu-accordion-mobile-shadow">
                                <div class="menu-accordion-mobile-inner">
                                    <ul id="child_mobile_menus" class="child-mobile-menus"></ul>
                                </div>
                            </div>
                        </div>
                    </div> --}}
                </div>
            </div>
            <div class="col-3 col-md-4 position-relative">
                <div class="block ms-0 ps-0">
                    <p class="text-right"><img class="standalone-logo-icon" src="{{ url('images/icon/gold_logo.svg') }}"
                            alt=""></p>
                    <p><a class="close-slide-menu" id="close_slide_mobile_menu"
                            href="javascript:void(0)"><strong>Close</strong></a></p>
                </div>
            </div>
        </div>
    </div>
</section>

<section id="slide_menu_area" class="slide-menu-area d-none d-md-block">
    <div class="slide-menu-close-area"></div>
    <div class="container-fluid">
        <div class="row justify-content-between">
            <div class="col-9 col-md-4">
                <div class="block menu-accordion">
                    @include('layouts.utility.menu.menu')
                </div>
            </div>
            <div class="col-md-4 child-menu-col d-flex align-items-center">
                <div class="block child-menu clearfix">
                    <div id="child_items" class="child-items custom-scrollbar scrollbar-macosx"></div>
                </div>
            </div>
            <div class="col-3 col-md-4 position-relative">
                <div class="block ms-0 ps-0">
                    <p class="text-right"><img class="standalone-logo-icon" src="{{ url('images/icon/gold_logo.svg') }}"
                            alt=""></p>
                    <p><a class="close-slide-menu" id="close_slide_menu"
                            href="javascript:void(0)"><strong>Close</strong></a></p>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="animate-header">
    <div class="container-fluid">
        <div class="row align-items-center header-elements">
            <div class="col-8 col-md-5 col-xl-3">
                <div class="block logo py-0 logo">
                    <a href="{{ url('/') }}">
                        @if (site_info('site_logo'))
                            <img class="d-none d-md-block"
                                src="{{ asset('storage/setting/' . site_info('site_logo')) }}"
                                alt="{{ site_info('site_name') }}">
                            <img class="d-md-none" src="{{ asset('storage/setting/' . site_info('site_icon_logo')) }}"
                                alt="{{ site_info('site_name') }}">
                        @else
                            <h1>{{ site_info('site_name') }}</h1>
                            @if (site_info('site_slogan'))
                                <p>{{ site_info('site_slogan') }}</p>
                            @endif
                        @endif
                    </a>
                </div>
            </div>
            <div class="col-md-6 col-xl-7 d-none d-xl-inline-block">
                <div class="position-relative">
                    <div class="block py-0 main-menu" id="main_menu">

                    </div>
                    <div class="block search-area clearfix w-75 m-auto" id="search_area_section">
                        <form action="#">
                            <div class="input-group">
                                <input type="text" class="form-control autocomplete" id="site_animate_search"
                                    placeholder="Search">
                                {{-- <span class="input-group-text text-uppercase btn btn-design btn-dark"><strong>Go</strong></span> --}}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-4 col-md-7 col-xl-2">
                <div class="block py-0 my-0 user-area clearfix">
                    <ul class="d-flex align-items-center float-right">
                        <li class="sell-trade-in d-none d-xl-inline pr-0">
                               <a type="button"
                            target="{{ isset($setting) ? '_blank' : '' }}"
                            href="{{ isset($setting) ? $setting->site_link : 'javascript:void(0);' }}"
                            class="{{ isset($setting) ? '' : 'disabled' }}"
                            style="{{ isset($setting) ? '' : 'pointer-events: none;' }}">
                             <span class="badge rounded-pill {{ isset($setting) ? '' : 'disabled' }}"
                                   style="{{ isset($setting) ? '' : 'pointer-events: none; opacity: 0.5;' }}">
                                 Sell | Trade-in
                             </span>
                         </a>
                        </li>
                        <li class="d-none d-xl-inline-block no-line">
                            <a class="search-item" id="search_area" href="javascript:void(0)">
                                <img src="{{ asset('images/icon/search_icon.svg') }}" alt="">
                                <img class="active" src="{{ asset('images/icon/search_icon.svg') }}" alt="">
                            </a>
                        </li>
                        @if (Session::get('shop_logged_data'))
                            <li class="shop-country no-line">
                                <a data-toggle="modal" data-page_url="{{ url()->current() }}"
                                    data-target="#locationModal" href="javascript:void(0)">
                                    <span class="shop-country-text" >SHIP TO </span>
                                    <span><img id="shop_country_image"
                                            src="{{ asset('images/flags/' . strtolower($page->location_data->country) . '.png') }}"
                                            alt="{{ $page->location_data->country_short_code }}"> {{ $page->location_data->country }}
                                        </span>
                                </a>
                            </li>
                        @endif
                        <li class="sep-none shopping-cart-item">

                            <a id="shopping_cart"
                                class="@if (Session::has('cart')) have-item @endif shopping-cart-click"
                                href="javascript:void(0)">
                                <img src="{{ asset('images/icon/cart.svg') }}" alt="">
                                <img src="{{ asset('images/icon/cart_full.svg') }}" alt="">
                                <span class="cart-count cart_count @if (!Session::has('cart')) d-none @endif">
                                    @if (Session::has('cart'))
                                        {{ $page->cart->quantity }}
                                    @endif
                                </span>
                            </a>
                        </li>
                        @auth
                            <li class="d-none d-xl-inline no-line user-custom-menu active">
                                <a href="javascript:void(0)">
                                    <img src="{{ asset('images/icon/login.svg') }}" alt="">
                                    <img class="hover-icon" src="{{ asset('images/icon/login_active.svg') }}"
                                        alt="">
                                </a>
                                <div class="dropdown-user-area">
                                    <ul>
                                        <li><a class="user-meta" href="{{ $page->my_profile }}">DASHBOARD<span>Hi,
                                                    {{ trim_text(auth()->user()->first_name . ' ' . auth()->user()->last_name, 20) }}</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('logout') }}"
                                                onclick="event.preventDefault(); document.getElementById('animate_logout_form').submit();">
                                                Logout
                                                <form id="animate_logout_form" action="{{ route('logout') }}"
                                                    method="POST" style="display: none;">
                                                    {{ csrf_field() }}
                                                </form>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        @else
                            @if (Session::has('cart'))
                                <li class="d-none d-xl-inline no-line user-custom-menu">
                                    <a data-toggle="modal" data-title="Customer Login"
                                        data-page_url="{{ url()->current() }}" data-target="#userLoginModal"
                                        href="javascript:void(0)">
                                        <img src="{{ asset('images/icon/login.svg') }}" alt="">
                                        <img class="hover-icon" src="{{ asset('images/icon/login_active.svg') }}"
                                            alt="">
                                    </a>
                                </li>
                            @else
                                <li class="d-none d-xl-inline no-line user-custom-menu">
                                    <a data-toggle="modal" data-title="Customer Login" data-target="#userLoginModal"
                                        href="javascript:void(0)">
                                        <img src="{{ asset('images/icon/login.svg') }}" alt="">
                                        <img class="hover-icon" src="{{ asset('images/icon/login_active.svg') }}"
                                            alt="">
                                    </a>
                                </li>
                            @endif
                        @endauth

                        <li>
                            <a id="toggle_menu_mobile" class="toggle-menu-mobile d-xl-none"
                                href="javascript:void(0)">
                                <img src="{{ asset('/images/icon/menu_c.svg') }}" alt="mobile_menu_icon" />
                            </a>

                            <a id="toggle_menu" class="toggle-menu d-none d-xl-block" href="javascript:void(0)">
                                <img src="{{ asset('/images/icon/menu_c.svg') }}" alt="menu_icon">
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="shopping-cart-section">
    <div class="container-fluid">
        <div class="row justify-content-end">
            <div class="col-md-5">
                <div class="block shopping-cart">
                    <a class="close-shopping-cart" href="#"><i class="fa-solid fa-xmark"></i></a>
                    <h4 class="text-uppercase"><strong>Shopping Cart</strong></h4>

                           
                    <div class="shopping_cart_items"  id="shopping-cart-item">
                        @if (!empty($page->cart->items) && count($page->cart->items) > 0)

                            <div class="shopping-cart-buttons pb-3">
                                <p class="mb-0">
                                    <a class="text-uppercase btn btn-design btn-dark"
                                        href="{{ $page->buy_now_url }}">
                                        <strong>Checkout</strong>
                                    </a>
                                    <a class="text-uppercase btn btn-design btn-light"
                                        href="{{ $page->view_cart_url }}">
                                        <strong>View Cart</strong>
                                    </a>
                                </p>

                            </div>
                            <div class="shopping-cart-item-list clearfix">
                                <div class="custom-scrollbar scrollbar-macosx clearfix">
                                    @foreach ($page->cart->items as $value)
                                     
                                            <div class="product-modal-data">
                                                <div class="image-holder">
                                                    @if ($value->product_item_image)
                                                        <img src="{{ $value->product_item_image }}"
                                                            alt="{{ $value->product_title }}">
                                                    @elseif ($value->product_image)
                                                        <img src="{{ $value->product_image }}"
                                                            alt="{{ $value->product_title }}">
                                                    @else
                                                        <img src="{{ asset('images/placeholder.png') }}"
                                                            alt="Placeholder Image">
                                                    @endif
                                                </div>
                                                <div class="order-item-meta">
                                                    <p class="mb-0">
                                                        <strong class="title">{{ $value->product_title }}</strong>
                                                        @if ($value->title)
                                                            <br>{{ $value->title }} - ({{ $value->condition }})
                                                        @endif
                                                        <br>
                                                        QTY: {{ $value->quantity }}
                                                        <br>Price: {{ getDefaultCurrencySymbol() . number_format($value->unit_total_price, 2) }}
                                                        @if ($value->local_price > 0)
                                                            <span
                                                                class="pl-3">{{ getDefaultCurrencySymbol() . $value->local_price }}</span>
                                                        @endif
                                                    </p>
                                                    <p class="sku-info mb-0">
                                                        <small>{!! $value->sku_details !!}</small>
                                                    </p>
                                                </div>
                                            </div>
                                        
                                    @endforeach
                                </div>

                            </div>
                        @else
                            <h5>Your cart is empty</h5>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>