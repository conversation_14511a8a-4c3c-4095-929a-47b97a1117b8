<?php

namespace App\Listeners;

use App\Events\AccountRegistered;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendAccountRegisteredNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(AccountRegistered $event)
    {
        $this->notifier->sendCustomerAccountRegisteredNotification($event->user);
    }
}
