<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrderRefundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_refunds', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->cascadeOnUpdate();
            $table->foreignId('refund_by_user_id')->nullable()->constrained('users')->cascadeOnUpdate();
            $table->foreignId('order_id')->nullable()->constrained('orders')->cascadeOnUpdate();
            $table->decimal('amount', 20, 2)->nullable();
            $table->string('sku')->unique();
            $table->longText('refund_reason');
            $table->longText('refund_images')->nullable();
            $table->ipAddress('ip')->nullable();
            $table->integer('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_refunds');
    }
}
