<?php

namespace App\Http\Controllers\Admin;

use App\Models\CountryMessageSetting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CountryMessageSettingController extends Controller
{
    public function index()
    {
        $admin_page_title = 'SMS Settings';
        $settings = CountryMessageSetting::all();
        return view('admin.country-message-settings.index', compact('settings', 'admin_page_title'));
    }

    public function create()
    {
    
        $admin_page_title = 'Create';
        $countriesList = $this->getCountryList();
        return view('admin.country-message-settings.create', compact('admin_page_title', 'countriesList'));
    }

    public function store(Request $request)
    {
       //dd($request->all());
        $request->validate([
            'country_code' => 'required|string|max:5|unique:country_message_settings,country_code',
            'mode' => 'required|in:otp,reply',
            'twilio_sms_from' => 'nullable|string',
        ]);

        CountryMessageSetting::create($request->all());

        return redirect()->route('admin.country-message-settings.index')->with('success', 'Country setting created successfully.');
    }

    public function edit(CountryMessageSetting $countryMessageSetting)
{
    $admin_page_title = 'Edit';

    $countriesList = $this->getCountryList();

    return view('admin.country-message-settings.edit', compact(
        'admin_page_title',
        'countryMessageSetting',
        'countriesList'
    ));
}


    public function update(Request $request, CountryMessageSetting $countryMessageSetting)
    {
        $request->validate([
            'country_code' => 'required|string|max:5|unique:country_message_settings,country_code,' . $countryMessageSetting->id,
            'mode' => 'required|in:otp,reply',
            'twilio_sms_from' => 'nullable|string',
        ]);

        $countryMessageSetting->update($request->all());

        return redirect()->route('admin.country-message-settings.index')->with('success', 'Country setting updated.');
    }

    public function destroy(CountryMessageSetting $countryMessageSetting)
    {
        $countryMessageSetting->delete();

        return redirect()->route('admin.country-message-settings.index')->with('success', 'Country setting deleted.');
    }
}
