<?php

namespace App\Http\Controllers\Admin;

use Session;
use Validator;
use App\Models\UserBilling;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class UserBillingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($user_id = null, $type=null)
    {
        $checkout_order = Session::get('checkout_order');
        if (empty($user_id)) {
            $user_billings = collect(Session::get('user_billings'));
            
        } else {
            $user_billings = UserBilling::where('user_id', $user_id)
                    ->orderByDesc('primary')
                    ->get();
            $session_user_billings = collect(Session::get('user_billings'))->where('user_id', $user_id);
            $user_billings = collect($user_billings->merge($session_user_billings));
        }
        if (count($user_billings) > 0) {
            return view('admin.jquery_live.user_billings', compact('user_billings', 'user_id', 'type', 'checkout_order'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
     public function store(Request $request, $user_id = null)
    {
        // dd($request->all());
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:user_billings',
            "tel_cell" => "required|string|max:20",
            'city' => 'required|string',
            'state' => 'required|string',
            'country' => 'required|string',
            'postal_code' => 'required|integer',
            'address' => 'required|string',
            'address_2' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $telCell = $request->get('tel_cell');
        $telCellCleaned = preg_replace('/\D/', '', $telCell);
       
            $item = new UserBilling;
            $item->user_id = $user_id;
            $item->first_name = $request->get('first_name');
            $item->last_name = $request->get('last_name');
            $item->email = $request->get('email');
            $item->tel_cell = $telCellCleaned;

            $item->company_name = $request->get('company_name');
            $item->country = $request->get('country');
            $item->postal_code = $request->get('postal_code');
            $item->city = $request->get('city');
            $item->state = $request->get('state');
            $item->address = $request->get('address');
            $item->address_2 = $request->get('address_2');
            $item->primary = $request->get('primary') ?? 0;
            $item->save();

            $message = '<strong>' . $item->first_name . '</strong> added successful';

            return response()->json([
                'message' => $message,
            ], 200);
        

        $item = (object)([
            'id' => uniqid(),
            'user_id' => $user_id,
            'first_name' => $request->get('first_name'),
            'last_name' => $request->get('last_name'),
            'email' =>  $request->get('email'),
            'tel_cell' => $telCellCleaned,
            'company_name' =>  $request->get('company_name'),
            'country' =>  $request->get('country'),
            'postal_code' =>  $request->get('postal_code'),
            'city' =>  $request->get('city'),
            'state' =>  $request->get('state'),
            'address' =>  $request->get('address'),
            'address_2' =>  $request->get('address_2'),
            'primary' => $request->get('primary') ?? 0,
        ]);

        if (Session::has('user_billings')) {
            $user_billings = Session::get('user_billings');
            $user_billings_collect = collect($user_billings);
            if (!empty($user_billings_collect)) {
                $items = $user_billings_collect->toArray();
                if (count($items) > 10) {
                    return response()->json(
                        [
                            'message' =>  'limit end'
                        ],
                        400
                    );
                }
                array_push($items, $item);
            }
        } else {
            $items = array($item);
        }

        $user_billings_items = collect($items);

        Session::put([
            'user_billings' => $user_billings_items,
        ]);

        $message = '<strong>' . $item->first_name . '</strong> added successful';

        return response()->json([
            'message' => $message,
        ], 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserBilling  $userBilling
     * @return \Illuminate\Http\Response
     */
    public function show(UserBilling $userBilling)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\UserBilling  $userBilling
     * @return \Illuminate\Http\Response
     */
    public function edit($id, $user_id = null)
    {
        if ($user_id) {
            $user_billings = DB::table('user_billings')->where('user_id', $user_id)->get();
            $session_user_billings = collect(Session::get('user_billings'))->where('user_id', $user_id);
            $item = collect($user_billings->merge($session_user_billings))->where('id', $id)->first();
        } else {
            $user_billings = collect(Session::get('user_billings'));
            $item = collect($user_billings)->where('id', $id)->first();
        }

        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserBilling  $userBilling
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id, $user_id = null)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            // "email" => "nullable|email | unique:user_billings,email" . $id,
            "tel_cell" => "required|string|max:20",
           
            'city' => 'required|string',
            'state' => 'required|string',
            'country' => 'required|string',
            'postal_code' => 'required|integer',
            'address' => 'required|string',
            'address_2' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }

        $user_billings_items = collect(Session::get('user_billings'));


        if ($user_id) {
            $item = UserBilling::findOrFail($id);
        } else {
            $item = collect($user_billings_items)->where('id', $id)->first();
        }
        $item->user_id = $user_id;
        $item->first_name = $request->get('first_name');
        $item->last_name = $request->get('last_name');
        $item->email = $request->get('email');
        $item->tel_cell = $request->get( 'tel_cell');

        $item->company_name = $request->get('company_name');
        $item->country = $request->get('country');
        $item->postal_code = $request->get('postal_code');
        $item->city = $request->get('city');
        $item->state = $request->get('state');
        $item->address = $request->get('address');
        $item->address_2 = $request->get('address_2');
        $item->primary = $request->get('primary') ?? 0;
        if ($user_id) {
            $item->save();
        }

        $user_billings = DB::table('user_billings')->where('user_id', $user_id)->get();
        foreach ($user_billings as $user_billing) {
            $user_billing = UserBilling::findOrFail($user_billing->id);
            $user_billing->primary = 0;
            $user_billing->update();
            if ($user_billing->id == $item->id) {
                $user_billing->primary = $request->get('primary') ?? 0;
                $user_billing->update();
            }
        }

        $message = '<strong>' . $item->first_name . '</strong> update successful';

        Session::put([
            'user_billings' => $user_billings_items,
        ]);

        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UserBilling  $userBilling
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, $user_id = null)
    {
        if ($user_id && !Session::has('user_billings')) {
            $item = UserBilling::findOrFail($id);
            $message = '<strong>' . $item->first_name . '</strong> update successful';
            $item->delete();

            return response()->json([
                'message' => $message,
                'status' => 'success',
            ], 200);
        }

        if (Session::has('user_billings')) {
            $user_billings_items = collect(Session::get('user_billings'));
            $item = collect($user_billings_items)->where('id', $id)->first();
            $message = '<strong>' . $item->first_name . '</strong> update successful';

            foreach ($user_billings_items as $key => $value) {
                if ($value->id == $id) {
                    unset($user_billings_items[$key]);
                    break;
                }
            }

            Session::put([
                'user_billings' => $user_billings_items,
            ]);
        }

        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);
    }
}
