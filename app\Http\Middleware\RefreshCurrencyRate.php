<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Adjustment;
use Carbon\Carbon;
use App\Http\Controllers\Controller;

class RefreshCurrencyRate
{
    public function handle(Request $request, Closure $next)
    {
        if (Session::has('shop_logged_data')) {
            $site_data = Session::get('shop_logged_data');

            // dd($site_data);
            //$location_data = $request->ipinfo->all;
            $country =  $site_data->country;

            if ($country) {
                $controller = new Controller();
                $toCurrency = $controller->getLocalCurrencyCode($country);
                $fromCurrency = getDefaultCurrencyCode();
                if ($toCurrency) {
                    $adjustment = \App\Models\CurrencyAdjustment::where('from_currency', $fromCurrency)
                        ->where('to_currency', $toCurrency)
                        ->latest('updated_at')
                        ->first();
                    if ($adjustment) {

                        $lastSessionUpdate = isset($site_data->rate_adjustment_last_updated)
                            ? Carbon::parse($site_data->rate_adjustment_last_updated)
                            : null;

                        // If adjustment changed, update currency rate
                        if (!$lastSessionUpdate || $lastSessionUpdate < $adjustment->updated_at) {
                            //  dd('asjustment needed');
                            $rate = $controller->getLocalCurrencyRate($country);
                            $site_data->currency_rate = $rate;
                            $site_data->rate_adjustment_last_updated = $adjustment->updated_at;
                            Session::put('shop_logged_data', $site_data);
                        } else {
                        }
                    } else {
                        $rate = $controller->getLocalCurrencyRate($country);
                        $site_data->currency_rate = $rate;
                        Session::put('shop_logged_data', $site_data);
                    }
                }
            }
        }

        return $next($request);
    }
}
