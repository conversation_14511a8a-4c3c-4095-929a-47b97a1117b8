<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TransactionMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;

class TransactionMethodController extends Controller
{
    private $folderPath = 'transaction_methods/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Transaction Methods';

        $items = TransactionMethod::orderBy('transaction_methods.id', 'desc')->paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.transaction_methods', compact('sl', 'items'));
        }

        return view('admin.setting.transaction_method.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Transaction Method';
        $transaction_method_image_info = Session::get('transaction_method_image');
        $countries = $this->countries();
        $currencies = $this->currenciesList();
        return view('admin.setting.transaction_method.create', compact('transaction_method_image_info', 'countries', 'currencies','admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string',
            'method_key' => 'required|string',
            'sub_title' => 'nullable|string',
            'public_key' => 'required|string',
            'private_key' => 'required|string',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'country*' => 'nullable|string',
            'allowed_currency*' => 'nullable|string',
            'ordering' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        $item = new TransactionMethod;
        $item->title = $request->get('title');
        $item->method_key = $request->get('method_key');
        $item->sub_title = $request->get('sub_title');
        $item->public_key = $request->get('public_key'); // example - IP-BL-128-678
        $item->private_key = $request->get('private_key');
        $item->discount_type = $request->get('discount_type');
        $item->discount_price = $request->get('discount_price');
        $item->country = implode(',', $request->get('country'));
        $item->allowed_currency = implode(',', $request->get('allowed_currency'));
        $item->status = $request->get('status') ?? 0;
        $item->ordering = $request->get('ordering') ?? 0;
        $item->save();

        $item = TransactionMethod::findOrFail($item->id);
        $transaction_method_image_info = Session::get('transaction_method_image');
        if ($transaction_method_image_info) {
            $image_name = 'transaction_method_image_' . rand(99, 999999999) . '.' . $transaction_method_image_info->extension;
            $folderName = $this->folderName() . '/' . $item->id . '/';
            $item->transaction_method_image = $folderName.'/' . $image_name;
            Storage::move($transaction_method_image_info->transaction_method_image, $this->folderPath . $folderName.'/' . $image_name);
            Session::forget('transaction_method_image');
        }
        $item->update();

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.transaction_methods.index'))->with('success', 'Transaction Method created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\TransactionMethod  $transactionMethod
     * @return \Illuminate\Http\Response
     */
    public function show(TransactionMethod $transactionMethod)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\TransactionMethod  $transactionMethod
     * @return \Illuminate\Http\Response
     */
    public function edit(TransactionMethod $transactionMethod)
    {
        $admin_page_title = 'Edit Transaction Method';
        $item = $transactionMethod;
        $item->countries = $this->countries();
        $item->currencies = $this->currenciesList();
        return view('admin.setting.transaction_method.edit', compact('item', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\TransactionMethod  $transactionMethod
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, TransactionMethod $transactionMethod)
    {
        $this->validate($request, [
            'title' => 'required|string',
            'method_key' => 'required|string',
            'sub_title' => 'nullable|string',
            'public_key' => 'required|string',
            'private_key' => 'required|string',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'country*' => 'nullable|string',
            'allowed_currency*' => 'nullable|string',
            'ordering' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        $item = $transactionMethod;
        $item->title = $request->get('title');
        $item->method_key = $request->get('method_key');
        $item->sub_title = $request->get('sub_title');
        $item->public_key = $request->get('public_key');
        $item->private_key = $request->get('private_key');
        $item->discount_type = $request->get('discount_type');
        $item->discount_price = $request->get('discount_price');
        $item->country = implode(',', $request->get('country'));
        $item->allowed_currency = implode(',', $request->get('allowed_currency'));
        $item->status = $request->get('status') ?? 0;
        $item->ordering = $request->get('ordering') ?? 0;
        $item->update();

        $message = 'Transaction Method successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.transaction_methods.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\TransactionMethod  $transactionMethod
     * @return \Illuminate\Http\Response
     */
    public function destroy(TransactionMethod $transactionMethod)
    {
        $item = TransactionMethod::findOrFail($transactionMethod->id);
        $this->deleteFile($this->folderPath, $item->transaction_method_image);
        $item->delete();

        // $items = TransactionMethod::paginate($this->itemPerPage);
        // $sl = SLGenerator($items);

        $message = '<strong>' . $item->title . '</strong> delete successful';
        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);

        // return view('admin.jquery_live.transaction_methods', compact('items', 'sl'));
    }

    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }

        $item = TransactionMethod::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target);

        $imgName = $item->slug . '_' . $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath . '/' . $folderName, $imgName);

        $item->$target = $folderName . '/' . $imgName;
        $item->update();

        $url = url('/storage/transaction_methods/' . $folderName . '/' . $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileRemove($target, $id)
    {
        $item = TransactionMethod::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();

        return response()->json('success', 200);
    }
}
