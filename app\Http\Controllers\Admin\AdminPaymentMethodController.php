<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AdminPaymentMethodController extends Controller
{
    /**
     * Display admin payment methods page
     */
    public function index(Request $request)
    {
        $query = PaymentMethod::query();
        $admin_page_title = 'Payment Methods';
        // Apply filters
        if ($request->has('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }

        if ($request->has('status') && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->has('search') && !empty($request->search)) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                    ->orWhere('subtitle', 'like', '%' . $request->search . '%')
                    ->orWhere('name', 'like', '%' . $request->search . '%');
            });
        }

        $paymentMethods = $query->orderBy('sort_order')->orderBy('title')->paginate(20);

        // Get statistics
        $stats = [
            'total' => PaymentMethod::count(),
            'active' => PaymentMethod::where('is_active', true)->count(),
            'inactive' => PaymentMethod::where('is_active', false)->count(),
            'traditional' => PaymentMethod::where('type', 'traditional')->count(),
            'bnpl' => PaymentMethod::where('type', 'bnpl')->count(),
            'crypto' => PaymentMethod::where('type', 'crypto')->count(),
        ];

        return view('admin.payment-methods.index', compact('paymentMethods', 'stats', 'admin_page_title'));
    }

    /**
     * Show form for creating new payment method
     */
    public function create()
    {
        $countries = $this->getCountriesList();
        $currencies = $this->getCurrenciesList();
        $admin_page_title = 'Create a method';

        return view('admin.payment-methods.create', compact('countries', 'currencies', 'admin_page_title'));
    }

    /**
     * Store new payment method
     */
    public function store(Request $request)
    {
        $validator = $this->getValidationRules($request);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $this->preparePaymentMethodData($request);

            // Handle logo upload
            if ($request->hasFile('logo')) {
                $data['logo_url'] = $this->handleLogoUpload($request->file('logo'));
            }

            $paymentMethod = PaymentMethod::create($data);

            DB::commit();

            return redirect()->route('admin.payment-methods.index')
                ->with('success', 'Payment method created successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating payment method: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to create payment method: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show specific payment method
     */
    public function show($id)
    {
        $paymentMethod = PaymentMethod::findOrFail($id);
        $admin_page_title = $paymentMethod->title;

        // Get recent transactions
        // $recentTransactions = Transaction::where('payment_method_id', $id)
        //     ->with('order')
        //     ->orderBy('created_at', 'desc')
        //     ->limit(10)
        //     ->get();
        $recentTransactions = [];
        // Get transaction statistics
        $transactionStats = $this->getTransactionStats($id);

        return view('admin.payment-methods.show', compact(
            'paymentMethod',
            'recentTransactions',
            'transactionStats',
            'admin_page_title'
        ));
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $paymentMethod = PaymentMethod::findOrFail($id);
        $countries = $this->getCountriesList();
        $currencies = $this->getCurrenciesList();
        $admin_page_title = 'Edit ' . $paymentMethod->title;

        return view('admin.payment-methods.edit', compact(
            'paymentMethod',
            'countries',
            'currencies',
            'admin_page_title'
        ));
    }

    /**
     * Update payment method
     */
    public function update(Request $request, $id)
    {
        $paymentMethod = PaymentMethod::findOrFail($id);

        $validator = $this->getValidationRules($request, $id);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $this->preparePaymentMethodData($request);

            // Handle logo upload
            if ($request->hasFile('logo')) {
                // Delete old logo
                if ($paymentMethod->logo_url) {
                    $this->deleteOldLogo($paymentMethod->logo_url);
                }

                $data['logo_url'] = $this->handleLogoUpload($request->file('logo'));
            }

            $paymentMethod->update($data);

            DB::commit();

            return redirect()->route('admin.payment-methods.show', $paymentMethod->id)
                ->with('success', 'Payment method updated successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating payment method: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to update payment method: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Delete payment method
     */
    public function destroy($id)
    {
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);

            // Check if method has transactions
            $hasTransactions = Transaction::where('payment_method_id', $id)->exists();

            if ($hasTransactions) {
                return redirect()->back()
                    ->with('error', 'Cannot delete payment method with existing transactions. Deactivate it instead.');
            }

            // Delete logo file
            if ($paymentMethod->logo_url) {
                $this->deleteOldLogo($paymentMethod->logo_url);
            }

            $paymentMethod->delete();

            return redirect()->route('admin.payment-methods.index')
                ->with('success', 'Payment method deleted successfully!');
        } catch (\Exception $e) {
            Log::error('Error deleting payment method: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to delete payment method: ' . $e->getMessage());
        }
    }

    /**
     * Toggle payment method status
     */
    public function toggleStatus($id)
    {
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);

            $paymentMethod->update([
                'is_active' => !$paymentMethod->is_active
            ]);

            $status = $paymentMethod->is_active ? 'activated' : 'deactivated';

            return redirect()->back()
                ->with('success', "Payment method {$status} successfully!");
        } catch (\Exception $e) {
            Log::error('Error toggling payment method status: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to update payment method status.');
        }
    }

    /**
     * Test payment method connection
     */
    public function testConnection($id)
    {
        try {
            $paymentMethod = PaymentMethod::findOrFail($id);

            // Implement connection testing based on payment method type
            $result = $this->performConnectionTest($paymentMethod);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Connection test successful!',
                    'details' => $result['details']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Connection test failed: ' . $result['error'],
                    'details' => $result['details']
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Connection test error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get transactions for payment method
     */
    public function getTransactions(Request $request, $id)
    {
        $paymentMethod = PaymentMethod::findOrFail($id);

        $query = Transaction::where('payment_method_id', $id)->with('order');

        // Apply filters
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->has('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(50);

        return view('admin.payment-methods.transactions', compact(
            'paymentMethod',
            'transactions'
        ));
    }

    /**
     * Bulk actions for payment methods
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'payment_method_ids' => 'required|array|min:1',
            'payment_method_ids.*' => 'exists:payment_methods,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->with('error', 'Invalid bulk action request.');
        }

        try {
            $action = $request->action;
            $ids = $request->payment_method_ids;
            $count = 0;

            foreach ($ids as $id) {
                $paymentMethod = PaymentMethod::find($id);
                if (!$paymentMethod) continue;

                switch ($action) {
                    case 'activate':
                        $paymentMethod->update(['is_active' => true]);
                        $count++;
                        break;
                    case 'deactivate':
                        $paymentMethod->update(['is_active' => false]);
                        $count++;
                        break;
                    case 'delete':
                        // Check for transactions
                        if (!Transaction::where('payment_method_id', $id)->exists()) {
                            if ($paymentMethod->logo_url) {
                                $this->deleteOldLogo($paymentMethod->logo_url);
                            }
                            $paymentMethod->delete();
                            $count++;
                        }
                        break;
                }
            }

            $actionText = $action === 'delete' ? 'deleted' : $action . 'd';

            return redirect()->back()
                ->with('success', "{$count} payment methods {$actionText} successfully!");
        } catch (\Exception $e) {
            Log::error('Bulk action error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Bulk action failed: ' . $e->getMessage());
        }
    }

    /**
     * Export payment methods
     */
    public function export(Request $request)
    {
        try {
            $paymentMethods = PaymentMethod::orderBy('sort_order')->get();

            $filename = 'payment_methods_' . date('Y-m-d_H-i-s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function () use ($paymentMethods) {
                $file = fopen('php://output', 'w');

                // CSV headers
                fputcsv($file, [
                    'ID',
                    'Name',
                    'Title',
                    'Type',
                    'Status',
                    'Countries',
                    'Currencies',
                    'Fee Bearer',
                    'Supports Split',
                    'Created At'
                ]);

                foreach ($paymentMethods as $method) {
                    fputcsv($file, [
                        $method->id,
                        $method->name,
                        $method->title,
                        $method->type,
                        $method->is_active ? 'Active' : 'Inactive',
                        implode(', ', $method->allowed_countries ?? []),
                        implode(', ', $method->allowed_currencies ?? []),
                        $method->fee_bearer,
                        $method->supports_split_payment ? 'Yes' : 'No',
                        $method->created_at->format('Y-m-d H:i:s'),
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            Log::error('Export error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Import payment methods
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'import_file' => 'required|file|mimes:csv,txt|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->with('error', 'Invalid import file.');
        }

        try {
            $file = $request->file('import_file');
            $data = array_map('str_getcsv', file($file->getRealPath()));
            $header = array_shift($data);

            $imported = 0;
            $errors = [];

            foreach ($data as $row) {
                try {
                    $rowData = array_combine($header, $row);

                    // Validate and create payment method
                    $this->createPaymentMethodFromImport($rowData);
                    $imported++;
                } catch (\Exception $e) {
                    $errors[] = "Row {$imported}: " . $e->getMessage();
                }
            }

            $message = "{$imported} payment methods imported successfully!";
            if (!empty($errors)) {
                $message .= " Errors: " . implode(', ', array_slice($errors, 0, 3));
                if (count($errors) > 3) {
                    $message .= " and " . (count($errors) - 3) . " more...";
                }
            }

            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Import error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    // Helper methods
    protected function getValidationRules(Request $request, $id = null): \Illuminate\Validation\Validator
    {
        $uniqueRule = $id ? "unique:payment_methods,name,{$id}" : 'unique:payment_methods,name';

        return Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', $uniqueRule],
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'type' => 'required|in:traditional,bnpl,crypto',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'allowed_countries' => 'nullable|array',
            'allowed_currencies' => 'nullable|array',
            'discount_type' => 'in:none,percentage,fixed',
            'discount_value' => 'nullable|numeric|min:0',
            'fee_bearer' => 'in:customer,system,split',
            'fee_percentage' => 'nullable|numeric|min:0|max:100',
            'fee_fixed' => 'nullable|numeric|min:0',
            'supports_split_payment' => 'boolean',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'supports_refunds' => 'boolean',
            'sandbox_mode' => 'boolean',
        ]);
    }

    protected function preparePaymentMethodData(Request $request): array
    {
        $data = $request->only([
            'name',
            'title',
            'subtitle',
            'type',
            'is_active',
            'sort_order',
            'allowed_countries',
            'allowed_currencies',
            'discount_type',
            'discount_value',
            'fee_bearer',
            'fee_percentage',
            'fee_fixed',
            'supports_split_payment',
            'min_amount',
            'max_amount',
            'supports_refunds',
            'sandbox_mode'
        ]);

        // Handle API configuration
        $apiConfig = [];
        foreach ($request->all() as $key => $value) {
            if (strpos($key, 'api_') === 0 && !empty($value)) {
                $apiConfig[substr($key, 4)] = $value;
            }
        }
        if (!empty($apiConfig)) {
            $data['api_config'] = $apiConfig;
        }

        // Handle payment options
        if ($request->has('payment_options')) {
            $data['payment_options'] = array_filter($request->payment_options);
        }

        // Handle split payment options
        if ($request->has('split_payment_options')) {
            $data['split_payment_options'] = array_filter($request->split_payment_options);
        }

        // Set defaults
        $data['is_active'] = $request->get('is_active', false);
        $data['supports_split_payment'] = $request->get('supports_split_payment', false);
        $data['supports_refunds'] = $request->get('supports_refunds', true);
        $data['sandbox_mode'] = $request->get('sandbox_mode', false);

        return $data;
    }

    protected function handleLogoUpload($file): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs('payment-methods', $filename, 'public');
        return Storage::url($path);
    }

    protected function deleteOldLogo(string $logoUrl): void
    {
        $path = str_replace('/storage/', '', $logoUrl);
        Storage::disk('public')->delete($path);
    }

    protected function getTransactionStats($paymentMethodId): array
    {
        // $transactions = Transaction::where('payment_method_id', $paymentMethodId);
        $transactions = [];

        // return [
        //     'total_count' => $transactions->count(),
        //     'successful_count' => $transactions->where('status', 'completed')->count(),
        //     'failed_count' => $transactions->where('status', 'failed')->count(),
        //     'total_amount' => $transactions->where('status', 'completed')->sum('amount'),
        //     'average_amount' => $transactions->where('status', 'completed')->avg('amount') ?? 0,
        //     'last_30_days' => $transactions->where('created_at', '>=', now()->subDays(30))->count(),
        // ];
        return [
            'total_count' => 1,
            'successful_count' => 1,
            'failed_count' => 1,
            'total_amount' => 1,
            'average_amount' => 1,
            'last_30_days' => 1,
        ];
    }

    protected function performConnectionTest(PaymentMethod $paymentMethod): array
    {
        // Implement specific connection tests for each payment method
        // This is a placeholder implementation
        return [
            'success' => true,
            'details' => 'Connection test not implemented for ' . $paymentMethod->name,
        ];
    }

    protected function createPaymentMethodFromImport(array $data): void
    {
        // Implementation for creating payment method from import data
        // This would need proper validation and data mapping
    }

    protected function getCountriesList()
    {
        $countriesFile = storage_path('countries.json');
        $countriesList = [];

        if (file_exists($countriesFile)) {
            $countriesJson = file_get_contents($countriesFile);
            $countriesArray = json_decode($countriesJson, true);

            foreach ($countriesArray as $country) {
                $countriesList[$country['code']] = $country['name'];
            }
        }
        return $countriesList;
    }

    protected function getCurrenciesList(): array
    {
        $currencies = storage_path('currency.json');
        $currenciesList = [];

        if (file_exists($currencies)) {
            $currenciesJson = file_get_contents($currencies);
            $currenciesArray = json_decode($currenciesJson, true);

            // Create a map of code => name
            foreach ($currenciesArray as $currency) {
                $currenciesList[$currency['code']] = $currency['name'];
            }
        }
        return $currenciesList;
    }
}
