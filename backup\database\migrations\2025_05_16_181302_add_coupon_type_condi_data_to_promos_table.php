<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCouponTypeCondiDataToPromosTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('promos') && !Schema::hasColumn('promos', 'coupon_type_condi_data')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->text('coupon_type_condi_data')->nullable();
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('promos') && Schema::hasColumn('promos', 'coupon_type_condi_data')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->dropColumn('coupon_type_condi_data');
            });
        }
    }
}
