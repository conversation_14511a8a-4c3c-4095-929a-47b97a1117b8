<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductAttribute;
use App\Models\ProductAttributeItem;
use Illuminate\Http\Request;
use Session;
use Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\CarrierOption;
use App\Models\CarrierOptionCountry;

class ProductAttributeItemController extends Controller
{
    private $folderPath = 'product_attributes/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $attribute_id)
    {
        $items = ProductAttributeItem::where('product_attribute_id', $attribute_id)->paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        $attribute = ProductAttribute::find($attribute_id);
        $admin_page_title = 'Add Attribute in ' . $attribute->title;

        if ($request->view == 'html') {
            return view('admin.jquery_live.attribute_items', compact('items', 'sl', 'attribute_id'));
        }

        return view('admin.product.attributes.item.index', compact('items', 'sl', 'attribute_id', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($attribute_id)
    {
        $attribute = ProductAttribute::find($attribute_id);
        $admin_page_title = 'Create Attribute in ' . $attribute->title;
        $attribute_types = $this->attribute_types();
        $input_types = $this->input_types();
        $option_icon_info = Session::get('option_icon');
        $option_text_icon_info = Session::get('option_text_icon');

        $collection = collect(Session::get('attribute_options'));
        $items = $collection->sortBy('ordering')->sortBy('ordering');

        return view('admin.product.attributes.item.create', compact('items', 'attribute_types', 'attribute_id', 'input_types', 'option_icon_info', 'option_text_icon_info', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $attribute_id)
    {
        $this->validate($request, [
            'page_title' => 'nullable|string|max:100',
            'input_type_id' => 'required|numeric',
            'attribute_type_id' => 'required|numeric',
            'input_required' => 'nullable|boolean',
            'ordering' => 'nullable|numeric',
        ]);

        $collection = collect(Session::get('attribute_options'));
        $attribute_options = $collection->sortBy('ordering');
        // dd($attribute_options);

        $item = new ProductAttributeItem;
        $item->product_attribute_id = $attribute_id;
        $item->title = $request->page_title;
        $item->input_type_id = $request->input_type_id;
        $item->attribute_type_id = $request->attribute_type_id;
        $item->input_required = $request->input_required ?? 0;
        $item->ordering = $request->ordering ?? 0;
        $item->save();

        $item = ProductAttributeItem::findOrFail($item->id);
        if (count($attribute_options) > 0) {
            $folderName = $this->folderName() . '/' . $item->id . '/';
            foreach ($attribute_options as $attribute_option) {
                if ($attribute_option->extension) {
                    $image_name = 'option_icon_' . uniqid() . '.' . $attribute_option->extension;
                    $attribute_option->folder = $this->folderName();
                    $attribute_option->file_path = $folderName . $image_name;
                    Storage::move($attribute_option->file_current, $this->folderPath . $folderName . '/' . $image_name);
                    $attribute_option->file_current = $this->folderPath . $folderName . '/' . $image_name;
                }

                if ($attribute_option->text_extension) {
                    $image_name = 'option_text_icon_' . uniqid() . '.' . $attribute_option->text_extension;
                    $attribute_option->folder = $this->folderName();
                    $attribute_option->text_file_path = $folderName . '/' . $image_name;
                    Storage::move($attribute_option->text_file_current, $this->folderPath . $folderName . '/' . $image_name);
                    $attribute_option->text_file_current = $this->folderPath . $folderName . '/' . $image_name;
                }
                $attribute_option->file_root = asset('storage/product_attributes/');
                $attribute_option->text_file_root = asset('storage/product_attributes/');
            }
            $item->attribute_options = serialize($attribute_options);
            $item->update();
        }

        Session::forget('attribute_options');

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.product_attribute_items.index', $attribute_id))->with('success', 'Created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProductAttributeItem  $productAttributeItem
     * @return \Illuminate\Http\Response
     */
    public function show(ProductAttributeItem $productAttributeItem)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductAttributeItem  $productAttributeItem
     * @return \Illuminate\Http\Response
     */
    public function edit($id, $attribute_id)
    {
        $item = ProductAttributeItem::find($id);
        $attribute = ProductAttribute::find($attribute_id);
        $admin_page_title = 'Edit Attribute in ' . $attribute->title;
        $item->attribute_types = $this->attribute_types();
        $item->input_types = $this->input_types();
        $item->attribute_options = unserialize($item->attribute_options);
        // dd($item->attribute_options);
        $option_icon_info = Session::get('option_icon');
        $file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_icon' . '/');

        $option_text_icon_info = Session::get('option_text_icon');
        $text_file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_text_icon' . '/');

        $attribute_options = $item->attribute_options;
        if ($attribute_options) {
            foreach ($attribute_options as $attribute_option) {
                $attribute_option->file_root = asset('storage/product_attributes/');
                $attribute_option->text_file_root = asset('storage/product_attributes/');
            }
        }
        if (Session::has('attribute_options')) {
            $collection = collect(Session::get('attribute_options'));
            foreach ($collection as $collection_item) {
                $collection_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_icon' . '/');
                if ($collection_item->text_file_path) {
                    $collection_item->text_file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_text_icon' . '/');
                }
            }
            if ($attribute_options) {
                $new_collection = collect($attribute_options->merge($collection));
                $item->attribute_options = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('attribute_options'));
                $item->attribute_options = $collection->sortBy('ordering');
            }
        }
        $carrierAttributeTypeId = 2;
        $countries = $this->getCountryList();
        return view('admin.product.attributes.item.edit', compact('item', 'countries', 'carrierAttributeTypeId', 'option_icon_info', 'option_text_icon_info', 'attribute_id', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductAttributeItem  $productAttributeItem
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id, $attribute_id)
    {
        $this->validate($request, [
            'page_title' => 'nullable|string|max:100',
            'input_type_id' => 'required|numeric',
            'attribute_type_id' => 'required|numeric',
            'input_required' => 'nullable|boolean',
            'ordering' => 'nullable|numeric',
        ]);
        $item = ProductAttributeItem::findOrFail($id);
        $item->product_attribute_id = $attribute_id;
        $item->title = $request->page_title;
        $item->input_type_id = $request->input_type_id;
        $item->attribute_type_id = $request->attribute_type_id;
        $item->input_required = $request->input_required ?? 0;
        $item->ordering = $request->ordering ?? 0;

        if (Session::has('attribute_options')) {
            $item->attribute_options = unserialize($item->attribute_options);
            $attribute_options = Session::get('attribute_options');

            $folderName = $this->folderName() . '/' . $item->id . '/';
            foreach ($attribute_options as $attribute_option) {
                if ($attribute_option->extension) {
                    $image_name = 'option_icon_' . uniqid() . '.' . $attribute_option->extension;
                    $attribute_option->folder = $this->folderName();
                    $attribute_option->file_path = $folderName . '/' . $image_name;
                    Storage::move($attribute_option->file_current, $this->folderPath . $folderName . '/' . $image_name);
                    $attribute_option->file_current = $this->folderPath . $folderName . '/' . $image_name;
                }

                if ($attribute_option->text_extension) {
                    $image_name = 'option_text_icon_' . uniqid() . '.' . $attribute_option->text_extension;
                    $attribute_option->folder = $this->folderName();
                    $attribute_option->text_file_path = $folderName . '/' . $image_name;
                    Storage::move($attribute_option->text_file_current, $this->folderPath . $folderName . '/' . $image_name);
                    $attribute_option->text_file_current = $this->folderPath . $folderName . '/' . $image_name;
                }
                $attribute_option->file_root = asset('storage/product_attributes/');
                $attribute_option->text_file_root = asset('storage/product_attributes/');
            }

            if ($item->attribute_options) {
                $new_collection = collect($item->attribute_options->merge(collect($attribute_options)));
            } else {
                $new_collection = collect($attribute_options);
            }
            $item->attribute_options = serialize($new_collection->sortBy('ordering'));
        }
        $this->removeEmptyDir($this->folderPath . $this->folderName() . '/' . $item->id);
        $item->update();

        Session::forget('attribute_options');

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.product_attribute_items.index', $attribute_id))->with('success', 'Item updated successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductAttributeItem  $productAttributeItem
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = ProductAttributeItem::findOrFail($id);
        if (!empty($item->attribute_options)) {
            $attribute_options = unserialize($item->attribute_options);
            foreach ($attribute_options as $attribute_option) {
                if (!empty($attribute_option->file_path)) {
                    Storage::delete($this->folderPath . $attribute_option->file_path);
                }
                if (!empty($attribute_option->text_file_path)) {
                    Storage::delete($this->folderPath . $attribute_option->text_file_path);
                }
            }
        }
        $item->delete();

        $message = '<strong>' . $item->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }

    public function options(Request $request, $attribute_id, $attribute_item_id = null)
    {
        $new_collection = collect(Session::get('attribute_options'));
        foreach ($new_collection as $new_collection_item) {
            $new_collection_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_icon' . '/');
            $new_collection_item->text_file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_text_icon' . '/');
        }

        if ($attribute_item_id) {
            $item = ProductAttributeItem::where([
                'id' => $attribute_item_id,
                'product_attribute_id' => $attribute_id
            ])->first();

            $attribute_options = unserialize($item->attribute_options);
            if ($attribute_options) {
                foreach ($attribute_options as $attribute_option) {
                    $attribute_option->file_root = asset('storage/product_attributes/');
                    $attribute_option->text_file_root = asset('storage/product_attributes/');
                }
            }

            if ($item->attribute_options) {
                $collection = collect($attribute_options->merge($new_collection));
            } else {
                $collection = $new_collection;
            }
            $items = $collection->sortBy('ordering');
        } else {
            $items = $new_collection->sortBy('ordering');
        }

        return view('admin.jquery_live.attribute_options', compact('items', 'attribute_id', 'attribute_item_id'));
    }

    public function storeOption(Request $request, $attribute_id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'description' => 'nullable|string|max:300',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'failed',
                'messages' => $validator->messages(),
            ], 200);
        }
        $option_icon = Session::get('option_icon');
        $option_text_icon = Session::get('option_text_icon');

        $item = (object)([
            'id' => uniqid(),
            'attribute_id' => $attribute_id,

            'file_current' => $option_icon->option_icon ?? null,
            'file_path' => $option_icon->file_path ?? null,
            'extension' => $option_icon->extension ?? null,

            'text_file_current' => $option_text_icon->option_text_icon ?? null,
            'text_file_path' => $option_text_icon->file_path ?? null,
            'text_extension' => $option_text_icon->extension ?? null,

            'title' => $request->get('title'),
            'description' => $request->get('description'),
            'ordering' =>  $request->get('ordering') ?? 0,
            'status' => $request->get('status') ?? 0,
        ]);

        if (Session::has('attribute_options')) {
            $items = Session::get('attribute_options');
            if (count($items) > 10) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  'limit end'
                    ],
                    200
                );
            }
            array_push($items, $item);
        } else {
            $items = array($item);
        }

        Session::put([
            'attribute_options' => $items,
        ]);

        Session::forget('option_icon');
        Session::forget('option_text_icon');

        $message = '<strong>' . $item->title . '</strong> added successful';
        return response()->json([
            'items' => $items,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    public function editOption($attribute_id, $option_id, $attribute_item_id = null)
    {

        $attribute_item = ProductAttributeItem::find($attribute_item_id);

        if ($attribute_item && $attribute_item->attribute_options) {
            $items = unserialize($attribute_item->attribute_options);
            foreach ($items as $item) {
                $item->file_root = asset('storage/product_attributes/');
                $item->file_img_upload_url = route('admin.product_attribute.items.file.upload', ['option_icon', $attribute_id, $option_id, $attribute_item_id]);
                $item->file_img_remove_url = route('admin.product_attribute.items.file.remove', ['option_icon', $attribute_id, $option_id, $attribute_item_id]);

                $item->text_file_root = asset('storage/product_attributes/');
                $item->text_file_img_upload_url = route('admin.product_attribute.items.file.upload', ['option_text_icon', $attribute_id, $option_id, $attribute_item_id]);
                $item->text_file_img_remove_url = route('admin.product_attribute.items.file.remove', ['option_text_icon', $attribute_id, $option_id, $attribute_item_id]);
            }
        }

        if (Session::has('attribute_options')) {
            $collection = collect(Session::get('attribute_options'));
            foreach ($collection as $collection_item) {
                $collection_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_icon' . '/');
                $collection_item->file_img_upload_url = route('admin.product_attribute.items.file.upload', ['option_icon', $attribute_id, $option_id, $attribute_item_id]);
                $collection_item->file_img_remove_url = route('admin.product_attribute.items.file.remove', ['option_icon', $attribute_id, $option_id, $attribute_item_id]);

                $collection_item->text_file_img_upload_url = route('admin.product_attribute.items.file.upload', ['option_text_icon', $attribute_id, $option_id, $attribute_item_id]);
                $collection_item->text_file_img_remove_url = route('admin.product_attribute.items.file.remove', ['option_text_icon', $attribute_id, $option_id, $attribute_item_id]);
            }
            if ($attribute_item && $attribute_item->attribute_options) {
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $items = $collection->sortBy('ordering');
            }
        }

        foreach ($items as $item) {
            if ($item->id == $option_id) {
                if ($item->file_path) {
                    $item->img_url = $item->file_root . '/' . $item->file_path;
                }
                if (!empty($item->text_file_path)) {
                    $item->text_img_url = $item->text_file_root . '/' . $item->text_file_path;
                }
                return response()->json($item, 200);
                break;
            }
        }
    }

    public function optionDestroy($attribute_id, $option_id, $attribute_item_id = null)
    {
        $attribute_item = ProductAttributeItem::find($attribute_item_id);
        if ($attribute_item && $attribute_item->attribute_options) {
            $items = unserialize($attribute_item->attribute_options);
            foreach ($items as $key => $item) {
                $item->file_root = asset('storage/product_attributes/');
                if ($item->id == $option_id) {
                    unset($items[$key]);
                    Storage::delete($this->folderPath . $item->file_path);
                    Storage::delete($this->folderPath . $item->text_file_path);
                    $message = '<strong>' . $item->title . '</strong> deleted successful';
                    break;
                }
            }
            $attribute_item->attribute_options = serialize($items);
            $attribute_item->update();
        }

        if (Session::has('attribute_options')) {
            $session_items = Session::get('attribute_options');
            foreach ($session_items as $key => $session_item) {
                $session_item->file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_icon' . '/');
                $session_item->text_file_root = asset('storage/session_files/' . Auth()->user()->username . '/' . 'option_text_icon' . '/');
                if ($session_item->id == $option_id) {
                    $auth_user = Auth()->user();
                    unset($session_items[$key]);
                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . 'option_icon/', $session_item->file_path);
                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . 'option_text_icon/', $session_item->text_file_path);
                    $message = '<strong>' . $session_item->title . '</strong> deleted successful';
                    break;
                }
            }
            Session::put([
                'attribute_options' => $session_items,
            ]);
            if ($attribute_item && $attribute_item->attribute_options) {
                $collection = collect(Session::get('attribute_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('attribute_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json([
            'items' => $items,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    public function editOptionUpdate(Request $request, $attribute_id, $option_id, $attribute_item_id = null)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'description' => 'nullable|string|max:300',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'failed',
                'messages' => $validator->messages(),
            ], 200);
        }
        $attribute_item = ProductAttributeItem::find($attribute_item_id);
        if ($attribute_item_id == 6) {
            $CarrierOption = CarrierOption::where('title', $request->title)->first();
            if (!$CarrierOption) {
                $CarrierOption = new CarrierOption();
            }
            $CarrierOption->title = $request->title;
            $CarrierOption->attribute_id = $attribute_item_id;
            $CarrierOption->description = $request->description;
            $CarrierOption->ordering = $request->get('ordering') ?? 0;
            $CarrierOption->status = $request->get('status') ?? 0;
            $CarrierOption->save();
            // Update the associated countries
            $countries = $request->input('countries', []); // country codes like ['BD', 'IN']

            // Delete existing country links
            CarrierOptionCountry::where('carrier_option_id', $CarrierOption->id)->delete();

            // Re-insert selected countries
            foreach ($countries as $code) {
                CarrierOptionCountry::create([
                    'carrier_option_id' => $CarrierOption->id,
                    'country_code' => $code,
                ]);
            }
        }

        if ($attribute_item && $attribute_item->attribute_options) {
            $items = unserialize($attribute_item->attribute_options);
            foreach ($items as $item) {
                if ($item->id == $option_id) {
                    $item->title = $request->title;
                    $item->description = $request->description;
                    $item->ordering = $request->get('ordering') ?? 0;
                    $item->status = $request->get('status') ?? 0;
                    $message = '<strong>' . $item->title . '</strong> update successful';
                }
            }
            $attribute_item->attribute_options = serialize($items);
            $attribute_item->update();
        }

        if (Session::has('attribute_options')) {
            $option_icon = Session::get('option_icon');
            foreach (Session::get('attribute_options') as $item) {
                if ($item->id == $option_id) {
                    $item->title = $request->title;
                    $item->description = $request->description;
                    $item->ordering = $request->get('ordering') ?? 0;
                    $item->status = $request->get('status') ?? 0;
                    $message = '<strong>' . $item->title . '</strong> update successful';
                }
            }
            Session::put([
                'attribute_options' => Session::get('attribute_options'),
            ]);

            if ($attribute_item && $attribute_item->attribute_options) {
                $collection = collect(Session::get('attribute_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('attribute_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json([
            'items' => $items,
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    public function fileAttributeItemUpload(Request $request, $target, $attribute_id, $option_id, $attribute_item_id = null, $title = null)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);
        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }
        $attribute_item = ProductAttributeItem::find($attribute_item_id);
        if ($attribute_item && $attribute_item->attribute_options) {
            $items = unserialize($attribute_item->attribute_options);
            foreach ($items as $item) {
                if ($item->id == $option_id) {
                    $CarrierOption = CarrierOption::where('title', $title)->first();
                    $imgName = $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
                    $folderName = $this->folderName() . '/' . $attribute_item->id . '/';
                    if ($target == 'option_text_icon') {
                        if (!empty($item->text_file_path)) {
                            $this->deleteFile($this->folderPath, $item->text_file_path);
                        }
                        $item->text_file_path = $folderName  . $imgName;
                        $item->text_file_current = $this->folderPath . $folderName . '/' . $imgName;
                        $request->$target->storeAs($this->folderPath . $folderName, $imgName);
                        $item->text_extension = $request->$target->extension();
                        if ($attribute_item_id == 6) {

                            $CarrierOption->text_file_path = $folderName  . $imgName;
                            $CarrierOption->text_file_current = $this->folderPath . $folderName . '/' . $imgName;
                            $CarrierOption->text_extension = $request->$target->extension();
                           
                        }
                    } else {
                        $this->deleteFile($this->folderPath, $item->file_path);
                        $item->file_path = $folderName  . $imgName;
                        $item->file_current = $this->folderPath . $folderName . $imgName;
                        $request->$target->storeAs($this->folderPath . $folderName, $imgName);
                        $item->extension = $request->$target->extension();
                        if ($attribute_item_id == 6) {

                            $CarrierOption->file_path = $folderName  . $imgName;
                            $CarrierOption->file_current = $this->folderPath . $folderName . $imgName;
                           
                        }
                    }
                    $item->file_root = asset('storage/product_attributes/');
                    $item->text_file_root = asset('storage/product_attributes/');
                     if ($attribute_item_id == 6) {
                    $CarrierOption->file_root = asset('storage/product_attributes/');
                    $CarrierOption->text_file_root = asset('storage/product_attributes/');
                     }
                    $url = url('/storage/product_attributes/' . $folderName . '/' . $imgName);
                    break;
                }
            }
            $attribute_item->attribute_options = serialize($items);
            $attribute_item->update();
             if ($attribute_item_id == 6) {
             $CarrierOption->save();
             }
        }

        if (Session::has('attribute_options')) {
            $session_items = Session::get('attribute_options');
            foreach ($session_items as $session_item) {
                if ($session_item->id == $option_id) {
                    $imgName = $target . "_" . $session_item->id . uniqid() . '.' . $request->$target->extension();
                    $folderName = $this->folderName();
                    $auth_user = Auth()->user();

                    if ($target == 'option_text_icon') {
                        $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target . '/', $session_item->text_file_path);
                        $request->$target->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);

                        $session_item->text_file_path = $folderName  . '/' . $imgName;
                        $session_item->text_file_current = $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName  . '/' . $imgName;
                        $session_item->text_extension = $request->$target->extension();
                    } else {
                        $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target . '/', $session_item->file_path);
                        $request->$target->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);

                        $session_item->file_path = $folderName  . '/' . $imgName;
                        $session_item->file_current = $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName  . '/' . $imgName;
                        $session_item->extension = $request->$target->extension();
                    }

                    $url = url('storage/session_files/' . $auth_user->username . '/' . $target . '/' . $folderName . '/' . $imgName);
                    break;
                }
            }
            Session::put([
                'attribute_options' => $session_items,
            ]);
            if ($attribute_item && $attribute_item->attribute_options) {
                $collection = collect(Session::get('attribute_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('attribute_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileAttributeItemRemove(Request $request, $target, $attribute_id, $option_id, $attribute_item_id = null)
    {
        $attribute_item = ProductAttributeItem::find($attribute_item_id);
        if ($attribute_item && $attribute_item->attribute_options) {
            $items = unserialize($attribute_item->attribute_options);
            foreach ($items as $item) {
                if ($item->id == $option_id) {
                    if ($target == 'option_text_icon') {
                        $this->deleteFile($this->folderPath, $item->text_file_path);
                        $item->text_file_path = null;
                        $item->text_file_current = null;
                        $item->text_extension = null;
                    } else {
                        $this->deleteFile($this->folderPath, $item->file_path);
                        $item->file_path = null;
                        $item->file_current = null;
                        $item->extension = null;
                    }
                    break;
                }
            }
            $attribute_item->attribute_options = serialize($items);
            $attribute_item->update();
        }

        if (Session::has('attribute_options')) {
            $session_items = Session::get('attribute_options');
            foreach ($session_items as $session_item) {
                if ($session_item->id == $option_id) {
                    $auth_user = Auth()->user();
                    if ($target == 'option_text_icon') {
                        $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $session_item->text_file_path);
                        $session_item->text_file_path = null;
                        $session_item->text_file_current = null;
                        $session_item->text_extension = null;
                    } else {
                        $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $session_item->file_path);
                        $session_item->file_path = null;
                        $session_item->file_current = null;
                        $session_item->extension = null;
                    }
                    break;
                }
            }

            Session::put([
                'attribute_options' => $session_items,
            ]);

            if ($attribute_item && $attribute_item->attribute_options) {
                $collection = collect(Session::get('attribute_options'));
                $new_collection = collect($items->merge($collection));
                $items = $new_collection->sortBy('ordering');
            } else {
                $collection = collect(Session::get('attribute_options'));
                $items = $collection->sortBy('ordering');
            }
        }

        return response()->json('success', 200);
    }
}
