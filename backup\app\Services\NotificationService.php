<?php

namespace App\Services;

use App\Mail\SiteEmail;
use App\Models\User;
use App\Models\UserGroupMap;
use App\Models\EmailTemplate;
use App\Models\AlertPreference;
use App\Models\Tracking;
use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use App\Models\ProductItem;

class NotificationService
{
    protected function getUserByEmail($email)
    {
        return User::where('email', $email)->first();
    }

    protected function shouldSend($user, $key)
    {
        $alertPreferences = unserialize($user->alert_preferences ?? 'a:0:{}');
        $preference = AlertPreference::where('key', $key)->first();
        if ($preference->email_force_checked && $preference->sms_force_checked) {
            return true;
        }
        return in_array($key, array_keys($alertPreferences));
    }

    protected function getTemplate($key)
    {
        $preference = AlertPreference::where('key', $key)->first();
        return EmailTemplate::where('alert_preference_id', $preference->id)->first();
    }
    protected function getTemplateById($id)
    {
        return EmailTemplate::where('id', $id)->first();
    }

    protected function sendNotification($user, $order, $cart, $template, $coupon, $productItem, $message_status)
    {
        Log::error($user);
        $controller = new Controller();
        $template->email_body = unserialize($template->email_body);
        $template->sms_body = unserialize($template->sms_body);

        $mailData = (object) [
            'user' => $user,
            'order' => $cart->order ?? $order, // fallback
            'subject' => $template->subject,
            'pre_header' => $template->pre_header,
            'email_body' => $template->email_body,
            'sms_status' => !!$template->sms_status,
            'sms_body' => $template->sms_body['body'],
            'promo_code' => $coupon->code ?? null,
            'promo_date' => '',
            'product_name' => $productItem->title ?? null
        ];
        $mailData->content = $controller->sendEmailData($mailData)->content;
        $mailData->sms_content = $controller->sendEmailData($mailData)->sms_body;
        $prepared = $controller->sendEmailData($mailData);
        if ($order) {
            $productItem = ProductItem::find($order->OrderItems()->first()->product_item_id);
            $prepared->store_location = $productItem->store_id;
        }
        Mail::to($user->email)->send(new SiteEmail($prepared));
        $adminUser = auth()->user();

        $lead_source = (object) [
            'admin_id'    => $adminUser?->id ?? null,
            'admin_email' => $adminUser?->email ?? null,
            'admin_phone' => ($adminUser?->tel_cell_country_code ?? '') . ($adminUser?->tel_cell ?? ''),
            'source'      => 'admin',
        ];

        $tracking = new Tracking();
        $tracking->order_id = $order->id ?? null;
        $tracking->content = serialize($controller->sendEmailData($mailData));
        $tracking->from_email = config('mail.from.address');
        $tracking->to_email = $user->email;
        $tracking->subject = $controller->sendEmailData($mailData)->subject;
        $tracking->lead_source = serialize($lead_source);
        $tracking->ip = $controller->getClientIpAddress();
        $tracking->save();
        if ($message_status && $user->tel_cell) {
            $sms_vars = $controller->sendEmailData($mailData)->used_vars;
            $this->sendMessage($user->tel_cell, $sms_vars, null, null, $template->sms_body['template_id'], $prepared->sms_body);
            $sms_tracking = Tracking::findOrFail($tracking->id);
            $sms_tracking->sms_content = serialize($mailData->sms_content);
            $sms_tracking->from_phone = config('app.twilio_number');
            $sms_tracking->to_phone = $user->tel_cell;
            $sms_tracking->update();
        }
    }
    protected function sendNotificationDirect($email, $phone, $productItem, $message_status, $template)
    {

        $controller = new Controller();
        $template->email_body = unserialize($template->email_body);
        $template->sms_body = unserialize($template->sms_body);
        $order = null;
        $mailData = (object) [
            'user' => null,
            'order' => null, // fallback
            'subject' => $template->subject,
            'pre_header' => $template->pre_header,
            'email_body' => $template->email_body,
            'sms_status' => !!$template->sms_status,
            'sms_body' => $template->sms_body['body'] ?? null,
            'promo_code' => $coupon->code ?? null,
            'promo_date' => '',
            'product_name' => $productItem->title ?? null
        ];
        $mailData->content = $controller->sendEmailData($mailData)->content;
        $mailData->sms_content = $controller->sendEmailData($mailData)->sms_body;
        $prepared = $controller->sendEmailData($mailData);
        if ($order) {
            $productItem = ProductItem::find($order->OrderItems()->first()->product_item_id);
            $prepared->store_location = $productItem->store_id;
        }
        Mail::to($email)->send(new SiteEmail($prepared));
        $adminUser = auth()->user();

        $lead_source = (object) [
            'admin_id'    => $adminUser?->id ?? null,
            'admin_email' => $adminUser?->email ?? null,
            'admin_phone' => ($adminUser?->tel_cell_country_code ?? '') . ($adminUser?->tel_cell ?? ''),
            'source'      => 'admin',
        ];

        $tracking = new Tracking();
        $tracking->order_id = $order->id ?? null;
        $tracking->content = serialize($controller->sendEmailData($mailData));
        $tracking->from_email = config('mail.from.address');
        $tracking->to_email = $email;
        $tracking->subject = $controller->sendEmailData($mailData)->subject;
        $tracking->lead_source = serialize($lead_source);
        $tracking->ip = $controller->getClientIpAddress();
        $tracking->save();
        if ($message_status && $phone && $mailData->sms_body) {
            $sms_vars = $controller->sendEmailData($mailData)->used_vars;
            $this->sendMessage($phone, $sms_vars, null, null, $template->sms_body['template_id'], $prepared->sms_body);
            $sms_tracking = Tracking::findOrFail($tracking->id);
            $sms_tracking->sms_content = serialize($mailData->sms_content);
            $sms_tracking->from_phone = config('app.twilio_number');
            $sms_tracking->to_phone = $phone;
            $sms_tracking->update();
        }
    }
    public function sendCustomerOrderStatusChangeNotifications($order, $user, $message_status, $template)
    {
        if ($this->shouldSend($user, 'order_status')) {
            $template = $this->getTemplateById($template);
            $this->sendNotification($user, $order, null, $template, null, null, $message_status,);
        }
    }
    function sendMessage($phone, $vars = null, $mode = 'otp', $twilioSenderId = null, $template, $smsContent = null)
    {
        $controller = new Controller();
        $whatsappSent = $controller->sendViaMetaWhatsApp($phone, $vars, $template, 'en_US');
        Log::error('Meta WhatsApp status: ' . $whatsappSent);
        if (empty($twilioSenderId)) {
            $twilioSenderId = env('TWILIO_SID');
        }

        if (!$whatsappSent) {

            $controller->sendViaTwilioSms($phone, $smsContent, $mode, $twilioSenderId);
            return true;
        }
    }
    public function sendCustomerNewOrderNotification($order, $cart)
    {
        $user = User::find($order->customer_id);
        if ($this->shouldSend($user, 'new_order')) {
            $template = $this->getTemplate('new_order');
            $this->sendNotification($user, $order, $cart, $template, null, null, true);
        }
    }

    public function sendCustomerNewOrderStoreNotification($order, $cart)
    {
        $user = User::find($order->customer_id);
        if ($this->shouldSend($user, 'new_order_store')) {
            $template = $this->getTemplate('new_order_store');
            $this->sendNotification($user, $order, $cart, $template);
        }
    }

    public function sendCustomerOrderShippedNotification($order, $cart, $message_status)
    {
        $user = User::find($order->customer_id);
        if ($this->shouldSend($user, 'order_shipped')) {
            $template = $this->getTemplate('order_shipped');
            $this->sendNotification($user, $order, $cart, $template, null, null, $message_status);
        }
    }

    public function sendCustomerOrderDeliveredNotification($order, $cart, $message_status)
    {
        $user = User::find($order->customer_id);
        if ($this->shouldSend($user, 'order_delivered')) {
            $template = $this->getTemplate('order_delivered');
            $this->sendNotification($user, $order, $cart, $template, null, null, $message_status);
        }
    }

    public function sendCustomerOrderCancelledNotification($order, $cart, $message_status)
    {
        $user = User::find($order->customer_id);
        if ($this->shouldSend($user, 'order_cancelled')) {
            $template = $this->getTemplate('order_cancelled');
            $this->sendNotification($user, $order, $cart, $template, null, null, $message_status);
        }
    }

    public function sendCustomerOrderRefundedNotification($order, $user, $message_status)
    {
        $user = User::find($order->customer_id);
        if ($this->shouldSend($user, 'order_refunded')) {
            $template = $this->getTemplate('order_refunded');
            $this->sendNotification($user, $order, null, $template, null, null, $message_status);
        }
    }

    public function sendCustomerPaymentSuccessNotification($user, $order)
    {
        if ($this->shouldSend($user, 'payment_success')) {
            $template = $this->getTemplate('payment_success');
            $this->sendNotification($user, $order, null, $template, null, null);
        }
    }

    public function sendCustomerPaymentFailedNotification($user, $order)
    {
        if ($this->shouldSend($user, 'payment_failed')) {
            $template = $this->getTemplate('payment_failed');
            $this->sendNotification($user, $order, null, $template, null, null);
        }
    }

    public function sendCustomerAccountRegisteredNotification($user, $message_status, $template)
    {
        if ($this->shouldSend($user, 'account_registered')) {
            $template = $this->getTemplateById($template);
            $this->sendNotification($user, null, null, $template, null, null, $message_status);
        }
    }

    public function sendCustomerPasswordResetNotification($user)
    {
        if ($this->shouldSend($user, 'password_reset')) {
            $template = $this->getTemplate('password_reset');
            $this->sendNotification($user, null, null, $template, null, null);
        }
    }
    public function sendCustomerReviewNotifications($order, $review, $user, $message_status, $template)
    {

        if ($this->shouldSend($user, 'review_alerts')) {
            $template = $this->getTemplateById($template);
            $this->sendNotification($user, null, null, $template, null, null, $message_status);
        }
    }
    public function sendContactFormNotification($user)
    {
        if ($this->shouldSend($user, 'contact_form')) {
            $template = $this->getTemplate('contact_form');
            $this->sendNotification($user, null, null, $template);
        }
    }

    public function sendNewsletterFormNotification($user)
    {
        if ($this->shouldSend($user, 'newsletter_form')) {
            $template = $this->getTemplate('newsletter_form');
            $this->sendNotification($user, null, null, $template);
        }
    }

    public function sendReviewFormNotification($user)
    {
        if ($this->shouldSend($user, 'review_form')) {
            $template = $this->getTemplate('review_form');
            $this->sendNotification($user, null, null, $template);
        }
    }

    public function sendCouponUsedNotification($user, $coupon)
    {
        if ($this->shouldSend($user, 'coupon_used')) {
            $template = $this->getTemplate('coupon_used');
            $this->sendNotification($user, null, null, $template, $coupon);
        }
    }

    public function sendRedeemRewardsNotification($user)
    {
        if ($this->shouldSend($user, 'redeem_rewards')) {
            $template = $this->getTemplate('redeem_rewards');
            $this->sendNotification($user, null, null, $template);
        }
    }

    public function sendLowInventoryNotification($user, $productItem)
    {
        if ($this->shouldSend($user, 'low_inventory')) {
            $template = $this->getTemplate('low_inventory');
            $this->sendNotification($user, null, null, $template, null, $productItem);
        }
    }

    public function sendNotifyMeOrderNotification($email, $phone, $productItem, $message_status)
    {
        // if ($this->shouldSend($user, 'notify_me_product')) {
        $template = $this->getTemplate('notify_me_product');
        $this->sendNotificationDirect($email, $phone, $productItem, $message_status, $template);
        // }
    }

    public function sendWishlistReminderNotification($user)
    {
        if ($this->shouldSend($user, 'wishlist_reminder')) {
            $template = $this->getTemplate('wishlist_reminder');
            $this->sendNotification($user, null, null, $template);
        }
    }
    public function sendRewardNotifications($reward, $user, $message_status, $template)
    {
        if ($this->shouldSend($user, 'rewards_alerts')) {
            $template = $this->getTemplateById($template);
            $this->sendNotification($user, null, null, $template, null, null, $message_status);
        }
    }
    public function sendPromotionalAlertsNotifications($user, $message_status, $template)
    {
        if ($this->shouldSend($user, 'promotional_alerts')) {
            $template = $this->getTemplateById($template);
            $this->sendNotification($user, null, null, $template, null, null, $message_status);
        }
    }
    protected function formatPhone($number)
    {
        return str_replace('+', '', $number);
    }
}
