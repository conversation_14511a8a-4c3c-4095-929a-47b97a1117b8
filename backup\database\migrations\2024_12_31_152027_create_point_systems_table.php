<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePointSystemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('point_systems', function (Blueprint $table) {
            $table->id();
            $table->string('country')->unique();
            $table->float('order_points')->default(0.00);
            $table->float('comment_points')->nullable()->default(0.00);
            $table->float('photo_points')->nullable()->default(0.00);
            $table->float('video_points')->nullable()->default(0.00);
            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('point_systems');
    }
}