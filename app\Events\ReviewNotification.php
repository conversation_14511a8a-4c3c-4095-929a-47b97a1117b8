<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class ReviewNotification
{
    use SerializesModels;

    public $productItem;
    public $review;
    public $user;
    public $message_status;
    public $template;

    public function __construct($productItem, $review, $user, $message_status, $template)
    {
        $this->productItem = $productItem;
        $this->review = $review;
        $this->user = $user;
        $this->message_status = $message_status;
        $this->template = $template;
    }
}
