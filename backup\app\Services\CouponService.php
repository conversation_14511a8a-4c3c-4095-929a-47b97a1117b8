<?php

namespace App\Services;

use App\Models\Coupon;
use App\Models\User;
use App\Models\CouponUsage;
use Illuminate\Support\Collection;
use Exception;

class CouponService
{
    // Validate coupon by code and user
    public function validateCoupon(string $code, User $user): Coupon
    {
        $coupon = Coupon::where('code', $code)
            ->where('status', 1)
            ->first();

        if (!$coupon) {
            throw new Exception("Coupon code is invalid or inactive.");
        }

        $now = now();

        // If coupon never expires, allow it regardless of dates
        if ($coupon->never_expire) {

            // Allow the coupon
        } else {
            // Check dates only if never_expire is false
            if (
                ($coupon->from_date && $now->lt($coupon->from_date)) ||  // now < from_date => not valid yet
                ($coupon->to_date && $now->gt($coupon->to_date))         // now > to_date => expired
            ) {
                throw new \Exception("Coupon is not valid at this time.");
            }
        }


        if ($coupon->usage_limit_per_user) {
            $usageCount = $coupon->usages()->where('user_id', $user->id)->count();
            if ($usageCount >= $coupon->usage_limit_per_user) {
                throw new Exception("You have already used this coupon the maximum number of times.");
            }
        }

        if ($coupon->usage_limit_total) {
            $totalUsageCount = $coupon->usages()->count();
            if ($totalUsageCount >= $coupon->usage_limit_total) {
                throw new Exception("This coupon has been fully used.");
            }
        }

        return $coupon;
    }

    /**
     * Apply coupon to a cart for a given user.
     * 
     * @param string $code Coupon code
     * @param User $user The user applying the coupon
     * @param Collection $cartItems Collection of cart items (should have product relations)
     * @param int|null $orderId Optional order ID when recording usage
     * 
     * @return array Discount info and eligible items
     * 
     * @throws Exception if coupon is invalid or not applicable
     */
    public function applyCoupon(string $code, User $user, Collection $cartItems, ?int $orderId = null): array
    {
        $coupon = $this->validateCoupon($code, $user);

        // Filter only the eligible cart items based on coupon condition type
        $eligibleItems = $this->filterEligibleItems($coupon, $cartItems);

        // Calculate subtotal for eligible items
        $subtotal = $eligibleItems->sum(fn($item) => $item->price * $item->quantity);

        // Check subtotal condition (if any)
        if ($coupon->condition_type === 'subtotal' && $subtotal < $coupon->min_subtotal) {
            throw new Exception("Cart subtotal does not meet coupon requirements.");
        }

        // Calculate discount amount
        $discount = $this->calculateDiscount($coupon, $subtotal);

        if ($discount <= 0) {
            throw new Exception("Coupon does not provide any discount for the current cart.");
        }

        // Record coupon usage if orderId is provided (finalizing order)
        if ($orderId) {
            $this->recordCouponUsage($coupon, $user, $orderId);
        }

        return [
            'discount' => $discount,
            'eligible_items' => $eligibleItems,
            'message' => 'Coupon applied successfully'
        ];
    }

    // Filters cart items eligible for discount based on coupon conditions
    protected function filterEligibleItems(Coupon $coupon, Collection $cartItems): Collection
    {
        switch ($coupon->condition_type) {
            case 'category':
                $allowedIds = $coupon->productCategories->pluck('id');
                return $cartItems->filter(fn($item) => $allowedIds->contains($item->product->category_id));

            case 'brand':
                $allowedIds = $coupon->productBrands->pluck('id');
                return $cartItems->filter(fn($item) => $allowedIds->contains($item->product->brand_id));

            case 'product':
                $allowedIds = $coupon->products->pluck('id');
                return $cartItems->filter(fn($item) => $allowedIds->contains($item->product->id));

            case 'attribute':
                $allowedIds = $coupon->productAttributes->pluck('id');
                return $cartItems->filter(function ($item) use ($allowedIds) {
                    return $item->product->attributes->pluck('id')->intersect($allowedIds)->isNotEmpty();
                });

            case 'subtotal':
            default:
                // Applies to entire cart (no filtering)
                return $cartItems;
        }
    }

    // Calculate discount amount based on coupon discount type
    protected function calculateDiscount(Coupon $coupon, float $subtotal): float
    {
        if ($coupon->discount_type === 'percentage') {
            return round($subtotal * ($coupon->discount / 100), 2);
        }

        // fixed amount, but not exceeding subtotal
        return min($coupon->discount, $subtotal);
    }

    // Record coupon usage in DB
    public function recordCouponUsage(Coupon $coupon, User $user, int $orderId): void
    {
        CouponUsage::create([
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
            'order_id' => $orderId,
            'used_at' => now(),
        ]);
    }
}
