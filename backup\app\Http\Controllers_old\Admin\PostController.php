<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\PostCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;
use Illuminate\Support\Str;

class PostController extends Controller
{
    private $folderPath = 'posts/';

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Posts';
        $posts = DB::table('posts')
        ->orderBy('posts.id', 'desc')
        ->paginate($this->itemPerPage);
        $sl = SLGenerator($posts);

        $categories = PostCategory::whereStatus(true)->first();
        if (!$categories) {
            $message = 'Please create the category first';
            return redirect()->route('admin.post-categories.create')->with('warning', $message);
        }

        return view('admin.post.index', compact('sl','posts', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Post';
        $categories = PostCategory::whereStatus(true)->first();
        if (!$categories) {
            $message = 'Please create the category first';
            return redirect()->route('admin.post-categories.create')->with('warning', $message);
        }
        $categories = PostCategory::whereStatus(true)->get();
        $post_image_info = Session::get('post_image');
        return view('admin.post.create', compact('categories','post_image_info', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            // 'slug' => 'required|string|alpha_dash|unique:posts',
            'description' => 'nullable|string',
            'category_id' => 'required|string|alpha_dash',
            'image' => 'nullable|image|max:' . $this->maxFileSize,
            'status' => 'nullable|boolean',
            'ordering' => 'nullable|integer',
            'meta_title' => 'nullable|string',
            'meta_description' => 'nullable|string',
            'meta_keyword' => 'nullable|string|max:100'
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|unique:posts,slug'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        $post = new Post;
        $post->title = $request->title;
        $post->slug = $item_slug;
        $post->category_id = $request->category_id;
        $post->meta_title = $request->meta_title;
        $post->meta_description = $request->meta_description;
        $post->meta_keyword = $request->meta_keyword;
        $post->description = $request->description;
        $post->status = $request->status ?? 0;
        $post->ordering = $request->ordering ?? 0;
        $post_image_info = Session::get('post_image');
        if ($post_image_info) {
            $image_name = 'post_image_' . uniqid() . '.' . $post_image_info->extension;
            $folderName = $this->folderName() . $item_slug.'/';
            Storage::move($post_image_info->post_image, $this->folderPath . $folderName.'/' . $image_name);
            $post->post_image = $folderName.'/' . $image_name;
            Session::forget('post_image');
        }
        if ($request->hasFile('post_image')) {
            $folderName = $this->folderName() . $item_slug.'/';
            $imgName = 'post_image_' . uniqid() . '.' . $request->post_image->extension();
            $request->post_image->storeAs($this->folderPath . $folderName, $imgName);
            $post->post_image = $folderName. '/'. $imgName;
        }
        $post->save();

        $message = 'Post successfully added.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.posts.index')->with('success', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Post  $post
     * @return \Illuminate\Http\Response
     */
    public function show(Post $post)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Post  $post
     * @return \Illuminate\Http\Response
     */
    public function edit(Post $post)
    {
        $admin_page_title = 'Edit Post';
        $categories = PostCategory::get();
        return view('admin.post.edit', compact('post','categories', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Post  $post
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Post $post)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            'slug' => 'required|string|alpha_dash|unique:posts,slug,' . $post->id,
            'description' => 'nullable|string',
            'category_id' => 'required|string|alpha_dash',
            'image' => 'nullable|image|max:' . $this->maxFileSize,
            'status' => 'nullable|boolean',
            'ordering' => 'nullable|integer',
            'meta_title' => 'nullable|string',
            'meta_description' => 'nullable|string',
            'meta_keyword' => 'nullable|string|max:100'
        ]);

        $post->title = $request->title;
        $post->slug = $request->slug;
        $post->category_id = $request->category_id;
        $post->meta_title = $request->meta_title;
        $post->meta_description = $request->meta_description;
        $post->meta_keyword = $request->meta_keyword;
        $post->description = $request->description;
        $post->status = $request->status ?? 0;
        $post->ordering = $request->ordering ?? 0;
        if ($request->hasFile('image')) {
            $folderName = $this->folderName() . $item_slug.'/';
            $imgName = str_replace(' ', '_', strtolower($post->title)) . '_' . 'post_image_' . uniqid() . '.' . $request->image->extension();
            $request->image->storeAs($this->folderPath . $folderName, $imgName);
            $post->image = $folderName. '/'. $imgName;
        }
        $post->update();

        $message = 'Post successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.posts.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Post  $post
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $post = Post::findOrFail($id);
        $this->deleteFile($this->folderPath, $post->image);
        $post->delete();

        $posts = DB::table('posts')
        ->orderBy('posts.id', 'desc')
        ->paginate($this->itemPerPage);

        $sl = SLGenerator($posts);

        return view('admin.jquery_live.posts', compact('posts', 'sl'));

    }

    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }

        $post = Post::findOrFail($id);

        $this->deleteFile($this->folderPath, $post->$target);

        $imgName = $target . "_" . $post->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName() . $post->slug.'/';
        $request->$target->storeAs($this->folderPath . $folderName, $imgName);

        $post->$target = $folderName. '/'. $imgName;
        $post->update();

        $url = url('/storage/posts/' . $folderName. '/'. $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileRemove($target, $id)
    {
        $post = Post::findOrFail($id);
        $this->deleteFile($this->folderPath, $post->$target);
        $post->$target = null;
        $post->update();

        return response()->json('success', 200);
    }
}
