<?php

namespace App\Mail;

use App\Models\EmailTemplate;
use App\Models\User;
use App\Models\AlertPreference;
use App\Models\Tracking;
use App\Models\UserGroupMap;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ResetPassword extends Mailable
{
    use Queueable, SerializesModels;

    // public $user = null;
    // public $actionText = null;
    // public $actionUrl = null;
    public $content = null;
    public $pre_header = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($token, $email)
    {
        // $email_template = EmailTemplate::where('type', 'reset_password')->first();
        $this->user = User::where('email', $email)->first();
        $this->reset_password_link = url('/reset-password').'?token='. $token;

        // $data = [
        //     ucfirst($user->first_name), ucfirst($user->last_name),
        //     $reset_password_link,
        // ];


        // $this->subject = $email_template->subject;
        // $this->pre_header = $email_template->pre_header;
        // $this->content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->email_body));
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $user = $this->user;
        $reset_password_link = $this->reset_password_link;
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();

            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'send_password_reset_link') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);

                    $mail_data = (object)[
                        'user' => $user,
                        'reset_password_link' => $reset_password_link,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];

                    $tracking = new Tracking();
                    // $tracking->cash_reward_id = $redeem_cash_reward->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();

                    // if ($user_email_template->sms_status && $user->tel_cell)
                    if ($user_email_template->sms_status && $user->tel_cell)
                    {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone =   $user->tel_cell;
                        // $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }

        $email_templates = EmailTemplate::where('system_type', 'send_password_reset_link')
        ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
        ->select('email_templates.*', 'alert_preferences.user_group_id')
        ->get();

        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });

        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
        ->join('users', 'users.id', 'user_group_maps.user_id')
        ->select(
            'user_group_maps.*',
            'users.email as user_email',
            'users.alert_preferences as user_alert_preferences',
        )
        ->where('users.verified', true)
        ->get();

        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();

                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'send_password_reset_link') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);

                                $mail_data = (object)[
                                    'user' => $user,
                                    'reset_password_link' => $reset_password_link,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];

                                $tracking = new Tracking();
                                // $tracking->cash_reward_id = $redeem_cash_reward->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();

                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell)
                                {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }

                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }

        return 0;

        // return $this->view('view.name');
        // return $this->subject($this->subject)->view('email_templates.reset_password');
    }
}
