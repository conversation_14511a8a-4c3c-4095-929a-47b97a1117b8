<?php

namespace App\Mail;

use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderConfirmed extends Mailable
{
    use Queueable, SerializesModels;

    public $cart = null;
    public $checkout_order = null;
    public $content = null;
    public $pre_header = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($cart, $checkout_order)
    {
        $email_template = EmailTemplate::find(3);
        $data = [
            ucfirst($checkout_order->customer->first_name),
            ucfirst($checkout_order->customer->last_name),
        ];
        $this->subject = $email_template->subject;
        $this->pre_header = $email_template->pre_header;
        $this->content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->email_body));
        if ($email_template->sms_status) {
            $message = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->sms_body));
            if ($checkout_order->customer->tel_cell) {
                app('App\Http\Controllers\Controller')->sendMessage($message, $checkout_order->customer->tel_cell);
            }
        }
        // dd($email_template,$cart, $checkout_order);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)->view('email_templates.order_confirmed');
    }
}
