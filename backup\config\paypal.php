<?php
return [
    // 'client_id' => app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal')->public_key,
    'client_id' => env('PAYPAL_CLIENT_ID', ''),
    // 'client_id' => env('PAYPAL_CLIENT_ID', app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal')->public_key),
    // 'secret' => app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal')->private_key,
    'secret' => env('PAYPAL_SECRET', ''),
    // 'secret' => env('PAYPAL_SECRET', app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal')->private_key),
    'settings' => array(
        'mode' => env('PAYPAL_MODE', 'sandbox'),
        'http.ConnectionTimeOut' => 30,
        'log.LogEnabled' => true,
        'log.FileName' => storage_path() . '/logs/paypal.log',
        'log.LogLevel' => 'ERROR'
    ),
];
