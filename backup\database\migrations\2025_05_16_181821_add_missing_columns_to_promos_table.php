<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToPromosTable extends Migration
{
    public function up()
    {
        Schema::table('promos', function (Blueprint $table) {
            if (!Schema::hasColumn('promos', 'coupon_type_condi_data')) {
                $table->text('coupon_type_condi_data')->nullable();
            }
            if (!Schema::hasColumn('promos', 'coupon_value')) {
                $table->string('coupon_value')->nullable();
            }
            if (!Schema::hasColumn('promos', 'customer_group')) {
                $table->string('customer_group')->nullable();
            }
            if (!Schema::hasColumn('promos', 'uses_per_coupon')) {
                $table->integer('uses_per_coupon')->nullable();
            }
            if (!Schema::hasColumn('promos', 'uses_per_customer')) {
                $table->integer('uses_per_customer')->nullable();
            }
            if (!Schema::hasColumn('promos', 'coupon_type_condition')) {
                $table->string('coupon_type_condition')->nullable();
            }
            if (!Schema::hasColumn('promos', 'send_mail')) {
                $table->boolean('send_mail')->default(0);
            }
            if (!Schema::hasColumn('promos', 'global')) {
                $table->boolean('global')->default(0);
            }
        });
    }

    public function down()
    {
        Schema::table('promos', function (Blueprint $table) {
            if (Schema::hasColumn('promos', 'coupon_type_condi_data')) {
                $table->dropColumn('coupon_type_condi_data');
            }
            if (Schema::hasColumn('promos', 'coupon_value')) {
                $table->dropColumn('coupon_value');
            }
            if (Schema::hasColumn('promos', 'customer_group')) {
                $table->dropColumn('customer_group');
            }
            if (Schema::hasColumn('promos', 'uses_per_coupon')) {
                $table->dropColumn('uses_per_coupon');
            }
            if (Schema::hasColumn('promos', 'uses_per_customer')) {
                $table->dropColumn('uses_per_customer');
            }
            if (Schema::hasColumn('promos', 'coupon_type_condition')) {
                $table->dropColumn('coupon_type_condition');
            }
            if (Schema::hasColumn('promos', 'send_mail')) {
                $table->dropColumn('send_mail');
            }
            if (Schema::hasColumn('promos', 'global')) {
                $table->dropColumn('global');
            }
        });
    }
}
