<?php

namespace App\Listeners;

use App\Events\ReviewNotification;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendReviewNotification implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(ReviewNotification $event)
    {
        $this->notifier->sendCustomerReviewNotifications($event->productItem, $event->review, $event->user, $event->message_status,  $event->template);
        //$this->notifier->sendAdminOrderDeliveredNotifications($event->order, $event->cart);
    }
}
