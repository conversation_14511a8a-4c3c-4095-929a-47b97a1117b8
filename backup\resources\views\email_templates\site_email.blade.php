<!doctype html>
<html>

<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>{{ site_info_mail('site_name') }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap" rel="stylesheet">

</head>

<body style="margin:0; padding:0; background-color:#f6f6f6; font-family: 'Montserrat', Arial, sans-serif; font-size:14px; line-height:1.6; color:#333;">
    <div style="display:none; max-height:0; overflow:hidden;">{!! $pre_header !!}</div>

    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" bgcolor="#f6f6f6" style="padding: 20px 0;">
        <tr>
            <td align="center">

                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" bgcolor="#ffffff" style="border-radius:8px; box-shadow:0 0 10px rgba(0,0,0,0.05);">
                    <!-- Header -->
                    <tr bgColor="#f1bb00">
                        <td style="padding: 20px; border-bottom: 2px solid #f1bb00;">

                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>

                                    <!-- Logo -->
                                    <td style="width: 40%; vertical-align: middle;">
                                        @if(site_info_mail('email_template_logo'))
                                        <a href="{{ url('/') }}" style="text-decoration:none;">
                                            <!-- <img src="{{ asset('storage/setting/' . site_info_mail('email_template_logo')) }}" alt="{{ site_info_mail('site_name') }}" width="150" style="display:block; border:none; outline:none; text-decoration:none;"> -->
                                            <img src="https://www.buy.codeitjs.com/storage/setting/2024/06-2024/email_template_logo_578678594.png" alt="{{ site_info_mail('site_name') }}" width="150" style="display:block; border:none; outline:none; text-decoration:none;">


                                        </a>
                                        @else
                                        <h1 style="margin:0; font-size: 24px; color:#000; font-weight: 700;">{{ site_info_mail('site_name') }}</h1>
                                        @endif
                                    </td>

                                    <!-- Contact Info -->
                                    <td style="width: 60%; text-align: right; vertical-align: middle; font-size: 14px; color: #444444; font-weight: 500;">
                                        @php
                                        $email = site_info_mail('email', $store_location) ?? site_info_mail('email');
                                        @endphp

                                        @if($email)
                                        <div style="margin-bottom: 6px;">
                                            <strong>Email:</strong> <a href="mailto:{{ $email }}" style="color:#444444; text-decoration:none;font-weight: bold;">{{ $email }}</a>
                                        </div>
                                        @endif


                                        @php
                                        $telCell = site_info_mail('tel_cell', $store_location) ?? site_info_mail('tel_cell');
                                        @endphp

                                        @if($telCell)
                                        <div>
                                            <strong>Call:</strong> <a href="tel:{{ $telCell }}" style="color:#444444; text-decoration:none;font-weight: bold;">{{ $telCell }}</a>
                                        </div>
                                        @endif

                                    </td>
                                </tr>
                            </table>

                        </td>
                    </tr>

                    <!-- Body Content -->
                    <tr>
                        <td style="padding: 30px 20px; font-size: 14px; line-height: 1.6; color: #333333;">
                            {!! $content !!}

                            {{-- Optional CTA --}}
                            @if(isset($cta_url) && isset($cta_text))
                            <div style="text-align:center; margin-top: 30px;">
                                <a href="{{ $cta_url }}" style="background-color:#f1bb00; color:#000; padding: 12px 24px; border-radius: 5px; font-weight: bold; text-decoration:none; display:inline-block;">
                                    {{ $cta_text }}
                                </a>
                            </div>
                            @endif

                            {{-- Example order list table with inline styles --}}
                            @if(isset($order_items))
                            <table role="presentation" cellspacing="0" cellpadding="8" border="1" width="100%" style="border-collapse: collapse; margin-top: 30px; font-size: 14px; color: #333;">
                                <thead>
                                    <tr style="background-color: #f1bb00; color: #000;">
                                        <th align="left" style="padding: 10px;">Item</th>
                                        <th align="left" style="padding: 10px;">Quantity</th>
                                        <th align="right" style="padding: 10px;">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order_items as $item)
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #ddd;">{{ $item->name }}</td>
                                        <td style="padding: 8px; border: 1px solid #ddd;">{{ $item->quantity }}</td>
                                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">{{ $item->price }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            @endif
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        @php
                        $siteName = site_info_mail('site_name', $store_location) ?? site_info_mail('site_name');
                        $address = site_info_mail('address', $store_location) ?? site_info_mail('address');
                        $zipCode = site_info_mail('zip_code', $store_location) ?? site_info_mail('zip_code');
                        $country = site_info_mail('country', $store_location) ?? site_info_mail('country');
                        $socialData = @unserialize(site_info_mail('socials', $store_location) ?? site_info_mail('socials'));
                        @endphp

                        <td style="background-color:#f9f9f9; padding: 20px; text-align: center; font-size: 12px; color: #999;">
                            <p style="margin:0 0 10px;">
                                <strong>{{ $siteName }}</strong><br>
                                {{ $address }} - {{ $zipCode }}, {{ $country }}<br>
                                <span>If we served you right, refer someone to us!</span><br>
                                &copy; 2018 - {{ date('Y') }} {{ $siteName }}
                            </p>
                            <p><a href="{{ $signedUrl }}">
                                    Unsubscribe
                                </a></p>
                            @if(is_object($socialData))
                            <div style="margin-top: 15px;">
                                @foreach((array) $socialData as $item)
                                @if(is_object($item) && !empty($item->url))
                                <a href="{{ $item->url }}" target="_blank" style="display:inline-block; margin: 0 6px;">
                                    <img src="{{ $item->email_icon ?? '' }}" alt="{{ $item->name ?? 'social' }}" width="auto" height="auto" style="vertical-align:middle;">
                                </a>
                                @endif
                                @endforeach
                            </div>
                            @endif
                        </td>

                    </tr>

                </table>

            </td>
        </tr>
    </table>

</body>

</html>