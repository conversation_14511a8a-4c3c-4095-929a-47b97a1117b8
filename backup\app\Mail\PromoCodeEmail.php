<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PromoCodeEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $details;

    public function __construct($details)
    {
        $this->details = (object) $details; // allow object-style access
        $this->subject = $details->subject;
    }

    public function build()
    {
        return $this->subject($this->subject)
            ->view('email_templates.promo_code');
    }
}
