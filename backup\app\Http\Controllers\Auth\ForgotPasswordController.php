<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use App\Mail\ResetPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Validator;
use Session;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use App\Models\AlertPreference;
use App\Models\Tracking;
use App\Models\UserGroupMap;
use App\Models\EmailTemplate;
use App\Mail\SiteEmail;

class ForgotPasswordController extends Controller
{
    use ApiResponseHelpers;

    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    use SendsPasswordResetEmails;

    public function __construct()
    {
        $this->middleware('guest');
    }

    // protected function validator(array $data)
    // {
    //     return Validator::make($data, [
    //         'email' => 'required|string|email|max:255|exists:users',
    //     ]);
    // }

    public function sendResetLinkEmail(Request $request): JsonResponse
    {
        // if ($this->validator($request->all())->fails()) {
        //     return $this->respondCreated($this->validator($request->all())->messages());
        // }
        $this->validate($request, [
                'email' => 'required|string|email|max:255|exists:users',
                'g-recaptcha-response' => 'required|recaptchav3:forgot_password,0.9',
            ],
            [
                'email.exists' => 'You are not authorized to reset password'
            ]
        );
        $user = User::where('email', request()->input('email'))->first();
        $token = Password::getRepository()->createNewToken();
        DB::table(config('auth.passwords.users.table'))->insert([
            'email' => $user->email,
            'token' => $token,
            'created_at' => Carbon::now()
        ]);

        $reset_password_link = url('/reset-password') . '?token=' . $token;

        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();

            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'send_password_reset_link') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);

                    $mail_data = (object)[
                        'user' => $user,
                        'reset_password_link' => $reset_password_link,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];

                    $tracking = new Tracking();
                    // $tracking->cash_reward_id = $redeem_cash_reward->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();

                    if ($user_email_template->sms_status && $user->tel_cell)
                    // if ($user_email_template->sms_status && $user->tel_cell)
                    {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        // $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }

        $email_templates = EmailTemplate::where('system_type', 'send_password_reset_link')
        ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
        ->select('email_templates.*', 'alert_preferences.user_group_id')
        ->get();

        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });

        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
        ->join('users',
            'users.id',
            'user_group_maps.user_id'
        )
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();

        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();

                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'send_password_reset_link') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);

                                $mail_data = (object)[
                                    'user' => $user,
                                    'reset_password_link' => $reset_password_link,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                            $mail_data->content = $this->sendEmailData($mail_data)->content;
                                            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];

                                $tracking = new Tracking();
                                // $tracking->cash_reward_id = $redeem_cash_reward->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();

                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell)
                                {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->to_phone =  $user->tel_cell;
                                    $sms_tracking->update();
                                }

                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }

        // Mail::to(request()->email)->send(new ResetPassword($token, $user->email));

        return $this->respondOk('Reset info sent to your email');
        // return response()->json([
        //     'message' => 'check your inbox',
        //     'url' => route('home'),
        //     // 'url' => route('password.thanks'),
        //     // 'email' =>  $user->email,
        // ], 200);
        // return redirect()->route('password.thanks')->with('email', $user->email);
    }

    public function afterSendResetLink()
    {
        $email = Session::get('email');
        if (!$email) return redirect()->with('success', 'your session expire');
        // if (!$email) return redirect()->to('/');
        // $page = (object) [
        //     'slug' => 'thank-you-reset',
        //     'meta_title' => 'Welcome',
        //     'title' => 'Reset Password',
        //     'content' => 'Password verification link send to this email ' . $email,
        // ];
        // $page->top_menu = $this->getMenu('top_menu', $page->slug);
        // $page->menu = $this->getMenu('main_menu', $page->slug);
        // return view('web.thanks', compact('page'));

        return redirect()->with('success', 'afterSendResetLink');
    }
}
