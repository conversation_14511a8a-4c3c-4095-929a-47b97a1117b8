<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WhatsappTemplateMeta;
use Illuminate\Http\Request;

class WhatsappTemplateMetaController extends Controller
{
    // Display list of templates
    public function index()
    {
        $admin_page_title  = 'SMS templates';
        $templates = WhatsappTemplateMeta::latest()->paginate(10);
        return view('admin.whatsapp-template-metas.index', compact('templates', 'admin_page_title'));
    }

    // Show form to create new template
    public function create()
    {
        $admin_page_title  = 'Create SMS templates';

        return view('admin.whatsapp-template-metas.create', compact('admin_page_title'));
    }

    // Store new template
    public function store(Request $request)
    {
        $request->validate([
            'template_id' => 'required|unique:whatsapp_template_metas,template_id',
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'variables' => 'required|array',
        ]);

        WhatsappTemplateMeta::create([
            'template_id' => $request->template_id,
            'title' => $request->title,
            'body' => $request->body,
            'variables' => json_encode($request->variables),
        ]);
        $message = 'Template created successfully.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.whatsapp-template-metas.index')->with('success', $message);
    }

    // Show form to edit existing template
    public function edit($id)
    {
        $admin_page_title  = 'Update SMS templates';

        $template = WhatsappTemplateMeta::findOrFail($id);
        $template->variables = json_decode($template->variables, true);
        return view('admin.whatsapp-template-metas.edit', compact('template', 'admin_page_title'));
    }

    // Update template
    public function update(Request $request, $id)
    {
        try {

            $template = WhatsappTemplateMeta::findOrFail($id);

            $request->validate([
                'title' => 'required|string|max:255',
                'body' => 'required|string',
                'variables' => 'required|array',       // expecting array here
                'variables.*' => 'string',             // each variable is a string
            ]);

            $template->title = $request->title;
            $template->body = $request->body;
            $template->variables = json_encode($request->variables);
            $template->save();
        } catch (\Exception $e) {
        }
        $message = 'Template updated successfully.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.whatsapp-template-metas.index')->with('success', $message);
    }


    // Delete template
    public function destroy(WhatsappTemplateMeta $whatsappTemplateMeta)
    {
        $whatsappTemplateMeta->delete();

        return redirect()->route('admin.whatsapp-template-metas.index')
            ->with('success', 'Template deleted successfully.');
    }
}
