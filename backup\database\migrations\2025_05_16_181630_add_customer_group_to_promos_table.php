<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCustomerGroupToPromosTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('promos') && !Schema::hasColumn('promos', 'customer_group')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->string('customer_group')->nullable();
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('promos') && Schema::hasColumn('promos', 'customer_group')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->dropColumn('customer_group');
            });
        }
    }
}
