<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateNotifyItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notify_items', function (Blueprint $table) {
            $table->id();
            $table->string('notify_email');
            $table->string('notify_phone');
            $table->foreignId('product_item_id')->constrained('product_items');
            $table->boolean('status_phone');
            $table->boolean('status_email'); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notify_items');
    }
}
