<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class NotifyMeOrder
{
    use SerializesModels;

    public $email;
    public $phone;
    public $productItem;
    public $message_status;

    public function __construct($email, $phone, $productItem, $message_status)
    {
        $this->email = $email;
        $this->phone = $phone;
        $this->productItem = $productItem;
        $this->message_status = $message_status;
    }
}
