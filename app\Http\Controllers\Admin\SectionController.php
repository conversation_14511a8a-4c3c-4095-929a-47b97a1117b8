<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
// use App\Models\Page;
use App\Models\Section;
use Illuminate\Http\Request;
// use Illuminate\Support\Facades\DB;
use Validator;
use Session;

class SectionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($page_id=null)
    {
        $items = Section::where('page_id', $page_id)->orderBy('sections.ordering')->paginate($this->itemPerPage);
        if(empty($page_id)){
            if (Session::has('page_sections')) {
                $items = collect(Session::get('page_sections'))->sortBy('ordering')->paginate($this->itemPerPage);
            }
        }
        // dd($items);
        $sl = SLGenerator($items);

        return view('admin.jquery_live.sections', compact('items', 'sl', 'page_id'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request,$page_id=null)
    {
        $validator = Validator::make($request->all(), [
            'primary_title' => 'required|string|max:195',
            'primary_title_color' => 'required|string|max:50',
            'secondary_title' => 'nullable|string|max:195',
            'secondary_title_color' => 'nullable|string|max:50',

            'section_identifier' => 'nullable|string|max:50',
            'section_class' => 'nullable|string|max:50',
            'section_position' => 'required|string|max:50',
            'section_background' => 'nullable|string|max:50',
            'section_background_2' => 'nullable|string|max:50',
            'section_link' => 'nullable|string|max:50',

            'module_type' => 'required|string|max:50',
            'module_class' => 'nullable|string|max:50',
            'module_background' => 'required|string|max:50',
            'module_text_color' => 'required|string|max:50',

            'module_content' => 'nullable|string',

            'primary_button' => 'nullable|string',
            'primary_button_link' => 'nullable|string',
            'secondary_button' => 'nullable|string',
            'secondary_button_link' => 'nullable|string',

            'remark' => 'nullable|string|max:150',
            'section_ordering' => 'nullable|numeric',
            'section_status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        $section_meta = (object)[
            'show_primary_title' => $request->show_primary_title ?? 0,
            'section_title_class' => $request->section_title_class,
            'secondary_title' => $request->secondary_title,
            'primary_title_color' => $request->primary_title_color,
            'secondary_title' => $request->secondary_title,
            'secondary_title_color' => $request->secondary_title_color,
            'section_title_class' => $request->section_title_class,
            'section_identifier' => $request->section_identifier,
            'section_class' => $request->section_class,
            // 'section_position' => $request->section_position,
            'section_background' => $request->section_background,
            'section_background_2' => $request->section_background_2,
            'section_link' => $request->section_link,
            'module_type' => $request->module_type,
            'slideshow_id' => $request->slideshow_id,
            'module_class' => $request->module_class,
            'module_background' => $request->module_background,
            'module_text_color' => $request->module_text_color,
            'module_content' => $request->module_content,
            'primary_button' => $request->primary_button,
            'primary_button_link' => $request->primary_button_link,
            'secondary_button' => $request->secondary_button,
            'secondary_button_link' => $request->secondary_button_link,
        ];
        if($page_id){
            $item = new Section();
            $item->title =  $request->primary_title;
            $item->page_id = $page_id;
            $item->section_position = $request->section_position;
            $item->module_type = $request->module_type;
            $item->section_meta = serialize($section_meta);
            $item->remark = $request->remark ?? null;
            $item->ordering = $request->section_ordering ?? 0;
            $item->status = $request->section_status ?? 0;
            $item->save();

        }else{
            $item = (object)[
                'id' => uniqid(),
                'title' => $request->primary_title,
                'section_position' => $request->section_position,
                'module_type' => $request->module_type,
                'section_meta' => $section_meta,
                'remark' => $request->remark ?? null,
                'ordering' => $request->section_ordering ?? 0,
                'status' => $request->section_status ?? 0,
            ];
            if (Session::has('page_sections')) {
                $items = Session::get('page_sections');
                array_push($items, $item);
            }else{
                $items = array($item);
            }

            Session::put([
                'page_sections' => $items,
            ]);
        }

        $message = $request->primary_title . ' successfully save';

        return response()->json([
            'status' => 'success',
            'message' => $message,
        ], 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Section  $section
     * @return \Illuminate\Http\Response
     */
    public function show(Section $section)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Section  $section
     * @return \Illuminate\Http\Response
     */
    public function edit($id, $page_id =null)
    {
        if(!empty($page_id)){
            $item = Section::find($id);
            $item->section_meta = unserialize($item->section_meta);
        }else{
            if (Session::has('page_sections')) {
                $items = Session::get('page_sections');
                foreach($items as $value){
                    if($value->id == $id){
                        return response()->json($value, 200);
                        break;
                    }
                }
            }
        }

        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Section  $section
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id, $page_id = null)
    {
        $validator = Validator::make($request->all(), [
            'primary_title' => 'required|string|max:191',
            'primary_title_color' => 'required|string|max:50',
            'secondary_title' => 'nullable|string|max:191',
            'secondary_title_color' => 'nullable|string|max:50',

            'section_identifier' => 'nullable|string|max:50',
            'section_class' => 'nullable|string|max:50',
            'section_position' => 'required|string|max:50',
            'section_background' => 'nullable|string|max:50',
            'section_background_2' => 'nullable|string|max:50',
            'section_link' => 'nullable|string|max:50',

            'module_type' => 'required|string|max:50',
            'module_class' => 'nullable|string|max:50',
            'module_background' => 'required|string|max:50',
            'module_text_color' => 'required|string|max:50',

            'module_content' => 'nullable|string',

            'primary_button' => 'nullable|string',
            'primary_button_link' => 'nullable|string',
            'secondary_button' => 'nullable|string',
            'secondary_button_link' => 'nullable|string',

            'remark' => 'nullable|string|max:150',
            'section_ordering' => 'nullable|numeric',
            'section_status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        $section_meta = (object)[
            'show_primary_title' => $request->show_primary_title ?? 0,
            'section_title_class' => $request->section_title_class,
            'secondary_title' => $request->secondary_title,
            'primary_title_color' => $request->primary_title_color,
            'secondary_title' => $request->secondary_title,
            'secondary_title_color' => $request->secondary_title_color,
            'section_identifier' => $request->section_identifier,
            'section_class' => $request->section_class,
            // 'section_position' => $request->section_position,
            'section_background' => $request->section_background,
            'section_background_2' => $request->section_background_2,
            'section_link' => $request->section_link,
            'module_type' => $request->module_type,
            'slideshow_id' => $request->slideshow_id,
            'module_class' => $request->module_class,
            'module_background' => $request->module_background,
            'module_text_color' => $request->module_text_color,
            'module_content' => $request->module_content,
            'primary_button' => $request->primary_button,
            'primary_button_link' => $request->primary_button_link,
            'secondary_button' => $request->secondary_button,
            'secondary_button_link' => $request->secondary_button_link,
        ];

        if (!empty($page_id)) {
            $item = Section::findOrFail($id);
            $item->title =  $request->primary_title;
            $item->page_id = $page_id;
            $item->section_position = $request->section_position;
            $item->module_type = $request->module_type;
            $item->section_meta = serialize($section_meta);
            $item->remark = $request->remark ?? null;
            $item->ordering = $request->section_ordering ?? 0;
            $item->status = $request->section_status ?? 0;
            $item->update();
        }else{
            if (Session::has('page_sections')) {
                $items = Session::get('page_sections');
                foreach ($items as $value) {
                    if ($value->id == $id) {
                        $value->title = $request->primary_title;
                        $value->section_position = $request->section_position;
                        $value->module_type = $request->module_type;
                        $value->section_meta = $section_meta;
                        $value->remark = $request->remark ?? null;
                        $value->ordering = $request->section_ordering ?? 0;
                        $value->status = $request->section_status ?? 0;
                    }
                }
                Session::put([
                    'page_sections' => $items,
                ]);
            }
        }

        $message = $request->primary_title . ' successfully save';

        return response()->json([
            'status' => 'success',
            'message' => $message,
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Section  $section
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, $page_id = null)
    {
        if($page_id){
            $item = Section::findOrFail($id);
            $item->delete();

            $message = $item->title . 'Delete successfully';
        }else{
            if (Session::has('page_sections')) {
                $items = Session::get('page_sections');
                foreach ($items as $key => $value) {
                    if ($value->id == $id) {
                        $message = $value->title . 'Delete successfully';
                        unset($items[$key]);
                    }
                }
                Session::put([
                    'page_sections' => $items,
                ]);
                if(count($items) == 0){
                    Session::forget('page_sections');
                }
            }
        }

        return response()->json(
            [
                'status' => 'success',
                'message' => $message
            ],
            200
        );
    }
}
