<?php

namespace App\Mail;

use App\Models\EmailCampaign;
use Illuminate\Bus\Queueable;
// use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EmailSchedule extends Mailable
{
    use Queueable, SerializesModels;

    public $user = null;
    public $content = null;
    public $pre_header = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($id, $user)
    {
        $campaign = EmailCampaign::find($id);

        $data = [
            ucfirst($user->first_name),
            ucfirst($user->last_name),
        ];
        $this->subject = $campaign->subject;
        $this->pre_header = $campaign->pre_header;
        $this->content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($campaign->email_body));
        if ($campaign->sms_status) {
            $message = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($campaign->sms_body));
            if($user->tel_cell){
                app('App\Http\Controllers\Controller')->sendMessage($message, $user->tel_cell_country_code . $user->tel_cell);
            }
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)->view('email_templates.email_schedule');
    }
}
