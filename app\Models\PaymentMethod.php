<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Session;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'title',
        'subtitle',
        'logo_url',
        'type',
        'is_active',
        'sort_order',
        'api_config',
        'payment_options',
        'allowed_countries',
        'allowed_currencies',
        'supported_currencies',
        'discount_type',
        'discount_value',
        'discount_conditions',
        'fee_bearer',
        'fee_percentage',
        'fee_fixed',
        'split_ratio',
        'supports_split_payment',
        'split_payment_options',
        'split_payment_threshold',
        'min_amount',
        'max_amount',
        'max_single_transaction',
        'supports_partial_transactions',
        'supports_refunds',
        'supports_recurring',
        'webhook_config',
        'sandbox_mode',
        'sandbox_config',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'api_config' => 'array',
        'payment_options' => 'array',
        'allowed_countries' => 'array',
        'allowed_currencies' => 'array',
        'supported_currencies' => 'array',
        'discount_conditions' => 'array',
        'split_payment_options' => 'array',
        'webhook_config' => 'array',
        'sandbox_config' => 'array',
        'supports_split_payment' => 'boolean',
        'supports_partial_transactions' => 'boolean',
        'supports_refunds' => 'boolean',
        'supports_recurring' => 'boolean',
        'sandbox_mode' => 'boolean',
        'discount_value' => 'decimal:2',
        'fee_percentage' => 'decimal:2',
        'fee_fixed' => 'decimal:2',
        'split_ratio' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'max_single_transaction' => 'decimal:2',
        'split_payment_threshold' => 'decimal:2',
    ];

    /**
     * Scope to get active payment methods
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get methods by type
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get methods available for a specific country
     */
    public function scopeForCountry(Builder $query, string $countryCode): Builder
    {
        $countryCode = strtoupper($countryCode);

        return $query->where(function ($q) use ($countryCode) {
            $q->whereJsonContains('allowed_countries', $countryCode)
                ->orWhereNull('allowed_countries');
        });
    }

    /**
     * Scope to get methods that support a specific currency
     */
    public function scopeForCurrency(Builder $query, string $currency): Builder
    {
        return $query->where(function ($q) use ($currency) {
            $q->whereJsonContains('allowed_currencies', $currency)
                ->orWhereNull('allowed_currencies');
        });
    }

    /**
     * Get payment methods available for checkout based on country and currency
     */
    public static function getAvailableForCheckout(string $country, string $currency): \Illuminate\Database\Eloquent\Collection
    {
        $country = strtoupper($country);

        return static::active()
            ->forCountry($country)
            ->forCurrency($currency)
            ->orderBy('sort_order')
            ->orderBy('title')
            ->get();
    }

    /**
     * Check if payment method supports a specific amount
     */
    public function supportsAmount(float $amount): bool
    {
        if ($this->min_amount && $amount < $this->min_amount) {
            return false;
        }

        if ($this->max_amount && $amount > $this->max_amount) {
            return false;
        }

        return true;
    }

    /**
     * Check if amount exceeds single transaction limit
     */
    public function exceedsSingleTransactionLimit(float $amount): bool
    {
        return $this->max_single_transaction && $amount > $this->max_single_transaction;
    }

    /**
     * Calculate number of partial transactions needed
     */
    public function calculatePartialTransactions(float $amount): array
    {
        if (!$this->exceedsSingleTransactionLimit($amount)) {
            return [$amount];
        }

        if (!$this->supports_partial_transactions) {
            throw new \Exception("Payment method does not support partial transactions for amount: {$amount}");
        }

        $transactions = [];
        $remaining = $amount;
        $maxPerTransaction = $this->max_single_transaction;

        while ($remaining > 0) {
            $transactionAmount = min($remaining, $maxPerTransaction);
            $transactions[] = $transactionAmount;
            $remaining -= $transactionAmount;
        }

        return $transactions;
    }

    /**
     * Calculate total fees for an amount
     */
    public function calculateFees(float $amount): array
    {
        $partialTransactions = $this->calculatePartialTransactions($amount);
        $totalFees = 0;
        $feePerTransaction = [];
        foreach ($partialTransactions as $transactionAmount) {
            $fee = ($transactionAmount * $this->fee_percentage / 100) + $this->fee_fixed;
            $feePerTransaction[] = $fee;
            $totalFees += $fee;
        }

        return [
            'total_fees' => $totalFees,
            'fee_per_transaction' => $feePerTransaction,
            'transaction_count' => count($partialTransactions),
            'partial_transactions' => $partialTransactions
        ];
    }

    /**
     * Get discount amount for a specific currency and amount
     */
    public function getDiscountAmount(float $amount, string $currency): float
    {
        if ($this->discount_type === 'none') {
            return 0;
        }

        // Check currency-specific discounts
        if ($this->discount_conditions && isset($this->discount_conditions[$currency])) {
            $discountConfig = $this->discount_conditions[$currency];
            $discountValue = $discountConfig['value'] ?? $this->discount_value;
            $discountType = $discountConfig['type'] ?? $this->discount_type;
        } else {
            $discountValue = $this->discount_value;
            $discountType = $this->discount_type;
        }

        if ($discountType === 'percentage') {
            return $amount * ($discountValue / 100);
        } elseif ($discountType === 'fixed') {
            return min($discountValue, $amount); // Don't exceed the amount
        }

        return 0;
    }
    public function getSplitPaymentConfiguration(string $splitOption): ?array
    {
        if (!$this->supports_split_payment || !$this->split_payment_options) {
            return null;
        }

        // Check if the requested option exists in our split_payment_options
        if (!in_array($splitOption, $this->split_payment_options)) {
            return null;
        }

        // Parse the option using the same logic as getSplitPaymentConfigurations
        [$first, $second] = explode('-', $splitOption);

        return [
            'label' => "{$first}% - {$second}%",
            'value' => $splitOption,
            'first_percentage' => (int)$first,
            'second_percentage' => (int)$second,
        ];
    }
    /**
     * Get split payment configurations
     */
    public function getSplitPaymentConfigurations(): array
    {
        if (!$this->supports_split_payment || !$this->split_payment_options) {
            return [];
        }

        $configurations = [];
        foreach ($this->split_payment_options as $option) {
            [$first, $second] = explode('-', $option);
            $configurations[] = [
                'label' => "{$first}% - {$second}%",
                'value' => $option,
                'first_percentage' => (int)$first,
                'second_percentage' => (int)$second,
            ];
        }

        return $configurations;
    }

    /**
     * Calculate split payment amounts
     */
    public function calculateSplitPayment($totalAmount, $splitOption, $includeShipping = true)
    {
        $splitConfig = $this->getSplitPaymentConfiguration($splitOption);

        if (!$splitConfig) {
            throw new \Exception("Invalid split option: {$splitOption}");
        }

        // Include shipping in total if specified
        // if ($includeShipping) {
        //     $shippingAmount = Session::get('shippingAmount', 0);
        //     $totalAmount += $shippingAmount;
        // }

        $firstAmount = round($totalAmount * $splitConfig['first_percentage'] / 100, 2);
        $secondAmount = round($totalAmount * $splitConfig['second_percentage'] / 100, 2);

        // Ensure amounts add up correctly
        $difference = $totalAmount - ($firstAmount + $secondAmount);
        if (abs($difference) > 0.01) {
            $secondAmount += $difference;
        }

        return [
            'total_amount' => $totalAmount,
            'first_transaction' => [
                'amount' => $firstAmount,
                'percentage' => $splitConfig['first_percentage']
            ],
            'second_transaction' => [
                'amount' => $secondAmount,
                'percentage' => $splitConfig['second_percentage']
            ],
            'split_option' => $splitOption
        ];
    }

    /**
     * Get API configuration for current environment
     */
    public function getApiConfig(): array
    {
        // if ($this->sandbox_mode && $this->sandbox_config) {
        //     return array_merge($this->api_config ?? [], $this->sandbox_config);
        // }
        return $this->api_config ?? [];
    }

    /**
     * Check if payment method is available for given conditions
     */
    public function isAvailable(string $country, string $currency, float $amount): bool
    {
        $country = strtoupper($country);
        if (!$this->is_active) {
            return false;
        }

        // Check country restriction
        if ($this->allowed_countries && !in_array($country, $this->allowed_countries)) {
            return false;
        }

        // Check currency restriction
        if ($this->allowed_currencies && !in_array($currency, $this->allowed_currencies)) {
            return false;
        }

        // Check amount limits
        if (!$this->supportsAmount($amount)) {
            return false;
        }

        return true;
    }

    /**
     * Get fee breakdown for customer display
     */
    public function getFeeBreakdown(float $amount): array
    {
        $feeCalculation = $this->calculateFees($amount);

        switch ($this->fee_bearer) {
            case 'customer':
                $customerFees = $feeCalculation['total_fees'];
                $systemFees = 0;
                break;
            case 'system':
                $customerFees = 0;
                $systemFees = $feeCalculation['total_fees'];
                break;
            case 'split':
                $customerFees = $feeCalculation['total_fees'];
                $systemFees = $feeCalculation['total_fees'];
                break;
            default:
                $customerFees = $feeCalculation['total_fees'];
                $systemFees = 0;
        }

        return [
            'customer_fees' => $customerFees,
            'system_fees' => $systemFees,
            'total_fees' => $feeCalculation['total_fees'],
            'fee_bearer' => $this->fee_bearer,
            'transaction_count' => $feeCalculation['transaction_count']
        ];
    }
}
