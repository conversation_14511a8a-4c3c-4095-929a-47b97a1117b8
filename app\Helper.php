<?php

use libphonenumber\PhoneNumberUtil;
use Illuminate\Support\Facades\Http;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\NumberParseException;
use App\Models\GeneralSetting;
use App\Models\WhatsAppTemplateMeta;
use App\Models\Setting;

function site_info($key = null)
{
    if (\Session::has('cart')) {
        $cart = \Session::get('cart');
        if ($cart->paid_grand_total_local > 0) {
            $transaction_methods = \App\Models\TransactionMethod::where('status', true)->orderBy('ordering')->where('method_key', 'pay_by_squadco')->get();
        } else {
            $transaction_methods = \App\Models\TransactionMethod::where('status', true)->orderBy('ordering')->get();
        }
    } else {
        $transaction_methods = \App\Models\TransactionMethod::where('status', true)->orderBy('ordering')->get();
    }

    $settings = \App\Models\Setting::first();
    $settings->transaction_methods = $transaction_methods;
    if ($settings->business_hours) {
        $settings->business_hours = unserialize($settings->business_hours);
    } else {
        $settings->business_hours = array();
    }

    if ($settings->socials) {
        $settings->socials = unserialize($settings->socials);
    } else {
        $settings->socials = array();
    }

    if (!$key) {
        return $settings;
    }
    return  $settings->$key;
}
function site_info_mail($key, $storeId = null)
{
    $settings = \App\Models\Setting::where('id', $storeId ?? null)->first() ?? \App\Models\Setting::first();

    if (!$key) {
        return $settings;
    }
    return $settings->$key;
}
// function transactionMethod($option){
//     $transaction_method = \App\Models\TransactionMethod::where('method_key', $option)->first();

//     return $transaction_method;
// }

function trim_text($text, $count)
{
    $string = explode(" ", $text);
    $trimText = null;

    $counter = 0;
    foreach ($string as $item) {
        if ($counter < $count) {
            $trimText .= " " . $item;
        } else {
            $trimText .= "...";
            break;
        }
        $counter++;
    }

    $trimText = trim($trimText);

    return $trimText;
}
if (!function_exists('getPhoneNumberCountry')) {
    /**
     * Get the country of a phone number.
     *
     * @param string $phoneNumber The phone number to parse.
     * @return string|null The country name or null if it can't be determined.
     */
    function getPhoneNumberCountry(string $phoneNumber): ?string
    {
        try {
            // Initialize the PhoneNumberUtil
            $phoneUtil = PhoneNumberUtil::getInstance();

            // Parse the phone number
            $number = $phoneUtil->parse($phoneNumber, null); // Pass `null` to auto-detect country

            // Get the country code from the parsed number
            $countryCode = $phoneUtil->getRegionCodeForNumber($number);

            // Return the country code or null if it cannot determine
            return $countryCode ? $countryCode : null;
        } catch (NumberParseException $e) {
            // Handle errors gracefully
            return null;
        }
    }
}

if (!function_exists('formatToInternational')) {
    /**
     * Convert a local phone number to international format.
     *
     * @param string $phoneNumber The local phone number.
     * @param string $countryCode The ISO 3166-1 alpha-2 country code (e.g., 'AE', 'IN').
     * @return string|null The formatted international phone number, or null on failure.
     */
    function formatToInternational(string $phoneNumber, string $countryCode): ?string
    {
        try {
            // Initialize the PhoneNumberUtil
            $phoneUtil = PhoneNumberUtil::getInstance();

            // Parse the phone number with the given country code
            $number = $phoneUtil->parse($phoneNumber, $countryCode);

            // Format the number to international format
            return $phoneUtil->format($number, PhoneNumberFormat::INTERNATIONAL);
        } catch (NumberParseException $e) {
            // Handle parsing errors
            return null;
        }
    }
}
if (!function_exists('convertUsdToBtc')) {
    /**
     * Convert USD to BTC using the current exchange rate.
     *
     * @param float $usdAmount
     * @return float|null
     */
    function convertUsdToBtc(float $usdAmount): ?float
    {
        try {
            // Fetch the current BTC to USD exchange rate
            $response = Http::get('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd');

            if ($response->successful() && isset($response->json()['bitcoin']['usd'])) {
                $btcRate = $response->json()['bitcoin']['usd'];
                // Convert USD to BTC
                return $usdAmount / $btcRate;
            }

            return null; // Return null if the API call fails
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Error fetching BTC rate: ' . $e->getMessage());
            return null;
        }
    }
}

function permissions($component, $key = null)
{
    return auth()->user()->permissions($component, $key);
}

function access($key = null)
{
    return auth()->user()->access($key);
}

function userGroup()
{
    return auth()->user()->userGroup();
}

function isAdmin()
{
    return auth()->user()->isAdmin();
}

function SLGenerator($data)
{
    return ($data->perPage() * $data->currentPage()) - ($data->perPage() - 1);
}

if (!function_exists('convert_weight')) {
    function convert_weight($value, $to_unit)
    {
        $from_unit = GeneralSetting::general_setting('weight_unit'); // e.g. 'kg', 'lb', 'oz'

        if ($from_unit === $to_unit) {
            return $value;
        }

        // Convert to kg first
        switch ($from_unit) {
            case 'lb':
                $value = $value / 2.20462;
                break;
            case 'oz':
                $value = $value / 35.274;
                break;
            case 'kg':
            default:
                // already in kg
                break;
        }

        // Now convert from kg to target
        switch ($to_unit) {
            case 'lb':
                return $value * 2.20462;
            case 'oz':
                return $value * 35.274;
            case 'kg':
            default:
                return $value;
        }
    }
}

if (!function_exists('convert_dimension')) {
    function convert_dimension($value, $to_unit)
    {
        $from_unit = GeneralSetting::general_setting('dimension_unit'); // e.g. 'cm', 'in'

        if ($from_unit === $to_unit) {
            return $value;
        }

        // Convert to cm first
        if ($from_unit === 'in') {
            $value = $value * 2.54;
        }

        // Now convert from cm to target
        if ($to_unit === 'in') {
            return $value / 2.54;
        }

        return $value;
    }

    function getDefaultCurrencySymbol()
    {
        $currencies = storage_path('currency.json');
        $currencyList = [];
        $default_currency = GeneralSetting::where('key', 'default_currency')->value('value') ?? 'USD';

        if (file_exists($currencies)) {
            $currenciesJson = file_get_contents($currencies);
            $currenciesArray = json_decode($currenciesJson, true);

            // Create a map of code => name
            foreach ($currenciesArray as $currency) {
                if ($default_currency == $currency['code']) {
                    return $currency['symbol'];
                }
            }
        }
        //dd($currenciesList);
        return '$';
    }
    function getDefaultCurrencyCode()
    {
        $currencies = storage_path('currency.json');
        $currencyList = [];
        $default_currency = GeneralSetting::where('key', 'default_currency')->value('value') ?? 'USD';


        //dd($currenciesList);
        return $default_currency;
    }
    function getCurrencylist()
    {
        $currencies = storage_path('currency.json');
        $currenciesList = [];

        if (file_exists($currencies)) {
            $currenciesJson = file_get_contents($currencies);
            $currenciesArray = json_decode($currenciesJson, true);

            // Create a map of code => name
            foreach ($currenciesArray as $currency) {
                $currenciesList[] = $currency['code'];
            }
        }
        return $currenciesList;
    }
    function is_assoc(array $arr)
    {
        return array_keys($arr) !== range(0, count($arr) - 1);
    }
    function getSmsTemplates()
    {
        $templates = WhatsappTemplateMeta::all()->map(function ($template) {
            return (object)[
                'id' => $template->template_id,
                'title' => $template->title,
                'body' => $template->body,
                'variables' => json_decode($template->variables, true),
            ];
        });


        return $templates;
    }
}
function getStoreNameById($id)
{
    $store = Setting::find($id);

    return $store->site_name;
}

function getLocalPrice($rate)
{
    $location_data = Session::get('shop_logged_data');

    if ($location_data) {
        return getCurrencyCodeBySymbol($location_data->currency_symbol) . ' ' . $location_data->currency_symbol . (number_format($rate * $location_data->currency_rate, 2));
    } else {
        return $rate;
    }
}
function getCurrencyCodeBySymbol($symbol)
{
    $currencies = storage_path('currency.json');
    $currencyList = [];
    if (file_exists($currencies)) {
        $currenciesJson = file_get_contents($currencies);
        $currenciesArray = json_decode($currenciesJson, true);

        // Create a map of code => name
        foreach ($currenciesArray as $currency) {
            if ($symbol == $currency['symbol']) {
                return $currency['code'];
            }
        }
    }

    function getCountries()
    {
        $countriesFile = storage_path('countries.json');
        $countriesList = [];

        if (file_exists($countriesFile)) {
            $countriesJson = file_get_contents($countriesFile);
            $countriesArray = json_decode($countriesJson, true);

            foreach ($countriesArray as $country) {
                $countriesList[$country['code']] = $country['name'];
            }
        }
        return $countriesList;
    }
}
