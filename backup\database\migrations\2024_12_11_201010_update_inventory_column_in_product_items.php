<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateInventoryColumnInProductItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('product_items')) {
            Schema::table('product_items', function (Blueprint $table) {
                if (!Schema::hasColumn('product_items', 'inventory_status')) {
                    $table->enum('inventory_status', ['available', 'incoming_otm', 'incoming_sold', 'unavailable'])->after('country_code');
                }
                if (!Schema::hasColumn('product_items', 'estimated_shipping_weeks')) {
                    $table->integer('estimated_shipping_weeks')->nullable()->after('inventory_status');
                }
                if (!Schema::hasColumn('product_items', 'condition')) {
                    $table->string('condition')->nullable()->after('estimated_shipping_weeks');
                }
                if (!Schema::hasColumn('product_items', 'notify_contacts')) {
                    $table->text('notify_contacts')->nullable()->after('condition');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('product_items')) {
            Schema::table('product_items', function (Blueprint $table) {
                if (Schema::hasColumn('product_items', 'inventory_status')) {
                    $table->dropColumn('inventory_status');
                }
                if (Schema::hasColumn('product_items', 'estimated_shipping_weeks')) {
                    $table->dropColumn('estimated_shipping_weeks');
                }
                if (Schema::hasColumn('product_items', 'condition')) {
                    $table->dropColumn('condition');
                }
                if (Schema::hasColumn('product_items', 'notify_contacts')) {
                    $table->dropColumn('notify_contacts');
                }
            });
        }
    }
}
