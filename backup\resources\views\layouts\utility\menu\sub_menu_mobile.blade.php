<div class="page_html" id="page_{{ $menu->id }}">
    <div class="block menu-accordion-mobile sub-page-menu">
        <h3 id="child_parent_title">
            <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
            </svg>
            <span>{{ $menu->menu_name }}</span>
        </h3>
        <div class="menu-accordion-mobile-shadow">
            <div class="menu-accordion-mobile-inner">
                <ul class="child-mobile-menus">
                    @foreach ($items as $item)
                        @if($item->access)
                        <li>
                            <a class="{{ $item->menu_active ?'active ':'' }}@if(count($item->menuChildren) > 0)have-sub-menu have-child-sub-items @endif" data-menu_id="{{ $item->id }}" @if(count($item->menuChildren) > 0) href="javascript:void(0)" @else href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" @endif{{$item->url_type=='custom_url'?'target="_blank"':''}}>
                                {{-- @if(count($item->menuChildren) > 0)onClick="slide('next')" @endif --}}
                                @if($item->menu_icon)<i class="{{ $item->menu_icon }}"></i>@endif
                                <span>{{$item->menu_name?$item->menu_name:$item->title}}</span>
                                @if(count($item->menuChildren) > 0)
                                <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                                    <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                                </svg>
                                @endif
                            </a>
                            @if($item->menuChildren)
                                @include('layouts.utility.menu.sub_menu_mobile',[
                                    'items' => $item->menuChildren,
                                    'menu' => $item,
                                ])
                            @endif
                        </li>
                        @endif
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
</div>

{{-- @if(count($items))
<ul class="have-sub-menus clearfix">
    @foreach ($items as $item)
       @if($item->access)
            <li>
               <a class="{{ $item->menu_active ?'active ':'' }}@if(count($item->menuChildren) > 0)have-sub-menu have-child-sub-items @endif" data-menu_id="{{ $item->id }}" @if(count($item->menuChildren) > 0) href="javascript:void(0)" @else href="{{ $item->url_type=='custom_url'? $item->slug : route('page', [$item->slug]) }}" @endif{{$item->url_type=='custom_url'?'target="_blank"':''}}>
                   @if($item->menu_icon)<i class="{{ $item->menu_icon }}"></i>@endif
                   <span>{{$item->menu_name?$item->menu_name:$item->title}}</span>
                   @if(count($item->menuChildren) > 0)
                    <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                        <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                    </svg>
                   @endif
               </a>
               @if($item->menuChildren)
               @include('layouts.utility.menu.sub_menu_mobile',[
                   'items' => $item->menuChildren,
                ])
               @endif
           </li>
       @endif
    @endforeach
</ul>
@endif --}}
