<?php

namespace App\Http\Controllers;

use URL;
use DateTime;
use Redirect;
use Validator;
use Carbon\Carbon;
use App\Models\Cart;
use App\Models\Page;
use App\Models\User;
use EasyPost\Parcel;
use PayPal\Api\Item;
use App\Models\Order;
use App\Models\Promo;
use App\Models\Slide;
use App\Services\Box;
use EasyPost\Address;
use PayPal\Api\Payer;
use App\Models\Review;
use App\Models\Reward;
use EasyPost\EasyPost;
use PayPal\Api\Amount;
use App\Mail\SiteEmail;
use App\Models\Collect;
use App\Models\Product;
use App\Models\Section;
use App\Models\Setting;
use PayPal\Api\Payment;
use App\Models\Tracking;
use PayPal\Api\ItemList;
use App\Models\OrderItem;
use App\Models\UserGroup;
use App\Models\CashReward;
use App\Models\AccessLabel;
use App\Models\DHLTracking;
use App\Models\OrderRefund;
use App\Models\PointSystem;
use App\Models\ProductItem;
use App\Models\ShippingBox;
use App\Models\UserBilling;
use Illuminate\Support\Str;
use PayPal\Api\Transaction;
use PayPal\Rest\ApiContext;
use App\Models\ProductBrand;
use App\Models\UserGroupMap;
use App\Models\UserShipping;
use App\Services\DHLService;
use Illuminate\Http\Request;
use PayPal\Api\RedirectUrls;
use App\Models\CustomerPromo;
use App\Models\EmailTemplate;
use App\Models\ProductSeries;
use App\Models\SentDiscount;
use App\Models\AlertPreference;
use App\Models\ProductCategory;
use App\Services\BTCPayService;
use App\Models\RedeemCashReward;
use PayPal\Api\PaymentExecution;
use App\Models\TransactionMethod;
use App\Models\EmailTemplateGroup;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ShippingMethodOption;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use App\Models\EstimatedDeliveryDate;
use PayPal\Auth\OAuthTokenCredential;
use App\Models\ProdcutCategoriesParent;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use App\Models\GeneralSetting;
use App\Models\ShippingRule;
use KingFlamez\Rave\Facades\Rave as Flutterwave;
use App\Events\OrderPlaced;

class WebPageController extends Controller
{
    private $reviewFolderPath = 'review_images/';
    private $refundFolderPath = 'refund_images/';
    private $_api_context;
    protected $btcPayService;
    protected $dhlService;
    public function __construct()
    {
        $paypal_conf = \Config::get('paypal');
        $this->btcPayService = new BTCPayService();
        $this->_api_context = new \PayPal\Rest\ApiContext(
            new \PayPal\Auth\OAuthTokenCredential(
                $paypal_conf['client_id'],
                $paypal_conf['secret']
            )
        );
        $this->_api_context->setConfig($paypal_conf['settings']);
        $this->dhlService = new DHLService();
    }
    public function readJson($path)
    {
        if (File::exists($path)) {
            $json = File::get($path);
            $data = json_decode($json, true);
            return collect($data);
        } else {
            return response()->json(['error' => 'File not found.'], 404);
        }
    }
    public function showAdmin()
    {
        return redirect()->route('login');
    }
    public function showBuildPage($slug, $page, $selector, $request)
    {
        switch ($page->page_key) {
            case 'tracking':
                return $this->viewTracking($slug, $page, $selector);
                break;
            case 'contactTeam':
                return $this->viewContactTeam($slug, $page, $selector);
                break;
            case 'storeinfo':
                return $this->viewstoreInfo($slug, $page, $selector);
                break;
            case 'home':
                return $this->showHome($slug, $page, $selector);
                break;
            case 'my_profile':
                return $this->showProfilePage($slug, $page, $selector);
                break;
            case 'checkout':
                return $this->viewCheckout($slug, $page, $selector);
                break;
            case 'cart':
                return $this->viewCart($slug, $page, $selector);
                break;
            case 'products':
                return $this->viewProducts($slug, $page, $selector);
                break;
            case 'thank_you':
                return $this->viewThankYou($slug, $page, $selector);
                break;
            case 'track_my_order':
                return $this->viewTrackMyOrder($slug, $page, $selector, $request);
                break;
            case 'product_not_available':
                return $this->ProductNotAvailable($slug, $page, $selector, $request);
                break;
            default:
                abort(404);
                break;
        }
    }
    public function viewstoreInfo($slug, $page, $selector)
    {
        $stores = Setting::where('country', $page->location_data->country)
            ->where('status', 1)
            ->with('childStores')
            ->get();
        $stores->each(function ($store) {
            $store->business_hours = unserialize($store->business_hours);
            $store->setting_meta = unserialize($store->setting_meta);
        });
        if ($page->checkAccessLabel($slug)) {
            return view('web.store-info', compact('page', 'stores'));
        } else {
            return abort(403);
        }
    }
    public function viewTracking($slug, $page, $selector)
    {
        $orderId = request('orderId');
        $trackingDetails = DHLTracking::where('order_id', $orderId)->first();
        if ($trackingDetails) {
            $shipmentTrackingNumber = $trackingDetails->shipmentTrackingNumber;
        } else {
            $shipmentTrackingNumber = null;
        }
        try {
            $trackingResponse = $this->dhlService->trackingParcel('9356579890');
            if ($trackingResponse instanceof \Illuminate\Http\JsonResponse) {
                $trackingData = $trackingResponse->json();
            } elseif (is_array($trackingResponse)) {
                $trackingData = $trackingResponse;
            } else {
                $trackingData = [];
            }
            $tracking = $trackingData['shipments'][0] ?? null;
        } catch (\Exception $e) {
            \Log::error('DHL Tracking Error: ' . $e->getMessage());
            $tracking = null;
        }
        // dd($tracking);
        if ($page->checkAccessLabel($slug)) {
            return view('web.tracking', compact('page', 'tracking', 'trackingDetails'));
        } else {
            return abort(403);
        }
    }
    public function viewContactTeam($slug, $page, $selector)
    {
        $cart = Session::get('cart');
        Session::forget('checkout_order');
        Session::forget('cart');
        if ($cart && !empty($cart)) {
            if ($page->checkAccessLabel($slug)) {
                return view('web.cart.contactTeam', compact('page', 'cart'));
            } else {
                return abort(403);
            }
        } else {
            return Redirect::route('home');
        }
    }
    public function showComponent($slug, $page, $selector)
    {
        switch ($page->page_key) {
            case 'product_brand':
                return $this->showProductBrand($slug, $page, $selector);
                break;
            case 'product_series':
                return $this->showProductSeries($slug, $page, $selector);
                break;
            case 'product_category':
                return $this->showProductCategory($slug, $page, $selector);
                break;
            case 'product':
                return $this->showProduct($slug, $page, $selector);
                break;
            default:
                abort(404);
                break;
        }
    }
    public function showPostCategories($slug, $page, $selector) {}
    public function showProductBrand($slug, $page, $selector)
    {
        $items = $this->productItemsData();
        $categories = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
            $query->where('status', 1);
        })->with('productCategories')->get();
        foreach ($categories as $category) {
            $category->product_have = $items->where('product_categories_id', $category->id)->count();
        }
        $brands = ProductBrand::where('status', true)->get();
        foreach ($brands as $brand) {
            $brand->product_have = $items->whereIn('product_brand_id', explode(',', unserialize($page->page_meta)->component_category_id))->count();
        }
        $page->brands = $brands;
        $products = Product::whereStatus(true)->get();
        foreach ($products as $product) {
            $product->product_have = $items->where('product_id', $product->id)->count();
        }
        $page->products = $products;
        $series = ProductSeries::whereStatus(true)->get();
        foreach ($series as $value) {
            $value->product_have = $items->where('product_service_id', $value->id)->count();
        }
        $page->series = $series;
        $page->min_price_range = $items->min('price');
        $page->max_price_range = $items->max('price');
        $page->items = $items;
        $page->products_page_slug = Page::where('page_key', 'products')->first();
        $url = route('page', [$page->products_page_slug->slug, 'fb' => unserialize($page->page_meta)->component_category_id, 'fmin' => $page->min_price_range, 'fmax' => $page->max_price_range]);
        return redirect(urldecode($url));
    }
    public function showProductSeries($slug, $page, $selector)
    {
        $items = $this->productItemsData();
        $categories = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
            $query->where('status', 1); // Check for active product categories
        })->with('productCategories')->get();
        foreach ($categories as $category) {
            $category->product_have = $items->where('product_categories_id', $category->id)->count();
        }
        $brands = ProductBrand::where('status', true)->get();
        foreach ($brands as $brand) {
            $brand->product_have = $items->where('product_brand_id', $brand->id)->count();
        }
        $page->brands = $brands;
        $products = Product::whereStatus(true)->get();
        foreach ($products as $product) {
            $product->product_have = $items->where('product_id', $product->id)->count();
        }
        $page->products = $products;
        $series = ProductSeries::whereStatus(true)->get();
        foreach ($series as $value) {
            $value->product_have = $items->whereIn('product_service_id', explode(',', unserialize($page->page_meta)->component_category_id))->count();
        }
        $page->series = $series;
        $page->min_price_range = $items->min('price');
        $page->max_price_range = $items->max('price');
        $page->items = $items;
        $page->products_page_slug = Page::where('page_key', 'products')->first();
        $url = route('page', [$page->products_page_slug->slug, 'fps' => unserialize($page->page_meta)->component_category_id, 'fmin' => $page->min_price_range, 'fmax' => $page->max_price_range]);
        return redirect(urldecode($url));
    }
    public function showProduct($slug, $page, $selector)
    {
        // dd($slug, unserialize($page->page_meta)->component_category_id, $page, $selector);
        $items = $this->productItemsData();
        $categories = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
            $query->where('status', 1); // Check for active product categories
        })->with('productCategories')->get();
        foreach ($categories as $category) {
            $category->product_have = $items->where('product_categories_id', $category->id)->count();
        }
        // dd($items,$categories);
        $page->categories = $categories;
        $brands = ProductBrand::where('status', true)->get();
        foreach ($brands as $brand) {
            $brand->product_have = $items->where('product_brand_id', $brand->id)->count();
        }
        $page->brands = $brands;
        $products = Product::whereStatus(true)->get();
        foreach ($products as $product) {
            $product->product_have = $items->whereIn('product_id', explode(',', unserialize($page->page_meta)->component_category_id))->count();
        }
        $page->products = $products;
        $series = ProductSeries::whereStatus(true)->get();
        foreach ($series as $value) {
            $value->product_have = $items->where('product_service_id', $value->id)->count();
        }
        $page->series = $series;
        $page->min_price_range = $items->min('price');
        $page->max_price_range = $items->max('price');
        $page->items = $items;
        $page->products_page_slug = Page::where('page_key', 'products')->first();
        $url = route('page', [$page->products_page_slug->slug, 'fp' => unserialize($page->page_meta)->component_category_id, 'fmin' => $page->min_price_range, 'fmax' => $page->max_price_range]);
        return redirect(urldecode($url));
    }
    public function showProductCategory($slug, $page, $selector)
    {
        $items = $this->productItemsData();
        $categories = ProdcutCategoriesParent::with(['productCategories' => function ($query) {
            $query->where('status', 1);
        }])->get();
        foreach ($categories as $category) {
            $category->product_have = $items->whereIn('product_categories_id',  unserialize($page->page_meta)->component_category_id)->count();
        }
        $page->categories = $categories;
        $brands = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
            $query->where('status', 1); // Check for active product categories
        })->with('productCategories')->get();
        foreach ($brands as $brand) {
            $brand->product_have = $items->where('product_brand_id', $brand->id)->count();
        }
        $page->brands = $brands;
        $products = Product::whereStatus(true)->get();
        foreach ($products as $product) {
            $product->product_have = $items->where('product_id', $product->id)->count();
        }
        $page->products = $products;
        $series = ProductSeries::whereStatus(true)->get();
        foreach ($series as $value) {
            $value->product_have = $items->where('product_service_id', $value->id)->count();
        }
        $page->series = $series;
        $page->min_price_range = $items->min('price');
        $page->max_price_range = $items->max('price');
        $page->items = $items;
        $page->products_page_slug = Page::where('page_key', 'products')->first();
        $url = route('page', [$page->products_page_slug->slug, 'fc' => unserialize($page->page_meta)->component_category_id, 'fmin' => $page->min_price_range, 'fmax' => $page->max_price_range]);
        return redirect(urldecode($url));
    }
    public function showHome($slug, $page, $selector)
    {
        $page->series = ProductSeries::whereStatus(true)->get();
        $product_items = count($this->productItemsData());
        $random_count = 1;
        if ($product_items > 4) {
            $random_count = 5;
        }
        if ($product_items != 0) {
            $page->recommended_items = collect($this->productItemsData())->random($random_count)->take(20);
        }
        $products_page_slug = Page::where('page_key', 'products')->first();
        $page->product_page_slug = $products_page_slug->slug;
        // dd($page);
        return $page->checkAccessLabel($slug) ? view('web.home', compact('page')) : abort(403);
    }
    function displayRating($rating)
    {
        $max_stars = 5;
        for ($i = 1; $i <= $max_stars; $i++) {
            if ($i <= $rating) {
                $icons[] = '<i class="fas fa-star text-warning"></i> ';
            } else {
                $icons[] = '<i class="far fa-star text-warning"></i> ';
            }
        }
        return $icons;
    }
    public function view_sections($page_id)
    {
        $sections_after_main = Section::where([
            'page_id' => $page_id,
            'section_position' => 'after_main',
            'status' => true,
        ])
            ->orderBy('sections.ordering')
            ->get();
        foreach ($sections_after_main as $value) {
            $value->section_meta = unserialize($value->section_meta);
            if ($value->module_type == 'categories') {
                $value->categories = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
                    $query->where('status', 1); // Check for active product categories
                })->with('productCategories')->get();;
            }
            if ($value->module_type == 'brands') {
                $value->brands = ProductBrand::whereStatus(true)->get();
            }
            if ($value->module_type == 'series') {
                $value->series = ProductSeries::whereStatus(true)->get();
            }
            if ($value->module_type == 'recommended') {
                // dd( count($this->productItemsData()));
                $product_items = count($this->productItemsData());
                $random_count = 1;
                if ($product_items > 4) {
                    $random_count = 5;
                }
                if ($product_items > 0) {
                    $value->recommended_items = collect($this->productItemsData())->random($random_count)->take(20);
                }
            }
            if ($value->module_type == 'reviews') {
                $ratings = Review::where([
                    'status' => 2,
                ])
                    ->where('parent_review_id', null)
                    ->orderBy('id', 'desc')
                    ->get();
                // dd($ratings);
                foreach ($ratings as $rating_value) {
                    $review_images = unserialize($rating_value->review_images);
                    $rating_value->image = $this->getRatingImage($review_images);
                    $user = User::find($rating_value->user_id);
                    $rating_value->reviewer_name = $user->first_name . '' . $user->last_name;
                    $rating_value->status = $this->reviewStatus($rating_value->status);
                    $rating_value->date_time = Carbon::parse($rating_value->created_at)->format('M, d Y g:i A');
                    $rating_value->last_update_time = Carbon::parse($rating_value->updated_at)->diffForHumans();
                    $rating_value->rating_icon = implode('', $this->displayRating($rating_value->rating));
                }
                // dd($ratings);
                $value->ratings = $ratings;
                $value->rating_count = $ratings->count();
            }
            if ($value->module_type == 'slideshow') {
                $value->slides = Slide::whereStatus(true)->where('slide_category_id', $value->section_meta->slideshow_id)->get();
            }
        }
        // dd($sections_after_main);
        $sections_before_main = Section::where([
            'page_id' => $page_id,
            'section_position' => 'before_main',
            'status' => true,
        ])
            ->orderBy('sections.ordering')
            ->get();
        // dd($sections_before_main);
        foreach ($sections_before_main as $value) {
            $value->section_meta = unserialize($value->section_meta);
            if ($value->module_type == 'categories') {
                $value->categories = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
                    $query->where('status', 1); // Check for active product categories
                })->with('productCategories')->get();
            }
            if ($value->module_type == 'brands') {
                $value->brands = ProductBrand::whereStatus(true)->get();
            }
            if ($value->module_type == 'series') {
                $value->series = ProductSeries::whereStatus(true)->get();
            }
            if ($value->module_type == 'recommended') {
                $product_items = count($this->productItemsData());
                $random_count = 1;
                if ($product_items > 4) {
                    $random_count = 5;
                }
                $value->recommended_items = collect($this->productItemsData())->random($random_count)->take(20);
            }
            if ($value->module_type == 'reviews') {
                $ratings = Review::where([
                    'status' => 2,
                ])->where('parent_review_id', null)
                    ->orderBy('id', 'desc')->take(4)->get();
                foreach ($ratings as $rating_value) {
                    $review_images = unserialize($rating_value->review_images);
                    $rating_value->image = $this->getRatingImage($review_images);
                    $user = User::find($rating_value->user_id);
                    $rating_value->reviewer_name = $user->first_name . '' . $user->last_name;
                    $rating_value->status = $this->reviewStatus($rating_value->status);
                    $rating_value->date_time = Carbon::parse($rating_value->created_at)->format('M, d Y g:i A');
                    $rating_value->last_update_time = Carbon::parse($rating_value->updated_at)->diffForHumans();
                    $rating_value->rating_icon = implode('', $this->displayRating($rating_value->rating));
                }
                $value->ratings = $ratings;
                $value->rating_count = $ratings->count();
            }
            if ($value->module_type == 'slideshow') {
                $value->slides = Slide::whereStatus(true)->where('slide_category_id', $value->section_meta->slideshow_id)->get();
            }
        }
        return (object)[
            'sections_before_main' => $sections_before_main,
            'sections_after_main' => $sections_after_main,
        ];
    }
    public function getRatingImage($items)
    {
        foreach ($items as $value) {
            if ($value->default) {
                return $value->file_path;
            }
        }
    }
    public function showProfilePage($slug, $page, $username)
    {
        if (empty(Auth()->user())) return redirect('/');
        $location_data = Session::get('shop_logged_data');
        if (request()->get('order_id')) {
            $order = Order::find(request()->get('order_id'));
            $order->order_items = $this->getOrderItems(OrderItem::where('order_id', $order->id)->get());
            $order->customer = User::find($order->customer_id);
            $today = Carbon::now();
            $order_return_date = Carbon::parse($order->order_date)->addDays(site_info('order_return_days'));
            $thresholdDateReturn = Carbon::parse($order->order_date)->copy()->addDays(site_info('order_return_days'));
            $order->order_return = false;
            if ($today->lessThan($thresholdDateReturn)) {
                $order->order_return = true;
                $order->order_return_text = 'Return period ends ' . $order_return_date->format('M, d Y');
            } else {
                $order->order_return_text = 'Return period ended ' . $order_return_date->format('M, d Y');
            }
            $order_warranty_date = Carbon::parse($order->order_date)->addDays(site_info('order_warranty_days'));
            $thresholdDateWarranty = Carbon::parse($order->order_date)->copy()->addDays(site_info('order_warranty_days'));
            $order->order_warranty = false;
            if ($today->lessThan($thresholdDateWarranty)) {
                $order->order_warranty = true;
                $order->order_warranty_text = 'Warranty period ends ' . $order_warranty_date->format('M, d Y');
            } else {
                $order->order_warranty_text = 'Warranty period ended ' . $order_warranty_date->format('M, d Y');
            }
            $order->order_date = $this->undoFormatDate($order->order_date);
            $order->order_time = Carbon::parse($order->created_at)->format('g:i A');
            // dd($this->colletOrderAmount(Collect::where('order_id', $order->id)->get()));
            $order->collect_amount = $this->colletOrderAmount(Collect::where('order_id', $order->id)->get())->total;
            $order->local_collect_amount = $this->colletOrderAmount(Collect::where('order_id', $order->id)->get())->local_total;
            $order->local_currency = $this->colletOrderAmount(Collect::where('order_id', $order->id)->get())->local_currency;
            $order->transaction_method_name = $this->colletOrderAmount(Collect::where('order_id', $order->id)->get())->transaction_method_name;
            $order->status = $this->orderStatus($order->status);
            $collect = Collect::where('order_id', request()->get('order_id'))->get();
            foreach ($collect as $value) {
                $value->collection_time = Carbon::parse($value->created_at)->format('g:i A');
                $value->collection_date = $this->undoFormatDate($value->collection_date);
            }
            $order->collect = $collect;
            $order->billing = UserBilling::where('id', $order->user_billing_id)->first();
            $order->shipping = UserShipping::where('id', $order->user_shipping_id)->first();
            $order->local_currency = $this->colletOrderAmount(Collect::where('order_id', request()->get('order_id'))->get())->local_currency;
            $order->transaction_method_name = $this->colletOrderAmount(Collect::where('order_id', request()->get('order_id'))->get())->transaction_method_name;
            $page->order = $order;
            $page->title = $order->order_no;
            // dd($page->order);
            return auth()->check() ? view('web.user.order', compact('page')) : abort(404);
        }
        $orders = Order::where('customer_id', auth()->user()->id)->with('orderRefund')->orderBy('orders.id', 'desc')
            ->paginate($this->itemPerPage);
        // dd($orders);
        //  $trackingdetails;
        foreach ($orders as $value) {
            $value->order_items = $this->getOrderItems(OrderItem::where('order_id', $value->id)->get());
            $value->customer = User::find($value->customer_id);
            // dd($this->colletOrderAmount(Collect::where('order_id', $value->id)->get()));
            $value->collect_amount = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->total;
            $value->local_collect_amount = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->local_total;
            $value->local_currency = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->local_currency;
            $value->transaction_method_name = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->transaction_method_name;
            // dd($value);
            $value->status = $this->orderStatus($value->status);
            $today = Carbon::now();
            $order_return_date = Carbon::parse($value->order_date)->addDays(site_info('order_return_days'));
            $thresholdDateReturn = Carbon::parse($value->order_date)->copy()->addDays(site_info('order_return_days'));
            $value->order_return = false;
            if ($today->lessThan($thresholdDateReturn)) {
                $value->order_return = true;
                $value->order_return_text = 'Return period ends ' . $order_return_date->format('M, d Y');
            } else {
                $value->order_return_text = 'Return period ended ' . $order_return_date->format('M, d Y');
            }
            $order_warranty_date = Carbon::parse($value->order_date)->addDays(site_info('order_warranty_days'));
            $thresholdDateWarranty = Carbon::parse($value->order_date)->copy()->addDays(site_info('order_warranty_days'));
            $value->order_warranty = false;
            if ($today->lessThan($thresholdDateWarranty)) {
                $value->order_warranty = true;
                $value->order_warranty_text = 'Warranty period ends ' . $order_warranty_date->format('M, d Y');
            } else {
                $value->order_warranty_text = 'Warranty period ended ' . $order_warranty_date->format('M, d Y');
            }
            $value->order_date = $this->undoFormatDate($value->order_date);
            $value->order_time = Carbon::parse($value->created_at)->format('g:i A');
            // $trackingdetails=$this->trackOrder($value);
            // dd($value->review);
        }
        // dump($trackingdetails);
        // dd($orders);
        $page->orders = $orders;
        //dd($orders[0]);
        $page->countries = $this->countries();
        $page->user_shippings = UserShipping::where('user_id', auth()->user()->id)->get();
        $page->user_billings = UserBilling::where('user_id', auth()->user()->id)->get();
        $page->countries = $this->countries();
        // dd($page);
        $rewards = Reward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $total_reward_points = 0;
        foreach ($rewards as $value) {
            $total_reward_points += $value->points;
        }
        $page->total_reward_points = $total_reward_points;
        $page->cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
        $cash_rewards = CashReward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $cash_total_reward = 0;
        foreach ($cash_rewards as $value) {
            $cash_total_reward += $value->amount;
        }
        $page->cash_total_reward = number_format($cash_total_reward, 2);
        $page->country = Session::has('shop_logged_data') ? $location_data->country : null;
        $page->currency_symbol = $location_data->currency_symbol;
        $page->country_short_code = $location_data->country_short_code;
        $page->local_cash_total_reward = $this->getLocalCurrencyRate($location_data->country) * $cash_total_reward;
        $paid_redeem_cash_rewards = RedeemCashReward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $paid_total_cash_rewards = 0;
        foreach ($paid_redeem_cash_rewards as $value) {
            $paid_total_cash_rewards += $value->amount;
        }
        $page->paid_total_cash_rewards =
            $paid_total_cash_rewards;
        $pending_redeem_cash_rewards = RedeemCashReward::where([
            'user_id' => auth()->user()->id,
            'status' => false,
        ])->get();
        $pending_total_cash_rewards = 0;
        foreach ($pending_redeem_cash_rewards as $value) {
            $pending_total_cash_rewards += $value->amount;
        }
        $page->pending_total_cash_rewards = $pending_total_cash_rewards;
        // dd($location_data);
        $page->banks = $location_data->banks;
        return auth()->check() ? view('web.user.profile', compact('page')) : abort(404);
        // return view('web.user.profile', compact('page'));
    }
    public function getOrderItems($order_items)
    {
        $location_data = Session::get('shop_logged_data');
        foreach ($order_items as $value) {
            $value->product = Product::find($value->product_id);
            $value->item = ProductItem::find($value->product_item_id);
        }
        $options = [];
        foreach ($order_items as $value) {
            $this->makeAttributeData(unserialize($value->item->item_attributes), $value);
            $options[] = $value->cart_group;
        }
        $items_data = array_unique($options);
        $items = array();
        foreach ($items_data as $value) {
            $count = collect($order_items)->where('cart_group', $value)->count();
            if ($count == 1) {
                $data = collect($order_items)->where('cart_group', $value)->all();
                $item = collect($order_items)->where('cart_group', $value)->first();
                $product = Product::find($item->product_id);
                if ($product) {
                    $item->attributes = app(\App\Http\Controllers\Admin\OrderController::class)->getAttributes($data, $product);
                    app(\App\Http\Controllers\Admin\OrderController::class)->getAttributeData($item->attributes, $item);
                }
                $item->product_title = $product->title;
                if ($item->title) {
                    $item->title = $item->extra_title . ' (' . $item->title . ')';
                } else {
                    $item->title = $item->extra_title;
                }
                if ($item->sub_title) {
                    $item->sub_title = $item->extra_sub_title . ' (' . $item->sub_title . ')';
                } else {
                    $item->sub_title = $item->extra_sub_title;
                }
                if (empty($item->product_item_image)) {
                    $item->product_image = asset('storage/products/' . $product->product_image);
                }
                $attributes = collect(unserialize($item->item->item_attributes))->sortBy('ordering');
                foreach ($attributes as $value) {
                    if ($value->attribute_type_id == 12) {
                        if (!empty($value->data->file_path)) {
                            $item->product_image =  asset('storage/products/' . $value->data->file_path);
                        }
                    }
                }
                $today = Carbon::now();
                $thresholdDateReturn = Carbon::parse($item->order_date)->copy()->addDays(site_info('order_return_days'));
                $order_return = false;
                if ($today->lessThan($thresholdDateReturn)) {
                    $order_return = true;
                }
                $item->categories = app(\App\Http\Controllers\Admin\OrderController::class)->getCategories(explode(',', $product->product_categories_id));
                $brand = ProductBrand::find($product->product_brand_id);
                $item->product_brand_title = $brand->title;
                $item->product_series_id = $product->product_series_id;
                $item->attribute_options = app(\App\Http\Controllers\Admin\OrderController::class)->getAttributeID(explode(',', $item->attribute_options));
                $item->sort_title = ucfirst(mb_substr($item->product_title, 0, 1));
                $item->sku_details = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetails($data);
                $item->sku_details_url = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetailsUrl($data);
                $item->request_refund_status = app(\App\Http\Controllers\Admin\OrderController::class)->requestRefundStatus($data);
                $item->condition = app(\App\Http\Controllers\Admin\OrderController::class)->makeCondition(unserialize($item->item->item_attributes));
                $item->sku_details_refund = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetailsRefund($data, $item->product_title, $item->title, $item->condition, $order_return);
                $item->condition_type = strtolower(str_replace([' ', ','], '', $item->condition));
                $item->available_item = app(\App\Http\Controllers\Admin\OrderController::class)->stockCalculation(ProductItem::where('product_id', $item->product_id)->where('status', 1)->whereIn('location_id', ['US', $location_data->country ?? ''])->get(), $item->cart_group);
                $item->quantity = $count;
                $item->item_price = app(\App\Http\Controllers\Admin\OrderController::class)->itemTotalPrice($data);
                $item->review = Review::where([
                    'product_item_id' => $item->product_item_id,
                    'user_id' => auth()->user()->id,
                ])->where('parent_review_id', null)->exists();
                if ($item->review) {
                    $item->review_item = Review::where([
                        'product_item_id' => $item->product_item_id,
                        'user_id' => auth()->user()->id,
                    ])->where('parent_review_id', null)->first();
                    $item->reward_item = Reward::where('review_id', $item->review_item->id)->first();
                }
                $items[] = $item;
            }
            // dd($items);
            if ($count > 1) {
                $data = collect($order_items)->where('cart_group', $value)->all();
                // dd($data);
                $item = collect($order_items)->where('cart_group', $value)->first();
                $item->extra_title = app(\App\Http\Controllers\Admin\OrderController::class)->makeExtraTitle($data);
                $item->sub_title = app(\App\Http\Controllers\Admin\OrderController::class)->makeSubTitle($data);
                $product = Product::find($item->product_id);
                if ($product) {
                    $item->attributes = app(\App\Http\Controllers\Admin\OrderController::class)->getAttributes($data, $product, true);
                    app(\App\Http\Controllers\Admin\OrderController::class)->getAttributeData($item->attributes, $item);
                }
                $item->product_title = $product->title;
                if ($item->title) {
                    $item->title = $item->extra_title . ', ' . $item->title;
                } else {
                    $item->title = $item->extra_title;
                }
                if ($item->sub_title) {
                    $item->sub_title = $item->extra_sub_title . ', ' . $item->sub_title;
                } else {
                    $item->sub_title = $item->extra_sub_title;
                }
                if (empty($item->product_item_image)) {
                    $item->product_image = asset('storage/products/' . $product->product_image);
                }
                $attributes = collect(unserialize($item->item->item_attributes))->sortBy('ordering');
                foreach ($attributes as $value) {
                    if ($value->attribute_type_id == 12) {
                        if (!empty($value->data->file_path)) {
                            $item->product_image =  asset('storage/products/' . $value->data->file_path);
                        }
                    }
                }
                $today = Carbon::now();
                $thresholdDateReturn = Carbon::parse($item->order_date)->copy()->addDays(site_info('order_return_days'));
                $order_return = false;
                if ($today->lessThan($thresholdDateReturn)) {
                    $order_return = true;
                }
                $item->categories = app(\App\Http\Controllers\Admin\OrderController::class)->getCategories(explode(',', $product->product_categories_id));
                $brand = ProductBrand::find($product->product_brand_id);
                $item->product_brand_title = $brand->title;
                $item->product_series_id = $product->product_series_id;
                $item->attribute_options = app(\App\Http\Controllers\Admin\OrderController::class)->getAttributeID(explode(',', $item->attribute_options));
                $item->sort_title = ucfirst(mb_substr($item->product_title, 0, 1));
                $item->sku_details = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetails($data);
                // $item->sku_details_refund = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetailsRefund($data);
                $item->sku_details_url = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetailsUrl($data);
                $item->request_refund_status = app(\App\Http\Controllers\Admin\OrderController::class)->requestRefundStatus($data);
                $item->condition = app(\App\Http\Controllers\Admin\OrderController::class)->makeCondition(unserialize($item->item->item_attributes));
                $item->sku_details_refund = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetailsRefund($data, $item->product_title, $item->title, $item->condition, $order_return);
                // $item->sku_details_refund = app(\App\Http\Controllers\Admin\OrderController::class)->makeDetailsRefund($data, $item->product_title, $item->title, $item->condition);
                $item->condition_type = strtolower(str_replace([' ', ','], '', $item->condition));
                $item->available_item = app(\App\Http\Controllers\Admin\OrderController::class)->stockCalculation(ProductItem::where('product_id', $item->product_id)->where('status', 1)->whereIn('location_id', ['US', $location_data->country ?? ''])->get(), $item->cart_group);
                $item->quantity = $count;
                $item->item_price = app(\App\Http\Controllers\Admin\OrderController::class)->itemTotalPrice($data);
                $item->review = Review::where([
                    'product_item_id' => $item->product_item_id,
                    'user_id' => auth()->user()->id,
                ])->where('parent_review_id', null)->exists();
                if ($item->review) {
                    $item->review_item = Review::where([
                        'product_item_id' => $item->product_item_id,
                        'user_id' => auth()->user()->id,
                    ])->where('parent_review_id', null)->first();
                    $item->reward_item = Reward::where('review_id', $item->review_item->id)->first();
                }
                $items[] = $item;
            }
        }
        return $items;
    }
    public function colletOrderAmount($items)
    {
        $total = 0;
        $local_total = 0;
        $local_convert_usd = 0;
        $local_currency = array();
        $transaction_method_name = array();
        foreach ($items as $value) {
            $total += $value->amount;
            if ($value->local_amount) {
                $local_total += $value->local_total;
                $local_convert_usd += ($value->local_total / $value->currency_rate);
            }
            if ($value->currency) {
                $local_currency[] = $value->currency;
            }
            if (!empty($value->transaction_method_id)) {
                $transaction_method_name[] = TransactionMethod::find($value->transaction_method_id)->title;
            }
        }
        return (object)[
            'total' => $total + $local_convert_usd,
            'local_total' => $local_total,
            'local_currency' => $local_currency,
            'transaction_method_name' => $transaction_method_name,
        ];
    }
    public function getEligibleStores($selectedCountry)
    {
        $storeIds = DB::table('shipping_rules')
            ->where(function ($query) use ($selectedCountry) {
                $query->where('destination_country', $selectedCountry)
                    ->orWhere('destination_country', '-1');
            })
            ->distinct()
            ->pluck('store_id')
            ->toArray();
        return $storeIds;
    }
    public function productItemsData($max_filter_price = null, $min_filter_price = null, $keywords = null)
    {
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        // dd($site_info);
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;

        $storeIds = $this->getEligibleStores($selectedCountry);

        //dd($storeIds);
        if ($max_filter_price && $min_filter_price) {
            if (count($storeIds) > 0) {
                $items = ProductItem::leftJoin('products', 'products.id', 'product_items.product_id')
                    ->select(
                        'product_items.*',
                        'products.product_categories_id as product_categories_id',
                        'products.product_brand_id as product_brand_id',
                        'products.title as product_title',
                        'products.slug as product_slug'
                    )
                    ->reorder()
                    ->where('product_items.status', 1)
                    ->whereIn('product_items.store_id', $storeIds)->get();
                //                    ->whereIn('product_items.location_id', [$allowedLocations, $site_info->country])->get();

            } else {
                $items = [];
            }
            // else {
            //     $items = ProductItem::leftJoin('products', 'products.id', 'product_items.product_id')
            //         ->select(
            //             'product_items.*',
            //             'products.product_categories_id as product_categories_id',
            //             'products.product_brand_id as product_brand_id',
            //             'products.title as product_title',
            //             'products.slug as product_slug'
            //         )
            //         ->reorder()
            //         ->where('product_items.status', 1)->get();
            // }
            foreach ($items as $item) {
                if ($item->discount_price && $item->discount_type) {
                    if ($item->discount_type == 1) {
                        $discount_amt = ($item->sale_price * $item->discount_price) / 100;
                    } else {
                        $discount_amt = $item->discount_price;
                    }
                    $item->price = $item->sale_price - $discount_amt;
                } else {
                    $item->price = $item->sale_price;
                }
            }
            $items_array = collect($items)->whereBetween('price', [$max_filter_price, $min_filter_price]);
        } else if ($keywords) {
            if (count($storeIds) > 0) {
                $query = ProductItem::query()->leftJoin('products', 'products.id', 'product_items.product_id')
                    ->leftJoin('product_brands', 'products.product_brand_id', 'product_brands.id')
                    ->select(
                        'product_items.*',
                        'products.product_categories_id as product_categories_id',
                        'products.product_brand_id as product_brand_id',
                        'products.title as product_title',
                        'product_brands.title as product_brand_title',
                        'products.slug as product_slug'
                    )
                    ->where('product_items.status', 1)
                    ->whereIn('product_items.store_id', $storeIds)->get();
                // ->whereIn('product_items.location_id', [$allowedLocations, $site_info->country]);
            } else {
                // $query = ProductItem::query()->leftJoin('products', 'products.id', 'product_items.product_id')
                //     ->leftJoin('product_brands', 'products.product_brand_id', 'product_brands.id')
                //     ->select(
                //         'product_items.*',
                //         'products.product_categories_id as product_categories_id',
                //         'products.product_brand_id as product_brand_id',
                //         'products.title as product_title',
                //         'product_brands.title as product_brand_title',
                //         'products.slug as product_slug'
                //     )
                //     ->where('product_items.status', 1);
                $items = [];
            }
            $brand = ProductBrand::where('title', 'LIKE', '%' . explode(' ', trim($keywords))[0] . '%')->first();
            if (!empty($brand)) {
                $query->where('product_brands.title', 'LIKE', '%' . $brand->title . '%');
                if (substr(strstr($keywords, ' '), 1)) {
                    $query->where('products.title', 'LIKE', '%' . substr(strstr($keywords, ' '), 1) . '%');
                }
            } else {
                $query->where('products.title', 'LIKE', '%' . $keywords . '%');
            }
            $items_array = $query->get();
        } else {
            if (count($storeIds) > 0) {
                //   dd($allowedLocations);
                $items_array = ProductItem::leftJoin('products', 'products.id', '=', 'product_items.product_id')
                    ->select(
                        'product_items.*',
                        'products.product_categories_id as product_categories_id',
                        'products.product_brand_id as product_brand_id',
                        'products.title as product_title',
                        'products.slug as product_slug'
                    )
                    ->where('product_items.status', 1)
                    ->whereIn('product_items.store_id', $storeIds)->get();
                // dd($items_array);
            } else {
                // $items_array = ProductItem::leftJoin('products', 'products.id', 'product_items.product_id')
                //     ->select(
                //         'product_items.*',
                //         'products.product_categories_id as product_categories_id',
                //         'products.product_brand_id as product_brand_id',
                //         'products.title as product_title',
                //         'products.slug as product_slug'
                //     )
                //     ->where('product_items.status', 1)->get();
                $items_array = [];
            }
            // dd($items_array);
        }
        //dd($items_array);
        // dd($items_array);
        $options = [];
        foreach ($items_array as $value) {
            $this->makeAttributeData(unserialize($value->item_attributes), $value);
            $options[] = $value->group_option;
        }
        //dd($items_array);
        //dd($options);
        $items_data = array_unique($options);
        $items = array();
        // dd($items_array);
        foreach ($items_data as $value) {
            //dd($value);
            //dd( collect($items_array)->where('group_option', $value));
            $count = collect($items_array)->where('group_option', $value)->count();


            if ($count == 1) {
                $data = collect($items_array)->where('group_option', $value)->all();

                $item = collect($items_array)->where('group_option', $value)->first();
                $product = Product::find($item->product_id);
                // dd($item);
                if ($product) {
                    $item->attributes = $this->getAttributes($data, $product);
                    $this->getAttributeData($item->attributes, $item);
                }

                $item->max_price = $this->selectMaxMinPrice($data)->max_price;
                $item->min_price = $this->selectMaxMinPrice($data)->min_price;
                $item->price = $this->selectMaxMinPrice($data)->price;
                $item->price_range = $this->selectMaxMinPrice($data)->price_range;
                $item->currency_rate_status = $this->selectMaxMinPrice($data)->currency_rate_status;
                if (Session::has('shop_logged_data') && !empty($this->selectMaxMinPrice($data)->local_max_price)) {
                    $item->local_max_price = $this->selectMaxMinPrice($data)->local_max_price;
                    $item->local_min_price = $this->selectMaxMinPrice($data)->local_min_price;
                    $item->local_price = $this->selectMaxMinPrice($data)->local_price;
                    $item->local_price_range = $this->selectMaxMinPrice($data)->local_price_range;
                    $item->local_currency_symbol = $this->selectMaxMinPrice($data)->local_currency_symbol;
                }
                $item->max_discount_percent = $this->selectMaxMinPrice($data)->max_discount_percent;
                $item->discount_upto = $this->selectMaxMinPrice($data)->discount_upto;
                if ($product) {
                    $item->product_title = $product->title;
                }
                if ($item->title) {
                    $item->title = $item->extra_title . ' (' . $item->title . ')';
                } else {
                    $item->title = $item->extra_title;
                }
                if ($item->sub_title) {
                    $item->sub_title = $item->extra_sub_title . ' (' . $item->sub_title . ')';
                } else {
                    $item->sub_title = $item->extra_sub_title;
                }
                if (empty($item->product_item_image)) {
                    $item->product_image = $product->product_image;
                }
                $attributes = collect(unserialize($item->item_attributes))->sortBy('ordering');
                // dd($attributes);
                foreach ($attributes as $value) {
                    if ($value->attribute_type_id == 12) {
                        if (!empty($value->data->file_path)) {
                            $item->product_image = $value->data->file_path;
                        }
                    }
                }
                if ($product) {
                    $item->categories = $this->getCategories(explode(',', $product->product_categories_id));
                    $brand = ProductBrand::find($item->product_brand_id);
                    $item->product_brand_title = $brand->title;
                    $item->product_series_id = $product->product_series_id;
                    $item->attribute_options = $this->getAttributeID(explode(',', $item->attribute_options));
                    $item->sort_title = ucfirst(mb_substr($item->product_title, 0, 1));
                }
                if ($max_filter_price && $min_filter_price) {
                    $item->max_filter_price = $max_filter_price;
                    $item->min_filter_price = $min_filter_price;
                }
                $locations_data = [];
                foreach ($data as $value_item) {
                    $locations_data[] =  $this->countries($value_item->location_id);
                }
                $item->locations = '';
                if (!empty($location_data->country) && $location_data->country != 'US') {
                    $item->locations = implode(',', array_unique($locations_data));
                    // $item->locations = $this->countries($item->location_id);
                }
                $item->estimate_delivery = '';
                if (!empty($location_data->country)) {
                    $estimate_delivery_data = EstimatedDeliveryDate::where([
                        'sku_location_id' => $item->location_id,
                        'ship_location_id' => $location_data->country,
                    ])->first();
                    if (!empty($estimate_delivery_data->sku_location_id)) {
                        $item->estimate_delivery = 'Delivery Date ' . $this->countries($estimate_delivery_data->sku_location_id) . ' to ' . $this->countries($estimate_delivery_data->ship_location_id);
                    }
                    // $item->attribute_options = $this->getAttributeID(unserialize($item->item_attributes));
                }
                $items[] = $item;
            }

            if ($count > 1) {

                $data = collect($items_array)->where('group_option', $value)->all();
                //dd($data);
                $item = collect($items_array)->where('group_option', $value)->first();
                $item->extra_title = $this->makeExtraTitle($data);
                $item->sub_title = $this->makeSubTitle($data);
                $product = Product::find($item->product_id);
                if ($product && $item->status) {
                    $item->attributes = $this->getAttributes($data, $product);
                    $this->getAttributeData($item->attributes, $item);
                }


                if (count($data) > 0) {
                    $item->max_price = $this->selectMaxMinPrice($data)->max_price;
                    $item->min_price = $this->selectMaxMinPrice($data)->min_price;
                    $item->price = $this->selectMaxMinPrice($data)->price;
                    $item->price_range = $this->selectMaxMinPrice($data)->price_range;
                    $item->currency_rate_status = $this->selectMaxMinPrice($data)->currency_rate_status;
                    if (Session::has('shop_logged_data') && !empty($this->selectMaxMinPrice($data)->local_max_price)) {
                        $item->local_max_price = $this->selectMaxMinPrice($data)->local_max_price;
                        $item->local_min_price = $this->selectMaxMinPrice($data)->local_min_price;
                        $item->local_price = $this->selectMaxMinPrice($data)->local_price;
                        $item->local_price_range = $this->selectMaxMinPrice($data)->local_price_range;
                        $item->local_currency_symbol = $this->selectMaxMinPrice($data)->local_currency_symbol;
                    }
                    $item->max_discount_percent = $this->selectMaxMinPrice($data)->max_discount_percent;
                    $item->discount_upto = true;
                    $locations_data = [];
                    foreach ($data as $value_item) {
                        $location_data = $this->countries($value_item->location_id);
                        // Handle object or array response
                        if (is_object($location_data) && !empty($location_data->country)) {
                            $locations_data[] = $location_data->country;
                        } elseif (is_array($location_data) && !empty($location_data['country'])) {
                            $locations_data[] = $location_data['country'];
                        }
                    }
                    $item->locations = '';
                    if (!empty($locations_data)) {
                        // Ensure array contains only scalar values, remove duplicates, and convert to a string
                        $locations_data = array_filter($locations_data, 'is_scalar');
                        $item->locations = implode(',', array_unique($locations_data));
                    }
                    $item->estimate_delivery = '';
                    if (!empty($location_data->country)) {
                        $estimate_delivery_data = EstimatedDeliveryDate::where([
                            'sku_location_id' => $item->location_id,
                            'ship_location_id' => $location_data->country,
                        ])->first();
                        if (!empty($estimate_delivery_data->sku_location_id)) {
                            $item->estimate_delivery = 'Delivery Date ' . $this->countries($estimate_delivery_data->sku_location_id) . ' to ' . $this->countries($estimate_delivery_data->ship_location_id);
                        }
                        // $item->attribute_options = $this->getAttributeID(unserialize($item->item_attributes));
                    }
                }
                // dd($this->selectMaxMinPrice($data));
                $item->product_title = $product->title;
                if ($item->title) {
                    $item->title = $item->extra_title . ', ' . $item->title;
                } else {
                    $item->title = $item->extra_title;
                }
                if ($item->sub_title) {
                    $item->sub_title = $item->extra_sub_title . ', ' . $item->sub_title;
                } else {
                    $item->sub_title = $item->extra_sub_title;
                }
                if (empty($item->product_item_image)) {
                    $item->product_image = $product->product_image;
                }
                $attributes = collect(unserialize($item->item_attributes))->sortBy('ordering');
                // dd($attributes);
                foreach ($attributes as $value) {
                    if ($value->attribute_type_id == 12) {
                        if (!empty($value->data->file_path)) {
                            $item->product_image = $value->data->file_path;
                            // $product_image_data = array((object)[
                            //     'file_path' => asset('storage/products/' . $value->data->file_path),
                            // ]);
                        }
                    }
                }
                // dd($item->product_image);
                $item->categories = $this->getCategories(explode(',', $product->product_categories_id));
                // $item->product_categories_id = $product->product_categories_id;
                // $item->product_brand_id = $product->product_brand_id;
                $brand = ProductBrand::find($item->product_brand_id);
                $item->product_brand_title = $brand->title;
                $item->product_series_id = $product->product_series_id;
                $item->attribute_options = $this->getAttributeID(explode(',', $item->attribute_options));
                $item->sort_title = ucfirst(mb_substr($item->product_title, 0, 1));
                // $item->attribute_options = $this->getAttributeID(unserialize($item->item_attributes));
                if ($max_filter_price && $min_filter_price) {
                    $item->max_filter_price = $max_filter_price;
                    $item->min_filter_price = $min_filter_price;
                }
                $items[] = $item;
            }
            $stock_map = [];

            foreach ($item->conditions->options as $conditionOption) {
                $conditionId = $conditionOption->id;
                //dd($conditionOption);
                // Filter items from $data where any attribute title matches the condition title
                $matchingItems = array_filter($data, function ($productItem) use ($conditionId) {
                    $attributes = collect(unserialize($productItem->item_attributes));
                    return $attributes->contains(function ($attr) use ($conditionId) {
                        return isset($attr->data->id) && Str::slug($attr->data->id) === Str::slug($conditionId);
                    });
                });

                // Check if all matching items are sold
                $allSold = collect($matchingItems)->every(function ($productItem) {
                    return $productItem->stock_status === 'sold';
                });

                // Map the condition title to 0 (out of stock) or 1 (available)
                $stock_map[$conditionId] = $allSold ? 0 : 1;
            }

            $item->stock_list = $stock_map;
        }

        // dd($items[0]->conditions);
        //dd(collect($items));
        return collect($items);
    }
    public function makeExtraTitle($items)
    {
        $extra_title = [];
        foreach ($items as $value) {
            if ($value->extra_title) {
                $extra_title[] = $value->extra_title;
            }
        }
        return implode(',', array_unique(explode(',', implode(',', $extra_title))));
    }
    public function makeSubTitle($items)
    {
        $sub_title = [];
        foreach ($items as $value) {
            if ($value->sub_title) {
                $sub_title[] = $value->sub_title;
            }
        }
        return implode(', ', array_unique(explode(', ', implode(', ', $sub_title))));
    }
    public function makeAttributeData($items, $item)
    {
        $title_array = [];
        $sub_title_array = [];
        $condition_text = [];
        $group_array = [];
        $option_keys_array = [];
        $attribute_option_keys_array = [];
        $condition = null;
        foreach (collect($items)->sortBy('ordering') as $value) {
            $data = is_object($value->data) ? $value->data : (is_array($value->data) ? (object)$value->data : null);
            if (!$data) {
                continue;
            }
            if ($value->input_type_id == 1 && $value->show_on_title) {
                $title_array[] = $data->title ?? ''; // Safely access 'title'
            }
            if ($value->input_type_id == 1 && $value->show_on_sub_title) {
                $sub_title_array[] = $data->title ?? '';
            }
            if ($value->input_type_id == 1 && $value->attribute_type_id != 1) {
                $group_array[] = preg_replace('/\s*/', '', strtolower($data->title ?? ''));
                $option_keys_array[] = ($data->title ?? '') . $item->product_id;
                $attribute_option_keys_array[] = $data->title ?? '';
            }
            if ($value->input_type_id == 2) {
                foreach ($data as $v) {
                    if (is_object($v)) {
                        $attribute_option_keys_array[] = $v->title ?? '';
                    }
                }
            }
            if ($value->attribute_type_id == 1 && !empty($data->description)) {
                $condition_text[] = $data->description;
            }
            if ($value->attribute_type_id == 1 && $value->input_type_id) {
                $condition = $data->title ?? '';
            }
        }
        $item->condition_text = count($condition_text) > 0 ? implode(',', $condition_text) : false;
        $item->extra_title = count($title_array) > 0 ? implode(', ', array_unique($title_array)) : false;
        $item->extra_sub_title = count($sub_title_array) > 0 ? implode(', ', array_unique($sub_title_array)) : false;
        $item->group_option = implode(',', array_unique($option_keys_array));
        $item->group = implode('-', array_unique($group_array));
        $item->attribute_options = implode(',', array_unique($attribute_option_keys_array));
        //                 if($condition == 'LIKE NEW' && $item->product_id == 3 ) {
        // $condition = 'likenew';
        //                 }
        $item->cart_group = preg_replace('/\s*/', '', strtolower(($item->group ?? '') . '-' . ($condition ?? '')));
        //     if($condition == 'LIKE NEW' && $item->product_id == 3 ) {
        //         $item->cart_group = 'sd';
        // }
    }
    public function selectMaxMinPrice($items)
    {
        // dd($items);
        $discount_upto = false;
        foreach ($items as $item) {
            if ($item->discount_price && $item->discount_type) {
                if ($item->discount_type == 2) {
                    $discount_upto = true;
                }
                if ($item->discount_type == 1) {
                    $discount_amt = ($item->sale_price * $item->discount_price) / 100;
                    $discount_percent = $item->discount_price;
                } else {
                    $discount_amt = $item->discount_price;
                    $discount_percent = ($item->discount_price * 100) / $item->sale_price;
                }
                $item->discount_percent = ceil($discount_percent);
                $item->price = $item->sale_price - $discount_amt;
                // dd($item);
            } else {
                $item->price = $item->sale_price;
            }
        }
        // dd($items);
        $collection  = new Collection($items);
        $max_discount_percent =  $collection->max('discount_percent');
        $max_price =  $collection->max('price');
        $min_price =  $collection->min('price');
        $price =  $max_price;
        $currency_rate = 0;
        $currency_rate_status = false;
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
            // dd($location_data);
            $currency_rate = $location_data->currency_rate;
            $currency_symbol = $location_data->currency_symbol;
            if ($currency_rate > 0) {
                $currency_rate_status = true;
            }
        }
        $price_range = getDefaultCurrencySymbol() . number_format($max_price, 2);
        if (Session::has('shop_logged_data')) {
            $local_price_range = $currency_symbol . number_format($max_price * $currency_rate, 2);
        }
        if (count($items) > 0 & $max_price != $min_price) {
            $price_range = getDefaultCurrencySymbol() . number_format($min_price, 2) . ' - ' . getDefaultCurrencySymbol() . number_format($max_price, 2);
            if (Session::has('shop_logged_data')) {
                $local_price_range = $currency_symbol . number_format($min_price * $currency_rate, 2) . ' - ' . $currency_symbol . number_format($max_price * $currency_rate, 2);
            }
        }
        if ($currency_rate > 0) {
            return (object)[
                'price' => $price,
                'price_range' => $price_range,
                'max_price' => $max_price,
                'min_price' => $min_price,
                'local_price' => $price * $currency_rate,
                'local_price_range' => $local_price_range,
                'local_max_price' => $max_price * $currency_rate,
                'local_min_price' => $min_price * $currency_rate,
                'max_discount_percent' => $max_discount_percent,
                'discount_upto' => $discount_upto,
                'currency_rate_status' => $currency_rate_status,
                'primary_currency_symbol' => getDefaultCurrencySymbol(),
                'local_currency_symbol' => $currency_symbol,
            ];
        }
        // dd($price_range);
        return (object)[
            'price' => $price,
            'price_range' => $price_range,
            'max_price' => $max_price,
            'min_price' => $min_price,
            'max_discount_percent' => $max_discount_percent,
            'discount_upto' => $discount_upto,
            'currency_rate_status' => $currency_rate_status,
            'primary_currency_symbol' => getDefaultCurrencySymbol(),
        ];
    }
    public function getAttributeData($items, $item)
    {

        foreach ($items as $value) {
            if ($value->attribute_type_id == 1) {
                $conditions = $value;
            }
            if ($value->attribute_type_id == 2) {
                $networks = $value;
            }
        }

        if (!empty($conditions) && count($conditions->options) > 0) {
            $item->conditions = $conditions;
        }
        if (!empty($networks) && count($networks->options) > 0) {
            $item->networks = $networks;
        }
        return $item;
    }
    public function getCategories($categories)
    {
        $category_titles  = array();
        foreach ($categories as $category) {
            $category = ProdcutCategoriesParent::with(['productCategories' => function ($query) {
                $query->where('status', 1);
            }])
                ->whereHas('productCategories', function ($query) use ($categories) {
                    $query->where('parent_category_id', $categories);
                })
                ->first();
            if ($category) {
                $category_titles[] = $category->title;
            }
        }
        return $categories = implode(',', $category_titles);
    }
    public function getAttributes($items, $product)
    {
        foreach ($items as $value) {
            $attributes[] = collect(unserialize($value->item_attributes))->toArray();
        }
        $attributes_array = [];
        foreach ($attributes as $attribute) {
            $attribute = $this->getAttribute($attribute);
            $attributes_array = array_merge($attributes_array, $attribute);
        }
        $attributes_options = array();
        $data_checkbox = array();
        $data_option = array();
        foreach ($attributes_array as $attribute_array) {
            if ($attribute_array->id == $attribute_array->id) {
                $attributes_options[$attribute_array->id]['id'] = $attribute_array->id;
                $attributes_options[$attribute_array->id]['title'] = $attribute_array->title;
                $attributes_options[$attribute_array->id]['attribute_type_id'] = $attribute_array->attribute_type_id;
                $attributes_options[$attribute_array->id]['options'][] = $attribute_array->options;
                if ($attribute_array->input_type_id == 1) {
                    $data_option[] = $attribute_array->data;
                }
                if ($attribute_array->input_type_id == 2) {
                    $data_checkbox = $attribute_array->data;
                }
            }
        }
        $option_items = array_merge($data_option, $data_checkbox);
        foreach ($attributes_options as $attributes_option) {
            $options[] = (object)$attributes_option;
        }
        foreach ($options as $option) {
            $unique_options_id = array_unique(explode(',', implode(',', $option->options)));
            $option->options = $this->makeAttribute($unique_options_id, $product, $option_items);
        }
        return $options;
    }
    public function makeAttribute($items, $product, $option_items)
    {
        $attributes = unserialize($product->product_attributes);
        $data = [];
        foreach ($attributes as $attribute) {
            $array = $attribute->attribute_options->toArray();
            foreach ($array as $v) {
                $data[] = $v;
            }
        }
        $collection  = collect($data);
        $item_data = array();
        foreach ($items as $item) {
            $attribute_option = $collection->where('id', $item)->first();
            if ($attribute_option) {
                $item_data[] = $attribute_option;
            } else {
                $item = collect($option_items)->where('id', $item)->first();
                if ($item) {
                    $item->file_current = null;
                    $item->file_path = null;
                    $item->extension = null;
                    $item->text_file_current = null;
                    $item->text_file_path = null;
                    $item->text_extension = null;
                    $item_data[] = $item;
                }
            }
        }
        return $item_data;
    }
    public function getAttribute($items)
    {
        foreach ($items as $item) {
            if ($item->input_type_id == 1) {
                $item->options = $item->data->id;
            }
            if ($item->input_type_id == 2) {
                $item->options = $this->getOption($item->data);
            }
        }
        return $items;
    }
    public function getOption($items)
    {
        foreach ($items as $item) {
            $options[] = $item->id;
        }
        return implode(',', $options);
    }
    public function getAttributeID($items)
    {
        $options = [];
        foreach ($items as $value) {
            $options[] = preg_replace('/\s*/', '', strtolower($value));
        }
        return implode(',', $options);
    }
    public function viewProducts($slug, $page, $selector)
    {
        Session::forget('sort_selected_options');
        if ($selector) {
            $page = $this->productDetails($page, $selector);
            if ($page->product_unavailable) {
                $request = Request::create($slug, 'GET');
                return $this->ProductNotAvailable($slug, $page, $selector, $request);
            }
            return $page->checkAccessLabel($slug) ? view('web.products.item', compact('page')) : abort(403);
        }
        $items = $this->productItemsData();
        $categories = ProdcutCategoriesParent::whereHas('productCategories', function ($query) {
            $query->where('status', 1);
        })->with('productCategories')->get();
        foreach ($categories as $category) {
            $category->product_have = $items->where('product_categories_id', $category->id)->count();
        }
        $page->categories = $categories;
        $brands = ProductBrand::where('status', true)->get();
        foreach ($brands as $brand) {
            $brand->product_have = $items->where('product_brand_id', $brand->id)->count();
        }
        $page->brands = $brands;
        $products = Product::whereStatus(true)->get();
        foreach ($products as $product) {
            $product->product_have = $items->where('product_id', $product->id)->count();
        }
        $page->products = $products;
        $series = ProductSeries::whereStatus(true)->get();
        foreach ($series as $value) {
            $value->product_have = $items->where('product_service_id', $value->id)->count();
        }
        $page->series = $series;
        $page->min_price_range = $items->min('price');
        $page->max_price_range = $items->max('price');
        $page->items = $items;
        $page->fc = request()->get('fc');
        $page->fb = request()->get('fb');
        $page->fps = request()->get('fps');
        $page->fp = request()->get('fp');
        if ($page->fc && count(explode(',', $page->fc)) == 1) {
            $get_category = ProductCategory::where('id', $page->fc)->first();
            $page->page_title = '<span>Buy</span> ' . $get_category->title;
        } elseif ($page->fb && count(explode(',', $page->fb)) == 1) {
            $get_brand = ProductBrand::where('id', $page->fb)->first();
            $page->page_title = '<span>Buy</span> ' . $get_brand->title;
        } elseif ($page->fps && count(explode(',', $page->fps)) == 1) {
            $get_series = ProductSeries::where('id', $page->fps)->first();
            $page->page_title = '<span>Buy</span> ' . $get_series->title;
        } elseif ($page->fp && count(explode(',', $page->fp)) == 1) {
            $get_product = Product::find($page->fp);
            $page->page_title = 'Buy <span>' . $get_product->title . '</span>';
        } else {
            $page->page_title = 'Featured <span>Devices</span>';
        }
        if (request()->get('fs') || request()->get('fc') || request()->get('fb') ||  request()->get('fps') || request()->get('fp') || request()->get('fpa') || request()->get('fmin') || request()->get('fmax')) {
            $items = $this->productItemsData();

            if (request()->get('fmin') && request()->get('fmax')) {
                $items = $this->productItemsData(request()->get('fmin'), request()->get('fmax'));
            }
            if (request()->fs == 'a_z') {
                $items = $items->sortBy('sort_title');
            }
            if (request()->fs == 'z_a') {
                $items = $items->sortByDesc('sort_title');
            }
            if (request()->fs == 'l_h') {
                $items = $items->sortBy('max_price');
            }
            if (request()->fs == 'h_l') {
                $items = $items->sortByDesc('max_price');
            }
            if (request()->fc) {
                $items = $items->whereIn('product_categories_id', explode(',', request()->fc));
            }
            if (request()->fb) {
                $items = $items->whereIn('product_brand_id', explode(',', request()->fb));
            }
            if (request()->fps) {
                $items = $items->whereIn('product_series_id', explode(',', request()->fps));
            }
            if (request()->fc &&  request()->fb && request()->fps) {
                $items = $items->whereIn('product_categories_id', explode(',', request()->fc))
                    ->whereIn('product_brand_id', explode(',', request()->fb))
                    ->whereIn('product_series_id', explode(',', request()->fps));
            }
            if (request()->fc &&  request()->fb) {
                $items = $items->whereIn('product_categories_id', explode(',', request()->fc))
                    ->whereIn('product_brand_id', explode(',', request()->fb));
            }
            if (request()->fb && request()->fps) {
                $items = $items->whereIn('product_brand_id', explode(',', request()->fb))
                    ->whereIn('product_series_id', explode(',', request()->fps));
            }
            if (request()->fp) {
                $items = $items->whereIn('product_id', explode(',', request()->fp));
            }
            if (request()->fp && request()->fpa) {
                $new_items = [];
                foreach (explode(',', request()->fpa) as $option) {
                    foreach ($items as $item) {
                        // dd($option, explode(',', $item->attribute_options));
                        if (in_array($option, explode(',', $item->attribute_options))) {
                            $new_items[] = $item;
                        }
                    }
                }
                $items = $new_items;
            }
            if (request()->fc) {
                $fc = explode(',', request()->fc);
            }
            if (request()->fb) {
                $fb = explode(',', request()->fb);
            }
            if (request()->fs) {
                $fs = explode(',', request()->fs);
            }
            if (request()->reset) {
                Session::forget('sort_selected_options');
            }
            $selected_array = (object)[
                'categories' => $fc ?? array(),
                'brands' => $fb ?? array(),
                'series' => $fs ?? array(),
            ];
            Session::put('sort_selected_options', $selected_array);
            // dd($page->fc);
            $page->items = collect($items)->paginate(25);
        }
        $page->products_page_slug = Page::where('page_key', 'products')->first();
        // dd($items);
        return $page->checkAccessLabel($slug) ? view('web.products.index', compact('page')) : abort(403);
    }
    public function viewTrackMyOrder($slug, $page, $selector, $request)
    {
        if ($request->action == 'search') {
            $this->validate($request, [
                'order_id' => 'required|string|max:255',
            ]);
            $page->order = Order::where('order_no', $request->order_id)->first();
            $page->have_order = Order::where('order_no', $request->order_id)->exists();
            if (!empty($page->order)) {
                $page->order->status = $this->orderStatus($page->order->status);
            }
        }
        return $page->checkAccessLabel($slug) ? view('web.track_my_order', compact('page')) : abort(403);
    }
    // Controller Method
    public function viewThankYou($slug, $page, $selector)
    {
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            $checkoutOrder = Session::get('checkout_order');
//dd($checkoutOrder->cart->order->id);
         $orderObj =  Order::find($checkoutOrder->cart->order->id);
            // Dispatch event to handle all notifications async
            event(new OrderPlaced($orderObj, $cart));

            // Clear checkout sessions
            Session::forget('stripe_checkout_session');
            Session::forget('squadco_payment_ref');
            Session::forget('cart');
            Session::forget('checkout_order');

            Session::flash('success', 'Payment success !!');

            $page->cart = $cart;
            $page->checkout_order = $checkoutOrder;
            // $tracking_code = $this->getEasyPostShipping(null, true, $checkout_order->shipping->shipping_method_data);
            $page->payment_method = TransactionMethod::where('method_key', $checkoutOrder->payment->payment_method)->first();
            $page->tracking_code = $tracking_code ?? null;
        }

        return $page->checkAccessLabel($slug) && Session::get('success')
            ? view('web.cart.thank', compact('page'))
            : redirect()->route('home');
    }

    public function productDetails($page, $selector)
    {
        // dd($selector);
        // dd($selector);
        $page = Page::where('page_key', 'product_details')->first();
        $slug = $page->slug;
        $user = User::where('username', $slug)->first();
        $page = Page::whereSlug($slug)->where('status', 1)->first();
        $location_data = Session::get('shop_logged_data');
        if ($user) $page = Page::where('page_key', 'my_profile')->where('status', 1)->first();
        if (Auth()->user()) $page->user_menu = $this->getMenu('user_menu', $slug);
        $page->sections_before_main = $this->view_sections($page->id)->sections_before_main;
        $page->sections_after_main = $this->view_sections($page->id)->sections_after_main;
        $page->top_menu = $this->getMenu('top_menu', $slug);
        $page->menu = $this->getMenu('main_menu', $slug);
        $page->footer_menu = $this->getMenu('footer_menu', $slug);
        $page->footer_menu_b = $this->getMenu('footer_menu_b', $slug);
        $page->footer_menu_c = $this->getMenu('footer_menu_c', $slug);
        $profile_page = Page::where('page_key', 'my_profile')->first();
        $page->review_images = array();
        if (Session::has('review_images')) {
            $page->review_images = Session::get('review_images');
        }
        $page->refund_images = array();
        if (Session::has('refund_images')) {
            $page->refund_images = Session::get('refund_images');
        }
        $page->countries = $this->countries();
        $products_page = Page::where('page_key', 'products')->first();
        if (!empty($products_page)) {
            $page->view_products_url = route('page', $products_page->slug);
        }
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            foreach ($cart->items as $value) {
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)
                        ->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                } else {
                    continue;
                    //$value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                }
            }
            $page->cart = $cart;
            $checkout_page = Page::where('page_key', 'checkout')->first();
            $cart_page = Page::where('page_key', 'cart')->first();
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if (!empty($checkout_page)) {
                $page->buy_now_url = route('page', $checkout_page->slug);
            }
            if (!empty($cart_page)) {
                $page->view_cart_url = route('page', $cart_page->slug);
            }
            if (!empty($thank_page)) {
                $page->view_thank_you_url = route('page', $thank_page->slug);
            }
        }
        if (Auth()->user() && !empty($profile_page)) {
            $user_group_map = UserGroupMap::where('user_id', Auth()->user()->id)->first();
            $page->user_group_id = $user_group_map->user_group_id;
            $page->my_profile = route('page', $profile_page->slug);
            $orders = Order::where('customer_id', Auth()->user()->id)->get();
            $order_items = array();
            foreach ($orders as $value) {
                $orders_array[] = $value->id;
            }
            $reviews_items = Review::where([
                'user_id' => auth()->user()->id,
            ])
                ->where('parent_review_id', null)
                ->get(['product_item_id', 'product_sku_s']);
            $reviews_items_array = array();
            $product_sku_s_array = array();
            foreach ($reviews_items as $value) {
                $reviews_items_array[] = $value->product_item_id;
                $product_sku_s_array[] = $value->product_sku_s;
            }
            //dd($reviews_items[2]);
            // $except_items = explode(',', str_replace(' ', '', implode(',', $product_sku_s_array)));
            $items = OrderItem::join('orders', 'orders.id', 'order_items.order_id')
                ->select('order_items.*', 'orders.customer_id as customer_id', 'orders.order_no as order_no')
                ->where('orders.customer_id', auth()->user()->id)
                // ->whereNotIn('product_item_id', $reviews_items_array)
                ->get();
            // dd($this->getOrderItems($items));
            $items = collect($this->getOrderItems($items))->whereNotIn('sku_details', $product_sku_s_array);
            // $items = collect($this->getOrderItems($items))->whereNotIn('product_item_id', $reviews_items_array);
            // dd($items);
            $page->products = $items;
        }
        //dd( $product_sku_s_array);
        $defaultCurrencyCode = getDefaultCurrencyCode();
        $defaultCurrencySymbol = getDefaultCurrencySymbol();

        if ($user) return $this->showProfilePage($slug, $page, $user->username);
        if ($page) {
            $countries = $this->countries();
            foreach ($countries as $key => $value) {
                $countries_data[$key] = $value . ' (' . $defaultCurrencyCode . ' ' . $defaultCurrencySymbol . ')';
            }
            $page->counties_currency = $countries_data;
            $shop_logged_data = (object)[
                'location_popup' => true,
                'country' => '',
                'country_name' => '',
                'currency' => '',
            ];
            $page->location_data = $shop_logged_data;
            // dd(Session::get('shop_logged_data'));
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                $shop_logged_data = (object)[
                    'location_popup' => false,
                    'country' => $location_data->country,
                    'country_name' => $location_data->country_name,
                    'currency' => $location_data->currency,
                    'currency_rate' => $location_data->currency_rate,
                    'currency_symbol' => $location_data->currency_symbol,
                    'country_code' => $location_data->country_code,
                    'country_short_code' => $location_data->country_short_code,
                    'banks' => $location_data->banks,
                    'rate_adjustment_last_updated' => $this->getLatestCurrencyAdjustmentTime($location_data->country)
                ];
                Session::put([
                    'shop_logged_data' => $shop_logged_data,
                ]);
                $page->location_data = Session::get('shop_logged_data');
            } else {
                $location_data = request()->ipinfo->all;
                if ($location_data && !empty($location_data['country_name'])) {
                    $shop_currency = '';
                    if ($location_data['country'] != 'US' && $location_data['country'] != 'us') {
                        $shop_currency = 'Payment option in <strong>' . $this->getLocalCurrencyName($location_data['country']) . '</strong> available at checkout';
                    }
                    $shop_logged_data = (object)[
                        'location_popup' => true,
                        'country' => $location_data['country'],
                        'country_name' => 'Your location is set to ' . $location_data['country_name'],
                        'currency' => $shop_currency,
                        'currency_rate' => $this->getLocalCurrencyRate($location_data['country']),
                        'currency_symbol' => $this->getLocalCurrencySymbol($location_data['country']),
                        'country_code' => $this->getLocalCountryCode($location_data['country']),
                        'country_short_code' => $this->getLocalCountryShortCode($location_data['country']),
                        'banks' => $this->getBanks($location_data['country']),
                        'rate_adjustment_last_updated' => $this->getLatestCurrencyAdjustmentTime($location_data['country'])
                    ];
                    Session::put([
                        'shop_logged_data' => $shop_logged_data,
                    ]);
                    $page->location_data = Session::get('shop_logged_data');
                    $page->currency_rate = $this->getLocalCurrencyRate($location_data['country']);
                }
            }
        }
        $group_id = request()->get('group');
        $max_query_price = request()->get('max');
        $min_query_price = request()->get('min');
        $condition = preg_replace('/\s*/', '', strtolower(request()->get('condition')));
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        // $query = collect($this->productItemsData());
        // $item = $query->where('product_slug', $selector)->where('group',$group_id);
        //dd($selector);
        $product = Product::where('slug', $selector)->first();
        if (count($storeIds) > 0) {
            if($product) {
            $items_array = ProductItem::where('product_id', $product->id)->where('status', 1)->whereIn('store_id', $storeIds)->get();
            } else {
                $items_array = collect();
            }
        } else {
            // $items_array = ProductItem::where('product_id', $product->id)->where('status', 1)->get();

            // if ($product && $product->id) {
            //     $items_array = ProductItem::where('product_id', $product->id)
            //         ->where('status', 1)
            //         ->get();
            // } else {
            //     $items_array = collect(); // Return an empty collection if product is null
            // }
            $items_array = collect();
        }
        // dd($items_array);
        foreach ($items_array as $value) {
            $discount_amt = 0;
            $this->makeAttributeData(unserialize($value->item_attributes), $value);
            if ($value->discount_price && $value->discount_type) {
                if ($value->discount_type == 1) {
                    $discount_amt = ($value->sale_price * $value->discount_price) / 100;
                } else {
                    $discount_amt = $value->discount_price;
                }
                $value->price = $value->sale_price - $discount_amt;
                // $value->price = number_format($value->sale_price - $discount_amt, 2);
            } else {
                $value->price = $value->sale_price;
            }
            $value->price_off_amt = number_format(ceil($discount_amt), 2) ?? 0;
            $currency_rate = 0;
            $currency_rate_status = false;
            $currency_symbol = getDefaultCurrencySymbol();
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                // dd($location_data);
                $currency_rate = $location_data->currency_rate;
                $currency_symbol = $location_data->currency_symbol;
                if ($currency_rate > 0) {
                    $currency_rate_status = true;
                }
            }
            $value->local_price = number_format(($value->price * $currency_rate), 2);
            $value->local_currency_symbol = $currency_symbol;
            $value->currency_rate_status = $currency_rate_status;
        }
        // dd($items_array);
        if ($max_query_price && $min_query_price) {
            $items_array = collect($items_array)->whereBetween('price', [$max_query_price, $min_query_price]);
            // dd($items_array, $max_query_price, $min_query_price);
        }
        // dd(collect($items_array)->where('group', $group_id));
        $max_price = collect($items_array)
            ->where('group', $group_id)
            ->where('stock_status', 'yes')
            ->max('price');
        // dd($group_id,$items_array, $max_price);
        foreach ($items_array as $value) {
            if ($value->stock_status === 'yes' && $value->price == $max_price) {
                $max_price = $value->price;
                $item = $value;
            }
        }
        if (empty($item)) {
            // $page = Page::whereSlug('product-not-available')->where('status', 1)->first();
            $page->product_unavailable = true;
            //$page->checkAccessLabel('product-not-available'); // this is fine
            return $page;
        }
        //dd($product->title);
        if (!empty($product)) {
            // dd($item,$product->title);
            $item->product_title = $product->title;
        }
        if ($item->title) {
            $item->title = $item->extra_title . ', ' . $item->title;
        } else {
            $item->title = $item->extra_title;
        }
        if ($item->sub_title) {
            $item->sub_title = $item->extra_sub_title . ', ' . $item->sub_title;
        } else {
            $item->sub_title = $item->extra_sub_title;
        }
        // dd($item);
        // $item->extra_title = $this->makeExtraTitle(unserialize($item->item_attributes));
        // $item->sub_title = $this->makeSubTitle($data);
        $brand = ProductBrand::find($product->product_brand_id);
        $item->product_brand_title = $brand->title;
        $item->product_description = $product->description;
        $item->product_specifications = unserialize($product->specifications);
        $item->product_shipping = $product->shipping;
        $item->product_warranty_returns = $product->warranty_returns;
        // $item->selected_condition = $condition ?? '';
        // dd($product,$item);
        //   dd($items_array);
        $items = collect($items_array)->where('group', $group_id);

        foreach ($items as $value) {
            $value->group = $this->getItemAttribute(unserialize($value->item_attributes));
            $value->condition = $this->makeCondition(unserialize($value->item_attributes));
        }
        foreach ($items as $value) {
            $conditions_data[] = $value->condition;
        }
        //dd($items);
        foreach (array_unique($conditions_data) as $condition_v) {
            $conditions[$condition_v] = collect($items)->where('condition', $condition_v);
        }
        // dd($conditions);
        $new_conditions = array();
        // dd($conditions );
        foreach ($conditions as $key => $value) {
            $new_conditions[$key]['items'] = $value;
            $new_conditions[$key]['key'] =  preg_replace('/\s*/', '', strtolower($key));
            $new_conditions[$key]['title'] = $key;
            $new_conditions[$key]['price'] = $this->getConditionItemPrice($value)->price;
            $new_conditions[$key]['local_price'] = $this->getConditionItemPrice($value)->local_price;
            $new_conditions[$key]['currency_rate_status'] = $this->getConditionItemPrice($value)->currency_rate_status;
            $new_conditions[$key]['max_price'] = $this->getConditionItemPrice($value)->max_price;
            $new_conditions[$key]['last_sold_item_id'] = $this->getLastSoldSkuAndPrice($value)->id;
            $new_conditions[$key]['last_sold_price'] = $this->getLastSoldSkuAndPrice($value)->price;
            $new_conditions[$key]['last_sold_local_price'] = $this->getLastSoldSkuAndPrice($value)->local_price;
        }
        if ($item->stock_status == 'sold') {
            $item->last_sold_item_id =  $this->getLastSoldSkuAndPrice((object)$item)->id;
            $item->last_sold_price =  $this->getLastSoldSkuAndPrice((object)$item)->price;
            $item->last_sold_local_price =  $this->getLastSoldSkuAndPrice((object)$item)->local_price;
        } else {
            $item->last_sold_item_id =  null;
            $item->last_sold_price =  null;
            $item->last_sold_local_price =  null;
        }
        foreach ($conditions as $key => $value) {
            if (!empty($condition)) {
                $new_conditions[$key]['active'] = false;
                if ($condition == preg_replace('/\s*/', '', strtolower($key))) {
                    $new_conditions[$key]['active'] = true;
                }
            } else {
                $new_conditions[$key]['active'] = $this->getActiveConditionItem($new_conditions, $value);
            }
        }
        // dd($new_conditions);
        $item->active_sku_items = $this->getActiveItems($new_conditions)->items;
        //dd($item->active_sku_items);
        $item->active_sku_item_images = $this->getActiveItems($new_conditions)->sku_item_images;
        //$newActive = $this->getActiveItems($new_conditions)->currentActive;

        $item->conditions = $new_conditions;
        $active_condition = collect($new_conditions)->where('active', true)->first();
        //$active_condition = $newActive;
        if (!is_null($active_condition) && isset($active_condition['title'])) {
            $item->condition = strtoupper($active_condition['title']);
        } else {
            // Handle the case where no active condition is found
            $item->condition = '1'; // or any default value or error handling logic
        }
        $item_attributes = unserialize($item->item_attributes);
        foreach ($item_attributes as $value) {
            if ($value->attribute_type_id == 2) {
                $networks = $value->data;
                break;
            }
        }
        // $item->condition = strtoupper($condition);
        $item->networks = $networks ?? false;
        $page->inventory_status = $product->inventory_status;
        $page->parent_product_id = $product->id;
        //dd($item);
        $page->item = $item;
        $page->items = $items;
        $product_item_image = asset('storage/products/' . $product->product_image);
        $attributes = collect(unserialize($item->item_attributes))->sortBy('ordering');
        foreach ($attributes as $value) {
            if ($value->attribute_type_id == 12) {
                if (!empty($value->data->file_path)) {
                    $product_item_image =  asset('storage/products/' . $value->data->file_path);
                }
            }
        }
        if ($item->product_item_image) {
            $product_item_image = asset('storage/product_item_images/' . $item->product_item_image);
        }
        $item->product_item_image = $product_item_image;
        $ratings = Review::where([
            'product_id' => $item->product_id,
            'status' => 2,
        ])->where('parent_review_id', null)->get();
        foreach ($ratings as $value) {
            $review_images = unserialize($value->review_images);
            $value->image = $this->getRatingImage($review_images);
            $value->review_images = $review_images;
            $user = User::find($value->user_id);
            $product = Product::find($value->product_id);
            $value->reviewer_name = $user->first_name . '' . $user->last_name;
            $value->status = $this->reviewStatus($value->status);
            $value->date_time = Carbon::parse($value->created_at)->format('M, d Y g:i A');
            $value->rating_icon = implode('', $this->displayRating($value->rating));
        }
        $page->ratings = $ratings;
        $page->rating_count = $ratings->count();
        // dd($page);
        $page->series = ProductSeries::whereStatus(true)->get();
        // dd($page->brands);
        $product_items = count($this->productItemsData());
        $random_count = 1;
        if ($product_items > 4) {
            $random_count = 5;
        }
        //dd($page->products);
        $page->recommended_items = collect($this->productItemsData())->random($random_count)->take(20);
        return $page;
    }

    public function getActiveItems($items)
    {
        // dd($items);
        $location_data = Session::get('shop_logged_data');
        $cart_id = array();
        $cart_sku_partial_paid_id = array();
        if (Session::has('cart')) {
            $cart_items = Session::get('cart')->items;
            foreach ($cart_items as $cart_value) {
                foreach ($cart_value->cart_items as $cart_item) {
                    $cart_id[] = $cart_item->id;
                    if ($cart_item->sku_partial_paid) {
                        $cart_sku_partial_paid_id[] = $cart_item->id;
                    }
                }
            }
        };
        $sku_items = collect();

        foreach ($items as $value) {
            if ($value['active']) {
                $sku_items = collect($value['items'])->filter(function ($item) {
                    return !isset($item['stock_status']) || $item['stock_status'] !== 'sold';
                })->values(); // ->values() to reindex the collection
                break;
            }
        }

        $collection =  new Collection($sku_items);
        // dd($sku_items);
        // $max_price =  $collection->max('max_price');
        $max_price =  $collection->max('price');
        foreach ($sku_items as $value) {
            $value->sku_location = '';
            $value->location_message = '';
            if (!empty($value->location_id)) {
                if (!empty($location_data) && isset($location_data->country) && $location_data->country !== 'US') {
                    //$value->sku_location = $this->countries($value->location_id);
                    $value->sku_location = $value->location_id;
                    $value->location_message = '<strong class="text-primary">' . $value->sku_location . '</strong> (Free Shipping to ' . $location_data->country . ' Store)';
                }
                if (
                    isset($location_data, $location_data->country, $value, $value->location_id) &&
                    $location_data->country == $value->location_id &&
                    $location_data->country != 'US'
                ) {
                    $value->location_message = '<strong class="text-primary">' . $value->sku_location . '</strong> (Free in-store pickup today)';
                }
            }
            if (!empty($location_data->country)) {
                $value->ship_location = $location_data->country;
            }
            $value->estimate_delivery = '';
            if (!empty($location_data->country)) {
                $estimate_delivery_data = EstimatedDeliveryDate::where([
                    'sku_location_id' => $value->location_id,
                    'ship_location_id' => $location_data->country,
                ])->first();
                if (!empty($estimate_delivery_data->sku_location_id)) {
                    $minDurationDays = $estimate_delivery_data->min_duration;
                    $maxDurationDays = $estimate_delivery_data->max_duration;
                    // Get the current date
                    $currentDate = Carbon::now();
                    // Calculate the estimated delivery dates
                    $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                    $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);
                    // Format the dates
                    $minFormatted = $minDeliveryDate->format('M d');
                    $maxFormatted = $maxDeliveryDate->format('M d');
                    $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                }
            }
            $value->checked = false;
            // if($value->max_price == $max_price){
            if ($value->price == $max_price) {
                $active_sku = $value->product_item_sku;
                $sku_item_images = unserialize($value->product_item_images);
                $value->checked = true;
            }
            $value->in_cart = false;
            $value->sku_partial_paid = false;
            if (in_array($value->id, $cart_id)) {
                $value->in_cart = true;
            }
            if (in_array($value->id, $cart_sku_partial_paid_id)) {
                $value->sku_partial_paid = true;
            }
        }
        if (!empty($sku_item_images)) {
            foreach ($sku_item_images as $value) {
                $value->sku = $active_sku;
            }
        }
        // dd($sku_items);
        return (object)[
            'items' => $sku_items,
            'sku_item_images' => $sku_item_images ?? false,
        ];
    }
    public function getActiveItems2($items)
    {

        $location_data = Session::get('shop_logged_data');
        $cart_id = array();
        $cart_sku_partial_paid_id = array();
        if (Session::has('cart')) {
            $cart_items = Session::get('cart')->items;
            foreach ($cart_items as $cart_value) {
                foreach ($cart_value->cart_items as $cart_item) {
                    $cart_id[] = $cart_item->id;
                    if ($cart_item->sku_partial_paid) {
                        $cart_sku_partial_paid_id[] = $cart_item->id;
                    }
                }
            }
        };
        $sku_items = array();
        $new_active = array();

        // dump($items);
        $items_array = array_values($items); // reindex numerically

        for ($i = 0; $i < count($items_array); $i++) {
            $value = $items_array[$i];

            if ($value['active']) {
                $sku_items = $value['items'];
                //dd($sku_items);
                $new_active =  $value;
                $firstItem = $sku_items->first();

                if ($sku_items->count() === 1 && isset($firstItem['stock_status']) && $firstItem['stock_status'] === 'sold') {
                    // Use next item if exists
                    if (isset($items_array[$i + 1])) {
                        $sku_items = $items_array[$i + 1]['items'];
                        $new_active =  $items_array[$i + 1];
                    }
                }

                break;
            }
        }

        $collection =  new Collection($sku_items);
        // dd($sku_items);
        // $max_price =  $collection->max('max_price');
        $max_price =  $collection->max('price');
        foreach ($sku_items as $value) {
            $value->sku_location = '';
            $value->location_message = '';
            $storeLocation  = \App\Models\Setting::find($value->store_id)?->country;

            if (!empty($value->store_id)) {
                // if (!empty($location_data) && isset($location_data->country) && $location_data->country !== 'US') {
                //$value->sku_location = $this->countries($value->location_id);
                $value->sku_location = $value->location_id;
                // $value->location_message = '<strong class="text-primary">' . $value->sku_location . '</strong> (Free Shipping to ' . $location_data->country . ' Store)';
                $value->location_message = '<strong class="text-primary">' . $storeLocation . '</strong>';
                // }
                if (
                    isset($location_data, $location_data->country, $value, $value->location_id) &&
                    $location_data->country == $value->location_id &&
                    $location_data->country != 'US'
                ) {
                    $value->location_message = '<strong class="text-primary">' . $storeLocation . '</strong>';
                }
            }
            if (!empty($location_data->country)) {
                $value->ship_location = $location_data->country;
            }
            $value->estimate_delivery = '';
            if (!empty($location_data->country)) {
                $estimate_delivery_data = EstimatedDeliveryDate::where([
                    'sku_location_id' => $value->location_id,
                    'ship_location_id' => $location_data->country,
                ])->first();
                if (!empty($estimate_delivery_data->sku_location_id)) {
                    $minDurationDays = $estimate_delivery_data->min_duration;
                    $maxDurationDays = $estimate_delivery_data->max_duration;
                    // Get the current date
                    $currentDate = Carbon::now();
                    // Calculate the estimated delivery dates
                    $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                    $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);
                    // Format the dates
                    $minFormatted = $minDeliveryDate->format('M d');
                    $maxFormatted = $maxDeliveryDate->format('M d');
                    $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                }
            }
            $value->checked = false;
            // if($value->max_price == $max_price){
            if ($value->price == $max_price) {
                $active_sku = $value->product_item_sku;
                $sku_item_images = unserialize($value->product_item_images);
                $value->checked = true;
            }
            $value->in_cart = false;
            $value->sku_partial_paid = false;
            if (in_array($value->id, $cart_id)) {
                $value->in_cart = true;
            }
            if (in_array($value->id, $cart_sku_partial_paid_id)) {
                $value->sku_partial_paid = true;
            }
        }
        if (!empty($sku_item_images)) {
            foreach ($sku_item_images as $value) {
                $value->sku = $active_sku;
            }
        }
        // dd($sku_items);
        return (object)[
            'items' => $sku_items,
            'sku_item_images' => $sku_item_images ?? false,
            'currentActive' =>  $new_active
        ];
    }
    public function getActiveConditionItem($items, $value)
    {
        $collection =  new Collection($items);
        // dd($collection);
        $max_price =  $collection->max('max_price');
        // dd($max_price);
        $status = false;
        // dd($condition);
        if (empty($condition)) {
            foreach ($value as $v) {
                if ($max_price == $v->price) {
                    $status = true;
                }
            }
        }
        return $status;
    }
    public function getLastSoldSkuAndPrice($items)
    {
        // Extract product_item_ids from the $items collection
        $productItemIds = collect($items)->pluck('id')->toArray();
        $local_price = null;
        // Query the order_items table for the latest sold product_item
        $lastSold = DB::table('order_items')
            ->whereIn('product_item_id', $productItemIds)
            ->orderByDesc('created_at')
            ->first();
        if ($lastSold) {


            return (object)[
                'id' => $lastSold->product_item_id,
                'price' => getDefaultCurrencySymbol() . $lastSold->price,
                'local_price' => $this->getLocalPrice($lastSold->price)
            ];
        }

        // Return first item data if no matching sales found
        return (object)[
            'id' => $items->first()->id,
            'price' => getDefaultCurrencySymbol() . $items->first()->sale_price,
            'local_price' => $this->getLocalPrice($items->first()->sale_price)
        ];
    }
    public function getLocalPrice($price)
    {
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
            $local_price = $location_data->currency_symbol . number_format($price * $location_data->currency_rate, 2) . '+';
            return $local_price;
        }
        return null;
    }
    public function getConditionItemPrice($items)
    {
        foreach ($items as $item) {
            // $item->price = $item['discount_price'] ??  $item['sale_price'];
            if ($item['discount_price'] && $item['discount_type']) {
                if ($item->discount_type == 1) {
                    $discount_amt = ($item['sale_price'] * $item['discount_price']) / 100;
                    $discount_percent = $item['discount_price'];
                } else {
                    $discount_amt = $item['discount_price'];
                    $discount_percent = ($item['discount_price'] * 100) / $item['sale_price'];
                }
                $item->discount_percent = ceil($discount_percent);
                $item->price = $item['sale_price'] - $discount_amt;
            } else {
                $item->price = $item['sale_price'];
            }
            $item->stock_status = $item['stock_status'];
        }
        $collection  = new Collection($items);
        $min_price =  $collection->min('price');
        $max_price =  $collection->max('price');
        $price = $min_price;
        $currency_rate_status = false;
        $local_price = 0;
        if (count($items) == 1) {
            $price = getDefaultCurrencySymbol() . number_format($min_price, 2);
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                $local_price = $location_data->currency_symbol . number_format($min_price * $location_data->currency_rate, 2);
                if ($location_data->currency_rate > 0) {
                    $currency_rate_status = true;
                }
            }
        } else {
            $price = getDefaultCurrencySymbol() . number_format($min_price, 2) . '+';
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                $local_price = $location_data->currency_symbol . number_format($min_price * $location_data->currency_rate, 2) . '+';
                if ($location_data->currency_rate > 0) {
                    $currency_rate_status = true;
                }
            }
        }
        // dd($currency_rate_status);
        return (object)[
            'price' => $price,
            'local_price' => $local_price,
            'currency_rate_status' => $currency_rate_status,
            'max_price' => $max_price,
        ];
    }
    public function makeCondition($items)
    {
        foreach ($items as $value) {
            if ($value->attribute_type_id == 1) {
                return $value->data->title;
            }
        }

        // fallback if no attribute_type_id == 1
        return $items[0]->data->title ?? '';
    }

    public function getItemAttribute($items)
    {
        $group_array = array();
        foreach (collect($items)->sortBy('ordering') as $value) {
            if ($value->input_type_id == 1 && $value->attribute_type_id != 1) {
                $group_array[] = preg_replace('/\s*/', '', strtolower($value->data->title));
            }
        }
        return implode('-', $group_array);
    }
    public function productSort(Request $request)
    {
        $items = $this->productItemsData();
        if ($request->fmin && $request->fmax) {
            $items = $this->productItemsData($request->fmin, $request->fmax);
        }
        if ($request->fs == 'a_z') {
            $items = $items->sortBy('sort_title');
        }
        if ($request->fs == 'z_a') {
            $items = $items->sortByDesc('sort_title');
        }
        if ($request->fs == 'l_h') {
            $items = $items->sortBy('max_price');
        }
        if ($request->fs == 'h_l') {
            $items = $items->sortByDesc('max_price');
        }
        if ($request->fc) {
            $items = $items->whereIn('product_categories_id', explode(',', $request->fc));
        }
        if ($request->fb) {
            $items = $items->whereIn('product_brand_id', explode(',', $request->fb));
        }
        if ($request->fps) {
            $items = $items->whereIn('product_series_id', explode(',', $request->fps));
        }
        if ($request->fc &&  $request->fb && $request->fps) {
            $items = $items->whereIn('product_categories_id', explode(',', $request->fc))
                ->whereIn('product_brand_id', explode(',', $request->fb))
                ->whereIn('product_series_id', explode(',', $request->fps));
        }
        if ($request->fc &&  $request->fb) {
            $items = $items->whereIn('product_categories_id', explode(',', $request->fc))
                ->whereIn('product_brand_id', explode(',', $request->fb));
        }
        if ($request->fb && $request->fps) {
            $items = $items->whereIn('product_brand_id', explode(',', $request->fb))
                ->whereIn('product_series_id', explode(',', $request->fps));
        }
        if ($request->fp) {
            $items = $items->whereIn('product_id', explode(',', $request->fp));
        }
        if ($request->fp && $request->fpa) {
            $new_items = [];
            foreach (explode(',', $request->fpa) as $option) {
                foreach ($items as $item) {
                    // dd($option, explode(',', $item->attribute_options));
                    if (in_array($option, explode(',', $item->attribute_options))) {
                        $new_items[] = $item;
                    }
                }
            }
            $items = $new_items;
        }
        if ($request->fc) {
            $fc = explode(',', $request->fc);
        }
        if ($request->fb) {
            $fb = explode(',', $request->fb);
        }
        if ($request->fs) {
            $fs = explode(',', $request->fs);
        }
        if ($request->reset) {
            Session::forget('sort_selected_options');
        }
        $selected_array = (object)[
            'categories' => $fc ?? array(),
            'brands' => $fb ?? array(),
            'series' => $fs ?? array(),
        ];
        Session::put('sort_selected_options', $selected_array);
        // if ($request->fmin && $request->fmax) {
        //     $items = $query->whereBetween('max_price', [$request->fmin, $request->fmax]);
        //     $items = $query->whereBetween('min_price', [$request->fmin, $request->fmax]);
        // }
        $items = collect($items)->paginate(9);
        $items->appends(array(
            'fs' => $request->fs,
            'fc' => $request->fc,
            'fb' => $request->fb,
            'fps' => $request->fps,
            'fp' => $request->fp,
            'fpa' => $request->fpa,
            'fmin' => $request->fmin,
            'fmin' => $request->fmin,
            'fmax' => $request->fmax,
        ));
        $viewMode = GeneralSetting::get('product_list_view_mode', 'grid');
        return view('web.jquery_live.products', compact('items', 'viewMode'));
    }
    public function getProductAttribute(Request $request)
    {
        $item = Product::find($request->pfp);
        $attributes = unserialize($item->product_attributes);
        foreach ($attributes as $key => $value) {
            if ($value->attribute_type_id == 1 || $value->attribute_type_id == 2) {
                unset($attributes[$key]);
            }
        }
        if (request()->get('fpa') && count($attributes) > 0) {
            foreach ($attributes as $value) {
                foreach ($value->attribute_options as $option) {
                    $option->checked = false;
                    $option->val_id = preg_replace('/\s*/', '', strtolower(str_replace('&', '', $option->title)));
                    if (in_array($option->val_id, explode(',', request()->get('fpa')))) {
                        $option->checked = true;
                    }
                }
            }
        } else {
            foreach ($attributes as $value) {
                foreach ($value->attribute_options as $option) {
                    $option->checked = false;
                    $option->val_id = preg_replace('/\s*/', '', strtolower(str_replace('&', '', $option->title)));
                }
            }
        }
        // dd($attributes);
        return view('web.jquery_live.attributes', compact('attributes'));
    }
    public function categoriesBrandsSort(Request $request)
    {
        $brands = ProductBrand::all();
        $brands_id = array();
        foreach ($brands as $brand) {
            $cat_array = explode(',', $brand->product_categories_id);
            $req_categories = explode(',', $request->cfc);
            foreach ($req_categories as $req_cat_id) {
                if (in_array($req_cat_id, $cat_array)) {
                    $brands_id[] = $brand->id;
                }
            }
        }
        foreach (array_unique($brands_id) as $value) {
            $items[] =  ProductBrand::find($value);
        }
        if (empty($brands_id) && $request->cfc) {
            $items = array();
        }
        if (empty($request->cfc)) {
            $items = ProductBrand::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        $selected_options_array = array();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            $selected_options_array = $sort_selected_options->brands;
        }
        return count($items) == 0 ? '<p class="text-light">No Brand available</p>' : view('web.jquery_live.brands', compact('items', 'checked', 'selected_options_array'));
    }
    public function categoriesSeriesSort(Request $request)
    {
        $items = ProductSeries::whereIn('product_category_id', explode(',', $request->cfc))->get();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            if ($request->cfc && $sort_selected_options->brands) {
                $items = ProductSeries::whereIn('product_brand_id', $sort_selected_options->brands)
                    ->whereIn('product_category_id', explode(',', $request->cfc))
                    ->get();
            }
        }
        if (count($items) == 0 && $request->cfc) {
            $items = array();
        }
        if (empty($request->cfc)) {
            $items = ProductSeries::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        $selected_options_array = array();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            $selected_options_array = $sort_selected_options->series;
        }
        return count($items) == 0 ? '<p class="text-light">No Series available</p>' : view('web.jquery_live.series', compact('items', 'checked', 'selected_options_array'));
    }
    public function categoriesProductsSort(Request $request)
    {
        $items = Product::whereIn('product_categories_id', explode(',', $request->cfc))->get();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            if ($request->cfc && $sort_selected_options->brands) {
                $items = collect($items)->whereIn('product_brand_id', $sort_selected_options->brands);
            }
            if ($request->cfc && $sort_selected_options->series) {
                $items = collect($items)->whereIn('product_series_id', $sort_selected_options->series);
            }
        }
        if (count($items) == 0 && $request->cfc) {
            $items = array();
        }
        if (empty($request->cfc)) {
            $items = Product::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        return count($items) == 0 ? '<p class="text-light">No Modal available</p>' : view('web.jquery_live.product_options', compact('items', 'checked'));
    }
    public function brandsCategoriesSort(Request $request)
    {
        $brands = ProductBrand::whereIn('id', explode(',', $request->bfb))->get();
        $categories_id = array();
        foreach ($brands as $brand) {
            $categories_id[] = $brand->product_categories_id;
        }
        foreach (array_unique(explode(',', implode(',', $categories_id))) as $value) {
            $items[] =  ProductCategory::find($value);
        }
        if (empty($categories_id) && $request->bfb) {
            $items = array();
        }
        if (empty($request->bfb)) {
            $items = ProductCategory::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        $selected_options_array = array();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            $selected_options_array = $sort_selected_options->categories;
        }
        return count($items) == 0 ? '<p class="text-light">No Category available</p>' : view('web.jquery_live.categories', compact('items', 'checked', 'selected_options_array'));
    }
    public function brandsSeriesSort(Request $request)
    {
        $items = ProductSeries::whereIn('product_brand_id', explode(',', $request->bfb))->get();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            if ($request->bfb && $sort_selected_options->categories) {
                $items = ProductSeries::whereIn('product_brand_id', explode(',', $request->bfb))
                    ->whereIn('product_category_id', $sort_selected_options->categories)
                    ->get();
            }
        }
        if (count($items) == 0 && $request->bfb) {
            $items = array();
        }
        if (empty($request->bfb)) {
            $items = ProductSeries::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        $selected_options_array = array();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            $selected_options_array = $sort_selected_options->series;
        }
        return count($items) == 0 ? '<p class="text-light">No Series available</p>' : view('web.jquery_live.series', compact('items', 'checked', 'selected_options_array'));
    }
    public function brandsProductsSort(Request $request)
    {
        $items = Product::whereIn('product_brand_id', explode(',', $request->bfb))->get();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            if ($request->bfb && $sort_selected_options->categories) {
                $items = collect($items)->whereIn('product_categories_id', $sort_selected_options->categories);
            }
            if ($request->bfb && $sort_selected_options->series) {
                $items = collect($items)->whereIn('product_series_id', $sort_selected_options->series);
            }
        }
        if (count($items) == 0 && $request->bfb) {
            $items = array();
        }
        if (empty($request->bfb)) {
            $items = Product::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        return count($items) == 0 ? '<p class="text-light">No Modal available</p>' : view('web.jquery_live.product_options', compact('items', 'checked'));
    }
    public function seriesCategoriesSort(Request $request)
    {
        $series = ProductSeries::whereIn('id', explode(',', $request->sfps))->get();
        $categories_id = array();
        foreach ($series as $value) {
            $categories_id[] = $value->product_category_id;
        }
        foreach (array_unique(explode(',', implode(',', $categories_id))) as $value) {
            $items[] =  ProductCategory::find($value);
        }
        if (empty($categories_id) && $request->sfps) {
            $items = array();
        }
        if (empty($request->sfps)) {
            if (Session::has('sort_selected_options')) {
                $sort_selected_options = Session::get('sort_selected_options');
                $brands = ProductBrand::whereIn('id', $sort_selected_options->brands)->get();
                $categories_id = array();
                foreach ($brands as $brand) {
                    $categories_id[] = $brand->product_categories_id;
                }
                $session_items = array();
                foreach (array_unique(explode(',', implode(',', $categories_id))) as $value) {
                    $session_items[] =  ProductCategory::find($value);
                }
                $items = $session_items;
            } else {
                $items = ProductCategory::get();
            }
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        $selected_options_array = array();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            $selected_options_array = $sort_selected_options->categories;
        }
        return count($items) == 0 ? '<p class="text-light">No Category available</p>' : view('web.jquery_live.categories', compact('items', 'checked', 'selected_options_array'));
    }
    public function seriesBrandsSort(Request $request)
    {
        $series = ProductSeries::whereIn('id', explode(',', $request->sfps))->get();
        $brand_id = array();
        foreach ($series as $value) {
            $brand_id[] = $value->product_brand_id;
        }
        foreach (array_unique(explode(',', implode(',', $brand_id))) as $value) {
            $items[] =  ProductBrand::find($value);
        }
        if (empty($brand_id) && $request->sfps) {
            $items = array();
        }
        if (empty($request->sfps)) {
            if (Session::has('sort_selected_options')) {
                $sort_selected_options = Session::get('sort_selected_options');
                $brands = ProductBrand::all();
                $brands_id = array();
                foreach ($brands as $brand) {
                    $cat_array = explode(',', $brand->product_categories_id);
                    $req_categories = $sort_selected_options->categories;
                    foreach ($req_categories as $req_cat_id) {
                        if (in_array($req_cat_id, $cat_array)) {
                            $brands_id[] = $brand->id;
                        }
                    }
                }
                $session_items = array();
                foreach (array_unique($brands_id) as $value) {
                    $session_items[] =  ProductBrand::find($value);
                }
                $items = $session_items;
            } else {
                $items = ProductBrand::get();
            }
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        $selected_options_array = array();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            $selected_options_array = $sort_selected_options->brands;
        }
        return count($items) == 0 ? '<p class="text-light">No Brand available</p>' : view('web.jquery_live.brands', compact('items', 'checked', 'selected_options_array'));
    }
    public function seriesProductsSort(Request $request)
    {
        $items = Product::whereIn('product_series_id', explode(',', $request->sfps))->get();
        if (Session::has('sort_selected_options')) {
            $sort_selected_options = Session::get('sort_selected_options');
            if ($request->sfps && $sort_selected_options->categories) {
                $items = collect($items)->whereIn('product_categories_id', $sort_selected_options->categories);
            }
            if ($request->sfps && $sort_selected_options->brands) {
                $items = collect($items)->whereIn('product_brand_id', $sort_selected_options->brands);
            }
        }
        if (count($items) == 0 && $request->sfs) {
            $items = array();
        }
        if (empty($request->sfps)) {
            $items = Product::get();
        }
        $checked = false;
        if (count($items) == 1) {
            $checked = true;
        }
        if (request()->get('fp') && count($items) > 0) {
            foreach ($items as $value) {
                $value->checked = false;
                if (request()->get('fp') == $value->id) {
                    $value->checked = true;
                }
            }
        }
        // dd($items);
        return count($items) == 0 ? '<p class="text-light">No Modal available</p>' :  view('web.jquery_live.product_options', compact('items', 'checked'));
    }
    public function allCategories()
    {
        $items = ProductCategory::get();
        $checked = false;
        $selected_options_array = array();
        return count($items) == 0 ? null : view('web.jquery_live.categories', compact('items', 'checked', 'selected_options_array'));
    }
    public function allBrands()
    {
        $items = ProductBrand::get();
        $checked = false;
        $selected_options_array = array();
        return count($items) == 0 ? null : view('web.jquery_live.brands', compact('items', 'checked', 'selected_options_array'));
    }
    public function allSeries()
    {
        $items = ProductSeries::get();
        $checked = false;
        $selected_options_array = array();
        return count($items) == 0 ? null : view('web.jquery_live.series', compact('items', 'checked', 'selected_options_array'));
    }
    public function allProducts()
    {
        $items = Product::get();
        $checked = false;
        return count($items) == 0 ? null : view('web.jquery_live.product_options', compact('items', 'checked'));
    }
    public function productsMaxMinPrice()
    {
        $items = $this->productItemsData();
        $min_price =  collect($items)->min('price');
        $max_price =  collect($items)->max('price');
        return response()->json([
            'max_price' => ceil($max_price),
            'min_price' => ceil($min_price),
            'status' => 'success',
        ], 200);
    }
    public function getSubmenu($menu_id)
    {
        $items = Page::where([
            ['parent_menu_id', $menu_id],
        ])
            ->orderBy('pages.ordering')
            ->get(['id', 'slug', 'title', 'access_label_id']);
        foreach ($items as $key => $page) {
            // dd($page);
            $page->index_no = ++$key;
            $page->path = $page->slug;
            $page->items = $this->publicChildPages($page->children, $page->index_no, $page->slug);
            if (!is_null($page->access_label_id)) {
                $pageRoleIds = AccessLabel::findOrFail($page->access_label_id)->user_group_id;
                $pageRoleIds = explode(',', $pageRoleIds);
            }
            $page->access = false;
            if (($page->access_label_id == 1) || (auth()->check() && in_array(auth()->user()->user_group_id, $pageRoleIds))) {
                $page->access = true;
            }
            foreach ($page->items as $item) {
                if ($item->menu_active) {
                    $page->menu_active = true;
                }
            }
        }
        return count($items) > 0 ? view('web.jquery_live.sub_menu', compact('items', 'menu_id')) : null;
    }
    public function publicChildPages($items, $index_no, $path)
    {
        foreach ($items as $key => $item) {
            $item->index_no = $index_no . '.' . ++$key;
            $item->path = $path . '/' . $item->slug;
            $item->items = $this->publicChildPages($item->children, $item->index_no, $item->path);
            if (!is_null($item->access_label_id)) {
                $pageRoleIds = AccessLabel::findOrFail($item->access_label_id)->user_group_id;
                $pageRoleIds = explode(',', $pageRoleIds);
            }
            $item->access = false;
            if (($item->access_label_id == 1) || (auth()->check() && in_array(auth()->user()->user_group_id, $pageRoleIds))) {
                $item->access = true;
            }
            foreach ($item->items as $item_data) {
                if ($item_data->menu_active) {
                    $item->menu_active = true;
                }
            }
        }
        return $items;
    }
    public function getProductItems($product_id, $group, $condition)
    {
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        // dd($allowedLocations);
        if (count($storeIds) > 0) {
            $items_data = ProductItem::where('product_id', $product_id)
                ->where('status', 1)
                ->where(function ($query) use ($storeIds) {
                    $query->whereIn('store_id', $storeIds)->get();
                })
                ->get();
        } else {
            // dd('helo');
            $items_data = collect();
        }
        foreach ($items_data as $value) {
            $discount_amt = 0;
            if ($value->discount_price && $value->discount_type) {
                if ($value->discount_type == 1) {
                    $discount_amt = ($value->sale_price * $value->discount_price) / 100;
                    $discount_percent = $value->discount_price;
                } else {
                    $discount_amt = $value->discount_price;
                    $discount_percent = ($value->discount_price * 100) / $value->sale_price;
                }
                $value->discount_percent = ceil($discount_percent);
                // $value->price = number_format($value->sale_price - $discount_amt, 2);
                $value->price = $value->sale_price - $discount_amt;
            } else {
                $value->price = $value->sale_price;
            }
            $currency_rate = 0;
            $currency_rate_status = false;
            $currency_symbol = getDefaultCurrencySymbol();
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                // dd($location_data);
                $currency_rate = $location_data->currency_rate;
                $currency_symbol = $location_data->currency_symbol;
                if ($currency_rate > 0) {
                    $currency_rate_status = true;
                }
            }
            $value->local_price = number_format(($value->price * $currency_rate), 2);
            $value->local_currency_symbol = $currency_symbol;
            $value->currency_rate_status = $currency_rate_status;
            $value->price_off_amt = number_format(ceil($discount_amt), 2) ?? 0;
            $value->group = $this->getItemAttribute(unserialize($value->item_attributes));
            $value->condition = preg_replace('/\s*/', '', strtolower($this->makeCondition(unserialize($value->item_attributes))));
        }
        $collection = collect($items_data);
        // dd($group, $condition, $collection);
        $items = $collection->where('group', $group)->where('condition', $condition);
        // dd($items);
        $cart_id = array();
        $cart_sku_partial_paid_id = array();
        if (Session::has('cart')) {
            $cart_items = Session::get('cart')->items;
            foreach ($cart_items as $cart_value) {
                foreach ($cart_value->cart_items as $cart_item) {
                    $cart_id[] = $cart_item->id;
                    if ($cart_item->sku_partial_paid) {
                        $cart_sku_partial_paid_id[] = $cart_item->id;
                    }
                }
            }
        };
        $collection =  new Collection($items);
        $max_price =  $collection->max('max_price');
        foreach ($items as $value) {
            $value->sku_location = '';
            $value->location_message = '';
            if (!empty($value->location_id)) {
                if (!empty($location_data) && $location_data->country != 'US') {
                    $value->sku_location = $value->location_id;
                    $value->location_message = '<strong class="text-primary">' . $value->sku_location . '</strong>';
                }
                if (!empty($location_data) &&  $location_data->country == $value->location_id && $location_data->country != 'US') {
                    $value->location_message = '<strong class="text-primary">' . $value->sku_location . '</strong>';
                }
            }
            if (!empty($location_data->country)) {
                $value->ship_location = $location_data->country;
            }
            $value->estimate_delivery = '';
            if (!empty($location_data->country)) {
                $estimate_delivery_data = EstimatedDeliveryDate::where([
                    'sku_location_id' => $value->location_id,
                    'ship_location_id' => $location_data->country,
                ])->first();
                if (!empty($estimate_delivery_data->sku_location_id)) {
                    $minDurationDays = $estimate_delivery_data->min_duration;
                    $maxDurationDays = $estimate_delivery_data->max_duration;
                    // Get the current date
                    $currentDate = Carbon::now();
                    // Calculate the estimated delivery dates
                    $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                    $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);
                    // Format the dates
                    $minFormatted = $minDeliveryDate->format('M d');
                    $maxFormatted = $maxDeliveryDate->format('M d');
                    $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                }
            }
            $value->checked = false;
            if ($value->max_price == $max_price) {
                $active_sku = $value->product_item_sku;
                $sku_item_images = unserialize($value->product_item_images);
                $value->checked = true;
            }
            $value->in_cart = false;
            $value->sku_partial_paid = false;
            if (in_array($value->id, $cart_id)) {
                $value->in_cart = true;
            }
            if (in_array($value->id, $cart_sku_partial_paid_id)) {
                $value->sku_partial_paid = true;
            }
        }
        if (count($items) > 0) {
            return view('web.jquery_live.product_sku_items', compact('items'));
        }
    }
    public function getProductItemNetworks($id)
    {
        $item = ProductItem::find($id);
        $item_attributes = unserialize($item->item_attributes);
        $networks = [];
        foreach ($item_attributes as $value) {
            if ($value->attribute_type_id == 2) {
                $networks = $value->data;
                break;
            }
        }
        if (count($networks) > 0) {
            return view('web.jquery_live.product_sku_item_networks', compact('networks'));
        }
    }

    public function getProductImages(Request $request, $id)
    {
        $product = Product::find($id);
        $images = array((object)[
            'file_path' => asset('storage/products/' . $product->product_image),
            'type' => 'image'
        ]);


        //dd($product_image_data);
        $sku = '';
        $type = $request->type ?? false;
        return view('web.jquery_live.product_sku_item_images', compact('images', 'sku', 'type'));
    }



    public function getProductItemImages(Request $request, $id)
    {
        $item = ProductItem::find($id);
        $product = Product::find($item->product_id);
        $product_image_data = array((object)[
            'file_path' => asset('storage/products/' . $product->product_image),
            'type' => 'image'
        ]);
        $attributes = collect(unserialize($item->item_attributes))->sortBy('ordering');
        foreach ($attributes as $value) {
            if ($value->attribute_type_id == 12) {
                if (!empty($value->data->file_path)) {
                    $product_image_data = array((object)[
                        'file_path' => asset('storage/products/' . $value->data->file_path),
                        'type' => 'image'
                    ]);
                }
            }
        }
        if ($item->product_item_image) {
            $product_image_data = array((object)[
                'file_path' => asset('storage/product_item_images/' . $item->product_item_image),
                'type' => 'image'
            ]);
        }
        if (!empty($item->product_item_images)) {
            $images = unserialize($item->product_item_images);
            foreach ($images as $value) {
                $item_image_data[] = ((object)[
                    'file_path' => asset('storage/product_item_images/' . $value->file_path),
                    'type' => 'image'
                ]);
            }
            $images = $item->stock_status == 'yes' ? array_merge($product_image_data, $item_image_data) : $product_image_data;
        } else {
            $images = $product_image_data;
        }
        $item_video_data = [];
        if (!empty($item->product_item_videos)) {
            $videos = unserialize($item->product_item_videos);

            foreach ($videos as $value) {
                $item_video_data[] = ((object)[
                    'file_path' => asset('storage/product_item_videos/' . $value->file_path),
                    'type' => 'video'
                ]);
            }
            $images = $item->stock_status == 'yes' ? array_merge($product_image_data, $item_video_data) : $product_image_data;
        }
        $sku = $item->product_item_sku;
        $type = $request->type ?? false;
        return view('web.jquery_live.product_sku_item_images', compact('images', 'sku', 'type'));
    }
    public function getProductItem($id, $add_cart = false)
    {
        $item = ProductItem::where('id', $id)->first();
        $this->makeAttributeData(unserialize($item->item_attributes), $item);
        $product = Product::find($item->product_id);
        $item->product_title = $product->title;
        if ($item->title) {
            $item->title = $item->extra_title . ', ' . $item->title;
        } else {
            $item->title = $item->extra_title;
        }
        if ($item->sub_title) {
            $item->sub_title = $item->extra_sub_title . ', ' . $item->sub_title;
        } else {
            $item->sub_title = $item->extra_sub_title;
        }
        // dd($item);
        $discount_amt = 0;
        if ($item->discount_price && $item->discount_type) {
            if ($item->discount_type == 1) {
                $discount_amt = ($item->sale_price * $item->discount_price) / 100;
                $discount_percent = $item->discount_price;
            } else {
                $discount_amt = $item->discount_price;
                $discount_percent = ($item->discount_price * 100) / $item->sale_price;
            }
            $item->discount_percent = ceil($discount_percent);
            $item->price = $item->sale_price - $discount_amt;
        } else {
            $item->price = $item->sale_price;
        }
        $currency_rate = 0;
        $currency_rate_status = false;
        $currency_symbol = getDefaultCurrencySymbol();
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
            // dd($location_data);
            $currency_rate = $location_data->currency_rate;
            $currency_symbol = $location_data->currency_symbol;
            if ($currency_rate > 0) {
                $currency_rate_status = true;
            }
        }
        $item->local_price = $item->price * $currency_rate;
        $item->local_price_format = number_format(($item->local_price), 2);
        $item->local_currency_symbol = $currency_symbol;
        $item->currency_rate_status = $currency_rate_status;
        $item->price_off_amt = number_format(ceil($discount_amt), 2) ?? 0;
        $product_item_image = asset('storage/products/' . $product->product_image);
        $attributes = collect(unserialize($item->item_attributes))->sortBy('ordering');
        foreach ($attributes as $value) {
            if ($value->attribute_type_id == 12) {
                if (!empty($value->data->file_path)) {
                    $product_item_image =  asset('storage/products/' . $value->data->file_path);
                }
            }
        }
        if ($item->product_item_image) {
            $product_item_image = asset('storage/product_item_images/' . $item->product_item_image);
        }
        $item->product_item_image = $product_item_image;
        $item->open_box_with_length = $product->open_box_shipping_length;
        $item->open_box_with_width = $product->open_box_shipping_width;
        $item->open_box_with_height = $product->open_box_shipping_weight;
        $item->open_box_with_weight = $product->open_box_shipping_weight;
        $item->box_with_length = $product->box_with_shipping_length;
        $item->box_with_width = $product->box_with_shipping_width;
        $item->box_with_height = $product->box_with_shipping_weight;
        $item->box_with_weight = $product->box_with_shipping_weight;
        // dd($item);
        $cart_id = array();
        if (Session::has('cart')) {
            $items = Session::get('cart')->items;
            foreach ($items as $value) {
                // dd($value->cart_items);
                foreach ($value->cart_items as $cart_item) {
                    $cart_id[] = $cart_item->id;
                }
            }
        };
        $item->in_cart = false;
        if (in_array($item->id, $cart_id)) {
            $item->in_cart = true;
        }
        $item->sku_partial_paid = false;
        if ($add_cart) {
            return $item;
        }
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart_page = Page::where('page_key', 'cart')->first();
        if (!empty($checkout_page)) {
            $item->buy_now_url = route('page', $checkout_page->slug);
            $item->view_cart_url = route('page', $cart_page->slug);
            $item->cart_url = route('add_cart', $item->id);
        }
        return response()->json($item, 200);
    }
    public function collectProductSku($items)
    {
        $sku_s = array();
        foreach ($items as $value) {
            $sku_s[] = $value->product_item_sku;
        }
        return implode(',', $sku_s);
    }
    public function addCart(Request $request, $id)
    {
        //    dd('hello');
        $location_data = Session::get('shop_logged_data');
        $checkout_order = Session::get('checkout_order');
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart_page = Page::where('page_key', 'cart')->first();
        $products_page = Page::where('page_key', 'products')->first();
        $buy_now_url = route('page', $checkout_page->slug);
        $view_cart_url = route('page', $cart_page->slug);
        $redirect_page = route('page', $products_page->slug);
        $redirect_page_status = false;
        if (in_array($request->current_page, [$view_cart_url, $buy_now_url])) {
            $redirect_page_status = true;
        }
        $cart_page_status = false;
        if ($request->current_page == $view_cart_url) {
            $cart_page_status = true;
        }
        if ($request->added_cart != 'yes') {
            $item = $this->getProductItem($id, true);
            $cart_id = array();
            $status = true;
            if (Session::has('cart')) {
                if (Session::get('cart')->items) {
                    $cart = Session::get('cart');
                    $items = $cart->items;
                    $status = true;
                    $exist_cart_sku_data = array();
                    foreach ($items as $value) {
                        $exist_cart_sku_data[] = $this->collectProductSku($value->cart_items);
                    }
                    $exist_cart_sku = explode(',', implode(',', $exist_cart_sku_data));
                    foreach ($items as $key => $value) {
                        if (in_array($item->product_item_sku, $exist_cart_sku)) {
                            //remove the sku item on cart
                            $status = false;
                            if ($item->cart_group == $value->cart_group) {
                                $value->cart_items = $this->makeCartItem($value->cart_items, $this->getProductItem($id, true), $request);
                                $value->quantity = count($value->cart_items);
                                $value->length = $value->quantity * $value->open_box_with_length;
                                $value->width = $value->quantity * $value->open_box_with_width;
                                $value->height = $value->quantity * $value->open_box_with_height;
                                $value->weight = $value->quantity * $value->open_box_with_weight;
                                $value->box_length = $value->quantity * $value->box_with_shipping_length;
                                $value->box_width = $value->quantity * $value->box_with_shipping_width;
                                $value->box_height = $value->quantity * $value->box_with_shipping_weight;
                                $value->box_weight = $value->quantity * $value->box_with_shipping_weight;
                                if ($value->quantity == 0) {
                                    unset($items[$key]);
                                    break;
                                }
                            }
                        } else {
                            //add the sku item on cart
                            if ($item->cart_group == $value->cart_group) {
                                $status = false;
                                $value->cart_items = $this->makeCartItem($value->cart_items, $this->getProductItem($id, true), $request);
                                $value->quantity = count($value->cart_items);
                                $value->length = $value->quantity * $value->open_box_with_length;
                                $value->width = $value->quantity * $value->open_box_with_width;
                                $value->height = $value->quantity * $value->open_box_with_height;
                                $value->weight = $value->quantity * $value->open_box_with_weight;
                                $value->box_length = $value->quantity * $value->box_with_shipping_length;
                                $value->box_width = $value->quantity * $value->box_with_shipping_width;
                                $value->box_height = $value->quantity * $value->box_with_shipping_weight;
                                $value->box_weight = $value->quantity * $value->box_with_shipping_weight;
                                break;
                            } else {
                                $status = true;
                                $item->cart_items = array($this->getProductItem($id, true));
                                $item->quantity = count($item->cart_items);
                                $item->length = $item->quantity * $item->open_box_with_length;
                                $item->width = $item->quantity * $item->open_box_with_width;
                                $item->height = $item->quantity * $item->open_box_with_height;
                                $item->weight = $item->quantity * $item->open_box_with_weight;
                                $item->box_length = $item->quantity * $item->box_with_shipping_length;
                                $item->box_width = $item->quantity * $item->box_with_shipping_width;
                                $item->box_height = $item->quantity * $item->box_with_shipping_weight;
                                $item->box_weight = $item->quantity * $item->box_with_shipping_weight;
                            }
                        }
                    }
                    if ($status) {
                        array_push($items, $item);
                    }
                } else {
                    $item->cart_items = array($this->getProductItem($id, true));
                    $item->quantity = count($item->cart_items);
                    $items = array($item);
                }
            } else {
                $item->cart_items = array($this->getProductItem($id, true));
                $item->quantity = count($item->cart_items);
                $items = array($item);
            }
            $site_data = Session::get('shop_logged_data');
            $site_info = Setting::first();
            $selectedCountry = $site_data->country ?? $site_info->country;
            $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
            $total_price = 0;
            $local_total_price = 0;
            $quantity = 0;
            $storeIds = $this->getEligibleStores($selectedCountry);
            foreach ($items as $value) {
                // dd($value->cart_items);
                // dd($value);
                if (count($storeIds) < 1) {
                    continue;
                }
                $quantity += $value->quantity;
                // $value->unit_total_price = $value->quantity * $value->price;
                $value->unit_total_price = $this->itemTotalPrice($value->cart_items);
                $total_price += $value->unit_total_price;
                if (Session::has('shop_logged_data')) {
                    $value->local_unit_total_price = ($value->quantity * $value->price) * $location_data->currency_rate;
                    $local_total_price += ($value->unit_total_price * $location_data->currency_rate);
                }
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                $value->condition_type = strtolower(str_replace([' ', ','], '', $value->condition));
                $value->sku_details = $this->makeDetails($value->cart_items);
                $value->sku_details_url = $this->makeDetailsUrl($value->cart_items);
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('product_items.store_id', $storeIds)->get(), $value->cart_group);
                } else {
                    //$value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                }
                $value->sku_partial_paid = $value->sku_partial_paid ?? false;
            }
            $shipping_amount = !empty($checkout_order) && $checkout_order->shipping_status ? $checkout_order->shipping->shipping_amount : 0;
            $shipping_amount_local = !empty($checkout_order) && $checkout_order->shipping_status ? ($checkout_order->shipping->shipping_amount * $location_data->currency_rate) : 0;
            $cash_value = 0;
            $cash_value_local = 0;
            if (Session::has('cart')) {
                if (Session::get('cart')->items && $cart->use_reward_point_status) {
                    $rewards = Reward::where([
                        'user_id' => auth()->user()->id,
                        'status' => true,
                    ])->get();
                    $total_reward_points = 0;
                    foreach ($rewards as $value) {
                        $total_reward_points += $value->points;
                    }
                    $total_reward_points = $total_reward_points;
                    $cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
                    $cash_value_local = round($cash_value * $location_data->currency_rate, 2);
                }
            }
            $cash_total_reward = 0;
            $cash_total_reward_local = 0;
            if (Session::has('cart')) {
                if (Session::get('cart')->items && $cart->use_cash_value_status) {
                    $cash_rewards = CashReward::where([
                        'user_id' => auth()->user()->id,
                        'status' => true,
                    ])->get();
                    $cash_total_reward = 0;
                    foreach ($cash_rewards as $value) {
                        $cash_total_reward += $value->amount;
                    }
                    $cash_total_reward = round($cash_total_reward, 2);
                    $cash_total_reward_local = round($cash_total_reward * $location_data->currency_rate, 2);
                }
            }
            $total = round($total_price, 2);
            $total_local = round($local_total_price, 2);
            $local_currency_symbol = $location_data ? $location_data->currency_symbol : '';
            $grand_total = ($total + $shipping_amount) - ($cash_value + $cash_total_reward);
            $grand_total_local = ($total_local + $shipping_amount_local) - ($cash_value_local + $cash_total_reward_local);
            $cart = (object)[
                'quantity' => $quantity,
                'total' => $total,
                'total_local' => $total_local,
                'grand_total' => $grand_total,
                'paid_grand_total' => $cart->paid_grand_total ?? 0,
                'grand_total_local' => $grand_total_local,
                'paid_grand_total_local' => $cart->paid_grand_total_local ??  0,
                'local_currency_symbol' => $local_currency_symbol,
                'items' => $quantity > 0 ? $items : array(),
                'redirect_page' => $redirect_page,
                'redirect_page_status' => $redirect_page_status,
                'cart_page' => $cart_page_status,
                'amount_50_50' =>  getDefaultCurrencySymbol() . ($grand_total * 0.5) . ' - $' . ($grand_total - round($grand_total * 0.5, 2)),
                'amount_50_50_local' => $local_currency_symbol . (number_format($grand_total_local * 0.5, 2)) . ' - ' . $local_currency_symbol . number_format($grand_total_local - round($grand_total_local * 0.5, 2), 2),
                'amount_60_40' => getDefaultCurrencySymbol() . ($grand_total * 0.6) . ' - $' . ($grand_total - round($grand_total * 0.6, 2)),
                'amount_60_40_local' => $local_currency_symbol . (number_format(($grand_total_local * 0.6), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - $grand_total_local * 0.6), 2)),
                'amount_80_20' => getDefaultCurrencySymbol() . ($grand_total * 0.8) . ' - $' . ($grand_total - $grand_total * 0.8),
                'amount_80_20_local' => $local_currency_symbol . (number_format(($grand_total_local * 0.8), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - round($grand_total_local * 0.8, 2)), 2)),
                'amount_due' => $cart->amount_due ?? false,
                'order_create' => empty($cart->order) ? false : true,
                'use_reward_point_status' => empty($cart->use_reward_point_status) ? false : true,
                'use_cash_value_status' => empty($cart->use_cash_value_status) ? false : true,
            ];
            Session::put([
                'cart' => $cart,
            ]);
            $userId = auth()->id();
            $sessionId = $request->session()->getId();
            Cart::updateOrCreate(
                [
                    'user_id' => $userId ?? null,
                    'session_id' => $userId ? null : $sessionId,
                ],
                [
                    'cart_items' => json_encode($cart),
                    'last_activity' => now(),
                ]
            );
            // dd(Session::get('cart'));
        } else {
            $cart = Session::get('cart');
        }
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            foreach ($cart->items as $value) {
                foreach ($value->cart_items as $cart_item) {
                    $cart_id[] = $cart_item->id;
                }
            }
            foreach ($cart->items as $value) {
                $value->in_cart = false;
                if (in_array($value->id, $cart_id)) {
                    $value->in_cart = true;
                }
            }
        }
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        if ($request->add_to_cart) {
            // dd("hi");
            if (count($storeIds) > 0) {
                if (Session::has('cart')) {
                    $cart = Session::get('cart');
                    // dd($cart);
                    foreach ($cart->items as $value) {
                        $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                        if (count($storeIds) > 0) {
                            $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                        }
                    }
                }
            }
            Session::PUT(['cart_last_updated' => now()]);
            return view('web.jquery_live.cart_items', compact('cart', 'buy_now_url', 'view_cart_url', 'redirect_page', 'cart_page_status', 'checkout_order'));
        }
        if (!empty($request->checkout)) {
            return view('web.jquery_live.checkout_items', compact('cart', 'buy_now_url', 'view_cart_url', 'redirect_page', 'cart_page_status', 'checkout_order'));
        }
        Session::PUT(['cart_last_updated' => now()]);
        return response()->json($cart, 200);
    }
    public function checkAbandonedCarts()
    {
        $cartLastUpdated = Session::get('cart_last_updated');
        // dd($cartLastUpdated);
    }
    //     public function sendAbandonedCartEmail()
    // {
    //    $user=auth()->user();
    //     if(!$user){
    //         $checkout_order= Session::get('checkout_order');
    //         $user=$checkout_order->customer;
    //     }else if($user){
    //         $user=auth()->user();
    //     }else{
    //         \Log::info('user',"not exist any user ");
    //     }
    //     $cart = Session::get('cart');
    //     // $emailData = [
    //     //     'user' => $user,
    //     //     'cart' => $cart,
    //     //     'discount_code' => 'SAVE10', // Optional discount or incentive
    //     // ];
    //     // dd($cart);
    //     $user_email_templates_group = EmailTemplateGroup::where('title', 'AbandonedCarts')->first();
    //     $user_email_templates = EmailTemplate::where('email_template_group_id', $user_email_templates_group->id)->get();
    //     if($cart){
    //         foreach ($user_email_templates as $user_email_template) {
    //             $user_email_template->email_body = unserialize($user_email_template->email_body);
    //             $user_email_template->sms_body = unserialize($user_email_template->sms_body);
    //             $mail_data = (object)[
    //                 'customer_email' => $user->email,
    //                 'Customer_Name' => $user->first_name,
    //                 'Quantity' => $cart->quantity,
    //                 'Price'=>$cart->total,
    //                 'subject' => $user_email_template->subject,
    //                 'pre_header' => $user_email_template->pre_header,
    //                 'email_body' => $user_email_template->email_body,
    //                 'sms_status' => $user_email_template->sms_status ? true : false,
    //                 'sms_body' => $user_email_template->sms_body,
    //                 'product_url' => "https://buy.codeitjs.com/",
    //                 'your_company_name' => '1GuyGadjets',
    //             ];
    //             $sms_message = $this->sendEmailData($mail_data)->sms_body;
    //             $user->tel_cell = str_replace('+', '', $user->tel_cell );
    //             if($user->tel_cell){
    //                 $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell , $sms_message);
    //             }
    //         }
    //         $mail_data->subject = $this->sendEmailData($mail_data)->subject;
    //         $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
    //         $mail_data->content = $this->sendEmailData($mail_data)->content;
    //         $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
    //         Mail::to($user->email)->send(new  SiteEmail($mail_data));
    //     }
    //     // dd($mail_data);
    // }
    public function itemTotalPrice($items)
    {
        $total = 0;
        foreach ($items as $value) {
            $total += $value->price;
        }
        return $total;
    }
    public function makeCartItem($items, $item, $request)
    {
        $item->sku_partial_paid = $item->sku_partial_paid ?? false;
        $exist_cart_sku = array();
        foreach ($items as $value) {
            $exist_cart_sku[] = $value->product_item_sku;
        }
        if (in_array($item->product_item_sku, $exist_cart_sku)) {
            // dd('dddd');
            foreach ($items as $key => $value) {
                if ($value->id  == $item->id) {
                    // dd($value);
                    unset($items[$key]);
                    break;
                }
            }
        } else {
            $items = array_merge($items, array($item));
        }
        // dd($items);
        return $items;
    }
    public function makeDetails($items)
    {
        $location_data = Session::get('shop_logged_data');
        $sku_details_url = array();
        foreach ($items as $value) {
            $value->locations = $this->countries($value->location_id);
            $value->sku_location = '';
            if (!empty($value->location_id)) {
                //$value->sku_location = $this->countries($value->location_id);
                $value->sku_location = $value->location_id;
            }
            $value->estimate_delivery = '';
            if (!empty($location_data->country)) {
                $estimate_delivery_data = EstimatedDeliveryDate::where([
                    'sku_location_id' => $value->location_id,
                    'ship_location_id' => $location_data->country,
                ])->first();
                if (!empty($estimate_delivery_data->sku_location_id)) {
                    $minDurationDays = $estimate_delivery_data->min_duration;
                    $maxDurationDays = $estimate_delivery_data->max_duration;
                    // Get the current date
                    $currentDate = Carbon::now();
                    // Calculate the estimated delivery dates
                    $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                    $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);
                    // Format the dates
                    $minFormatted = $minDeliveryDate->format('M d');
                    $maxFormatted = $maxDeliveryDate->format('M d');
                    $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                }
            }
            $sku = 'SKU: ' . $value->product_item_sku;
            $storeLocation  = \App\Models\Setting::find($value->store_id)?->country;
            if (!empty($storeLocation)) {
                $sku =
                    'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $storeLocation . '</strong>';
            }
            if (!empty($value->sku_location && $value->estimate_delivery)) {
                $sku =
                    'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $storeLocation . '</strong> <img src="' . asset("images/icon/delivery_black.png") . '" alt=""> ' . $value->estimate_delivery;
            }
            $sku_details_url[] = $sku;
        }
        return implode('<br />', $sku_details_url);
    }
    public function makeDetailsUrl($items)
    {
        $location_data = Session::get('shop_logged_data');
        $sku_details_url = array();
        if (count($items) > 1) {
            foreach (collect($items) as $value) {
                $value->locations = $this->countries($value->location_id);
                $value->sku_location = '';
                if (!empty($value->location_id)) {
                    //$value->sku_location = $this->countries($value->location_id);
                    $value->sku_location = $value->location_id;
                }
                $value->estimate_delivery = '';
                if (!empty($location_data->country)) {
                    $estimate_delivery_data = EstimatedDeliveryDate::where([
                        'sku_location_id' => $value->location_id,
                        'ship_location_id' => $location_data->country,
                    ])->first();
                    if (!empty($estimate_delivery_data->sku_location_id)) {
                        $minDurationDays = $estimate_delivery_data->min_duration;
                        $maxDurationDays = $estimate_delivery_data->max_duration;
                        // Get the current date
                        $currentDate = Carbon::now();
                        // Calculate the estimated delivery dates
                        $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                        $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);
                        // Format the dates
                        $minFormatted = $minDeliveryDate->format('M d');
                        $maxFormatted = $maxDeliveryDate->format('M d');
                        $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                    }
                }
                $sku = 'SKU: ' . $value->product_item_sku;
                if (!empty($value->sku_location)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong>';
                }
                if (!empty($value->sku_location && $value->estimate_delivery)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong> <img src="' . asset("images/icon/delivery_black.png") . '" alt=""> ' . $value->estimate_delivery;
                }
                if ($value->sku_partial_paid) {
                    $route = route('live.products.condition.item', $value->id);
                    $image_route = route('live.products.item.images', $value->id);
                    $sku_details_url[] = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $sku . '</a>';
                } else {
                    $route = route('live.products.condition.item', $value->id);
                    $image_route = route('live.products.item.images', $value->id);
                    $sku_remove_route = route('add_cart', [$value->id]);
                    // $sku_remove_route = route('add_cart', [$value->id, 'checkout' => true]);
                    $sku_details_url[] = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $sku . '</a><a class="remove-cart-sku-item remove_cart_sku_item" data-url="' . $sku_remove_route . '" href="javascript:void(0)""><i class="fa-solid fa-xmark"></i></a>';
                }
            }
        } else {
            foreach ($items as $value) {
                $value->locations = $this->countries($value->location_id);
                $value->sku_location = '';
                if (!empty($value->location_id)) {
                    //$value->sku_location = $this->countries($value->location_id);
                    $value->sku_location = $value->location_id;
                }
                $value->estimate_delivery = '';
                if (!empty($location_data->country)) {
                    $estimate_delivery_data = EstimatedDeliveryDate::where([
                        'sku_location_id' => $value->location_id,
                        'ship_location_id' => $location_data->country,
                    ])->first();
                    if (!empty($estimate_delivery_data->sku_location_id)) {
                        $minDurationDays = $estimate_delivery_data->min_duration;
                        $maxDurationDays = $estimate_delivery_data->max_duration;
                        // Get the current date
                        $currentDate = Carbon::now();
                        // Calculate the estimated delivery dates
                        $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                        $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);
                        // Format the dates
                        $minFormatted = $minDeliveryDate->format('M d');
                        $maxFormatted = $maxDeliveryDate->format('M d');
                        $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                    }
                }
                $sku = 'SKU: ' . $value->product_item_sku;
                if (!empty($value->sku_location)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong>';
                }
                if (!empty($value->sku_location && $value->estimate_delivery)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong> <img src="' . asset("images/icon/delivery_black.png") . '" alt=""> ' . $value->estimate_delivery;
                }
                $route = route('live.products.condition.item', $value->id);
                $image_route = route('live.products.item.images', $value->id);
                $sku_details_url[] = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $sku . '</a>';
            }
        }
        return implode('<br />', $sku_details_url);
    }
    public function stockCalculation($items, $cart_group)
    {
        // dd($cart_group);
        $item_data_array = array();
        foreach ($items as $value) {
            $item_data_array[] = $this->getProductItem($value->id, true);
        }
        $total_item = count(collect($item_data_array)->where('cart_group', $cart_group));
        // dd($total_item, $cart_group);
        $total_cart_item = count(collect($item_data_array)->where('cart_group', $cart_group)->where('in_cart', true));
        // dd($total_item - $total_cart_item);
        // dd($total_item,$total_cart_item);
        return ($total_item - $total_cart_item);
    }
    public function getItemImages($items)
    {
        foreach ($items as $value) {
            $images[] = asset('storage/products/' . $value->file_path);
        }
        return $images;
    }
    public function stripeCard($cart)
    {
        // Extensive null and type checking
        if (!$cart || !Session::has('cart')) {
            Log::error('Cart is null or not in session');
            return null;
        }
        $checkoutOrder = Session::get('checkout_order');
        if (!$checkoutOrder) {
            Log::error('Checkout order is null');
            return null;
        }
        // Null-safe shipping method retrieval
        $shippingMethod = optional($checkoutOrder->shipping)->shipping_method ?? 'shipping_standard';
        $shippingAmount = $checkoutOrder->shipping->shipping_amount ?? 0;
        // Determine shipping text with null-safe approach
        $shippingText = $checkoutOrder->shipping->shipping_method_data['carrier'] ?? 'shipping_standard';
        // Initialize Stripe Client with error checking
        $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card');
        if (!$transactionMethod) {
            Log::error('Transaction method is null');
            return null;
        }
        $stripe = new \Stripe\StripeClient($transactionMethod->private_key ?? '');
        // Prepare line items with null-safe checks
        $description = [];
        $priceData = [];
        // Null-safe cart items iteration
        $cartItems = $cart->items ?? [];
        foreach ($cartItems as $item) {
            // Null-safe item property access
            $itemDescription = ($item->product_item_sku ?? 'N/A') . '-' .
                ($item->product_title ?? '') . ' ' .
                ($item->title ?? '') . ' ' .
                ($item->sub_title ?? '');
            $description[] = trim($itemDescription);
            // Prepare shipping options
            $shipping = [
                [
                    'shipping_rate_data' => [
                        'type' => 'fixed_amount',
                        'fixed_amount' => [
                            'amount' => max(0, intval($shippingAmount * 100)),
                            'currency' => 'usd',
                        ],
                        'display_name' => $shippingText,
                    ],
                ],
            ];
            // Null-safe image handling
            $images = [];
            if ($item->product_item_images) {
                try {
                    $unserializedImages = unserialize($item->product_item_images);
                    $images = array_filter([
                        $item->product_item_image,
                        ...(is_array($unserializedImages) ? $this->getItemImages($unserializedImages) : [])
                    ]);
                } catch (\Exception $e) {
                    Log::error('Image unserialization error: ' . $e->getMessage());
                    $images = [$item->product_item_image];
                }
            }
            $unitPrice = isset($item->discounted_price) && $item->discounted_price > 0
                ? $item->discounted_price
                : ($item->price ?? 0);
            $priceData[] = [
                'price_data' => [
                    'product_data' => [
                        'name' => trim($itemDescription),
                        'images' => $images ?: [],
                    ],
                    'unit_amount' => max(0, intval(($unitPrice ?? 0) * 100)),
                    'currency' => 'USD',
                ],
                'quantity' => max(1, intval($item->quantity ?? 1)),
            ];
        }
        // Null-safe coupon creation
        $coupon = null;
        if (($cart->paid_grand_total ?? 0) == 0 &&
            ($transactionMethod->discount_price ?? 0) > 0
        ) {
            try {
                $coupon = $transactionMethod->discount_type == 1
                    ? $stripe->coupons->create([
                        'percent_off' => $transactionMethod->discount_price,
                        'duration' => 'once',
                        'name' => 'Transaction Discount',
                    ])
                    : $stripe->coupons->create([
                        'amount_off' => intval($transactionMethod->discount_price) * 100,
                        'duration' => 'once',
                        'name' => 'Transaction Discount',
                        'currency' => 'USD',
                    ]);
            } catch (\Exception $e) {
                Log::error('Coupon creation error: ' . $e->getMessage());
            }
        }
        // Determine customer email with null-safe checks
        $payEmail = optional($checkoutOrder->payment)->email ?? '<EMAIL>';
        if (optional($checkoutOrder->payment)->payment_same_as_contact == 0) {
            $userBilling = UserBilling::find(optional($checkoutOrder->payment)->user_billing_id);
            $payEmail = $userBilling->email ?? $payEmail;
        }
        // Prepare Stripe Checkout Session parameters
        $checkoutSessionParams = [
            'ui_mode' => 'hosted',
            'mode' => 'payment',
            'customer_email' => $payEmail,
            'payment_method_types' => ['card', 'alipay'],
            'line_items' => $priceData,
            'success_url' => URL::route('stripe'),
            'cancel_url' => URL::route('stripe'),
            'shipping_options' => $shipping,
            'payment_intent_data' => [
                'description' => implode('|', array_filter($description))
            ]
        ];
        // Add coupon if exists
        if ($coupon) {
            $checkoutSessionParams['discounts'] = [
                ['coupon' => $coupon->id]
            ];
        }
        //    dd($checkoutSessionParams);
        $checkoutSession = $stripe->checkout->sessions->create($checkoutSessionParams);
        // Store checkout session in session
        Session::put('stripe_checkout_session', $checkoutSession);
        // Create order if not exists
        if (empty($cart->order)) {
            $this->makeOrder();
        }
        if (!empty($cart->promo_code)) { // Check if promo_code is not empty
            CustomerPromo::create([
                'promo_id' => $cart->promo_code,
                'customer_id' => $cart->coupan_user_id
            ]);
        }
        return $checkoutSession->url;
    }
    public function getPaymentStripeStatus()
    {
        $thank_page = Page::where('page_key', 'thank_you')->first();
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $checkout_order = Session::get('checkout_order');
        $cart = Session::get('cart');
        $checkout_session = Session::get('stripe_checkout_session');
        $stripe = new \Stripe\StripeClient(app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card')->private_key);
        // $stripe = new \Stripe\StripeClient(config('app.stripe_secret'));
        $checkout_session = $stripe->checkout->sessions->retrieve($checkout_session->id, []);
        if ($cart->paid_grand_total == 0) {
            if (app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card')->discount_price) {
                $discount_amt = 0;
                if (app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card')->discount_type == 1) {
                    $discount_amt = ($cart->total * app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card')->discount_price) / 100;
                } else {
                    $discount_amt = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card')->discount_price;
                }
            }
        }
        if ($checkout_session->payment_status == 'paid') {
            $this->makeCollection($cart->grand_total - $discount_amt, app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card')->id, $checkout_session->id, 2, $discount_amt);
            return Redirect::route('page', $thank_page->slug);
        }
        Session::flash('error', 'Payment failed !!');
        return Redirect::route('page', $checkout_page->slug);
    }
    public function viewCheckout($slug, $page, $selector)
    {

        $shipping_method_options = $this->getEasyPostShipping();
        $checkout_order = Session::get('checkout_order');
        //dd($checkout_order);
        $location_data = Session::get('shop_logged_data');
        // dd($location_data);
        $cart = Session::get('cart');
        $site_info = Setting::first();
        if (Auth::user() && !Session::has('checkout_order')) {
            $customer = (object)[
                'first_name' => Auth::user()->first_name,
                'last_name' => Auth::user()->last_name,
                'email' => Auth::user()->email,
                'tel_cell' => Auth::user()->tel_cell,
                // 'tel_cell_country_code' => Auth::user()->tel_cell_country_code,
                'company_name' => Auth::user()->company_name,
                'city' => Auth::user()->city,
                'state' => Auth::user()->state,
                'country' => Auth::user()->country,
                'country_name' => Auth::user()->country_name,
                'postal_code' => Auth::user()->postal_code,
                'address' => Auth::user()->address,
                'address_2' => Auth::user()->address_2,
                'email_notifications' => Auth::user()->email_notifications,
                'sms_notifications' => Auth::user()->sms_notifications,
            ];
            $checkout_order = (object)[
                'customer' => $customer,
                'customer_tab_status' => true,
                'customer_status' => true,
                'shipping_tab_status' => true,
                'shipping_status' => false,
                'payment_tab_status' => false,
                'payment_status' => false,
                'shipping_methods' => $shipping_method_options,
                'location_data' => $location_data
            ];
            Session::put([
                'checkout_order' => $checkout_order,
            ]);
        }
        // if (Session::has('checkout_order') && !empty($checkout_order->shipping_methods)) {
        //     $shipping_method_options = $checkout_order->shipping_methods;
        // }
        $page->shipping_method_options = $shipping_method_options;
        $selectedCountry = $location_data->country ?? $site_info->country;
        if ($location_data) {
            $allowedLocations = ($selectedCountry === $location_data->country) ? null : $selectedCountry;
        } else {
            $allowedLocations = [];
        }
        $storeIds = $this->getEligibleStores($selectedCountry);
        if (Session::has('cart')) {
            foreach ($cart->items as $value) {
                if (count($storeIds) < 1) {
                    continue;
                }
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                }
            }
            $page->cart = $cart;
            $page->countries = $this->countries();
            $products_page = Page::where('page_key', 'products')->first();
            $page->redirect_page = route('page', $products_page->slug);
            $user_shippings = array();
            $user_billings = array();
            if (auth()->user()) {
                $user_shippings = UserShipping::where('user_id', auth()->user()->id)->get();
                $user_billings = UserBilling::where('user_id', auth()->user()->id)->get();
            }
        }
        if (Session::has('checkout_order') && Session::has('cart')) {
            $page->checkout_order = Session::get('checkout_order');
        }

        $countries = $this->countries();
        $total_reward_points = 0;
        if (auth()->user()) {
            $rewards = Reward::where([
                'user_id' => auth()->user()->id,
                'status' => true,
            ])->get();
            foreach ($rewards as $value) {
                $total_reward_points += $value->points;
            }
            $page->total_reward_points = $total_reward_points;
            $page->cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
            $cash_rewards = CashReward::where([
                'user_id' => auth()->user()->id,
                'status' => true,
            ])->get();
            $cash_total_reward = 0;
            foreach ($cash_rewards as $value) {
                $cash_total_reward += $value->amount;
            }
            $page->cash_total_reward = number_format($cash_total_reward, 2);
        }
        $products_page = Page::where('page_key', 'products')->first();
        $rates = $checkout_order->shipping_methods['shipping_rates'] ?? [];
        $shipping_methods = $checkout_order->shipping_methods ?? [];
        if (array_keys($rates) !== range(0, count($rates) - 1)) {
            $rates = [$rates]; // Normalize to array of arrays if associative
        }
        $international_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === '1GG' || strtoupper($method['service']) === 'P');
        })->sortBy(function ($method) {
            return strtoupper($method['service']) === '1GG' ? 0 : 1;
        });

        $domestic_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === 'N' || strtoupper($method['service']) == 'STANDARD');
        });

        return $page->checkAccessLabel($slug) && Session::has('cart') ? view('web.cart.checkout', compact('shipping_methods', 'international_methods', 'domestic_methods', 'checkout_order', 'page', 'user_shippings', 'user_billings', 'countries')) : redirect::route('page', $products_page->slug);
    }
    public function viewCheckout2($slug, $page, $selector)
    {
        // dd(Auth::user());
        $shipping_method_options = $this->getEasyPostShipping();
        //dd($shipping_method_options);
        $checkout_order = Session::get('checkout_order');
        $location_data = Session::get('shop_logged_data');

        //dd(Session::get('checkout_order'));
        if (Auth::user() && !Session::has('checkout_order')) {
            $shipping_method_options = $this->getEasyPostShipping();

            $customer = (object)[
                'first_name' => Auth::user()->first_name,
                'last_name' => Auth::user()->last_name,
                'email' => Auth::user()->email,
                'tel_cell' => Auth::user()->tel_cell,
                // 'tel_cell_country_code' => Auth::user()->tel_cell_country_code,
                'company_name' => Auth::user()->company_name,
                'city' => Auth::user()->city,
                'state' => Auth::user()->state,
                'country' => Auth::user()->country,
                'country_name' => Auth::user()->country_name,
                'postal_code' => Auth::user()->postal_code,
                'address' => Auth::user()->address,
                'address_2' => Auth::user()->address_2,
                'email_notifications' => Auth::user()->email_notifications,
                'sms_notifications' => Auth::user()->sms_notifications,
            ];
            $shipping_mthod = $this->getEasyPostShipping();

            $checkout_order = (object)[
                'customer' => $customer,
                'customer_tab_status' => true,
                'customer_status' => true,
                'shipping_tab_status' => true,
                'shipping_status' => false,
                'payment_tab_status' => false,
                'payment_status' => false,
                'shipping_methods' => $this->getEasyPostShipping(),
            ];
            Session::put([
                'checkout_order' => $checkout_order,
            ]);
        }
        //dd($checkout_order->shipping_methods);
        if (Session::has('checkout_order') && !empty($checkout_order->shipping_methods)) {
            $shipping_method_options = $checkout_order->shipping_methods;
        }
        $page->shipping_method_options = $this->getEasyPostShipping();
        $page->shipping_method_options = $shipping_method_options ?? '';
        $cart = Session::get('cart');
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        if (Session::has('cart')) {
            foreach ($cart->items as $value) {
                if (count($storeIds) < 1) {
                    continue;
                }
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                }
            }
            $page->cart = $cart;
            $page->countries = $this->countries();
            $products_page = Page::where('page_key', 'products')->first();
            $page->redirect_page = route('page', $products_page->slug);
            $user_shippings = array();
            $user_billings = array();
            if (auth()->user()) {
                $user_shippings = UserShipping::where('user_id', auth()->user()->id)->get();
                $user_billings = UserBilling::where('user_id', auth()->user()->id)->get();
            }
        }
        if (Session::has('checkout_order') && Session::has('cart')) {
            $page->checkout_order = Session::get('checkout_order');
        }
        // dd($page->checkout_order);
        $countries = $this->countries();
        $total_reward_points = 0;
        if (auth()->user()) {
            $rewards = Reward::where([
                'user_id' => auth()->user()->id,
                'status' => true,
            ])->get();
            foreach ($rewards as $value) {
                $total_reward_points += $value->points;
            }
            $page->total_reward_points = $total_reward_points;
            $page->cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
            $cash_rewards = CashReward::where([
                'user_id' => auth()->user()->id,
                'status' => true,
            ])->get();
            $cash_total_reward = 0;
            foreach ($cash_rewards as $value) {
                $cash_total_reward += $value->amount;
            }
            $page->cash_total_reward = number_format($cash_total_reward, 2);
        }
        $location_data = Session::get('shop_logged_data');
        $page->shipping_method_options = $this->getEasyPostShipping();
        $products_page = Page::where('page_key', 'products')->first();
        $rates = $checkout_order->shipping_methods['shipping_rates'] ?? [];

        $shipping_methods = $checkout_order->shipping_methods ?? [];
        if (array_keys($rates) !== range(0, count($rates) - 1)) {
            $rates = [$rates]; // Normalize to array of arrays if associative
        }

        $international_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === '1GG' || strtoupper($method['service']) === 'P');
        })->sortBy(function ($method) {
            return strtoupper($method['service']) === '1GG' ? 0 : 1;
        });

        $domestic_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === 'N' || strtoupper($method['service']) == 'STANDARD');
        });

        // Pass all to view:
        //return view('web.jquery_live.checkout_order', compact('shipping_methods', 'international_methods', 'domestic_methods', 'checkout_order', 'countries', 'user_shippings'));
        return $page->checkAccessLabel($slug) && Session::has('cart') ? view('web.cart.checkout', compact('shipping_methods', 'international_methods', 'domestic_methods', 'checkout_order', 'page', 'user_shippings', 'user_billings', 'countries')) : redirect::route('page', $products_page->slug);
    }
    public function viewCart($slug, $page, $selector)
    {
        // dd('ddddd');
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);

        $cart = Session::get('cart');
        if (Session::has('cart')) {
            foreach ($cart->items as $value) {
                if (count($storeIds) < 1) {
                    continue;
                }
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->where('stock_status', 'yes')->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                }
            }
            $page->cart = $cart;
            $products_page = Page::where('page_key', 'products')->first();
            $page->redirect_page = route('page', $products_page->slug);
        }
        // dd($slug, $page, $selector);
        $products_page = Page::where('page_key', 'products')->first();

        return $page->checkAccessLabel($slug) && Session::has('cart') ? view('web.cart.cart', compact('page')) : redirect::route('page', $products_page->slug);
    }
    public function removeProductItem(Request $request, $id)
    {
        $checkout_order = Session::get('checkout_order');
        $location_data = Session::get('shop_logged_data');
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            foreach ($cart->items as $key => $value) {
                if ($value->id == $id) {
                    unset($cart->items[$key]);
                }
            }
            $site_data = Session::get('shop_logged_data');
            $site_info = Setting::first();
            $selectedCountry = $site_data->country ?? $site_info->country;
            $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
            $storeIds = $this->getEligibleStores($selectedCountry);

            $total_price = 0;
            $local_total_price = 0;
            $quantity = 0;
            foreach ($cart->items as $value) {
                if (count($storeIds) < 1) {
                    continue;
                }
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                }
                $quantity += $value->quantity;
                // $value->unit_total_price = $value->quantity * $value->price;
                $value->unit_total_price = $this->itemTotalPrice($value->cart_items);
                $total_price += $value->unit_total_price;
                if (Session::has('shop_logged_data')) {
                    $value->local_unit_total_price = ($value->quantity * $value->price) * $location_data->currency_rate;
                    $local_total_price += ($value->unit_total_price * $location_data->currency_rate);
                }
                $value->sku_details_url = $this->makeDetailsUrl($value->cart_items);
                $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
            }
            $products_page = Page::where('page_key', 'products')->first();
            $checkout_page = Page::where('page_key', 'checkout')->first();
            $cart_page = Page::where('page_key', 'cart')->first();
            $redirect_page = route('page', $products_page->slug);
            $buy_now_url = route('page', $checkout_page->slug);
            $view_cart_url = route('page', $cart_page->slug);
            $redirect_page_status = false;
            if (in_array($request->current_page, [$view_cart_url, $buy_now_url])) {
                $redirect_page_status = true;
            }
            $cart_page_status = false;
            // dd($request->current_page,$view_cart_url);
            if ($request->current_page == $view_cart_url) {
                $cart_page_status = true;
            }
            // $shipping_amount = !empty($checkout_order) ? $checkout_order->shipping->shipping_amount : 0;
            $shipping_amount =  0;
            $shipping_amount_local = 0;
            // $shipping_amount_local = !empty($checkout_order) ? ($checkout_order->shipping->shipping_amount * $location_data->currency_rate) : 0;
            $cash_value = 0;
            $cash_value_local = 0;
            $total_reward_points = 0;
            if (Session::has('cart') && $cart->use_reward_point_status) {
                $rewards = Reward::where([
                    'user_id' => auth()->user()->id,
                    'status' => true,
                ])->get();
                foreach ($rewards as $value) {
                    $total_reward_points += $value->points;
                }
                $total_reward_points = $total_reward_points;
                $cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
                $cash_value_local = round($cash_value * $location_data->currency_rate, 2);
            }
            $cash_total_reward = 0;
            $cash_total_reward_local = 0;
            if (Session::has('cart') && $cart->use_cash_value_status) {
                $cash_rewards = CashReward::where([
                    'user_id' => auth()->user()->id,
                    'status' => true,
                ])->get();
                $cash_total_reward = 0;
                foreach ($cash_rewards as $value) {
                    $cash_total_reward += $value->amount;
                }
                $cash_total_reward = round($cash_total_reward, 2);
                $cash_total_reward_local = round($cash_total_reward * $location_data->currency_rate, 2);
            }
            $total = round($total_price, 2);
            $total_local = round($local_total_price, 2);
            $local_currency_symbol = $location_data ? $location_data->currency_symbol : '';
            $grand_total = ($total + $shipping_amount) - ($cash_value + $cash_total_reward);
            $grand_total_local = ($total_local + $shipping_amount_local) - ($cash_value_local + $cash_total_reward_local);
            $cart = (object)[
                'quantity' => $quantity,
                'total' => $total,
                'total_local' => $total_local,
                'grand_total' => $grand_total,
                'paid_grand_total' => $cart->paid_grand_total ?? 0,
                'grand_total_local' => $grand_total_local,
                'paid_grand_total_local' => $cart->paid_grand_total_local ?? 0,
                'local_currency_symbol' => $location_data->currency_symbol,
                'items' => $cart->items,
                'redirect_page' => $redirect_page,
                'redirect_page_status' => $redirect_page_status,
                'cart_page' => $cart_page_status,
                'amount_50_50' =>  getDefaultCurrencySymbol() . ($grand_total * 0.5) . ' - $' . ($grand_total - round($grand_total * 0.5, 2)),
                'amount_50_50_local' => $local_currency_symbol . (number_format($grand_total_local * 0.5, 2)) . ' - ' . $local_currency_symbol . number_format($grand_total_local - round($grand_total_local * 0.5, 2), 2),
                'amount_60_40' => getDefaultCurrencySymbol() . ($grand_total * 0.6) . ' - $' . ($grand_total - round($grand_total * 0.6, 2)),
                'amount_60_40_local' => $local_currency_symbol . (number_format(($grand_total_local * 0.6), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - $grand_total_local * 0.6), 2)),
                'amount_80_20' => getDefaultCurrencySymbol() . ($grand_total * 0.8) . ' - $' . ($grand_total - $grand_total * 0.8),
                'amount_80_20_local' => $local_currency_symbol . (number_format(($grand_total_local * 0.8), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - round($grand_total_local * 0.8, 2)), 2)),
                'amount_due' => $cart->amount_due ?? false,
                'order_create' => $cart->order_create ?? false,
                'use_reward_point_status' => empty($cart->use_reward_point_status) ? false : true,
                'use_cash_value_status' => empty($cart->use_cash_value_status) ? false : true,
            ];
            Session::put([
                'cart' => $cart,
            ]);
            if (count($cart->items) == 0) {
                Session::forget('cart');
            }
            $site_data = Session::get('shop_logged_data');
            $site_info = Setting::first();
            $selectedCountry = $site_data->country ?? $site_info->country;
            $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
            $storeIds = $this->getEligibleStores($selectedCountry);

            if (!empty($request->add_to_cart)) {
                return view('web.jquery_live.cart_items', compact('cart', 'buy_now_url', 'redirect_page', 'view_cart_url', 'checkout_order'));
            }
            if (!empty($request->cart_data)) {
                $total_price = 0;
                $quantity = 0;
                foreach ($cart->items as $value) {
                    if (count($storeIds) < 1) {
                        continue;
                    }
                    $quantity += $value->quantity;
                    // $value->unit_total_price = $value->quantity * $value->price;
                    $value->unit_total_price = $this->itemTotalPrice($value->cart_items);
                    $total_price += $value->unit_total_price;
                    $value->sku_details_url = $this->makeDetailsUrl($value->cart_items);
                    if (count($storeIds) > 0) {
                        $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                    }
                    // else {
                    //     $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                    // }
                }
                return response()->json($cart, 200);
            }
            $cash_total_reward = 0;
            $total_reward_points = 0;
            if (auth()->user()) {
                $rewards = Reward::where([
                    'user_id' => auth()->user()->id,
                    'status' => true,
                ])->get();
                foreach ($rewards as $value) {
                    $total_reward_points += $value->points;
                }
                $total_reward_points = $total_reward_points;
                $cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
                $cash_rewards = CashReward::where([
                    'user_id' => auth()->user()->id,
                    'status' => true,
                ])->get();
                foreach ($cash_rewards as $value) {
                    $cash_total_reward += $value->amount;
                }
                $cash_total_reward = number_format($cash_total_reward, 2);
            }
            // dd($cart_page_status);
            // dd($cart);
            return view('web.jquery_live.checkout_items', compact('cart', 'buy_now_url', 'redirect_page', 'view_cart_url', 'cart_page_status', 'checkout_order', 'cash_total_reward', 'cash_value', 'total_reward_points'));
        }
    }
    public function cartItems(Request $request)
    {
        // dd(Session::has('cart'));
        if (Session::has('cart')) {
            $checkout_page = Page::where('page_key', 'checkout')->first();
            $cart_page = Page::where('page_key', 'cart')->first();
            $products_page = Page::where('page_key', 'products')->first();
            $buy_now_url = route('page', $checkout_page->slug);
            $view_cart_url = route('page', $cart_page->slug);
            $redirect_page = route('page', $products_page->slug);
            $checkout_order = Session::get('checkout_order');
            $location_data = Session::get('shop_logged_data');
            $cart_page_status = false;
            if ($request->current_page == $view_cart_url) {
                $cart_page_status = true;
            }
            $site_data = Session::get('shop_logged_data');
            $site_info = Setting::first();
            $selectedCountry = $site_data->country ?? $site_info->country;
            $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
            $storeIds = $this->getEligibleStores($selectedCountry);
            $cart = Session::get('cart');
            foreach ($cart->items as $value) {
                if (count($storeIds) < 1) {
                    continue;
                }
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                // dd($value->condition);
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                } else {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                }
                // $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->get(), $value->attribute_options);
            }
            if (count($cart->items) == 0) {
                Session::forget('cart');
            }
            return view('web.jquery_live.checkout_items', compact('cart', 'buy_now_url', 'view_cart_url', 'redirect_page', 'cart_page_status', 'checkout_order'));
        }
    }
    public function getCartSkuItem($product_id, $cart_group)
    {
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);

        // dd($site_data);
        // $product = Product::where('status', true)->find($product_id);
        if (count($storeIds) > 0) {
            $product_item = ProductItem::whereIn('product_items.store_id', $storeIds)->get()->find($product_id);
        } else {
            $product_item = collect();
        }
        $product = Product::find($product_item->product_id);
        $this->makeAttributeData(unserialize($product_item->item_attributes), $product_item);
        $product_item->condition = $this->makeCondition(unserialize($product_item->item_attributes));
        if (!empty($product)) {
            $product_item->product_title = $product->title;
        }
        if ($product_item->title) {
            $product_item->title = $product_item->extra_title . ', ' . $product_item->title;
        } else {
            $product_item->title = $product_item->extra_title;
        }
        if ($product_item->sub_title) {
            $product_item->sub_title = $product_item->extra_sub_title . ', ' . $product_item->sub_title;
        } else {
            $product_item->sub_title = $product_item->extra_sub_title;
        }
        if (count($storeIds) > 0) {
            $items_array = ProductItem::where('product_id', $product->id)->where('status', 1)->whereIn('store_id', $storeIds)->get();
        } else {
            $items_array = collect();
        }
        // dd($items_array);
        $discount_upto = false;
        foreach ($items_array as $value) {
            $discount_amt = 0;
            $this->makeAttributeData(unserialize($value->item_attributes), $value);
            if ($value->discount_price && $value->discount_type) {
                if ($value->discount_type == 1) {
                    $discount_amt = ($value->sale_price * $value->discount_price) / 100;
                    $discount_percent = $value->discount_price;
                    $discount_upto = true;
                } else {
                    $discount_amt = $value->discount_price;
                    $discount_percent = ($value->discount_price * 100);
                }
                $value->discount_percent = ceil($discount_percent);
                $value->price = $value->sale_price - $discount_amt;
            } else {
                $value->price = $value->sale_price;
            }
            $value->price_off_amt = number_format(ceil($discount_amt), 2) ?? 0;
            $currency_rate = 0;
            $currency_rate_status = false;
            $currency_symbol = getDefaultCurrencySymbol();
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                // dd($location_data);
                $currency_rate = $location_data->currency_rate;
                $currency_symbol = $location_data->currency_symbol;
                if ($currency_rate > 0) {
                    $currency_rate_status = true;
                }
            }
            $value->local_price = number_format(($value->price * $currency_rate), 2);
            $value->local_currency_symbol = $currency_symbol;
            $value->currency_rate_status = $currency_rate_status;
        }
        // dd($cart_group, $items_array);
        $items = collect($items_array)->where('cart_group', $cart_group);
        // $items = collect($items_array)->where('attribute_options', $attribute_options);
        $cart_id = array();
        $cart_sku_partial_paid_id = array();
        if (Session::has('cart')) {
            $cart_items = Session::get('cart')->items;
            foreach ($cart_items as $cart_value) {
                foreach ($cart_value->cart_items as $cart_item) {
                    $cart_id[] = $cart_item->id;
                    if ($cart_item->sku_partial_paid) {
                        $cart_sku_partial_paid_id[] = $cart_item->id;
                    }
                }
            }
        };
        $availableItems = $items->filter(function ($value) {
            return $value->stock_status !== 'sold';
        });

        $items = $availableItems;
        foreach ($items as $value) {

            $value->in_cart = false;
            $value->sku_partial_paid = false;
            if (in_array($value->id, $cart_id)) {
                $value->in_cart = true;
            }
            if (in_array($value->id, $cart_sku_partial_paid_id)) {
                $value->sku_partial_paid = true;
            }
        }
        return view('web.jquery_live.sku_items', compact('items', 'product_item'));
    }
    public function applyPromo(Request $request)
    {
        $request->validate(['promo_code' => 'required|string']);
        $cart = Session::get('cart');
        $cart->promo_code = null;
        $cart->totalDiscount = null;
        $cart->discounted_price = null;
        $cart->totalDiscount_local = null;
        $customer = auth()->id();
        $alreadyCustomer = UserShipping::where('user_id', $customer)->first();
        $customerGroup = $alreadyCustomer ? 'active' : 'new';
        $coupan_user_id = $alreadyCustomer ? $alreadyCustomer->id : 0;
        // Retrieve the promo code details
        $promo = Promo::where('promo_code', $request->promo_code)
            ->where('status', 1)
            ->first();
        if (!$promo) {
            return response()->json(['success' => false, 'error' => 'Invalid promo code or not active.']);
        }
        if (!$customer && $promo->guest_checkout != 1) {
            return response()->json(['success' => false, 'error' => 'You need to sign up to use this coupan']);
        }
        $freeShipping = false;
        // Check if promo is expired
        $currentDate = now();
        if (!$promo->never_expire && ($currentDate < $promo->from_date || $currentDate > $promo->to_date)) {
            return response()->json(['success' => false, 'error' => 'This promo code is expired.']);
        }
        // Validate Customer Group
        if (!in_array($customerGroup, explode(',', $promo->customer_group))) {
            return response()->json(['success' => false, 'error' => 'This promo code is not applicable to your account.']);
        }
        if ($promo->uses_per_coupon > 0) {
            $couponUsageCount = CustomerPromo::where('promo_id', $customer)->count();
            if ($couponUsageCount >= $promo->uses_per_coupon) {
                return response()->json(['success' => false, 'error' => 'This promo code has reached its usage limit.']);
            }
        }
        if ($promo->uses_per_customer > 0) {
            $customerUses = CustomerPromo::where('customer_id', $customer)
                ->where('promo_id', $promo->id)
                ->count();
            if ($customerUses >= $promo->uses_per_customer) {
                return response()->json(['success' => false, 'error' => 'You have already used this promo code.']);
            }
        }
        $totalDiscount = 0;
        $disc_unit_price = 0;
        $currency_rate = 0;
        $currency_rate_status = false;
        $currency_symbol = getDefaultCurrencySymbol();
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
            $currency_rate = $location_data->currency_rate;
            $currency_symbol = $location_data->currency_symbol;
            if ($currency_rate > 0) {
                $currency_rate_status = true;
            }
        }
        if ($promo->coupon_value === 'discount') {
            foreach ($cart->items as $item) {
                $itemDiscount = 0;
                $product_data = Product::where('id', $item->product_id)->select('product_categories_id', 'product_brand_id', 'title')->first();
                $products_items = ProductItem::where('product_id', $item->product_id)
                    ->select('product_item_sku')
                    ->get();
                // Check promo conditions
                if ($promo->coupon_type_condition === 'no_condition' || $promo->coupon_type_condition === 'cart_subtotal') {
                    $itemDiscount = $promo->discount_type == 1
                        ? $item->price * ($promo->discount_price / 100) // Percentage discount
                        : $promo->discount_price;
                    // $disc_unit_price =$item->price - $itemDiscount;
                    $cart->discount_or_subtotal = 'cart_subtotal';
                } else {
                    // Apply discount based on specific conditions (category, brand, etc.)
                    switch ($promo->coupon_type_condition) {
                        case 'category':
                            $condition_values = json_decode($promo->coupon_type_condi_data, true);
                            if (is_array($condition_values) && in_array($product_data->product_categories_id, $condition_values)) {
                                $itemDiscount = $promo->discount_type == 1
                                    ? $item->price * ($promo->discount_price / 100)
                                    : $promo->discount_price;
                            }
                            $disc_unit_price = $item->price - $itemDiscount;
                            break;
                        case 'brand':
                            $condition_values = json_decode($promo->coupon_type_condi_data, true);
                            if (is_array($condition_values) && in_array($product_data->product_brand_id, $condition_values)) {
                                $itemDiscount = $promo->discount_type == 1
                                    ? $item->price * ($promo->discount_price / 100)
                                    : $promo->discount_price;
                            }
                            $disc_unit_price = $item->price - $itemDiscount;
                            break;
                        case 'products':
                            $condition_values = json_decode($promo->coupon_type_condi_data, true);
                            if (is_array($condition_values) && in_array($product_data->title, $condition_values)) {
                                $itemDiscount = $promo->discount_type == 1
                                    ? $item->price * ($promo->discount_price / 100)
                                    : $promo->discount_price;
                            }
                            $disc_unit_price = $item->price - $itemDiscount;
                            break;
                        case 'sku_attributes':
                            $condition_values = json_decode($promo->coupon_type_condi_data, true);
                            if (is_array($condition_values)) {
                                foreach ($products_items as $product_item) {
                                    if (in_array($product_item->product_item_sku, $condition_values)) {
                                        $product_data->id ==
                                            $itemDiscount = $promo->discount_type == 1
                                            ? $item->price * ($promo->discount_price / 100)
                                            : $promo->discount_price;
                                        $disc_unit_price = $item->price - $itemDiscount;
                                        break; // Stop the loop once a match is found
                                    }
                                }
                            }
                            break;
                        default:
                            continue 2;
                    }
                }
                $item->discounted_price = $disc_unit_price;
                if ($item->discounted_price) {
                    $item->discounted_price_local = $disc_unit_price  * $currency_rate;
                }
                // Add the discount for this item to the total
                $totalDiscount += $itemDiscount;
            }
        } elseif ($promo->coupon_value === "free_shipping") {
            foreach ($cart->items as $item) {
                $item->discounted_price = null;
            }
            $freeShipping = true;
        } else {
            $totalDiscount = 0;
        }
        if ($totalDiscount) {
            $cart->totalDiscount_local = $totalDiscount * $currency_rate;
        }
        $cart->totalDiscount = $totalDiscount;
        $cart->freeShipping = $freeShipping;
        $cart->promo_code = $promo->id;
        $cart->coupan_user_id = $coupan_user_id;
        //  $cart->grand_total=$cart->grand_total - round($totalDiscount, 2);
        //  if($cart->total_local > 0){
        //    $cart->grand_total_local=$cart->grand_total_local - round($totalDiscount, 2);
        //  }
        Session::put('cart', $cart);
        // Return success response with promo details
        return response()->json([
            'success' => true,
            'promo_code' => $promo->promo_code,
            'promo_discount' => round($totalDiscount, 2),
            'new_total' => $cart->grand_total_local - round($totalDiscount, 2),
            'items' => array_map(function ($item) {
                return [
                    'id' => $item->id,
                    'unit_total_price' => $item->unit_total_price,
                    'discounted_price' => $item->discounted_price,
                ];
            }, $cart->items),
        ]);
    }
    public function searchLive($query)
    {
        $items =  collect($this->productItemsData(null, null, $query));
        if (count($items) == 0) return response()->json(null, 200);
        foreach ($items as $value) {
            $item_title = $value->product_brand_title . ' ' . $value->product_title;
            if ($value->title) {
                $item_title = $item_title . ' ' . $value->title;
            }
            if ($value->sub_title) {
                $item_title = $item_title . ' [' . $value->sub_title . ']';
            }
            // dd($value);
            $data[] = [
                'value' => $item_title,
                'url' => route('page', ['products', $value->product_slug, 'group' => $value->group, 'max' => $value->max_filter_price, 'min' => $value->min_filter_price]),
            ];
        }
        return response()->json($data, 200);
    }
    function getRandomStringUniqid($length = 16)
    {
        $string = uniqid(rand());
        $randomString = substr($string, 0, $length);
        return $randomString;
    }
    public function squadcoPay($request)
    {
        try {
            // Get necessary session data
            $cart = Session::get('cart');
            $location_data = Session::get('shop_logged_data');
            $checkout_order = Session::get('checkout_order');
            $shippingAmount_local = session('shippingAmount_local', 0);
            $shippingAmount = Session::get('shippingAmount') ?? 0;
            $cart_totalDiscount = $cart->totalDiscount ?? 0;
            if (!$cart || !$checkout_order) {
                throw new \Exception('Invalid cart or checkout data');
            }
            // Get product descriptions
            $description = [];
            foreach ($cart->items as $value) {
                $description[] = $value->product_item_sku . '-' . $value->product_title . ' ' . $value->title . ' ' . $value->sub_title;
            }
            // dd($checkout_order);
            $currency = ($checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') ? 'NGN' : 'USD';
            $amount = intval(($cart->grand_total - $cart->paid_grand_total));
            if ($request->pay_by_squadco != 'pay_by_squadco_local' &&  $cart_totalDiscount > 0) {
                if ($cart_totalDiscount > 0) {
                    $amount_totalDiscount = intval(($cart_totalDiscount) * 100);
                }
                $amount = $amount - $amount_totalDiscount;
            }
            $discount_amt = 0;
            $amount_due = false;
            $transaction_ref = $this->getRandomStringUniqid();
            // Initialize payment variables
            //  dd($transaction_ref);
            // Handle discount calculation for non-local payment
            if (
                Session::has('shop_logged_data') &&
                $request->pay_by_squadco != 'pay_by_squadco_local' &&
                $cart->paid_grand_total == 0
            ) {
                $payment_method = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_squadco');
                if ($payment_method->discount_price) {
                    if ($payment_method->discount_type == 1) {
                        $discount_amt = ($cart->total * $payment_method->discount_price) / 100;
                    } else {
                        $discount_amt = $payment_method->discount_price;
                    }
                    $amount = $amount - $discount_amt;
                    if ($shippingAmount > 0) {
                        $amount = $amount + $shippingAmount;
                    }
                    $amount = intval($amount * 100);
                }
            }
            $shippingAmount_local = session('shippingAmount_local', 0);
            if (
                isset($cart->grand_total_local, $cart->totalDiscount_local) &&
                $cart->grand_total_local > 0 &&
                $shippingAmount_local > 0 &&
                $cart->totalDiscount_local > 0
            ) {
                $cart->grand_total_local_cal = ($cart->grand_total_local + $shippingAmount_local) - $cart->totalDiscount_local;
            } elseif (
                isset($cart->grand_total_local, $cart->totalDiscount_local) &&
                $cart->grand_total_local > 0 &&
                $cart->totalDiscount_local > 0
            ) {
                $cart->grand_total_local_cal = $cart->grand_total_local  - $cart->totalDiscount_local;
            } elseif ((empty($cart->totalDiscount_local) || !isset($cart->totalDiscount_local))  && $shippingAmount_local > 0) {
                $cart->grand_total_local_cal = $cart->grand_total_local + $shippingAmount_local;
            }
            $grandTotalLocal = isset($cart->grand_total_local_cal) ? $cart->grand_total_local_cal : $cart->grand_total_local;
            // Handle local payment calculation
            if (Session::has('shop_logged_data') && $request->pay_by_squadco == 'pay_by_squadco_local') {
                $currency = $this->getLocalCurrencyCode($location_data->country);
                $amount = round($grandTotalLocal - $cart->paid_grand_total_local, 2);
                // Handle maximum amount limit
                if ($amount >= 5000000) {
                    $amount = 5000000;
                    $amount_due = true;
                } else if ($request->split_payment_amount) {
                    $amount_due = true;
                    switch ($request->split_payment_amount) {
                        case '50_50':
                            $amount = round($grandTotalLocal * 0.5, 2);
                            break;
                        case '60_40':
                            $amount = round($grandTotalLocal * 0.6, 2);
                            break;
                        case '80_20':
                            $amount = round($grandTotalLocal * 0.8, 2);
                            break;
                    }
                }
                $amount = (int) round($amount * 100); // Convert NGN to kobo
            }
            // Get customer payment details
            $pay_email = $checkout_order->customer->email;
            $customer_name = $checkout_order->customer->first_name . ' ' . $checkout_order->customer->last_name;
            $phone_number = $checkout_order->customer->tel_cell;
            // Handle different billing address
            if ($checkout_order->payment->payment_same_as_contact == 0) {
                $user_billing = UserBilling::where('id', $checkout_order->payment->user_billing_id)->first();
                if ($user_billing) {
                    $pay_email = $user_billing->email;
                    $customer_name = $user_billing->first_name . ' ' . $user_billing->last_name;
                    $phone_number = $user_billing->tel_cell;
                }
            }
            // Update cart data
            $cart->intent_pay = $amount;
            $cart->discount_amt = $discount_amt;
            $cart->transaction_refs = array($transaction_ref);
            $cart->amount_due = $amount_due;
            // Handle split payment cart updates
            if ($amount_due) {
                $cart->cart_total_local = $cart->grand_total_local;
                if (!in_array($transaction_ref, $cart->transaction_refs)) {
                    array_push($cart->transaction_refs, $transaction_ref);
                }
            }
            // Convert amount to cents for Squadco
            $amount = intval(round($amount));
            // Prepare payment payload
            $payment_payload = [
                "authorization_key" => app('App\Http\Controllers\Controller')->transactionMethod('pay_by_squadco')->private_key,
                "customer_name" => $customer_name,
                "phone_number" => $phone_number,
                "amount" => $amount,
                "email" => $pay_email,
                "currency" => $currency,
                "initiate_type" => "inline",
                "transaction_ref" => $transaction_ref,
                "callback_url" => URL::route('squadco'),
                "pass_charge" => false,
                "payment_channels" => ['card', 'bank', 'ussd', 'transfer'],
                "metadata" => (object)$description,
            ];
            // Update session data
            Session::put('cart', $cart);
            Session::put('squadco_payment_ref', $transaction_ref);
            // Create order if doesn't exist
            if (empty($cart->order)) {
                $this->makeOrder();
            }
            return $payment_payload;
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Payment initialization failed',
                'message' => $e->getMessage()
            ], 422);
        }
    }
    public function getPaymentSquadcoStatus(Request $request)
    {
        $checkout_order = Session::get('checkout_order');
        $location_data = Session::get('shop_logged_data');
        $cart = Session::get('cart');
        $thank_page = Page::where('page_key', 'thank_you')->first();
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $user = auth()->user();
        $squadco_payment_ref = Session::get('squadco_payment_ref');
        try {
            $data = $this->verifySquadcoPayment($squadco_payment_ref);
            sleep(2);
            if (Session::has('cart')) {
                if ($cart->amount_due) {
                    return $this->handlePartialPayment($cart, $data, $location_data, $thank_page);
                } else {
                    return $this->handleFullPayment($cart, $data, $thank_page);
                }
            }
            throw new \Exception('Cart not found');
        } catch (\Exception $e) {
            Session::flash('error', 'Payment failed! ' . $e->getMessage());
            return redirect()->route('page', $checkout_page->slug);
        }
    }
    private function verifySquadcoPayment($payment_ref)
    {
        $headers = [
            'Authorization' => 'sandbox_sk_d84d7ca27a9a317aebf1b991283f3fc498da0fee882a',
            'Content-Type' => 'application/json',
        ];
        $url = 'https://sandbox-api-d.squadco.com/transaction/verify/' . $payment_ref;
        $promise = Http::withHeaders($headers)->async()->get($url);
        return $promise->then(fn($response) => $response->json())->wait();
    }
    private function isPaymentSuccessful($data)
    {
        return
            $data['status'] == 200 &&
            $data['data']['transaction_status'] == "success";
    }
    private function handlePartialPayment($cart, $data, $location_data, $checkout_page)
    {
        $transaction_method = app('App\Http\Controllers\Controller')
            ->transactionMethod('pay_by_squadco')->id;
        //   dd($data);
        if ($cart->paid_grand_total == 0) {
            $this->makeCollection(
                $cart->intent_pay,
                $transaction_method,
                $data['data']['transaction_ref'],
                3,
                $cart->discount_amt
            );
            // dd($this->sendPartialPaymentEmails($cart));
            $this->sendPartialPaymentEmails($cart);
        } else {
            $this->makeCollection(
                $cart->intent_pay,
                $transaction_method,
                $data['data']['transaction_ref'],
                3,
                $cart->discount_amt
            );
        }
        $this->updateCartAfterPartialPayment($cart, $location_data);
        Session::forget('squadco_payment_ref');
        Session::flash('success', 'Payment successful!');
        return redirect()->route('page', $checkout_page->slug);
    }
    private function handleFullPayment($cart, $data, $thank_page)
    {
        // dd($data['data']['transaction_ref']);
        $transaction_method = app('App\Http\Controllers\Controller')
            ->transactionMethod('pay_by_squadco')->id;
        $this->makeCollection(
            $cart->intent_pay,
            $transaction_method,
            $data['data']['transaction_ref'],
            2,
            $cart->discount_amt
        );
        // Session::forget('squadco_payment_ref');
        Session::flash('success', 'Payment successful!');
        return redirect()->route('page', $thank_page->slug);
    }
    private function updateCartAfterPartialPayment($cart, $locationData)
    {
        // Update cart totals and item statuses
        $cart->paid_grand_total = round($cart->paid_grand_total + ($cart->intent_pay / $locationData->currency_rate), 2);
        $cart->paid_grand_total_local = round(($cart->paid_grand_total_local + $cart->intent_pay), 2);
        foreach ($cart->items as $item) {
            $item->sku_partial_paid = true;
            $item->cart_items = $this->makeItemStatus($item->cart_items);
            $item->sku_details_url = $this->makeDetailsUrl($item->cart_items);
        }
        // Save updated cart to session
        Session::put('cart', $cart);
    }
    public function sendPartialPaymentEmails($cart)
    {
        $this->sendCustomerEmails($cart);
    }
    public function sendCustomerEmails($cart)
    {
        $customerEmail = $cart->user->email;
        $customerName = trim($cart->user->first_name . ' ' . $cart->user->last_name);
        $firstName = $cart->user->first_name;
        //customer send email
        $alert_preferences = unserialize($cart->user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            // dd($user_email_templates);
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template) {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    // dd($cart);
                    if ($cart->order_create) {
                        $mail_data = (object)[
                            'user' => $cart->user,
                            'order' => $cart->order,
                            'subject' => $user_email_template->subject,
                            'pre_header' => $user_email_template->pre_header,
                            'email_body' => $user_email_template->email_body,
                            'sms_status' => $user_email_template->sms_status ? true : false,
                            'sms_body' => $user_email_template->sms_body,
                        ];
                    }
                    $sms_message = $this->sendEmailData($mail_data)->sms_body;
                    $cart->user->tel_cell = str_replace('+', '', $cart->user->tel_cell);
                    if ($cart->user->tel_cell) {
                        $this->checkWhatsappExist($user_email_template->sms_status, $cart->user->tel_cell, $sms_message);
                    }
                }
            }
        }
        $mail_data->subject = $this->sendEmailData($mail_data)->subject;
        $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
        $mail_data->content = $this->sendEmailData($mail_data)->content;
        $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
        Mail::to($cart->user->email)->send(new SiteEmail($mail_data));
        $email_templates_admin = EmailTemplate::find(20);
        $email_templates_admin->sms_body = unserialize($email_templates_admin->sms_body);
        $mail_data_admin = (object)[
            'user' => $cart->user,
            'order' => $cart->order,
            'subject' => $email_templates_admin->subject,
            'pre_header' => $email_templates_admin->pre_header,
            'email_body' => $email_templates_admin->email_body,
            'sms_status' => $email_templates_admin->sms_status ? true : false,
            'sms_body' => $email_templates_admin->sms_body,
        ];
        $mail_data_admin->subject = $this->sendEmailData($mail_data_admin)->subject;
        $mail_data_admin->pre_header = $this->sendEmailData($mail_data_admin)->pre_header;
        $mail_data_admin->content = $this->sendEmailData($mail_data_admin)->content;
        $mail_data_admin->sms_content = $this->sendEmailData($mail_data_admin)->sms_body;
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', [6]) // Correct: Pass an array to whereIn
            ->join('users', 'users.id', '=', 'user_group_maps.user_id') // Use explicit comparison operator
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences'
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data_admin));
        }
    }
    //admin send email
    public function getEasyPostShipping($request = null, $selected_shipment = null)
    {
        // dd( $selected_shipment);
        $checkoutOrder = Session::get('checkout_order');
        $cart = Session::get('cart');
        $shop_data = Session::get('shop_logged_data');
        $primary_shipping_address = UserShipping::where('user_id', auth()->id())
            ->where('primary', true)
            ->get()->first();
        if (auth()->check()) {
            $city = $primary_shipping_address->city ?? '';
            $zip = $primary_shipping_address->postal_code ?? '';
            $country = strtoupper($primary_shipping_address->country ?? '');
        } else if ($checkoutOrder && $checkoutOrder->customer) {
            $customer = $checkoutOrder->customer;
            $city = $customer->city ?? null;
            $zip = $customer->postal_code ?? null;
            $country = $customer->country ?? null;
        } else {
            $city = request()->get('guest_city');
            $zip = request()->get('guest_postal_code');
            $country = request()->get('guest_country');
        }
        $currentDate = new DateTime();
        $currentDate->modify('+1 day');
        $newDate = $currentDate->format('Y-m-d');
        $weight = 1;
        $length = 1;
        $width = 1;
        $height = 1;
        $location = [];
        $items = null;
        if ($cart) {
            foreach ($cart->items as $item) {
                $items = $item->cart_items;
                foreach ($item->cart_items as $cart_item) {
                    $weight = $cart_item->box_with_weight ?? 1;
                    $length = $cart_item->box_with_length ?? 1;
                    $width = $cart_item->box_with_width ?? 1;
                    $height = $cart_item->box_with_heigh ?? 1;
                    $location[] = isset($cart_item->location_id) ? $cart_item->location_id  : 'NG';
                }
            }
        }
        $location = array_unique($location);
        $site_data = Setting::whereIn('country', $location)->where('status', 1)->get();
        //dd($site_data);
        $pickup_available = false;
        $pickup_locations = [];
        foreach ($location as $item_location) {
            if (empty($shop_data)) {
                break;
            }
            if ($item_location == $shop_data->country) {
                $pickup_available = true;
                $pickup_locations[] = $item_location;
            }
        }
        $packageData = [];
        $rates = [];
        $uniqueRates = [];
        $productCode = null;
        $landed_cost = (object)[];
        $landed_cost_data = [];
        $unique_landed_cost = [];
        //dd($site_data);
        foreach ($site_data as $site) {
            $packageData = [
                'accountNumber' => '*********',
                'originCountryCode' => $site->country,
                'originPostalCode' => $site->zip_code,
                'originCityName' => $site->city,
                'destinationCountryCode' => $country,
                'destinationCityName' => $city,
                'destinationPostalCode' => $zip,
                'weight' => $weight,
                'length' => $length,
                'width' => $width,
                'height' => $height,
                'plannedShippingDate' => $newDate,
                'isCustomsDeclarable' => 'true',
                'unitOfMeasurement' => 'metric'
            ];
            $landed_cost = (object)[
                'items' => $items,
                'shipper_country' => $site->country,
                'shipper_city' => $site->city,
                'shipper_zip' => $site->zip_code,
                'reciver_country' => $country,
                'reciver_city' => $city,
                'reciver_zip' => $zip,
                'weight' => $weight,
                'length' => $length,
                'width' => $width,
                'height' => $height,
                'plannedShippingDate' => $newDate,
                'isCustomsDeclarable' => true,
                'unitOfMeasurement' => 'metric'
            ];
            // dd( $packageData);
            $siteRates = $this->dhlService->getRates($packageData);
            //dd($siteRates);
            $landed_cost_data = $this->dhlService->landedCost($landed_cost);
            if ($landed_cost_data) {
                $unique_landed_cost = $landed_cost_data;
            }
            
            if ($siteRates->status == 400) {

                return [
                    'error' => true,
                    'status' => 400,
                    'message' => 'Shipping unavailable'
                ];
            }
            if ($siteRates->status == 200) {
                foreach ($siteRates->shipping_methods as $pro) {
                    $productCode = $pro['productCode'];
                    if ($productCode && !isset($uniqueRates[$productCode])) {
                        $uniqueRates[$productCode] = $pro;
                    }
                }
            }
        }
        $rates = array_values($uniqueRates);
        $landed = array_values($unique_landed_cost);
        $landed_data = [];
        $items_list = [];
        if ($landed) {
            foreach ($landed[0] as $cost) {
                $items_list = $cost['items'][0]['breakdown'];
            }
        }
        if (!empty($items_list)) {
            foreach ($items_list as $list) {
                if ($list['name'] == 'CIF') {
                    $landed_data[] = [
                        'name' => $list['name'],
                        'price' => $list['price'],
                    ];
                }
            }
        }
        $rate_data = [];
        foreach ($rates as $rate) {
            $rate_data[] = [
                'id' => $rate['productCode'],
                'carrier' => $rate['productName'],
                'service' => $rate['productCode'],
                'rate' => $rate['totalPrice'][0]['price'],
                'retail_rate' => $rate['totalPrice'][0]['price'],
                'currency' => isset($rate['totalPrice'][0]['priceCurrency']) ? $rate['totalPrice'][0]['priceCurrency'] : 'USD',
                'currency_symbol' => $this->getCurrencySymbol(isset($rate['totalPrice'][0]['priceCurrency']) ? $rate['totalPrice'][0]['priceCurrency'] : 'USD'),
                'carrier_account_id' => $rate['productCode'],
                'shipment_id' => $rate['productCode'],
                'estimated_days' => $rate['deliveryCapabilities']['estimatedDeliveryDateAndTime'],
                "tax" => $landed_data[0]['price'] ?? 0,
                "tax_name" => $landed_data[0]['name'] ?? 'CIF',
            ];
        }
        $rate_data[] = [
            'id' => 'BUDGET',
            'carrier' => '1GG Budget',
            'service' => '1GG',
            'rate' => 60.00,
            'retail_rate' => 60.00,
            'currency' => 'USD',
            'currency_symbol' => getDefaultCurrencySymbol(),
            'carrier_account_id' => 'BUDGET',
            'shipment_id' => 'BUDGET',
            'estimated_days' => '',
        ];
        $selectedRate = [];
        if ($selected_shipment && $selected_shipment != 'Standard') {
            foreach ($rates as $rate) {
                if ($rate['productCode'] == $selected_shipment) {
                    Session::put('selectedShipping', $rate);
                    $selectedRate[] = [
                        'id' => $rate['productCode'],
                        'carrier' => $rate['productName'],
                        'service' => $rate['productCode'],
                        'rate' => $rate['totalPrice'][0]['price'],
                        'retail_rate' => $rate['totalPrice'][0]['price'],
                        'currency' => $rate['totalPrice'][0]['priceCurrency'],
                        'currency_symbol' => $this->getCurrencySymbol($rate['totalPrice'][0]['priceCurrency']),
                        'carrier_account_id' => $rate['productCode'],
                        'shipment_id' => $rate['productCode'],
                        'estimated_days' => $rate['deliveryCapabilities']['estimatedDeliveryDateAndTime'],
                        "tax" => $landed_data[0]['price'] ?? 0,
                        "tax_name" => $landed_data[0]['name'] ?? 'CIF',
                    ];
                } elseif ($rate['productCode'] == 'Pickup') {
                    $selectedRate[] = [
                        'id' => 'PICKUP',
                        'carrier' => 'Pickup at Store',
                        'service' => 'Pickup',
                        'rate' => 0.00,
                        'retail_rate' => 0.00,
                        'currency' => 'USD',
                        'currency_symbol' => getDefaultCurrencySymbol(),
                        'carrier_account_id' => 'PICKUP',
                        'shipment_id' => 'PICKUP',
                        'estimated_days' => '',
                    ];
                } elseif ($rate['productCode'] == '1GG') {
                    $selectedRate[] = [
                        'id' => 'IGG',
                        'carrier' => '1GG Budget',
                        'service' => 'IGG',
                        'rate' => 60.00,
                        'retail_rate' => 60.00,
                        'currency' => 'USD',
                        'currency_symbol' => getDefaultCurrencySymbol(),
                        'carrier_account_id' => 'IGG',
                        'shipment_id' => 'IGG',
                        'estimated_days' => '',
                    ];
                }
            }
            //dd($selectedRate);
            // return ['shipping_rates' => $selectedRate[0], 'status' => 200, 'landed_cost' => $landed_data];

            return ['shipping_rates' => $selectedRate[0], 'status' => 200, 'landed_cost' => $landed_data];
        } else {
            $selectedRate[] = [
                'id' => 'FREE',
                'carrier' => 'Free Shipping',
                'service' => 'Standard',
                'rate' => 0.00,
                'retail_rate' => 0.00,
                'currency' => 'USD',
                'currency_symbol' => getDefaultCurrencySymbol(),
                'carrier_account_id' => 'FREE',
                'shipment_id' => 'FREE',
            ];
            
            return ['shipping_rates' => $selectedRate[0], 'status' => 200, 'landed_cost' => $landed_data];
        }
        //dd($rate_data);
        return ['shipping_rates' => $rate_data, 'status' => 200, 'landed_cost' => $landed_data];
    }
    public function initiateBTCPay(Request $request)
    {
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        foreach ($cart->items as $item) {
            $data = [
                'amount' => $cart->grand_total,
                'currency' => 'USD',
            ];
        }
        try {
            $invoice = $this->btcPayService->createInvoice($data);
            Session::put('btc_invoice_id', $invoice['id']);
            return $invoice['checkoutLink'];
        } catch (\Exception $e) {
            Log::error('BTCPay Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    public function initiateFlutterPay(Request $request, $payment, $customer, $location_data)
    {

        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        $reference = Flutterwave::generateReference();
        $payment_method = app('App\Http\Controllers\Controller')->transactionMethod('flutterwave');
        $allowedCurrencies = explode(',', $payment_method->allowed_currency);

        // Trim any whitespace
        $allowedCurrencies = array_map('trim', $allowedCurrencies);
        $finalAmount = $cart->grand_total;

        // $paymentData = $this->getLocalCostAndCurrency($payment_method, $location_data);

        $data = [
            'payment_options' => 'card,banktransfer',
            'amount' => $finalAmount,
            'email' => $payment->email,
            'tx_ref' => $reference,
            'currency' => getDefaultCurrencyCode(),
            'redirect_url' => route('callback'),
            'customer' => [
                'email' => $customer->email,
                "phone_number" => $payment->tel_cell,
                "name" => $payment->first_name
            ],
        ];

        $payment = Flutterwave::initializePayment($data);

        if ($payment['status'] !== 'success') {
            // notify something went wrong
            return;
        }


        return $payment['data']['link'];
    }
    public function handleWebhook(Request $request)
    {
        \Log::info('Headers', $request->headers->all());
        \Log::info('Payload', $request->all());
        $signature = $request->header('BTCPay-Sig');
        $payload = $request->getContent();
        $secret = '****************************';
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        // Verify the signature
        if (!hash_equals('sha256=' . $expectedSignature, $signature)) {
            Log::error('Invalid BTCPay signature', ['signature' => $signature]);
            return response()->json(['message' => 'Invalid signature'], 403);
        }
        $data = json_decode($payload, true);
        $eventType = $data['type'] ?? null;
        \Log::info('Webhook Event Received', ['type' => $eventType]);
        // Handle the InvoiceSettled event
        if ($eventType === 'InvoiceSettled') {
            \Log::info('InvoiceSettled event processed successfully123333.');
            try {
                \Log::info('InvoiceSettled event processed successfully inside try.');
            } catch (\Exception $e) {
                Log::error('Error processing InvoiceSettled webhook', ['error' => $e->getMessage()]);
                return response()->json(['message' => 'Error processing webhook'], 500);
            }
        }
        return response()->json(['message' => 'Webhook received'], 200);
    }
    public function postCheckout(Request $request)
    {
        $contacTeam = Page::where('page_key', 'contactTeam')->first();
        $location_data = array();
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
        }
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        $countries = $this->countries();
        $thank_page = Page::where('page_key', 'thank_you')->first();
        $local_currency_symbol = $location_data ? $location_data->currency_symbol : '';
        $customer_status = false;
        $shipping_tab_status = false;
        $shipping_status = false;
        $payment_tab_status = false;
        $payment_status = false;
        $make_payment = false;
        $expedited_method = null;
        $priority_method = null;
        $standard_method = null;
        $guest_tell = $request->get('guest_tel_cell');

        if ($request->continue_shipping && empty($cart->order)) {
            if (Auth::user()) {
                $customer = $checkout_order->customer;
                $validator = Validator::make($request->all(), [
                    'guest_first_name' => 'required|string|max:255',
                    'guest_last_name' => 'required|string|max:255',
                    'guest_email' => 'nullable|email',
                    "guest_tel_cell" => "required|string|max:20",
                    // 'guest_tel_cell_country_code' => 'nullable|string',
                    'guest_company_name' => 'nullable|string',
                    'guest_city' => 'nullable|string',
                    'guest_state' => 'nullable|string',
                    'guest_country' => 'nullable|string',
                    'guest_postal_code' => 'nullable|integer',
                    'guest_address_line_1' => 'nullable|string',
                    'guest_address_line_2' => 'nullable|string',
                    'email_notifications' => 'nullable|boolean',
                    'sms_notifications' => 'nullable|boolean',
                ]);
            } else {
                // dd($request->guest_tel_cell);
                $validator = Validator::make($request->all(), [
                    'guest_first_name' => 'required|string|max:255',
                    'guest_last_name' => 'required|string|max:255',
                    'guest_email' => 'nullable|email',
                    "guest_tel_cell" => "required|string|max:20",
                    // 'guest_tel_cell_country_code' => 'nullable|string',
                    'guest_company_name' => 'nullable|string',
                    'guest_city' => 'nullable|string',
                    'guest_state' => 'nullable|string',
                    'guest_country' => 'nullable|string',
                    'guest_postal_code' => 'nullable|integer',
                    'guest_address_line_1' => 'nullable|string',
                    'guest_address_line_2' => 'nullable|string',
                    'email_notifications' => 'nullable|boolean',
                    'sms_notifications' => 'nullable|boolean',
                ]);
                $customer = (object)[
                    'first_name' => $request->get('guest_first_name'),
                    'last_name' => $request->get('guest_last_name'),
                    'email' => $request->get('guest_email'),
                    // 'tel_cell_country_code' => $guest_tel_cell_country_code ?? null,
                    'tel_cell' => preg_replace('/[^\d+]/', '', $guest_tell),
                    'company_name' => $request->get('guest_company_name'),
                    'city' => $request->get('guest_city'),
                    'state' => $request->get('guest_state'),
                    'country' => $request->get('guest_country'),
                    'country_name' => $this->countries($request->get('guest_country')),
                    'postal_code' => $request->get('guest_postal_code'),
                    'address' => $request->get('guest_address_line_1'),
                    'address_2' => $request->get('guest_address_line_2'),
                    'email_notifications' => $request->get('email_notifications') ?? 0,
                    'sms_notifications' => $request->get('sms_notifications') ?? 0,
                ];
            }
            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->messages(),
                ], 400);
            }
            $customer_status = true;
            $shipping_tab_status = true;
        }
        if (Auth::user()) {
            $customer = $checkout_order->customer;
        } else {
            $customer = (object)[
                'first_name' => $request->get('guest_first_name'),
                'last_name' => $request->get('guest_last_name'),
                'email' => $request->get('guest_email'),
                // 'tel_cell_country_code' => $guest_tel_cell_country_code ?? null,
                'tel_cell' => preg_replace('/[^\d+]/', '', $guest_tell),
                'company_name' => $request->get('guest_company_name'),
                'city' => $request->get('guest_city'),
                'state' => $request->get('guest_state'),
                'country' => $request->get('guest_country'),
                'country_name' => $this->countries($request->get('guest_country')),
                'postal_code' => $request->get('guest_postal_code'),
                'address' => $request->get('guest_address_line_1'),
                'address_2' => $request->get('guest_address_line_2'),
                'email_notifications' => $request->get('email_notifications') ?? 0,
                'sms_notifications' => $request->get('sms_notifications') ?? 0,
            ];
        }
        $shipping_amount = 0;
        $shipping_id = $request->get('shipping_method');
        if ($request->continue_payment && empty($cart->order)) {
            if ($request->shipping_same_address) {
                $validator = Validator::make($request->all(), [
                    'shipping_method' => 'required|array',
                    'shipping_method.*' => 'string',
                ]);
            } else {
                if (Auth::user()) {
                    $validator = Validator::make($request->all(), [
                        'user_shipping_id' => 'required|integer|exists:user_shippings,id',
                        'shipping_method' => 'required|array',
                        'shipping_method.*' => 'string',
                    ], [
                        'user_shipping_id.required' => 'Add your Shipping details'
                    ]);
                } else {
                    $validator = Validator::make($request->all(), [
                        'shipping_first_name' => 'required|string|max:255',
                        'shipping_last_name' => 'required|string|max:255',
                        'shipping_email' => 'required|string|email',
                        'shipping_tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
                        // 'shipping_tel_cell_country_code' => 'nullable|string',
                        'shipping_company_name' => 'nullable|string',
                        'shipping_city' => 'nullable|string',
                        'shipping_state' => 'nullable|string',
                        'shipping_country' => 'nullable|string',
                        'shipping_postal_code' => 'nullable|integer',
                        'shipping_address_line_1' => 'nullable|string',
                        'shipping_address_line_2' => 'nullable|string',
                        'shipping_method' => 'required|array',
                        'shipping_method.*' => 'string',
                    ]);
                }
            }
            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->messages(),
                ], 400);
            }
            $customer_status = true;
            $shipping_tab_status = true;
            $shipping_status = true;
            $payment_tab_status = true;
        } else {
            $customer_status = true;
            $shipping_tab_status = true;
            $shipping_status = true;
            $payment_tab_status = true;
        }
        $shipping_amount = 0;
        $shipping_method = [];
        $shipping_id = $request->get('shipping_method');

        foreach ($shipping_id ?? [] as $id) {
            $shipping_method[] = $this->getEasyPostShipping($request, $id);
        }
        // dd($shipping_method);
        $geust_shipping_tell = $request->get('shipping_tel_cell');
        if ($request->tax) {
            foreach ($shipping_method as $method) {
                if (isset($method['shipping_rates']['service']) && $method['shipping_rates']['service'] == 'P') {
                    $retail_rate = $method['shipping_rates']['retail_rate'] ?? 0;
                    $shipping_amount += $retail_rate + $request->tax;
                } else {
                    $retail_rate = $method['shipping_rates']['retail_rate'] ?? 0;
                    $shipping_amount += $retail_rate;
                }
            }
        } else {
            foreach ($shipping_method as $method) {
                $retail_rate = $method['shipping_rates']['retail_rate'] ?? 0;
                $shipping_amount += $retail_rate;
            }
        }
        $shipping = (object)[
            'first_name' => $request->shipping_same_address ? $checkout_order->customer->first_name : $request->get('shipping_first_name'),
            'last_name' => $request->shipping_same_address ? $checkout_order->customer->last_name : $request->get('shipping_last_name'),
            'email' => $request->shipping_same_address ? $checkout_order->customer->email : $request->get('shipping_email'),
            'tel_cell' => $request->shipping_same_address ? $checkout_order->customer->tel_cell : preg_replace('/[^\d+]/', '', $geust_shipping_tell),
            'company_name' => $request->shipping_same_address ? $checkout_order->customer->company_name : $request->get('shipping_company_name'),
            'city' => $request->shipping_same_address ? $checkout_order->customer->city : $request->get('shipping_city'),
            'state' => $request->shipping_same_address ? $checkout_order->customer->state : $request->get('shipping_state'),
            'country' => $request->shipping_same_address ? $checkout_order->customer->country : $request->get('shipping_country'),
            'country_name' => $request->shipping_same_address ? $checkout_order->customer->country_name : $this->countries($request->get('shipping_country')),
            'postal_code' => $request->shipping_same_address ? $checkout_order->customer->postal_code : $request->get('shipping_postal_code'),
            'address' => $request->shipping_same_address ? $checkout_order->customer->address : $request->get('shipping_address_line_1'),
            'address_2' => $request->shipping_same_address ? $checkout_order->customer->address_2 : $request->get('shipping_address_line_2'),
            'shipping_method' => $request->get('shipping_method'),
            'shipping_method_data' => $request->get('shipping_method') ? $shipping_method : '',
            'shipping_same_address' => $request->get('shipping_same_address') ?? 0,
            'shipping_amount' => $shipping_amount,
            'shipping_amount_local' => $location_data ? $shipping_amount * $location_data->currency_rate : 0,
            'user_shipping_id' => $request->get('user_shipping_id'),
            'tax' => $request->tax ?? null,
            'user_shipping' => UserShipping::where('id', $request->get('user_shipping_id'))->first(),
        ];
        if ($request->payment_method == 'pay_by_stripe_card' && empty($cart->order)) {
            if (!$request->payment_same_as_contact) {
                if (Auth::user()) {
                    $validator = Validator::make($request->all(), [
                        'user_billing_id' => 'required|integer|exists:user_billings,id',
                    ], [
                        'user_billing_id.required' => 'The user billing required',
                    ]);
                } else {
                    $validator = Validator::make($request->all(), [
                        'payment_first_name' => 'required|string|max:255',
                        'payment_last_name' => 'required|string|max:255',
                        'payment_email' => 'required|string|email|max:255|unique:user_billings,email',
                        'payment_tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
                        // 'payment_tel_cell_country_code' => 'nullable|string',
                        'payment_company_name' => 'nullable|string',
                        'payment_city' => 'nullable|string',
                        'payment_state' => 'nullable|string',
                        'payment_country' => 'nullable|string',
                        'payment_postal_code' => 'nullable|integer',
                        'payment_address_line_1' => 'nullable|string',
                        'payment_address_line_2' => 'nullable|string',
                    ]);
                }
                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->messages(),
                    ], 400);
                }
            }
        }
        $payment = (object)[
            'first_name' => $request->payment_same_as_contact ? $checkout_order->customer->first_name : $request->get('payment_first_name'),
            'last_name' => $request->payment_same_as_contact ? $checkout_order->customer->last_name : $request->get('payment_last_name'),
            'email' => $request->payment_same_as_contact ? $checkout_order->customer->email : $request->get('payment_email'),
            'tel_cell' => $request->payment_same_as_contact ? $checkout_order->customer->tel_cell : preg_replace('/[^\d+]/', '', $request->get('payment_tel_cell')),
            // 'tel_cell_country_code' => $request->payment_same_as_contact ? $checkout_order->customer->tel_cell_country_code :  $payment_tel_cell_country_code ?? null,
            'company_name' => $request->payment_same_as_contact ? $checkout_order->customer->company_name : $request->get('payment_company_name'),
            'city' => $request->payment_same_as_contact ? $checkout_order->customer->city : $request->get('payment_city'),
            'state' => $request->payment_same_as_contact ? $checkout_order->customer->state : $request->get('payment_state'),
            'country' => $request->payment_same_as_contact ? $checkout_order->customer->country : $request->get('payment_country'),
            'country_name' => $request->payment_same_as_contact ? $checkout_order->customer->country_name : $this->countries($request->get('payment_country')),
            'postal_code' => $request->payment_same_as_contact ? $checkout_order->customer->postal_code : $request->get('payment_postal_code'),
            'address' => $request->payment_same_as_contact ? $checkout_order->customer->address : $request->get('payment_address_line_1'),
            'address_2' => $request->payment_same_as_contact ? $checkout_order->customer->address_2 : $request->get('payment_address_line_2'),
            'payment_method' => $request->get('payment_method'),
            'pay_by_squadco' => $request->get('pay_by_squadco'),
            'payment_same_as_contact' => $request->get('payment_same_as_contact') ?? 0,
            'user_billing_id' => $request->get('user_billing_id'),
            'user_billing' => UserBilling::where('id', $request->get('user_billing_id'))->first(),
            'payment_option' => $request->payment_option,
        ];
        if ($checkout_order && $checkout_order->customer_status && $checkout_order->shipping_status && $request->place_order) {
            $validator = Validator::make($request->all(), [
                'payment_method' => 'nullable|string',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->messages(),
                ], 400);
            }
            $make_payment = true;
            $payment_status = true;
        }
        if ($checkout_order && $checkout_order->customer_status) {
            $customer_status = true;
            $shipping_tab_status = true;
        }
        if ($checkout_order && $checkout_order->shipping_status) {
            $shipping_status = true;
            $payment_tab_status = true;
        }
        if ($checkout_order && $checkout_order->payment_status) {
            $payment_status = true;
            $payment_tab_status = true;
        }
        $checkout_order = (object)[
            'customer' => $customer,
            'customer_tab_status' => true,
            'customer_status' => $customer_status,
            'shipping' => $shipping,
            'shipping_methods' => $checkout_order->shipping_methods ?? $this->getEasyPostShipping(),
            'shipping_tab_status' => $shipping_tab_status,
            'shipping_status' => $shipping_status,
            'payment' => $payment ?? null,
            'payment_tab_status' => $payment_tab_status,
            'payment_status' => $payment_status,
            'location_data' => $location_data,
            'cart' => Session::get('cart'),
        ];
        Session::put([
            'checkout_order' => $checkout_order,
        ]);
        if ($checkout_order->shipping_methods && $checkout_order->shipping_methods['status'] == 400) {
            //   dd($checkout_order->shipping_methods);
            return response()->json([
                'status' => 'error',
                'message' => 'This address shipment is not available.',
            ], 400);
        }

        $site_info = Setting::first();
        $maximum_amount = $site_info->price_range;
        // dd($maximum_amount);
        if ($cart && $cart->grand_total > $maximum_amount) {
            $this->makeOrder();
            return ['cart_amount' => $cart->grand_total, 'maximum_amount' => $maximum_amount, 'page_slug' => $contacTeam->slug];
        }
        if ($request->payment_option == 'pay_later') {
            if ($cart) {
                $this->makeOrder();
                return ['cart_amount' => $cart->grand_total, 'maximum_amount' => $maximum_amount, 'page_slug' => $contacTeam->slug];
            }
        } else {
            $validator = Validator::make($request->all(), [
                'payment_method' => 'required|string',
            ]);
        }
        if ($checkout_order && $checkout_order->customer_status && $checkout_order->shipping_status && $checkout_order->payment_status && $make_payment) {
            if (auth()->user()) {
            } else {
                $validator = Validator::make($request->all(), [
                    'shipping_first_name' => 'required|string|max:255',
                    'shipping_last_name' => 'required|string|max:255',
                    'shipping_email' => 'required|string|email|',
                    'shipping_tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
                    // 'shipping_tel_cell_country_code' => 'nullable|string',
                    'shipping_company_name' => 'nullable|string',
                    'shipping_city' => 'nullable|string',
                    'shipping_state' => 'nullable|string',
                    'shipping_country' => 'nullable|string',
                    'shipping_postal_code' => 'nullable|integer',
                    'shipping_address_line_1' => 'nullable|string',
                    'shipping_address_line_2' => 'nullable|string',
                    'shipping_method' => 'required|string',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->messages(),
                    ], 400);
                }
            }
            if ($request->payment_method == 'pay_by_stripe_card') {
                $redirect_url = $this->stripeCard($cart);
            }
            if ($request->payment_method == 'pay_by_paypal') {
                $redirect_url = $this->payWithPaypal();
            }
            if ($request->payment_method == 'pay_by_flutter') {
                $redirect_url = $this->flutterWave($cart);
            }
            if ($request->payment_method == 'pay_by_squadco') {
                $validator = Validator::make($request->all(), [
                    'pay_by_squadco' => 'required|string',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->messages(),
                    ], 400);
                }
                if ($request->pay_by_squadco == 'pay_by_squadco_usd' || $request->pay_by_squadco == 'pay_by_squadco_local') {
                    if ($request->split_payment == 'split_payment') {
                        $validator = Validator::make($request->all(), [
                            'split_payment_amount' => 'required|string',
                        ], [
                            'split_payment_amount.required' => 'You have to choose split payment option',
                        ]);
                        if ($validator->fails()) {
                            return response()->json([
                                'messages' => $validator->messages(),
                            ], 400);
                        }
                    }
                    $data = $this->squadcoPay($request);
                    return response()->json($data, 200);
                }
            }
            if ($request->payment_method == 'btc') {
                $this->makeOrder();
                $data = $this->initiateBTCPay($request);
                return response()->json([
                    'redirect_url' => $data,
                ], 200);
            }
            if ($request->payment_method == 'flutterwave') {
                $this->makeOrder();
                $data = $this->initiateFlutterPay($request,  $payment, $customer, $location_data);

                return response()->json([
                    'redirect_url' => $data,
                ], 200);
            }
            return response()->json([
                'redirect_url' => $redirect_url,
            ], 200);
        }

        $cart = Session::get('cart');
        $currency_rate = 0;
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
            $currency_rate = $location_data->currency_rate;
        }
        $shippingAmount = $checkout_order->shipping->shipping_amount ?? 0;
        if (isset($page->cart->freeShipping)) {
            if ($page->cart->freeShipping) {
                $shippingAmount = 0;
            }
        }
        $shippingAmount_local = $shippingAmount * $currency_rate;
        if (Session::has('cart')) {
            $total = round($cart->total, 2);
            $total_local = round($cart->total_local, 2);
            $grand_total = $total + $shippingAmount;
            $payment_method = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_squadco');
            if ($payment_method->discount_price) {
                if ($payment_method->discount_type == 1) {
                    $discount_amt = ($total_local * $payment_method->discount_price) / 100;
                }
            }
            if ($shippingAmount_local > 0) {
                $grand_total_local = $cart->grand_total_local + $shippingAmount_local;
            } else {
                $grand_total_local = $cart->grand_total_local;
            }
            $calculated_grand_total = $grand_total;
            $calculated_grand_total_local = $grand_total_local;
            if (isset($cart->totalDiscount_local) && $cart->totalDiscount_local > 0 && isset($cart->totalDiscount) && $cart->totalDiscount > 0) {
                $calculated_grand_total = $calculated_grand_total - $cart->totalDiscount; // Subtract discount for calculation
                $calculated_grand_total_local = $calculated_grand_total_local - $cart->totalDiscount_local; // Subtract discount for calculation
                $cart->amount_50_50 = getDefaultCurrencySymbol() . ($calculated_grand_total * 0.5) . ' - $' . ($calculated_grand_total - round($calculated_grand_total * 0.5, 2));
                $cart->amount_50_50_local = $local_currency_symbol . (number_format($calculated_grand_total_local * 0.5, 2)) . ' - ' . $local_currency_symbol . number_format($calculated_grand_total_local - round($calculated_grand_total_local * 0.5, 2), 2);
                $cart->amount_60_40 = getDefaultCurrencySymbol() . ($calculated_grand_total * 0.6) . ' - $' . ($calculated_grand_total - round($calculated_grand_total * 0.6, 2));
                $cart->amount_60_40_local = $local_currency_symbol . (number_format(($calculated_grand_total_local * 0.6), 2)) . ' - ' . $local_currency_symbol . (number_format(($calculated_grand_total_local - $calculated_grand_total_local * 0.6), 2));
                $cart->amount_80_20 = getDefaultCurrencySymbol() . ($calculated_grand_total * 0.8) . ' - $' . ($calculated_grand_total - $calculated_grand_total * 0.8);
                $cart->amount_80_20_local = $local_currency_symbol . (number_format(($calculated_grand_total_local * 0.8), 2)) . ' - ' . $local_currency_symbol . (number_format(($calculated_grand_total_local - round($calculated_grand_total_local * 0.8, 2)), 2));
            } else {
                $cart->amount_50_50 = getDefaultCurrencySymbol() . ($grand_total * 0.5) . ' - $' . ($grand_total - round($grand_total * 0.5, 2));
                $cart->amount_50_50_local = $local_currency_symbol . (number_format($grand_total_local * 0.5, 2)) . ' - ' . $local_currency_symbol . number_format($grand_total_local - round($grand_total_local * 0.5, 2), 2);
                $cart->amount_60_40 = getDefaultCurrencySymbol() . ($grand_total * 0.6) . ' - $' . ($grand_total - round($grand_total * 0.6, 2));
                $cart->amount_60_40_local = $local_currency_symbol . (number_format(($grand_total_local * 0.6), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - $grand_total_local * 0.6), 2));
                $cart->amount_80_20 = getDefaultCurrencySymbol() . ($grand_total * 0.8) . ' - $' . ($grand_total - $grand_total * 0.8);
                $cart->amount_80_20_local = $local_currency_symbol . (number_format(($grand_total_local * 0.8), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - round($grand_total_local * 0.8, 2)), 2));
            }
        }

        $user_shippings = array();
        $user_billings = array();
        if (auth()->user()) {
            $user_shippings = UserShipping::where('user_id', auth()->user()->id)->get();
            $user_billings = UserBilling::where('user_id', auth()->user()->id)->get();
        }
        $location_data = Session::get('shop_logged_data');
        $shipping_methods  = $this->getEasyPostShipping($request);
        Session::put('cart', $cart);
        Session::put(['cart' => $cart]);
        Session::save();
        $shipping_methods = $checkout_order->shipping_methods;
        $rates = $shipping_methods['shipping_rates'] ?? [];

        if (array_keys($rates) !== range(0, count($rates) - 1)) {
            $rates = [$rates]; // Normalize to array of arrays if associative
        }

        $international_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === '1GG' || strtoupper($method['service']) === 'P');
        })->sortBy(function ($method) {
            return strtoupper($method['service']) === '1GG' ? 0 : 1;
        });

        $domestic_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === 'N' || strtoupper($method['service']) == 'STANDARD');
        });
        return view('web.jquery_live.checkout_order', compact('checkout_order', 'international_methods', 'domestic_methods', 'countries', 'location_data', 'cart', 'user_shippings', 'user_billings', 'shipping_methods'));
    }

    public function postCheckout2(Request $request)
    {
        // dd($request->all());
        $contacTeam = Page::where('page_key', 'contactTeam')->first();
        $location_data = Session::get('shop_logged_data');
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        //  $this->calculateOptimalBox($cart);
        $countries = $this->countries();
        $thank_page = Page::where('page_key', 'thank_you')->first();
        // Check if grand_total exceeds 160
        $local_currency_symbol = $location_data ? $location_data->currency_symbol : '';
        $customer_status = false;
        $shipping_tab_status = false;
        $shipping_status = false;
        $payment_tab_status = false;
        $payment_status = false;
        $make_payment = false;
        $expedited_method = null;
        $priority_method = null;
        $standard_method = null;
        if ($request->continue_shipping && empty($cart->order)) {
            if (Auth::user()) {
                $validator = Validator::make($request->all(), [
                    'guest_first_name' => 'required|string|max:255',
                    'guest_last_name' => 'required|string|max:255',
                    'guest_email' => 'nullable|email',
                    "guest_tel_cell" => "required|string|max:20",
                    // 'guest_tel_cell_country_code' => 'nullable|string',
                    'guest_company_name' => 'nullable|string',
                    'guest_city' => 'nullable|string',
                    'guest_state' => 'nullable|string',
                    'guest_country' => 'nullable|string',
                    'guest_postal_code' => 'nullable|integer',
                    'guest_address_line_1' => 'nullable|string',
                    'guest_address_line_2' => 'nullable|string',
                    'email_notifications' => 'nullable|boolean',
                    'sms_notifications' => 'nullable|boolean',
                ]);
            } else {
                // dd($request->guest_tel_cell);
                $validator = Validator::make($request->all(), [
                    'guest_first_name' => 'required|string|max:255',
                    'guest_last_name' => 'required|string|max:255',
                    'guest_email' => 'nullable|email',
                    "guest_tel_cell" => "required|string|max:20",
                    // 'guest_tel_cell_country_code' => 'nullable|string',
                    'guest_company_name' => 'nullable|string',
                    'guest_city' => 'nullable|string',
                    'guest_state' => 'nullable|string',
                    'guest_country' => 'nullable|string',
                    'guest_postal_code' => 'nullable|integer',
                    'guest_address_line_1' => 'nullable|string',
                    'guest_address_line_2' => 'nullable|string',
                    'email_notifications' => 'nullable|boolean',
                    'sms_notifications' => 'nullable|boolean',
                ]);
            }
            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->messages(),
                ], 400);
            }
            $customer_status = true;
            $shipping_tab_status = true;
        }
        $guest_tell = $request->get('guest_tel_cell');
        $customer = (object)[
            'first_name' => $request->get('guest_first_name'),
            'last_name' => $request->get('guest_last_name'),
            'email' => Auth::user() ? Auth::user()->email : $request->get('guest_email'),
            // 'tel_cell_country_code' => $guest_tel_cell_country_code ?? null,
            'tel_cell' => preg_replace('/[^\d+]/', '', $guest_tell),
            'company_name' => $request->get('guest_company_name'),
            'city' => $request->get('guest_city'),
            'state' => $request->get('guest_state'),
            'country' => $request->get('guest_country'),
            'country_name' => $this->countries($request->get('guest_country')),
            'postal_code' => $request->get('guest_postal_code'),
            'address' => $request->get('guest_address_line_1'),
            'address_2' => $request->get('guest_address_line_2'),
            'email_notifications' => $request->get('email_notifications') ?? 0,
            'sms_notifications' => $request->get('sms_notifications') ?? 0,
        ];
        $shipping_amount = 0;
        $shipping_id = $request->get('shipping_method');
        $shipping_method = [];
        foreach ($shipping_id ?? [] as $id) {
            $shipping_method[] = $this->getEasyPostShipping($request, $id);
        }
        if ($request->continue_payment && empty($cart->order)) {
            if ($request->shipping_same_address) {
                $validator = Validator::make($request->all(), [
                    'shipping_method' => 'required|array',
                    'shipping_method.*' => 'string',
                ]);
            } else {
                if (Auth::user()) {
                    $validator = Validator::make($request->all(), [
                        'user_shipping_id' => 'required|integer|exists:user_shippings,id',
                        'shipping_method' => 'required|array',
                        'shipping_method.*' => 'string',
                    ], [
                        'user_shipping_id.required' => 'Add your Shipping details'
                    ]);
                } else {
                    $validator = Validator::make($request->all(), [
                        'shipping_first_name' => 'required|string|max:255',
                        'shipping_last_name' => 'required|string|max:255',
                        'shipping_email' => 'required|string|email',
                        'shipping_tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
                        // 'shipping_tel_cell_country_code' => 'nullable|string',
                        'shipping_company_name' => 'nullable|string',
                        'shipping_city' => 'nullable|string',
                        'shipping_state' => 'nullable|string',
                        'shipping_country' => 'nullable|string',
                        'shipping_postal_code' => 'nullable|integer',
                        'shipping_address_line_1' => 'nullable|string',
                        'shipping_address_line_2' => 'nullable|string',
                        'shipping_method' => 'required|array',
                        'shipping_method.*' => 'string',
                    ]);
                }
            }
            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->messages(),
                ], 400);
            }
            $customer_status = true;
            $shipping_tab_status = true;
            $shipping_status = true;
            $payment_tab_status = true;
        }
        //   dd($checkout_order->customer->tel_cell);
        $shipping_amount = 0;
        $geust_shipping_tell = $request->get('shipping_tel_cell');
        if ($request->tax) {
            foreach ($shipping_method as $method) {
                if (isset($method['shipping_rates']['service']) && $method['shipping_rates']['service'] == 'P') {
                    $retail_rate = $method['shipping_rates']['retail_rate'] ?? 0;
                    $shipping_amount += $retail_rate + $request->tax;
                } else {
                    $retail_rate = $method['shipping_rates']['retail_rate'] ?? 0;
                    $shipping_amount += $retail_rate;
                }
            }
        } else {
            foreach ($shipping_method as $method) {
                $retail_rate = $method['shipping_rates']['retail_rate'] ?? 0;
                $shipping_amount += $retail_rate;
            }
        }
        $shipping = (object)[
            'first_name' => $request->shipping_same_address ? $checkout_order->customer->first_name : $request->get('shipping_first_name'),
            'last_name' => $request->shipping_same_address ? $checkout_order->customer->last_name : $request->get('shipping_last_name'),
            'email' => $request->shipping_same_address ? $checkout_order->customer->email : $request->get('shipping_email'),
            'tel_cell' => $request->shipping_same_address ? $checkout_order->customer->tel_cell : preg_replace('/[^\d+]/', '', $geust_shipping_tell),
            'company_name' => $request->shipping_same_address ? $checkout_order->customer->company_name : $request->get('shipping_company_name'),
            'city' => $request->shipping_same_address ? $checkout_order->customer->city : $request->get('shipping_city'),
            'state' => $request->shipping_same_address ? $checkout_order->customer->state : $request->get('shipping_state'),
            'country' => $request->shipping_same_address ? $checkout_order->customer->country : $request->get('shipping_country'),
            'country_name' => $request->shipping_same_address ? $checkout_order->customer->country_name : $this->countries($request->get('shipping_country')),
            'postal_code' => $request->shipping_same_address ? $checkout_order->customer->postal_code : $request->get('shipping_postal_code'),
            'address' => $request->shipping_same_address ? $checkout_order->customer->address : $request->get('shipping_address_line_1'),
            'address_2' => $request->shipping_same_address ? $checkout_order->customer->address_2 : $request->get('shipping_address_line_2'),
            'shipping_method' => $request->get('shipping_method'),
            'shipping_method_data' => $request->get('shipping_method') ? $shipping_method : '',
            'shipping_same_address' => $request->get('shipping_same_address') ?? 0,
            'shipping_amount' => $shipping_amount,
            'shipping_amount_local' => $location_data ? $shipping_amount * $location_data->currency_rate : 0,
            'user_shipping_id' => $request->get('user_shipping_id'),
            'tax' => $request->tax ?? null,
            'user_shipping' => UserShipping::where('id', $request->get('user_shipping_id'))->first(),
        ];
        if ($request->payment_method == 'pay_by_stripe_card' && empty($cart->order)) {
            if (!$request->payment_same_as_contact) {
                if (Auth::user()) {
                    $validator = Validator::make($request->all(), [
                        'user_billing_id' => 'required|integer|exists:user_billings,id',
                    ], [
                        'user_billing_id.required' => 'The user billing required',
                    ]);
                } else {
                    $validator = Validator::make($request->all(), [
                        'payment_first_name' => 'required|string|max:255',
                        'payment_last_name' => 'required|string|max:255',
                        'payment_email' => 'required|string|email|max:255|unique:user_billings,email',
                        'payment_tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
                        // 'payment_tel_cell_country_code' => 'nullable|string',
                        'payment_company_name' => 'nullable|string',
                        'payment_city' => 'nullable|string',
                        'payment_state' => 'nullable|string',
                        'payment_country' => 'nullable|string',
                        'payment_postal_code' => 'nullable|integer',
                        'payment_address_line_1' => 'nullable|string',
                        'payment_address_line_2' => 'nullable|string',
                    ]);
                }
                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->messages(),
                    ], 400);
                }
            }
        }
        $payment = (object)[
            'first_name' => $request->payment_same_as_contact ? $checkout_order->customer->first_name : $request->get('payment_first_name'),
            'last_name' => $request->payment_same_as_contact ? $checkout_order->customer->last_name : $request->get('payment_last_name'),
            'email' => $request->payment_same_as_contact ? $checkout_order->customer->email : $request->get('payment_email'),
            'tel_cell' => $request->payment_same_as_contact ? $checkout_order->customer->tel_cell : preg_replace('/[^\d+]/', '', $request->get('payment_tel_cell')),
            // 'tel_cell_country_code' => $request->payment_same_as_contact ? $checkout_order->customer->tel_cell_country_code :  $payment_tel_cell_country_code ?? null,
            'company_name' => $request->payment_same_as_contact ? $checkout_order->customer->company_name : $request->get('payment_company_name'),
            'city' => $request->payment_same_as_contact ? $checkout_order->customer->city : $request->get('payment_city'),
            'state' => $request->payment_same_as_contact ? $checkout_order->customer->state : $request->get('payment_state'),
            'country' => $request->payment_same_as_contact ? $checkout_order->customer->country : $request->get('payment_country'),
            'country_name' => $request->payment_same_as_contact ? $checkout_order->customer->country_name : $this->countries($request->get('payment_country')),
            'postal_code' => $request->payment_same_as_contact ? $checkout_order->customer->postal_code : $request->get('payment_postal_code'),
            'address' => $request->payment_same_as_contact ? $checkout_order->customer->address : $request->get('payment_address_line_1'),
            'address_2' => $request->payment_same_as_contact ? $checkout_order->customer->address_2 : $request->get('payment_address_line_2'),
            'payment_method' => $request->get('payment_method'),
            'pay_by_squadco' => $request->get('pay_by_squadco'),
            'payment_same_as_contact' => $request->get('payment_same_as_contact') ?? 0,
            'user_billing_id' => $request->get('user_billing_id'),
            'user_billing' => UserBilling::where('id', $request->get('user_billing_id'))->first(),
            'payment_option' => $request->payment_option,
        ];
        if ($checkout_order && $checkout_order->customer_status && $checkout_order->shipping_status && $request->place_order) {
            $validator = Validator::make($request->all(), [
                'payment_method' => 'nullable|string',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->messages(),
                ], 400);
            }
            $make_payment = true;
            $payment_status = true;
        }
        if ($checkout_order && $checkout_order->customer_status) {
            $customer_status = true;
            $shipping_tab_status = true;
        }
        if ($checkout_order && $checkout_order->shipping_status) {
            $shipping_status = true;
            $payment_tab_status = true;
        }
        if ($checkout_order && $checkout_order->payment_status) {
            $payment_status = true;
            $payment_tab_status = true;
        }
        $location_data = array();
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
        }
        $checkout_order = (object)[
            'customer' => $customer,
            'customer_tab_status' => true,
            'customer_status' => $customer_status,
            'shipping' => $shipping,
            'shipping_methods' => $checkout_order->shipping_methods,
            'shipping_tab_status' => $shipping_tab_status,
            'shipping_status' => $shipping_status,
            'payment' => $payment ?? null,
            'payment_tab_status' => $payment_tab_status,
            'payment_status' => $payment_status,
            'location_data' => $location_data,
            'cart' => Session::get('cart'),
        ];
        Session::put([
            'checkout_order' => $checkout_order,
        ]);
        if ($checkout_order->shipping_methods && $checkout_order->shipping_methods['status'] == 400) {
            //   dd($checkout_order->shipping_methods);
            return response()->json([
                'status' => 'error',
                'message' => 'This address shipment is not available.',
            ], 400);
        }
        // dd($request->all());
        $site_info = Setting::first();
        $maximum_amount = $site_info->price_range;
        // dd($maximum_amount);
        if ($cart && $cart->grand_total > $maximum_amount) {
            $this->makeOrder();
            return ['cart_amount' => $cart->grand_total, 'maximum_amount' => $maximum_amount, 'page_slug' => $contacTeam->slug];
        }
        if ($request->payment_option == 'pay_later') {
            if ($cart) {
                $this->makeOrder();
                return ['cart_amount' => $cart->grand_total, 'maximum_amount' => $maximum_amount, 'page_slug' => $contacTeam->slug];
            }
        } else {
            $validator = Validator::make($request->all(), [
                'payment_method' => 'required|string',
            ]);
        }
        if ($checkout_order && $checkout_order->customer_status && $checkout_order->shipping_status && $checkout_order->payment_status && $make_payment) {
            if (auth()->user()) {
            } else {
                $validator = Validator::make($request->all(), [
                    'shipping_first_name' => 'required|string|max:255',
                    'shipping_last_name' => 'required|string|max:255',
                    'shipping_email' => 'required|string|email|',
                    'shipping_tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
                    // 'shipping_tel_cell_country_code' => 'nullable|string',
                    'shipping_company_name' => 'nullable|string',
                    'shipping_city' => 'nullable|string',
                    'shipping_state' => 'nullable|string',
                    'shipping_country' => 'nullable|string',
                    'shipping_postal_code' => 'nullable|integer',
                    'shipping_address_line_1' => 'nullable|string',
                    'shipping_address_line_2' => 'nullable|string',
                    'shipping_method' => 'required|string',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->messages(),
                    ], 400);
                }
            }
            if ($request->payment_method == 'pay_by_stripe_card') {
                $redirect_url = $this->stripeCard($cart);
            }
            if ($request->payment_method == 'pay_by_paypal') {
                $redirect_url = $this->payWithPaypal();
            }
            if ($request->payment_method == 'pay_by_flutter') {
                $redirect_url = $this->flutterWave($cart);
            }
            if ($request->payment_method == 'pay_by_squadco') {
                $validator = Validator::make($request->all(), [
                    'pay_by_squadco' => 'required|string',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->messages(),
                    ], 400);
                }
                if ($request->pay_by_squadco == 'pay_by_squadco_usd' || $request->pay_by_squadco == 'pay_by_squadco_local') {
                    if ($request->split_payment == 'split_payment') {
                        $validator = Validator::make($request->all(), [
                            'split_payment_amount' => 'required|string',
                        ], [
                            'split_payment_amount.required' => 'You have to choose split payment option',
                        ]);
                        if ($validator->fails()) {
                            return response()->json([
                                'messages' => $validator->messages(),
                            ], 400);
                        }
                    }
                    $data = $this->squadcoPay($request);
                    return response()->json($data, 200);
                }
            }
            if ($request->payment_method == 'btc') {
                $this->makeOrder();
                $data = $this->initiateBTCPay($request);
                return response()->json([
                    'redirect_url' => $data,
                ], 200);
            }
            return response()->json([
                'redirect_url' => $redirect_url,
            ], 200);
        }
        $cart = Session::get('cart');
        $currency_rate = 0;
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
            $currency_rate = $location_data->currency_rate;
        }
        $shippingAmount = $checkout_order->shipping->shipping_amount ?? 0;
        if (isset($page->cart->freeShipping)) {
            if ($page->cart->freeShipping) {
                $shippingAmount = 0;
            }
        }
        $shippingAmount_local = $shippingAmount * $currency_rate;
        if (Session::has('cart')) {
            $total = round($cart->total, 2);
            $total_local = round($cart->total_local, 2);
            $grand_total = $total + $shippingAmount;
            $payment_method = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_squadco');
            if ($payment_method->discount_price) {
                if ($payment_method->discount_type == 1) {
                    $discount_amt = ($total_local * $payment_method->discount_price) / 100;
                }
            }
            if ($shippingAmount_local > 0) {
                $grand_total_local = $cart->grand_total_local + $shippingAmount_local;
            } else {
                $grand_total_local = $cart->grand_total_local;
            }
            $calculated_grand_total = $grand_total;
            $calculated_grand_total_local = $grand_total_local;
            if (isset($cart->totalDiscount_local) && $cart->totalDiscount_local > 0 && isset($cart->totalDiscount) && $cart->totalDiscount > 0) {
                $calculated_grand_total = $calculated_grand_total - $cart->totalDiscount; // Subtract discount for calculation
                $calculated_grand_total_local = $calculated_grand_total_local - $cart->totalDiscount_local; // Subtract discount for calculation
                $cart->amount_50_50 = getDefaultCurrencySymbol() . ($calculated_grand_total * 0.5) . ' - $' . ($calculated_grand_total - round($calculated_grand_total * 0.5, 2));
                $cart->amount_50_50_local = $local_currency_symbol . (number_format($calculated_grand_total_local * 0.5, 2)) . ' - ' . $local_currency_symbol . number_format($calculated_grand_total_local - round($calculated_grand_total_local * 0.5, 2), 2);
                $cart->amount_60_40 = getDefaultCurrencySymbol() . ($calculated_grand_total * 0.6) . ' - $' . ($calculated_grand_total - round($calculated_grand_total * 0.6, 2));
                $cart->amount_60_40_local = $local_currency_symbol . (number_format(($calculated_grand_total_local * 0.6), 2)) . ' - ' . $local_currency_symbol . (number_format(($calculated_grand_total_local - $calculated_grand_total_local * 0.6), 2));
                $cart->amount_80_20 = getDefaultCurrencySymbol() . ($calculated_grand_total * 0.8) . ' - $' . ($calculated_grand_total - $calculated_grand_total * 0.8);
                $cart->amount_80_20_local = $local_currency_symbol . (number_format(($calculated_grand_total_local * 0.8), 2)) . ' - ' . $local_currency_symbol . (number_format(($calculated_grand_total_local - round($calculated_grand_total_local * 0.8, 2)), 2));
            } else {
                $cart->amount_50_50 = getDefaultCurrencySymbol() . ($grand_total * 0.5) . ' - $' . ($grand_total - round($grand_total * 0.5, 2));
                $cart->amount_50_50_local = $local_currency_symbol . (number_format($grand_total_local * 0.5, 2)) . ' - ' . $local_currency_symbol . number_format($grand_total_local - round($grand_total_local * 0.5, 2), 2);
                $cart->amount_60_40 = getDefaultCurrencySymbol() . ($grand_total * 0.6) . ' - $' . ($grand_total - round($grand_total * 0.6, 2));
                $cart->amount_60_40_local = $local_currency_symbol . (number_format(($grand_total_local * 0.6), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - $grand_total_local * 0.6), 2));
                $cart->amount_80_20 = getDefaultCurrencySymbol() . ($grand_total * 0.8) . ' - $' . ($grand_total - $grand_total * 0.8);
                $cart->amount_80_20_local = $local_currency_symbol . (number_format(($grand_total_local * 0.8), 2)) . ' - ' . $local_currency_symbol . (number_format(($grand_total_local - round($grand_total_local * 0.8, 2)), 2));
            }
        }
        $user_shippings = array();
        $user_billings = array();
        if (auth()->user()) {
            $user_shippings = UserShipping::where('user_id', auth()->user()->id)->get();
            $user_billings = UserBilling::where('user_id', auth()->user()->id)->get();
        }
        $location_data = Session::get('shop_logged_data');
        $shipping_methods  = $this->getEasyPostShipping($request);
        Session::put('cart', $cart);
        Session::put(['cart' => $cart]);
        Session::save();
        $shipping_methods = $checkout_order->shipping_methods;
        $rates = $shipping_methods['shipping_rates'] ?? [];

        if (array_keys($rates) !== range(0, count($rates) - 1)) {
            $rates = [$rates]; // Normalize to array of arrays if associative
        }

        $international_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === '1GG' || strtoupper($method['service']) === 'P');
        })->sortBy(function ($method) {
            return strtoupper($method['service']) === '1GG' ? 0 : 1;
        });

        $domestic_methods = collect($rates)->filter(function ($method) {
            return isset($method['service']) && (strtoupper($method['service']) === 'N' || strtoupper($method['service']) == 'STANDARD');
        });
        return view('web.jquery_live.checkout_order', compact('checkout_order', 'international_methods', 'domestic_methods', 'countries', 'location_data', 'cart', 'user_shippings', 'user_billings', 'shipping_methods'));
    }
    public function payWithPaypal()
    {
        // Retrieve essential data with null checks
        $checkout_page = Page::where('page_key', 'checkout')->firstOrFail();
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        // Validate critical data
        if (!$cart || !$checkout_order) {
            throw new \Exception('Cart or checkout order is missing.');
        }
        $this->makeOrder();
        // Calculate cart amount with null-safe operations
        $cart_amount = max(0, $cart->grand_total - ($cart->paid_grand_total ?? 0));
        $item_array = [];
        $description = [];
        // Validate cart items
        if (empty($cart->items)) {
            throw new \Exception('No items in the cart.');
        }
        // Ensure cart->items is iterable
        if (!is_array($cart->items) && !$cart->items instanceof \Traversable) {
            throw new \Exception('Cart items are not iterable.');
        }
        // Create the item list with comprehensive error handling
        foreach ($cart->items as $value) {
            // Ensure all required fields exist
            $itemName = trim(
                ($value->product_item_sku ?? 'N/A') . '-' .
                    ($value->product_title ?? '') . ' ' .
                    ($value->title ?? '') . ' ' .
                    ($value->sub_title ?? '')
            );
            $description[] = $itemName;
            // Validate item price and quantity
            $itemPrice = max(0, floatval($value->price ?? 0));
            $itemQuantity = max(1, intval($value->quantity ?? 1));
            $item = new Item();
            $item->setName($itemName)
                ->setCurrency('USD')
                ->setQuantity(max(1, intval($value->quantity ?? 1)))
                ->setPrice(number_format(max(0, floatval($value->price ?? 0)), 2, '.', ''));
            $item_array[] = $item;
        }
        // Handle discounts with comprehensive checks
        $discount_amt = 0;
        $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal');
        if (($cart->paid_grand_total ?? 0) == 0 && $transactionMethod) {
            $discountPrice = $transactionMethod->discount_price ?? 0;
            if ($discountPrice > 0) {
                $discount_amt = $transactionMethod->discount_type == 1
                    ? ($cart->total * $discountPrice) / 100
                    : $discountPrice;
            }
            $cart_amount = max(0, $cart_amount - $discount_amt);
        }
        // Validate shipping information
        $shippingAmount = max(0, floatval(optional($checkout_order->shipping)->shipping_amount ?? 0));
        // Prepare the payer
        $payer = new Payer();
        $payer->setPaymentMethod('paypal');
        // Prepare the amount details with strict validation
        $amountDetails = [
            'subtotal' => number_format(max(0, $cart->total ?? 0), 2, '.', ''),
            'tax' => '0.00',
            'shipping' => number_format($shippingAmount, 2, '.', ''),
            'discount' => number_format($discount_amt, 2, '.', ''),
        ];
        $totalAmount = max(0.01, round(
            ($cart->total ?? 0) + $shippingAmount - $discount_amt,
            2
        ));
        $amount = new Amount();
        $amount->setCurrency('USD')
            ->setDetails($amountDetails)
            ->setTotal(number_format($totalAmount, 2, '.', ''));
        // Prepare the item list
        $item_list = new ItemList();
        $item_list->setItems($item_array);
        // Create the transaction
        $transaction = new Transaction();
        $transaction->setAmount($amount)
            ->setItemList($item_list)
            ->setDescription(implode('|', array_filter($description)));
        // Set the redirect URLs
        $redirect_urls = new RedirectUrls();
        $redirect_urls->setReturnUrl(URL::route('paypal'))
            ->setCancelUrl(URL::route('paypal'));
        // Create the payment
        $payment = new Payment();
        $payment->setIntent('sale')
            ->setPayer($payer)
            ->setRedirectUrls($redirect_urls)
            ->setTransactions([$transaction]);
        // Extensive logging for debugging
        \Log::info('PayPal Payment Request Details', [
            'cart_amount' => $cart_amount,
            'total_items' => count($item_array),
            'discount_amount' => $discount_amt,
            'shipping_amount' => $shippingAmount,
            'payment_details' => $payment->toArray()
        ]);
        // $payment->create($this->_api_context);
        // dd($payment);
        try {
            // Create payment with error handling
            $payment->create($this->_api_context);
            // Store PayPal payment ID in session
            Session::put('paypal_payment_id', $payment->getId());
            // Find approval URL
            // $redirect_url = null;
            foreach ($payment->getLinks() as $link) {
                if ($link->getRel() == 'approval_url') {
                    $redirect_url = $link->getHref();
                    break;
                }
            }
            //dd($redirect_url);
            // Redirect or handle errors
            // Ensure it's a string and redirecty
            return $redirect_url;
        } catch (PPConnectionException $ex) {
            // Detailed connection error logging
            \Log::error('PayPal Connection Error', [
                'error_message' => $ex->getMessage(),
                'error_data' => $ex->getData(),
                'debug_mode' => \Config::get('app.debug')
            ]);
        } catch (\Exception $ex) {
            // Catch-all for any other unexpected errors
            \Log::error('Unexpected PayPal Payment Error', [
                'error_message' => $ex->getMessage(),
                'error_trace' => $ex->getTraceAsString()
            ]);
        }
    }
    public function getPaymentPaypalStatus(Request $request)
    {
        $thank_page = Page::where('page_key', 'thank_you')->first();
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        $payment_id = Session::get('paypal_payment_id');
        Session::forget('paypal_payment_id');
        if (empty($request->input('PayerID')) || empty($request->input('token'))) {
            Session::flash('error', 'Payment failed');
            return Redirect::route('page', $checkout_page->slug);
        }
        $payment = Payment::get($payment_id, $this->_api_context);
        $execution = new PaymentExecution();
        $execution->setPayerId($request->input('PayerID'));
        $result = $payment->execute($execution, $this->_api_context);
        $transactions = $result->getTransactions();
        $relatedResources = $transactions[0]->getRelatedResources();
        $sale = $relatedResources[0]->getSale();
        $saleId = $sale->getId();
        $discount_amt = 0;
        if ($cart->paid_grand_total == 0) {
            $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal');
            if ($transactionMethod && $transactionMethod->discount_price) {
                if ($transactionMethod->discount_type == 1) {
                    $discount_amt = ($cart->total * $transactionMethod->discount_price) / 100;
                } else {
                    $discount_amt = $transactionMethod->discount_price;
                }
            }
        }
        if ($result->getState() == 'approved') {
            // Add checkout_order to the makeCollection method
            $this->makeCollection(
                $cart->grand_total - $discount_amt,
                app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal')->id,
                $saleId,
                2,
                $discount_amt,
            );
            // Redirect to thank you page
            return Redirect::route('page', $thank_page->slug);
        }
        Session::flash('error', 'Payment failed !!');
        return Redirect::route('page', $checkout_page->slug);
    }
    public function getLocationData($location_id)
    {
        $shop_currency = '';
        if ($location_id != 'US' && $location_id != 'us') {
            $shop_currency = 'Payment option in <strong>' . $this->getLocalCurrencyName($location_id) . '</strong> available at checkout';
        }
        $country_name = $this->countries($location_id);
        $data = (object)[
            'image_url' => asset('images/flags/' . strtolower($location_id) . '.png'),
            'country_name' => 'Your location is set to ' . $this->countries($location_id),
            'currency' => $shop_currency,
            'currency_name' => $this->getLocalCurrencyName($location_id),
        ];
        $shop_logged_data = (object)[
            'location_popup' => false,
            'country' => $location_id,
            'country_name' => $country_name,
            'currency' => $shop_currency,
            'currency_rate' => $this->getLocalCurrencyRate($location_id),
            'currency_symbol' => $this->getLocalCurrencySymbol($location_id),
            'country_code' => $this->getLocalCountryCode($location_id),
            'country_short_code' => $this->getLocalCountryShortCode($location_id),
            'banks' => $this->getBanks($location_id),
            'rate_adjustment_last_updated' => $this->getLatestCurrencyAdjustmentTime($country_name)
        ];
        // dd($shop_logged_data);
        //Session::flush();
        Session::forget('shop_logged_data');
        Session::put([
            'shop_logged_data' => $shop_logged_data,
        ]);
        return response()->json($data, 200);
    }
    public function siteLocationData(Request $request)
    {
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            // dd($cart);
            $location_data = Session::get('shop_logged_data');
            $past_grand_total = $cart->grand_total;
            $removed_items_total = 0;
            foreach ($cart->items as $value) {

                $cart_items = $value->cart_items;
                // Temporary array to hold filtered items
                $filtered_items = [];
                foreach ($cart_items as $key => $cart_item) {
                    // dd($cart_item->location_id);

                    if ($cart_item->location_id != 'US' && $request->location_country != $cart_item->location_id) {
                        // Add to removed items total
                        $removed_items_total += $cart_item->price;
                    } else {
                        // Add valid items to the filtered array
                        $filtered_items[$key] = $cart_item;
                    }
                }
                // Update the original cart items with the filtered ones
                $value->cart_items = $filtered_items;
                $value->quantity = count($value->cart_items);
            }
            // dd($removed_items_total, $past_grand_total);
            $cart = Session::get('cart');
            $total_price = 0;
            $local_total_price = 0;
            $quantity = 0;
            // dd($cart->items);
            foreach ($cart->items as $key => $value) {
                // Remove items with a quantity of 0
                if ($value->quantity == 0) {
                    unset($cart->items[$key]);
                    continue; // Skip further processing for this item
                }
                // Calculate total quantity
                $quantity += $value->quantity;
                // Calculate unit total price
                $value->unit_total_price = $this->itemTotalPrice($value->cart_items);
                $total_price += $value->unit_total_price;
                // If shop logged in data is available, calculate local price
                if (Session::has('shop_logged_data')) {
                    $value->local_unit_total_price = ($value->quantity * $value->price) * $location_data->currency_rate;
                    $local_total_price += ($value->unit_total_price * $location_data->currency_rate);
                }
                // Set condition and condition type
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                $value->condition_type = strtolower(str_replace([' ', ','], '', $value->condition));
                // Set SKU details and URL
                $value->sku_details = $this->makeDetails($value->cart_items);
                $value->sku_details_url = $this->makeDetailsUrl($value->cart_items);
                // Calculate stock availability based on location
                $site_data = Session::get('shop_logged_data');
                $site_info = Setting::first();
                $selectedCountry = $site_data->country ?? $site_info->country;
                $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
                $storeIds = $this->getEligibleStores($selectedCountry);
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(
                        ProductItem::where('product_id', $value->product_id)
                            ->where('status', 1)
                            ->whereIn('store_id', $storeIds)->get(),
                        $value->cart_group
                    );
                } else {
                    $value->available_item = $this->stockCalculation(
                        ProductItem::where('product_id', $value->product_id)
                            ->where('status', 1)
                            ->get(),
                        $value->cart_group
                    );
                }
            }
            // dd($cart);
            $cart->quantity = $quantity;
            $cart->total = round($total_price, 2);
            if ($request->location_country == 'US') {
                $cart->total_local = 0;
            } else {
                $cart->total_local = round($local_total_price, 2);
            }
            $cart->grand_total = $past_grand_total - $removed_items_total;
            // dd($cart);
            // Update the cart session
            Session::put('cart', $cart);
        }
        //dd($request->all());
        if ($request->location_country) {

            $shop_currency = '';
            if ($request->location_country != 'US' && $request->location_country != 'us') {
                $shop_currency = 'Payment option in <strong>' . $this->getLocalCurrencyName($request->location_country) . '</strong> available at checkout';
            }
            $country_name = '';
            // if ($request->location_country) {
            $country_name = 'Your location is set to ' . $this->countries($request->location_country);
            // }
            // $shop_logged_data = (object)[
            //     'location_popup' => false,
            //     'country' => $request->location_country,
            //     'country_name' => $country_name,
            //     'currency' => $shop_currency,
            //     'currency_rate' => $this->getLocalCurrencyRate($request->location_country),
            //     'currency_symbol' => $this->getLocalCurrencySymbol($request->location_country),
            //     'country_code' => $this->getLocalCountryCode($request->location_country),
            //     'country_short_code' => $this->getLocalCountryShortCode($request->location_country),
            //     'banks' => $this->getBanks($request->location_country),
            // ];
            // // dd($shop_logged_data);
            // Session::flush();
            // Session::put([
            //     'shop_logged_data' => $shop_logged_data,
            // ]);
            $location_data = Session::get('shop_logged_data');
            // dd($location_data);
            return redirect()->back();
            // return response()->json($location_data, 200);
        } else {
            $location_data = $request->ipinfo->all;
            if ($location_data && !empty($location_data['country_name'])) {
                $shop_currency = '';
                if ($location_data['country'] != 'US' && $location_data['country']  != 'us') {
                    $shop_currency = 'Payment option in <strong>' . $this->getLocalCurrencyName($location_data['country']) . '</strong> available at checkout';
                }
                $shop_logged_data = (object)[
                    'location_popup' => true,
                    'country' => $location_data['country'],
                    'country_name' => $location_data['country'],
                    'currency' => $shop_currency,
                    'currency_rate' => $this->getLocalCurrencyRate($location_data['country']),
                    'currency_symbol' => $this->getLocalCurrencySymbol($location_data['country']),
                    'country_code' => $this->getLocalCountryCode($location_data['country']),
                    'country_short_code' => $this->getLocalCountryShortCode($location_data['country']),
                    'rate_adjustment_last_updated' => $this->getLatestCurrencyAdjustmentTime($location_data['country'])
                ];

                Session::flush();
                Session::put([
                    'shop_logged_data' => $shop_logged_data,
                ]);
                $location_data = Session::get('shop_logged_data');
                return redirect()->back();
            }
        }
    }
    public function makeOrder()
    {
        \Log::info('order function started');
        // dd($checkout_order);
        $auth_user = Auth()->user();
        // $location_data = Session::get('shop_logged_data');
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        if (Session::has('checkout_order')) {
            if (Auth::user()) {
                $user = User::findOrFail(Auth::user()->id);
                $user->first_name = $checkout_order->customer->first_name;
                $user->last_name = $checkout_order->customer->last_name;
                // $user->tel_cell_country_code = $checkout_order->customer->tel_cell_country_code;
                $user->tel_cell = $checkout_order->customer->tel_cell;
                $user->company_name = $checkout_order->customer->company_name;
                $user->city = $checkout_order->customer->city;
                $user->state = $checkout_order->customer->state;
                $user->country = $checkout_order->customer->country;
                $user->postal_code = $checkout_order->customer->postal_code;
                $user->address = $checkout_order->customer->address;
                $user->address_2 = $checkout_order->customer->address_2;
                $user->email_notifications = $checkout_order->customer->email_notifications ?? 0;
                $user->sms_notifications = $checkout_order->customer->sms_notifications ?? 0;
                $user->update();
            } else {
                $user = User::where('email', $checkout_order->customer->email)->first();
                if (empty($user)) {
                    $user = User::where('email', $checkout_order->customer->email)->first();
                }
                if (empty($user)) {
                    $user = new User;
                    $user->first_name = $checkout_order->customer->first_name;
                    $user->last_name = $checkout_order->customer->last_name;
                    $user->email = $checkout_order->customer->email;
                    // $user->tel_cell_country_code = $checkout_order->customer->tel_cell_country_code;
                    $user->tel_cell = $checkout_order->customer->tel_cell;
                    $user->company_name = $checkout_order->customer->company_name;
                    $user->city = $checkout_order->customer->city;
                    $user->state = $checkout_order->customer->state;
                    $user->country = $checkout_order->customer->country;
                    $user->postal_code = $checkout_order->customer->postal_code;
                    $user->address = $checkout_order->customer->address;
                    $user->address_2 = $checkout_order->customer->address_2;
                    $user->email_notifications = $checkout_order->customer->email_notifications;
                    $user->sms_notifications = $checkout_order->customer->sms_notifications;
                    $user->save();
                    $group_map = new UserGroupMap();
                    $group_map->user_id = $user->id;
                    $group_map->user_group_id = 2;
                    $group_map->save();
                    $user_group_map = UserGroupMap::where('user_id', $user->id)->first();
                    $alert_preferences = AlertPreference::where('user_group_id', 'LIKE', '%' . $user_group_map->user_group_id . '%')->get();
                    $value_key_array = array();
                    foreach ($alert_preferences as $value) {
                        $value_key_array[$value->key] = [
                            'email' => $user->email_notifications ? 1 : 0,
                            'sms' => $user->sms_notifications ? 1 : 0,
                        ];
                    }
                    $user = User::findOrFail($user->id);
                    $user->alert_preferences = serialize($value_key_array);
                    $user->update();
                }
            }

            if ($checkout_order->shipping->shipping_same_address != 0) {
                $user_shipping = new UserShipping();
                $user_shipping->user_id = $auth_user ? Auth()->user()->id : $user->id;
                // $user_shipping->order_id = $order->id;
                $user_shipping->first_name = $checkout_order->shipping->first_name;
                $user_shipping->last_name = $checkout_order->shipping->last_name;
                $user_shipping->email = $checkout_order->shipping->email;
                $user_shipping->tel_cell = $checkout_order->shipping->tel_cell;
                // $user_shipping->tel_cell_country_code = $checkout_order->shipping->tel_cell_country_code;
                $user_shipping->company_name = $checkout_order->shipping->company_name;
                $user_shipping->country = $checkout_order->shipping->country;
                $user_shipping->postal_code = $checkout_order->shipping->postal_code;
                $user_shipping->city = $checkout_order->shipping->city;
                $user_shipping->state = $checkout_order->shipping->state;
                $user_shipping->address = $checkout_order->shipping->address;
                $user_shipping->address_2 = $checkout_order->shipping->address_2;

                $user_shipping->shipping_method = $checkout_order->shipping->shipping_method[0];
                $user_shipping->save();
            } else {
                $user_shipping = new UserShipping();
                $user_shipping->user_id = $auth_user ? Auth()->user()->id : $user->id;
                // $user_shipping->order_id = $order->id;
                $user_shipping->first_name = $checkout_order->customer->first_name;
                $user_shipping->last_name = $checkout_order->customer->last_name;
                $user_shipping->email = $checkout_order->customer->email;
                $user_shipping->tel_cell = $checkout_order->customer->tel_cell;
                // $user_shipping->tel_cell_country_code = $checkout_order->shipping->tel_cell_country_code;
                $user_shipping->company_name = $checkout_order->customer->company_name;
                $user_shipping->country = $checkout_order->customer->country;
                $user_shipping->postal_code = $checkout_order->customer->postal_code;
                $user_shipping->city = $checkout_order->customer->city;
                $user_shipping->state = $checkout_order->customer->state;
                $user_shipping->address = $checkout_order->customer->address;
                $user_shipping->address_2 = $checkout_order->customer->address_2;
                $user_shipping->shipping_method = $checkout_order->shipping->shipping_method[0];
                $user_shipping->save();
            }
            if ($checkout_order->payment->payment_same_as_contact != 0) {
                $user_billing = new UserBilling();
                $user_billing->user_id = $auth_user ? Auth()->user()->id : $user->id;
                // $user_billing->order_id = $order->id;
                $user_billing->first_name = $checkout_order->payment->first_name;
                $user_billing->last_name = $checkout_order->payment->last_name;
                $user_billing->email = $checkout_order->payment->email;
                $user_billing->tel_cell = $checkout_order->payment->tel_cell;
                // $user_billing->tel_cell_country_code = $checkout_order->payment->tel_cell_country_code;
                $user_billing->company_name = $checkout_order->payment->company_name;
                $user_billing->country = $checkout_order->payment->country;
                $user_billing->postal_code = $checkout_order->payment->postal_code;
                $user_billing->city = $checkout_order->payment->city;
                $user_billing->state = $checkout_order->payment->state;
                $user_billing->address = $checkout_order->payment->address;
                $user_billing->address_2 = $checkout_order->payment->address_2;
                $user_billing->payment_option = $checkout_order->payment->payment_option;
                $user_billing->save();
            } else {
                $user_billing = new UserBilling();
                $user_billing->user_id = $auth_user ? Auth()->user()->id : $user->id;
                // $user_billing->order_id = $order->id;
                $user_billing->first_name = $checkout_order->customer->first_name;
                $user_billing->last_name = $checkout_order->customer->last_name;
                $user_billing->email = $checkout_order->customer->email;
                $user_billing->tel_cell = $checkout_order->customer->tel_cell;
                // $user_billing->tel_cell_country_code = $checkout_order->payment->tel_cell_country_code;
                $user_billing->company_name = $checkout_order->customer->company_name;
                $user_billing->country = $checkout_order->customer->country;
                $user_billing->postal_code = $checkout_order->customer->postal_code;
                $user_billing->city = $checkout_order->customer->city;
                $user_billing->state = $checkout_order->customer->state;
                $user_billing->address = $checkout_order->customer->address;
                $user_billing->address_2 = $checkout_order->customer->address_2;
                $user_billing->payment_option = $checkout_order->payment->payment_option;
                $user_billing->save();
            }
            $order = new Order;
            $order->customer_id = $auth_user ? Auth()->user()->id : $user->id;
            $order->created_by_id = $auth_user ? Auth()->user()->id : $user->id;
            $order->user_shipping_id =  $user_shipping->id;
            $order->user_billing_id =  $user_billing->id;
            $order->payment_proof = $checkout_order->payment->payment_option;
            $order->order_no = $this->getRandomStringUniqid();
            $order->order_date = Carbon::now();
            $order->ip = $this->getClientIpAddress();
            $order->sub_total_amount = $cart->total;
            $order->total_amount = $cart->grand_total;
            $order->sales_tax_amount = 0;
            $order->shipping_amount = $checkout_order->shipping->shipping_amount;
            $order->shipping_method = $checkout_order->shipping->shipping_method[0];
            $order->payment_option = $checkout_order->payment->payment_option;
            $order->status = 1;
            $order->save();
            foreach ($cart->items as $cart_value) {
                foreach ($cart_value->cart_items as $sku_value) {
                    $order_item = new OrderItem;
                    $order_item->order_id = $order->id;
                    $order_item->product_id = $sku_value->product_id;
                    $order_item->product_item_id = $sku_value->id;
                    $order_item->price = $sku_value->price;
                    $order_item->sku = $sku_value->product_item_sku;
                    $order_item->save();
                    $product_item = ProductItem::findOrFail($sku_value->id);
                    if ($cart->amount_due == false) {
                        $product_item->stock_status = 'sold';
                        $product_item->partial_sale = 0;
                        $product_item->inventory_status = 'incoming_sold';
                        $product_item->update();
                    } else {
                        $product_item->stock_status = 'sold';
                        $product_item->partial_sale = 1;
                        $product_item->inventory_status = 'incoming_sold';
                        $product_item->update();
                    }
                }
            }
            $order->shipping_method_option = $checkout_order->shipping->shipping_method_data;
            $cart->order = $order;
            $cart->order_create = true;
            $cart->user = $user;
            // dd($cart);
            Session::put(['cart' => $cart]);
            // return true;
        }
        // return false;
    }
    public function  buySHippment($checkout_order, $order_id)
    {
        $cart = Session::get('cart');
        if (isset($checkout_order->shipping->shipping_method_data)) {
            if ($checkout_order->shipping->shipping_method_data) {
                $shippment = $this->dhlService->createShippment($checkout_order);
                // dd($shippment);
                if ($shippment->status_code == 201) {
                    $order = Order::find($order_id);
                    if ($order) {
                        $order->tracking_code = $shippment->response['shipmentTrackingNumber'];
                        $order->save();
                    }
                    DHLTracking::create([
                        'shipmentTrackingNumber' => $shippment->response['shipmentTrackingNumber'] ?? null,
                        'cancelPickupUrl' => $shippment->response['cancelPickupUrl'] ?? null,
                        'trackingUrl' => $shippment->response['trackingUrl'] ?? null,
                        'packages' => json_encode($shippment->response['packages'] ?? []), // Encode full array
                        'documents' => json_encode($shippment->response['documents'] ?? []), // Encode full array
                        'estimateDeliveryDate' => $shippment->response['estimatedDeliveryDate']['estimatedDeliveryDate'] ?? null,
                        'order_id' => $order_id,
                    ]);
                } else {
                    $order = Order::find($order_id);
                    if ($order) {
                        $order->tracking_code = null;
                        $order->save();
                    }
                }
            }
        }
    }

    public function createOrderFront($intent_pay, $pay_id, $transaction_id)
    {
        $auth_user = Auth()->user();
        $location_data = Session::get('shop_logged_data');
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        if (Session::has('checkout_order') && empty($auth_user)) {
            $user = User::where('email', $checkout_order->customer->email)->first();
            if (empty($user)) {
                $user = User::where('email', $checkout_order->customer->email)->first();
            }
            if (empty($user)) {
                $user = new User;
                $user->first_name = $checkout_order->customer->first_name;
                $user->last_name = $checkout_order->customer->last_name;
                $user->email = $checkout_order->customer->email;
                // $user->tel_cell_country_code = $checkout_order->customer->tel_cell_country_code;
                $user->tel_cell = $checkout_order->customer->tel_cell;
                $user->company_name = $checkout_order->customer->company_name;
                $user->city = $checkout_order->customer->city;
                $user->state = $checkout_order->customer->state;
                $user->country = $checkout_order->customer->country;
                $user->postal_code = $checkout_order->customer->postal_code;
                $user->address = $checkout_order->customer->address;
                $user->address_2 = $checkout_order->customer->address_2;
                $user->save();
            }
            $order = new Order;
            $order->customer_id = $auth_user ? Auth()->user()->id : $user->id;
            $order->created_by_id = $auth_user ? Auth()->user()->id : $user->id;
            $order->order_no = $this->getRandomStringUniqid();
            $order->order_date = Carbon::now();
            $order->ip = $this->getClientIpAddress();
            $order->sub_total_amount = $cart->total;
            $order->total_amount = $cart->grand_total;
            $order->sales_tax_amount = 0;
            $order->shipping_amount = $checkout_order->shipping->shipping_amount;
            // $order->shipping_method_option_id = $checkout_order->shipping->shipping_method;
            $order->status = 1;
            $order->save();
            $user_shipping = new UserShipping();
            $user_shipping->user_id = $auth_user ? Auth()->user()->id : $user->id;
            $user_shipping->order_id = $order->id;
            $user_shipping->first_name = $checkout_order->shipping->first_name;
            $user_shipping->last_name = $checkout_order->shipping->last_name;
            $user_shipping->email = $checkout_order->shipping->email;
            $user_shipping->tel_cell = $checkout_order->shipping->tel_cell;
            // $user_shipping->tel_cell_country_code = $checkout_order->shipping->tel_cell_country_code;
            $user_shipping->company_name = $checkout_order->shipping->company_name;
            $user_shipping->country = $checkout_order->shipping->country;
            $user_shipping->postal_code = $checkout_order->shipping->postal_code;
            $user_shipping->city = $checkout_order->shipping->city;
            $user_shipping->state = $checkout_order->shipping->state;
            $user_shipping->address = $checkout_order->shipping->address;
            $user_shipping->address_2 = $checkout_order->shipping->address_2;
            $user_shipping->save();
            $user_billing = new UserBilling();
            $user_billing->user_id = $auth_user ? Auth()->user()->id : $user->id;
            $user_billing->order_id = $order->id;
            $user_billing->first_name = $checkout_order->payment->first_name;
            $user_billing->last_name = $checkout_order->payment->last_name;
            $user_billing->email = $checkout_order->payment->email;
            $user_billing->tel_cell = $checkout_order->payment->tel_cell;
            // $user_billing->tel_cell_country_code = $checkout_order->payment->tel_cell_country_code;
            $user_billing->company_name = $checkout_order->payment->company_name;
            $user_billing->country = $checkout_order->payment->country;
            $user_billing->postal_code = $checkout_order->payment->postal_code;
            $user_billing->city = $checkout_order->payment->city;
            $user_billing->state = $checkout_order->payment->state;
            $user_billing->address = $checkout_order->payment->address;
            $user_billing->address_2 = $checkout_order->payment->address_2;
            $user_billing->save();
            foreach ($cart->items as $cart_value) {
                foreach ($cart_value->cart_items as $sku_value) {
                    $order_item = new OrderItem;
                    $order_item->order_id = $order->id;
                    $order_item->product_id = $sku_value->product_id;
                    $order_item->product_item_id = $sku_value->id;
                    $order_item->price = $sku_value->price;
                    $order_item->sku = $sku_value->product_item_sku;
                    $order_item->save();
                    $product_item = ProductItem::findOrFail($sku_value->id);
                    if ($cart->amount_due == false) {
                        $product_item->stock_status = 'sold';
                        $product_item->partial_sale = 0;
                        $product_item->inventory_status = 'incoming_sold';
                        $product_item->update();
                    } else {
                        $product_item->stock_status = 'sold';
                        $product_item->partial_sale = 1;
                        $product_item->inventory_status = 'incoming_sold';
                        $product_item->update();
                    }
                }
            }
            $currency = 'USD';
            if (Session::has('shop_logged_data') && $checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') {
                $currency = $this->getLocalCurrencyCode($location_data->country);
            }
            $collect = new Collect;
            $collect->collection_date = Carbon::now();
            $collect->customer_id = $auth_user ? Auth()->user()->id : $user->id;
            $collect->order_id = $order->id;
            $collect->transaction_method_id = $pay_id;
            $collect->transaction_id = $transaction_id;
            if (Session::has('shop_logged_data') && $checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') {
                $collect->amount = $intent_pay / $location_data->currency_rate;
                $collect->local_amount = $intent_pay;
            } else {
                $collect->amount = $intent_pay;
                $collect->local_amount = null;
            }
            $collect->currency = $currency;
            $collect->currency_rate = $location_data->currency_rate ?? null;
            $collect->save();
            $order = Order::findOrFail($order->id);
            $order->order = serialize($order);
            $order->order_items = serialize(OrderItem::where('order_id', $order->id)->get());
            $order->update();
            $cart->order = $order;
            $cart->currency = $currency;
            $cart->user = $user;
            Session::put(['cart' => $cart]);
            return true;
        }
        return false;
    }
    public function createOrderCollection($intent_pay, $pay_id, $transaction_id)
    {
        $cart = Session::get('cart');
        $location_data = Session::get('shop_logged_data');
        $checkout_order = Session::get('checkout_order');
        $currency = 'USD';
        if (Session::has('shop_logged_data') && $checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') {
            $currency = $this->getLocalCurrencyCode($location_data->country);
        }
        $collect = new Collect;
        $collect->collection_date = Carbon::now();
        $collect->customer_id = $cart->order->customer_id;
        $collect->order_id = $cart->order->id;
        $collect->transaction_method_id = $pay_id;
        $collect->transaction_id = $transaction_id;
        if (Session::has('shop_logged_data') && $checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') {
            $collect->amount = $intent_pay / $location_data->currency_rate;
            $collect->local_amount = $intent_pay;
        } else {
            $collect->amount = $intent_pay;
            $collect->local_amount = null;
        }
        $collect->currency = $currency;
        $collect->currency_rate = $location_data->currency_rate ?? null;
        $collect->save();
        return true;
    }
    public function siteCartData(Request $request)
    {
        $auth_user = Auth()->user();
        $location_data = Session::get('shop_logged_data');
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart_page = Page::where('page_key', 'cart')->first();
        $products_page = Page::where('page_key', 'products')->first();
        $buy_now_url = route('page', $checkout_page->slug);
        $view_cart_url = route('page', $cart_page->slug);
        $redirect_page = route('page', $products_page->slug);
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        if (Session::has('cart')) {
            // $checkout_order = Session::get('checkout_order');
            if ($request->shopping_cart_items) {
                $cart = Session::get('cart');
                foreach ($cart->items as $value) {
                    if (count($storeIds) < 1) {
                        continue;
                    }
                    $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                    if (count($storeIds) > 0) {
                        $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                    } else {
                        $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                    }
                }
                return view('web.jquery_live.cart_items', compact('cart', 'buy_now_url', 'view_cart_url', 'redirect_page'));
            }
            if ($request->shopping_cart_data) {
                $cart = Session::get('cart');
                return response()->json($cart, 200);
            }
            if ($request->shopping_cart_block) {
                $checkout_page = Page::where('page_key', 'checkout')->first();
                $cart_page = Page::where('page_key', 'cart')->first();
                $products_page = Page::where('page_key', 'products')->first();
                $buy_now_url = route('page', $checkout_page->slug);
                $view_cart_url = route('page', $cart_page->slug);
                $redirect_page = route('page', $products_page->slug);
                $cart_page_status = false;
                if ($request->current_page == $view_cart_url) {
                    $cart_page_status = true;
                }
                $site_data = Session::get('shop_logged_data');
                // dd($site_data);
                $selectedCountry = $site_data->country ?? 'NG';
                $allowedLocations = ($selectedCountry === 'US') ? ['US', 'NG']  : $selectedCountry;
                $storeIds = $this->getEligibleStores($selectedCountry);
                $cart = Session::get('cart');
                foreach ($cart->items as $value) {
                    $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                    // dd($value->condition);
                    if (count($storeIds) > 0) {
                        $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                    } else {
                        $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                    }
                    // $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->get(), $value->attribute_options);
                }
                if (count($cart->items) == 0) {
                    Session::forget('cart');
                }
                return view('web.jquery_live.checkout_items', compact('cart', 'buy_now_url', 'view_cart_url', 'redirect_page', 'cart_page_status', 'checkout_order'));
            }
        }
        return response()->json('', 200);
    }
    public function verifyPayment()
    {
        // $cart = Session::get('cart');
        // // $location_data = Session::get('shop_logged_data');
        // // Session::forget('cart');
        // // dd($cart);
        // $status = false;
        // $headers = [
        //     'Authorization' => 'sandbox_sk_d84d7ca27a9a317aebf1b991283f3fc498da0fee882a',
        //     'Content-Type' => 'application/json',
        // ];
        // if(!empty($cart->transaction_refs)){
        //     $items = $cart->transaction_refs;
        //     foreach($items as $value){
        //         $url =  'https://sandbox-api-d.squadco.com/transaction/verify/' . $value;
        //         $response = Http::withHeaders($headers)->get($url);
        //         $data = json_decode($response->getBody(), true);
        //         if ($data && $data['status'] == 200) {
        //             $status = true;
        //         }
        //     }
        // }
        // if($status && $cart->amount_due == false){
        //     Session::forget('cart');
        // }
        // if ($status && $cart->amount_due == true) {
        //     $cart->total = round($cart->total - (5000000 / $location_data->currency_rate), 2);
        //     $cart->grand_total_local = round(($cart->grand_total_local - 50000000), 2);
        //     Session::put('cart', $cart);
        // }
        // if ($cart->amount_due == false) {
        // } else {
        // if ($cart->grand_total_local == 5000000 || $cart->grand_total_local > 5000000) {
        //     $cart->total = round($cart->total - (5000000 / $location_data->currency_rate),2);
        //     $cart->grand_total_local = round(($cart->grand_total_local - 50000000),2);
        //     // dd($cart);
        //     Session::put('cart', $cart);
        // }
        // }
        // if(!empty($reference)){
        //     $url =  'https://sandbox-api-d.squadco.com/transaction/verify/' . $reference;
        //     $response = Http::withHeaders($headers)->get($url);
        //     $data = json_decode($response->getBody(), true);
        //     if ($data && $data['status'] == 200) {
        //         $status = true;
        //         if ($cart && $cart->amount_due == true) {
        //             $cart->total = $cart->total - (5000000/$location_data->currency_rate);
        //             $cart->grand_total_local = $cart->grand_total_local - 5000000;
        //             Session::put('cart', $cart);
        //         } else {
        //             // dd('reference');
        //             Session::forget('cart');
        //         }
        //     }
        // }
        // dd('stop');
        // $checkout_page = Page::where('page_key', 'checkout')->first();
        // $buy_now_url = route('page', $checkout_page->slug);
        // if (!$status) return redirect()->to($buy_now_url);
        // dd($status);
        // return $status;
    }
    public function payCartUpdate()
    {
        // $cart = Session::get('cart');
        // $location_data = Session::get('shop_logged_data');
        // if ($cart && $this->verifyPayment() && $cart->amount_due) {
        //     $cart->total = round($cart->total - (5000000 / $location_data->currency_rate), 2);
        //     $cart->grand_total_local = round(($cart->grand_total_local - 5000000), 2);
        //     Session::put('cart', $cart);
        // }
        // return response()->json($cart, 200);
    }
    function makeItemStatus($items)
    {
        foreach ($items as $value) {
            $value->sku_partial_paid = true;
            // $value->sku_partial_paid = $value->sku_partial_paid ?? false;
        }
        return $items;
    }
    public function updateProfile(Request $request)
    {
        // dd($request->all());
        if (empty(Auth()->user())) return redirect('/');
        $id = Auth()->user()->id;
        $user = User::findOrFail($id);
        $this->validate($request, [
            'username' => "required|string|max:255|alpha_dash|unique:users,username," . $id,
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            "email" => "nullable|email|unique:users,email," . $id,
            "tel_cell" => "nullable|unique:users,tel_cell," . $id,
            // 'tel_cell_country_code' => 'nullable|string',
            'city' => 'required|string',
            'company_name' => 'nullable|string',
            'state' => 'required|string',
            'country' => 'required|string',
            'postal_code' => 'required|integer',
            'address' => 'required|string',
            'address_2' => 'nullable|string',
            'password' => 'nullable|string|min:8',
        ]);
        $get_ip = $this->getClientIpAddress();
        // $tel_cell_country_code = null;
        // if ($request->tel_cell) {
        //     $tel_cell_country_code = '+' . $request->tel_cell_country_code;
        // }
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->username = $request->get('username');
        $user->email = $request->get('email');
        $user->state = $request->get('state');
        $user->city = $request->get('city');
        $user->postal_code = $request->get('postal_code');
        $user->country = $request->get('country');
        $user->company_name = $request->get('company_name');
        $user->tel_cell = preg_replace("/[^a-zA-Z0-9]+/", "", $request->get('tel_cell'));
        // if ($user->tel_cell) {
        //     $user->tel_cell_country_code = $tel_cell_country_code;
        // }
        $user->address = $request->get('address');
        $user->address_2 = $request->get('address_2');
        $user->email_notifications = $request->get('email_notifications') ?? 0;
        $user->sms_notifications = $request->get('sms_notifications') ?? 0;
        $user->alert_preferences = $request->get('alert_preferences') ? serialize($request->get('alert_preferences')) : null;
        $user->ip = $get_ip;
        // dd($user);
        $user->update();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'update_profile') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    $tracking->user_id = $user->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    // if ($user_email_template->sms_status && $user->tel_cell)
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        // $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'update_profile')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'update_profile') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                $tracking->user_id = $user->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        $url = $request->only('redirects_to');
        return (($request->get('btn') == 'update') ? back() : redirect()->to($url['redirects_to']))->with('success', 'Profile Updated Successful');
    }
    public function updateChangePassword($slug, Request $request)
    {
        if (empty(Auth()->user())) return redirect('/');
        $id = Auth()->user()->id;
        $user = User::findOrFail($id);
        $this->validate($request, [
            // 'old_password' => ['required', new OldPassword],
            'password' => 'required|string|alpha_dash|min:6|confirmed',
            'password_confirmation' => 'required'
        ]);
        $user->password = Hash::make($request->password);
        $user->update();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'update_password') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        // 'cash_reward' => $redeem_cash_reward,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    // $tracking->cash_reward_id = $redeem_cash_reward->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'update_password')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'update_password') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                // $tracking->cash_reward_id = $redeem_cash_reward->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        $url = $request->only('redirects_to');
        return (($request->get('btn') == 'result') ? back() : redirect()->to($url['redirects_to']))->with('success', 'Password Updated Successful');
    }
    public function contactForm(Request $request)
    {
        if (empty(Auth()->user())) return redirect('/');
        $this->validate($request, [
            'contact_first_name' => 'required|string|max:255',
            'contact_last_name' => 'required|string|max:255',
            "contact_email" => 'required|email',
            'contact_how_can_help' => 'required|string',
            'contact_subject' => 'required|string',
            'contact_message' => 'required|string',
        ]);
        $contact = (object)[
            'contact_first_name' => $request->contact_first_name,
            'contact_last_name' => $request->contact_last_name,
            'contact_email' => $request->contact_email,
            'contact_how_can_help' => $request->contact_how_can_help,
            'contact_subject' => $request->contact_subject,
            'contact_message' => $request->contact_message,
            'contact_phone' =>  $request->tel_cell,
        ];
        $user = auth()->user();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_template = EmailTemplate::find(18);
            $user_email_template->email_body = unserialize($user_email_template->email_body);
            $user_email_template->sms_body = unserialize($user_email_template->sms_body);
            $mail_data = (object)[
                'user' => $user,
                'contact' => $contact,
                'subject' => $user_email_template->subject,
                'pre_header' => $user_email_template->pre_header,
                'email_body' => $user_email_template->email_body,
                'sms_status' => $user_email_template->sms_status ? true : false,
                'sms_body' => $user_email_template->sms_body,
            ];
            $mail_data->subject = $this->sendEmailData($mail_data)->subject;
            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
            $mail_data->content = $this->sendEmailData($mail_data)->content;
            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
            $lead_source = (object)[
                'source' => 'checkout',
            ];
            $tracking = new Tracking();
            $tracking->content = serialize($this->sendEmailData($mail_data));
            $tracking->from_email = config('mail.from.address');
            $tracking->to_email = $user->email;
            $tracking->subject = $user_email_template->subject;
            $tracking->lead_source = serialize($lead_source);
            $tracking->ip = $this->getClientIpAddress();
            $tracking->save();
            $message = $this->checkWhatsappExist(true, $contact->contact_phone, $mail_data->sms_content);
            if ($user_email_template->sms_status && $contact->contact_phone) {
                $message = $this->checkWhatsappExist($user_email_template->sms_status, $contact->contact_phone, $mail_data->sms_content);
            }
            Mail::to($user->email)->send(new SiteEmail($mail_data));
        }
        $email_templates = EmailTemplate::where('system_type', 'send_message')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'send_message') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'contact' => $contact,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                // $tracking->cash_reward_id = $redeem_cash_reward->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        $url = $request->only('redirects_to');
        return (($request->get('btn') == 'result') ? back() : redirect()->to($url['redirects_to']))->with('success', 'Message Send Successful');
    }
    public function makeMyAccount($email)
    {
        $user = User::where('email', $email)->first();
        $user = User::findOrFail($user->id);
        $user_num = abs(crc32(uniqid()));
        $password = Str::random(8);
        // $password = Str::password(16, true, true, false, false);
        $user->username = Str::slug($user->first_name, '_') . $user_num;
        $user->password = Hash::make($password);
        $user->verified = 1;
        $user->update();
        $email_templates = EmailTemplate::where('system_type', 'create_account')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                $alert_preferences = unserialize($admin_user->user_alert_preferences);
                // dd($value);
                foreach ($alert_preferences as $key => $value) {
                    $alert_preference = AlertPreference::where('key', $key)->first();
                    // dd($alert_preference);
                    $email_template = EmailTemplate::where('alert_preference_id', $alert_preference->id)->first();
                    // dd($email_template);
                    if ($email_template && $email_template->system_type && $email_template->system_type == 'create_account') {
                        // dd($key, $value, $email_template);
                        $email_template->email_body = unserialize($email_template->email_body);
                        $email_template->sms_body = unserialize($email_template->sms_body);
                        $mail_data = (object)[
                            'user' => $user,
                            'order' => $cart->order,
                            'subject' => $email_template->subject,
                            'pre_header' => $email_template->pre_header,
                            'email_body' => $email_template->email_body,
                            'sms_status' => $email_template->sms_status ? true : false,
                            'sms_body' => $email_template->sms_body,
                        ];
                        $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                        $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                        $mail_data->content = $this->sendEmailData($mail_data)->content;
                        $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                        $lead_source = (object)[
                            'source' => 'update_user',
                        ];
                        $tracking = new Tracking();
                        $tracking->order_id = $cart->order->id;
                        $tracking->content = serialize($this->sendEmailData($mail_data));
                        $tracking->from_email = config('mail.from.address');
                        $tracking->to_email = $user->email;
                        $tracking->subject = $email_template->subject;
                        $tracking->lead_source = serialize($lead_source);
                        $tracking->ip = $this->getClientIpAddress();
                        $tracking->save();
                        // if ($email_template->sms_status && $user->tel_cell)
                        if ($email_template->sms_status && $user->tel_cell) {
                            $sms_tracking = Tracking::findOrFail($tracking->id);
                            $sms_tracking->sms_content = serialize($mail_data->sms_content);
                            $sms_tracking->from_phone = config('app.twilio_number');
                            $sms_tracking->to_phone = $user->tel_cell;
                            $sms_tracking->update();
                        }
                        Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                    }
                }
            }
        }
        // dd($admin_users, $email_templates);
        foreach ($email_templates as $email_template) {
            $alert_preference = AlertPreference::where('id', $email_template->alert_preference_id)->first();
            if (in_array(3, explode(',', $email_template->user_group_id))) {
                // $data[] = $email_template;
                // dd(explode(',', $email_template->user_group_id), $alert_preference);
                $email_template->email_body = unserialize($email_template->email_body);
                $email_template->sms_body = unserialize($email_template->sms_body);
                $mail_data = (object)[
                    'user' => $user,
                    'order' => $cart->order,
                    'subject' => $email_template->subject,
                    'pre_header' => $email_template->pre_header,
                    'email_body' => $email_template->email_body,
                    'sms_status' => $email_template->sms_status ? true : false,
                    'sms_body' => $email_template->sms_body,
                ];
                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                $mail_data->content = $this->sendEmailData($mail_data)->content;
                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                $lead_source = (object)[
                    'source' => 'checkout',
                ];
                $tracking = new Tracking();
                $tracking->order_id = $cart->order->id;
                $tracking->content = serialize($this->sendEmailData($mail_data));
                $tracking->from_email = config('mail.from.address');
                $tracking->to_email = $user->email;
                $tracking->subject = $email_template->subject;
                $tracking->lead_source = serialize($lead_source);
                $tracking->ip = $this->getClientIpAddress();
                $tracking->save();
                // if ($email_template->sms_status && $user->tel_cell)
                if ($email_template->sms_status && $user->tel_cell) {
                    $sms_tracking = Tracking::findOrFail($tracking->id);
                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                    $sms_tracking->from_phone = config('app.twilio_number');
                    $sms_tracking->to_phone = $user->tel_cell;
                    $sms_tracking->update();
                }
                // Send email to customer
                Mail::to($user->email)->send(new SiteEmail($mail_data));
            }
        }
        $user_group_map = UserGroupMap::where('user_id', $user->id)->first();
        $group_map = UserGroupMap::findOrFail($user_group_map->id);
        $group_map->user_group_id = 3;
        $group_map->update();
        $group_map = UserGroupMap::where('user_id', $user->id)->first();
        $alert_preferences = AlertPreference::where('user_group_id', 'LIKE', '%' . $group_map->user_group_id . '%')->get();
        $value_key_array = array();
        foreach ($alert_preferences as $value) {
            $value_key_array[$value->key] = [
                'email' => $user->email_notifications ? 1 : 0,
                'sms' => $user->sms_notifications ? 1 : 0,
            ];
        }
        $user = User::findOrFail($user->id);
        $user->alert_preferences = serialize($value_key_array);
        $user->update();
        return redirect('/')->with('success', 'Account create Successful');
    }
    public function liveAlertPreferences($user_group_id, $user_id = null)
    {
        $items = AlertPreference::where('user_group_id', 'LIKE', '%' . $user_group_id . '%')->whereStatus(true)->get();
        if ($user_id) {
            $item = User::findOrFail($user_id);
            if (!empty($item->alert_preferences)) {
                $item->alert_preferences = unserialize($item->alert_preferences);
                // dd($item->alert_preferences);
                foreach ($items as $value) {
                    $value->email_default = false;
                    $value->sms_default = false;
                    foreach ($item->alert_preferences as $key => $alert_preference) {
                        if (!empty($value->email_force_checked)) {
                            $value->email_default = true;
                        }
                        if (!empty($value->sms_force_checked)) {
                            $value->sms_default = true;
                        }
                        if ($value->key == $key) {
                            if (!empty($alert_preference['email']) == 1) {
                                $value->email_default = true;
                            }
                            if (!empty($alert_preference['sms']) == 1) {
                                $value->sms_default = true;
                            }
                            // dd($value);
                        }
                    }
                }
            }
            // dd($items);
        }
        return view('admin.jquery_live.user_alert_preferences', compact('items'));
    }
    public function pointToCash()
    {
        if (empty(Auth()->user())) return redirect('/');
        $rewards = Reward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $total_reward_points = 0;
        foreach ($rewards as $value) {
            $total_reward_points += $value->points;
            $update_reward = Reward::findOrFail($value->id);
            $update_reward->status = false;
            $update_reward->update();
        }
        $cash_value = $total_reward_points / $this->cash_value_per_points;
        $cash_reward = new CashReward;
        $cash_reward->user_id = auth()->user()->id;
        $cash_reward->points = $total_reward_points;
        $cash_reward->amount = round($cash_value, 2);
        $cash_reward->save();
        $user = auth()->user();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'convert_cash') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        'cash_reward' => $cash_reward,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $sms_message = $this->sendEmailData($mail_data)->sms_body;
                    $user->tel_cell = str_replace('+', '', $user->tel_cell);
                    if ($user->tel_cell) {
                        $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell, $sms_message);
                    }
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    $tracking->cash_reward_id = $cash_reward->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'convert_cash')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'convert_cash') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'cash_reward' => $cash_reward,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                $tracking->cash_reward_id = $cash_reward->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        return redirect()->back()->with('success', 'rewards points to cash Successful');
    }
    public function postRedeemCash(Request $request)
    {
        if (empty(Auth()->user())) return redirect('/');
        $location_data = Session::get('shop_logged_data');
        $transfer_meta = (object)[
            'transfer_type' => $request->transfer_type,
        ];
        if ($request->transfer_type == 'bank_account') {
            $validator = Validator::make($request->all(), [
                'transfer_type' => 'required|string|max:255',
                'bank' => 'required|string|max:255',
                'name_on_account' => 'required|string|max:255',
                'account_number' => 'required|string|max:255',
            ]);
            $transfer_meta->bank = $request->bank;
            $transfer_meta->name_on_account = $request->name_on_account;
            $transfer_meta->account_number = $request->account_number;
        } elseif ($request->transfer_type == 'zelle_payments') {
            $validator = Validator::make($request->all(), [
                'transfer_type' => 'required|string|max:255',
                'email_phone_number' => 'required|string|max:255',
            ]);
            $transfer_meta->email_phone_number = $request->email_phone_number;
        } elseif ($request->transfer_type == 'check') {
            $validator = Validator::make($request->all(), [
                'transfer_type' => 'required|string|max:255',
                'full_name' => 'required|string|max:255',
            ]);
            $transfer_meta->full_name = $request->full_name;
        } elseif ($request->transfer_type == 'cash_app_payment') {
            $validator = Validator::make($request->all(), [
                'transfer_type' => 'required|string|max:255',
                'cash_email_phone_number' => 'required|string|max:255',
            ]);
            $transfer_meta->email_phone_number = $request->cash_email_phone_number;
        } else {
            return response()->json([
                'message' => 'no transfer_type selected',
            ], 400);
        }
        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $cash_rewards = CashReward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $total_cash_reward_points = 0;
        $total_cash_reward = 0;
        foreach ($cash_rewards as $value) {
            $total_cash_reward_points += $value->points;
            $total_cash_reward += $value->amount;
            $update_cash_reward = CashReward::findOrFail($value->id);
            $update_cash_reward->status = false;
            $update_cash_reward->update();
        }
        $redeem_cash_reward = new RedeemCashReward;
        $redeem_cash_reward->user_id = auth()->user()->id;
        $redeem_cash_reward->points = $total_cash_reward_points;
        $redeem_cash_reward->amount = $total_cash_reward;
        if ($location_data->country != 'US' || $location_data->country != 'us') {
            $redeem_cash_reward->currency_rate = $this->getLocalCurrencyRate($location_data->country);
            $redeem_cash_reward->country = $location_data->country;
        }
        $redeem_cash_reward->transfer_type = $request->transfer_type;
        $redeem_cash_reward->transfer_meta = serialize($transfer_meta);
        $redeem_cash_reward->transfer_status = 1;
        // dd($redeem_cash_reward);
        $redeem_cash_reward->save();
        // dd($transfer_meta);
        // dd($request->all());
        $user = auth()->user();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'redeem_cash_reward') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        // 'cash_reward' => $redeem_cash_reward,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $sms_message = $this->sendEmailData($mail_data)->sms_body;
                    $user->tel_cell = str_replace('+', '', $user->tel_cell);
                    if ($user->tel_cell) {
                        $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell, $sms_message);
                    }
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    // $tracking->cash_reward_id = $redeem_cash_reward->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'redeem_cash_reward')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'redeem_cash_reward') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    // 'cash_reward' => $redeem_cash_reward,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                // $tracking->cash_reward_id = $redeem_cash_reward->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        return response()->json([
            'message' => 'Send to admin',
        ], 200);
    }
    public function useRewardPoints(Request $request, $status)
    {
        // dd($request->all(), $status);
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            $location_data = Session::get('shop_logged_data');
            $rewards = Reward::where([
                'user_id' => auth()->user()->id,
                'status' => true,
            ])->get();
            $total_reward_points = 0;
            foreach ($rewards as $value) {
                $total_reward_points += $value->points;
            }
            $total_reward_points = $total_reward_points;
            $cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
            // $cart->total = $status ? $cart->total - $cash_value : $cart->total + $cash_value;
            // if($cart->total_local > 0){
            //     $cart->total_local = round($cart->total * $location_data->currency_rate,2);
            // }
            $cart->grand_total = $status ? $cart->grand_total - $cash_value : $cart->grand_total + $cash_value;
            if ($cart->grand_total_local > 0) {
                $cart->grand_total_local = round($cart->grand_total * $location_data->currency_rate, 2);
            }
            $cart->use_reward_point_status = $status;
            Session::put([
                'cart' => $cart,
            ]);
        }
        // dd($status, $cash_value, $cart);
        $cash_rewards = CashReward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $cash_total_reward = 0;
        foreach ($cash_rewards as $value) {
            $cash_total_reward += $value->amount;
        }
        $cash_total_reward = number_format($cash_total_reward, 2);
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        $products_page = Page::where('page_key', 'products')->first();
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart_page = Page::where('page_key', 'cart')->first();
        $redirect_page = route('page', $products_page->slug);
        $buy_now_url = route('page', $checkout_page->slug);
        $view_cart_url = route('page', $cart_page->slug);
        $redirect_page_status = false;
        if (in_array($request->current_page, [$view_cart_url, $buy_now_url])) {
            $redirect_page_status = true;
        }
        $cart_page_status = false;
        if ($request->current_page == $view_cart_url) {
            $cart_page_status = true;
        }
        return view('web.jquery_live.checkout_items', compact('cart', 'buy_now_url', 'redirect_page', 'view_cart_url', 'cart_page_status', 'checkout_order', 'cash_total_reward', 'cash_value', 'total_reward_points'));
    }
    public function useCashValue(Request $request, $status)
    {
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            $location_data = Session::get('shop_logged_data');
            $cash_rewards = CashReward::where([
                'user_id' => auth()->user()->id,
                'status' => true,
            ])->get();
            $cash_total_reward = 0;
            foreach ($cash_rewards as $value) {
                $cash_total_reward += $value->amount;
            }
            $cash_value = round($cash_total_reward, 2);
            // $cart->total = $status ? $cart->total - $cash_value : $cart->total + $cash_value;
            // if ($cart->total_local > 0) {
            //     $cart->total_local = round($cart->total * $location_data->currency_rate, 2);
            // }
            $cart->grand_total = $status ? $cart->grand_total - $cash_value : $cart->grand_total + $cash_value;
            if ($cart->grand_total_local > 0) {
                $cart->grand_total_local = round($cart->grand_total * $location_data->currency_rate, 2);
            }
            $cart->use_cash_value_status = $status;
            Session::put([
                'cart' => $cart,
            ]);
        }
        $rewards = Reward::where([
            'user_id' => auth()->user()->id,
            'status' => true,
        ])->get();
        $total_reward_points = 0;
        foreach ($rewards as $value) {
            $total_reward_points += $value->points;
        }
        $total_reward_points = $total_reward_points;
        $cash_value = round($total_reward_points / $this->cash_value_per_points, 2);
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        $products_page = Page::where('page_key', 'products')->first();
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart_page = Page::where('page_key', 'cart')->first();
        $redirect_page = route('page', $products_page->slug);
        $buy_now_url = route('page', $checkout_page->slug);
        $view_cart_url = route('page', $cart_page->slug);
        $redirect_page_status = false;
        if (in_array($request->current_page, [$view_cart_url, $buy_now_url])) {
            $redirect_page_status = true;
        }
        $cart_page_status = false;
        if ($request->current_page == $view_cart_url) {
            $cart_page_status = true;
        }
        return view('web.jquery_live.checkout_items', compact('cart', 'buy_now_url', 'redirect_page', 'view_cart_url', 'cart_page_status', 'checkout_order', 'cash_total_reward', 'cash_value', 'total_reward_points'));
    }
    public function getBanks($location_country)
    {
        try {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.flutterwave.com/v3/banks/' . $location_country ?? $location_data->country,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => array(
                    "Authorization: Bearer FLWSECK_TEST-a4e1e7c1d120eb05f8c832eb54f77d99-X"
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $response_array = json_decode($response, true);
            $curl = curl_init();
            return $response_array['data'];
        } catch (\Exception $ex) {
            return array();
            // dd($ex->getMessage());
        }
    }
    public function updateProductReview(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            // 'product_id' => 'required|exists:products,id',
            // 'product_item_id' => 'required|exists:product_items,id',
            // 'product_sku_s' => 'required|string',
            'rating' => 'required|numeric|between:1,5',
            'review_message' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $review = Review::findOrFail($id);
        $review->user_id = auth()->user()->id;
        $user = User::findOrFail($review->user_id);
        // $review->product_id = $request->product_id;
        // $review->product_item_id = $request->product_item_id;
        // $review->product_sku_s = $request->product_sku_s;
        $review->rating = $request->rating;
        $review->message = $request->review_message;
        $review->ip = $this->getClientIpAddress();
        $review->status = 1;
        $review->update();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'update_review') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        'review' => $review,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    $tracking->review_id = $review->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'update_review')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'update_review') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'review' => $review,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $sms_message = $this->sendEmailData($mail_data)->sms_body;
                                $user->tel_cell = str_replace('+', '', $user->tel_cell);
                                if ($user->tel_cell) {
                                    $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell, $sms_message);
                                }
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                $tracking->review_id = $review->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell)
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        return response()->json([
            'message' => 'Product review submit successfully',
        ], 200);
    }
    public function updateProductReturn(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'edit_refund_reason' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $refund = OrderRefund::findOrFail($id);
        $refund->refund_reason = $request->edit_refund_reason;
        $refund->ip = $this->getClientIpAddress();
        $refund->save();
        $user = auth()->user();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'request_return') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        'refund' => $refund,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $sms_message = $this->sendEmailData($mail_data)->sms_body;
                    $user->tel_cell = str_replace('+', '', $user->tel_cell);
                    if ($user->tel_cell) {
                        $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell, $sms_message);
                    }
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    $tracking->review_id = $review->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'request_return')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'request_return') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'refund' => $refund,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                $tracking->review_id = $review->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell) {
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        return response()->json([
            'message' => 'Refund update successfully',
        ], 200);
    }
    public function postProductReturn(Request $request)
    {
        // $request->merge([
        //     'order_sku' => $request->input('product_sku'),
        // ]);
        $validator = Validator::make($request->all(), [
            'product_sku' => 'required|unique:order_refunds,sku|exists:order_items,sku',
            'refund_reason' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        //  dd($request->all());
        $refund = new OrderRefund;
        $refund->user_id = auth()->user()->id;
        $refund->order_id = $request->return_order_id;
        $refund->sku = $request->product_sku;
        $refund->refund_reason = $request->refund_reason;
        $refund->ip = $this->getClientIpAddress();
        $refund->status = 1;
        $refund->save();
        $refund_images_info = Session::get('refund_images');
        if ($refund_images_info) {
            $refund = OrderRefund::findOrFail($refund->id);
            $folderName = $this->folderName() . '/' . $refund->id . '/';
            foreach ($refund_images_info as $value) {
                $image_name = 'refund_image_' . uniqid() . '.' . $value->extension;
                $value->folder = $this->folderName();
                $value->file_path = $folderName . '/' . $image_name;
                Storage::move($value->refund_images, $this->refundFolderPath . $folderName . '/' . $image_name);
                Session::forget('refund_images');
            }
            $refund->refund_images = serialize($refund_images_info);
            $refund->update();
        }
        $user = auth()->user();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'request_return') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        'refund' => $refund,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $sms_message = $this->sendEmailData($mail_data)->sms_body;
                    $user->tel_cell = str_replace('+', '', $user->tel_cell);
                    if ($user->tel_cell) {
                        $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell, $sms_message);
                    }
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    // $tracking->review_id = $review->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    if ($user_email_template->sms_status && $user->tel_cell) {
                        $sms_tracking = Tracking::findOrFail($tracking->id);
                        $sms_tracking->sms_content = serialize($mail_data->sms_content);
                        $sms_tracking->from_phone = config('app.twilio_number');
                        $sms_tracking->to_phone = $user->tel_cell;
                        $sms_tracking->update();
                    }
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'request_return')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'request_return') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'refund' => $refund,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                // $tracking->review_id = $review->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell) {
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->to_phone =  $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        return response()->json([
            'message' => 'Refund submit successfully',
        ], 200);
    }
    public function postProductReview(Request $request)
    {
        // dd($request->all());
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'product_item_id' => 'required|exists:product_items,id',
            'product_sku_s' => 'required|string',
            'rating' => 'required|numeric|between:1,5',
            'review_message' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $review = new Review;
        $review->user_id = auth()->user()->id;
        $review->product_id = $request->product_id;
        $review->product_item_id = $request->product_item_id;
        $review->product_sku_s = $request->product_sku_s;
        $review->rating = $request->rating;
        $review->message = $request->review_message;
        $review->ip = $this->getClientIpAddress();
        $review->status = 1;
        $review->save();
        $review_images_info = Session::get('review_images');
        if ($review_images_info) {
            $review = Review::findOrFail($review->id);
            $folderName = $this->folderName() . '/' . $review->id . '/';
            foreach ($review_images_info as $value) {
                $image_name = 'review_image_' . uniqid() . '.' . $value->extension;
                $value->folder = $this->folderName();
                $value->file_path = $folderName . '/' . $image_name;
                Storage::move($value->review_images, $this->reviewFolderPath . $folderName . '/' . $image_name);
                Session::forget('review_images');
            }
            $review->review_images = serialize($review_images_info);
            $review->update();
        }
        $user = auth()->user();
        //customer send email
        $alert_preferences = unserialize($user->alert_preferences);
        foreach ($alert_preferences as $key => $value) {
            $alert_preference = AlertPreference::where('key', $key)->first();
            $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                if ($user_email_template && $user_email_template->system_type && $user_email_template->system_type == 'send_review') {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $user,
                        'review' => $review,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                    ];
                    $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                    $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                    $mail_data->content = $this->sendEmailData($mail_data)->content;
                    $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                    $sms_message = $this->sendEmailData($mail_data)->sms_body;
                    $user->tel_cell = str_replace('+', '', $user->tel_cell);
                    if ($user->tel_cell) {
                        $this->checkWhatsappExist($user_email_template->sms_status, $user->tel_cell, $sms_message);
                    }
                    $lead_source = (object)[
                        'source' => 'checkout',
                    ];
                    $tracking = new Tracking();
                    $tracking->review_id = $review->id;
                    $tracking->content = serialize($this->sendEmailData($mail_data));
                    $tracking->from_email = config('mail.from.address');
                    $tracking->to_email = $user->email;
                    $tracking->subject = $user_email_template->subject;
                    $tracking->lead_source = serialize($lead_source);
                    $tracking->ip = $this->getClientIpAddress();
                    $tracking->save();
                    Mail::to($user->email)->send(new SiteEmail($mail_data));
                }
            }
        }
        $email_templates = EmailTemplate::where('system_type', 'send_review')
            ->join('alert_preferences', 'alert_preferences.id', 'email_templates.alert_preference_id')
            ->select('email_templates.*', 'alert_preferences.user_group_id')
            ->get();
        $user_groups_array = array();
        foreach ($email_templates as $value) {
            $alert_preference = AlertPreference::find($value->alert_preference_id);
            $user_groups_array[] = $alert_preference->user_group_id;
        }
        $user_groups = array_unique(explode(',', implode(',', $user_groups_array)));
        $user_groups = array_filter($user_groups, function ($value) {
            return $value !== "3";
        });
        //admin send email
        $admin_users = UserGroupMap::whereIn('user_group_id', $user_groups)
            ->join('users', 'users.id', 'user_group_maps.user_id')
            ->select(
                'user_group_maps.*',
                'users.email as user_email',
                'users.alert_preferences as user_alert_preferences',
            )
            ->where('users.verified', true)
            ->get();
        foreach ($admin_users as $admin_user) {
            if ($admin_user->user_alert_preferences) {
                if (!empty($admin_user->user_alert_preferences)) {
                    $alert_preferences = unserialize($admin_user->user_alert_preferences);
                    foreach ($alert_preferences as $key => $value) {
                        $alert_preference = AlertPreference::where('key', $key)->first();
                        $email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)->get();
                        foreach ($email_templates as $email_template) {
                            if ($email_template && $email_template->system_type && $email_template->system_type == 'send_review') {
                                $email_template->email_body = unserialize($email_template->email_body);
                                $email_template->sms_body = unserialize($email_template->sms_body);
                                $mail_data = (object)[
                                    'user' => $user,
                                    'review' => $review,
                                    'subject' => $email_template->subject,
                                    'pre_header' => $email_template->pre_header,
                                    'email_body' => $email_template->email_body,
                                    'sms_status' => $email_template->sms_status ? true : false,
                                    'sms_body' => $email_template->sms_body,
                                ];
                                $mail_data->subject = $this->sendEmailData($mail_data)->subject;
                                $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
                                $mail_data->content = $this->sendEmailData($mail_data)->content;
                                $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
                                $lead_source = (object)[
                                    'source' => 'update_user',
                                ];
                                $tracking = new Tracking();
                                $tracking->review_id = $review->id;
                                $tracking->content = serialize($this->sendEmailData($mail_data));
                                $tracking->from_email = config('mail.from.address');
                                $tracking->to_email = $user->email;
                                $tracking->subject = $email_template->subject;
                                $tracking->lead_source = serialize($lead_source);
                                $tracking->ip = $this->getClientIpAddress();
                                $tracking->save();
                                // if ($email_template->sms_status && $user->tel_cell) {
                                if ($email_template->sms_status && $user->tel_cell) {
                                    $sms_tracking = Tracking::findOrFail($tracking->id);
                                    $sms_tracking->sms_content = serialize($mail_data->sms_content);
                                    $sms_tracking->from_phone = config('app.twilio_number');
                                    $sms_tracking->to_phone =  $user->tel_cell;
                                    // $sms_tracking->to_phone = $user->tel_cell;
                                    $sms_tracking->update();
                                }
                                Mail::to($admin_user->user_email)->send(new SiteEmail($mail_data));
                            }
                        }
                    }
                }
            }
        }
        return response()->json([
            'message' => 'Product review submit successfully',
        ], 200);
    }
    public function getReviewProduct($product_item_id)
    {
        $item = OrderItem::where('product_item_id', $product_item_id)->first();
        return response()->json($item, 200);
    }
    public function makeProductTitle($items)
    {
        $title_array = array();
        foreach (collect($items)->sortBy('ordering') as $value) {
            if ($value->input_type_id == 1 && $value->show_on_title) {
                $title_array[] = $value->data->title;
            }
            if ($value->attribute_type_id == 1 && $value->input_type_id) {
                $condition = $value->data->title;
            }
        }
        return (object)[
            'title' => implode(', ', array_unique($title_array)),
            'condition' => $condition,
        ];
    }
    public function getReviewItem($id)
    {
        $item = Review::find($id);
        $item->review_images = unserialize($item->review_images);
        // dd($item);
        return view('web.jquery_live.edit_review_item', compact('item'));
    }
    // public function updateReviewItem(Request $request,$id){
    //     dd($id, $request->all());
    // }
    public function filesUploadReview(Request $request, $target, $id)
    {
        $store_folder = $target;
        $file = $request->file($target);
        $validator = Validator::make($request->all(), [
            $target => 'file|mimes:jpg,jpeg,gif,png,svg,webp,mp4,avi,mov,wmv'
        ]);
        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }
        $item = Review::findOrFail($id);
        $imgName = $target . '_' . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName() . '/' . $item->id;
        $request->$target->storeAs($this->reviewFolderPath . $folderName, $imgName);
        $file_path = $folderName . '/' . $imgName;
        $image_info = (object)([
            'id' => uniqid(),
            'file_id' => uniqid(),
            $target => $file_path,
            'folder' => $this->folderName(),
            'file_path' => $file_path,
            'extension' => $file->extension(),
            'file_caption' => null,
            'file_order' => time(),
            'layout' => $request->view,
        ]);
        if ($item->$target) {
            $images = unserialize($item->$target);
            if (count($images) > 10) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  'upload limit end'
                    ],
                    200
                );
            }
            array_push($images, $image_info);
        } else {
            $images = array($image_info);
        }
        $item->$target = serialize($images);
        $item->update();
        $imagesCollection = collect(unserialize($item->$target));
        $images = $imagesCollection->sortBy('file_order');
        $route_image_info = '';
        // $route_image_info = route('web_auth.review.item.files.upload.image.info', [$target, $item->id]);
        foreach ($images as $image) {
            $image->route_image_remove = route('web_auth.review.item.files.remove', [$target, $image->file_id, $item->id]);
        }
        return view('admin.jquery_live.images', compact('images', 'item', 'store_folder', 'route_image_info'));
    }
    public function filesRemoveReview($target, $image_id, $id)
    {
        $item = Review::findOrFail($id);
        $images = unserialize($item->$target);
        foreach ($images as $key => $image) {
            if ($image->file_id == $image_id) {
                // dd($target . '/' . $image->file_path);
                Storage::delete('' . $target . '/' . $image->file_path);
                unset($images[$key]);
                break;
            }
        }
        if (count($images) < 0) {
            $item->$target = null;
        }
        $item->$target = serialize($images);
        $item->update();
        return response()->json('files_success', 200);
    }
    public function filesUploadRefund(Request $request, $target, $id)
    {
        $store_folder = $target;
        $file = $request->file($target);
        // Updated validation rules to support video files
        $validator = Validator::make($request->all(), [
            $target => 'file|mimes:jpg,jpeg,gif,png,svg,webp,mp4,avi,mov,wmv|max:' . $this->maxFileSize,
        ]);
        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
            }
        }
        $item = OrderRefund::findOrFail($id);
        // Generate filename with original extension
        $imgName = $target . '_' . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName() . '/' . $item->id;
        // Store the file
        $request->$target->storeAs($this->refundFolderPath . $folderName, $imgName);
        $file_path = $folderName . '/' . $imgName;
        // Determine file type
        $fileType = $file->getMimeType();
        $isImage = strpos($fileType, 'image/') === 0;
        $isVideo = strpos($fileType, 'video/') === 0;
        $file_info = (object)([
            'id' => uniqid(),
            'file_id' => uniqid(),
            $target => $file_path,
            'folder' => $this->folderName(),
            'file_path' => $file_path,
            'extension' => $file->extension(),
            'file_type' => $isImage ? 'image' : 'video',
            'file_caption' => null,
            'file_order' => time(),
            'layout' => $request->view,
        ]);
        // Check upload limit
        if ($item->$target) {
            $files = unserialize($item->$target);
            if (count($files) > 10) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  'Upload limit reached'
                    ],
                    200
                );
            }
            array_push($files, $file_info);
        } else {
            $files = array($file_info);
        }
        $item->$target = serialize($files);
        $item->update();
        $filesCollection = collect(unserialize($item->$target));
        $files = $filesCollection->sortBy('file_order');
        // Add remove route for each file
        foreach ($files as $file) {
            $file->route_file_remove = route('web_auth.review.item.files.remove', [$target, $file->file_id, $item->id]);
        }
        return view('admin.jquery_live.images', compact('files', 'item', 'store_folder'));
    }
    public function filesRemoveRefund($target, $image_id, $id)
    {
        $item = OrderRefund::findOrFail($id);
        $images = unserialize($item->$target);
        foreach ($images as $key => $image) {
            if ($image->file_id == $image_id) {
                Storage::delete('' . $target . '/' . $image->file_path);
                unset($images[$key]);
                break;
            }
        }
        if (count($images) < 0) {
            $item->$target = null;
        }
        $item->$target = serialize($images);
        $item->update();
        return response()->json('files_success', 200);
    }
    public function liveOrderReturnItem($id)
    {
        $item = OrderRefund::find($id);
        $item->refund_images = unserialize($item->refund_images);
        return view('web.jquery_live.edit_return', compact('item'));
    }
    public function liveOrderItems(Request $request, $order_id)
    {
        $sku_s = explode(', ', $request->order_sku_s);
        $items = OrderItem::where('order_id', $order_id)
            ->whereIn('sku', $sku_s)
            ->get();
        foreach ($items as $key => $value) {
            $refund_item = OrderRefund::where('sku', $value->sku)->first();
            if ($refund_item) {
                unset($items[$key]);
            } else {
                $value->title = 'SKU: ' . $value->sku . ' - $' . $value->price;
                $value->id = $value->sku;
            }
        }
        return view('web.jquery_live.options', compact('items'));
    }
    public function getMobileSubMenu(Request $request)
    {
        $items = Page::where('parent_menu_id', $request->menu_id)
            ->orderBy('pages.ordering')
            ->get(['id', 'slug', 'title', 'access_label_id', 'parent_menu_id']);
        $menu_id = $request->menu_id;
        if ($request->menu_pages) {
            $item = Page::where('id', $request->menu_id)->first();
            $have_parent_menu = count($item->menuChildren) > 0 ? 1 : 0;
            // dd($have_parent_menu);
            return view('web.jquery_live.sub_menu_pages', compact('menu_id', 'have_parent_menu'));
        }
        foreach ($items as $key => $page) {
            if (!is_null($page->access_label_id)) {
                $pageRoleIds = AccessLabel::findOrFail($page->access_label_id)->user_group_id;
                $pageRoleIds = explode(',', $pageRoleIds);
            }
            $page->access = false;
            if (($page->access_label_id == 1) || (auth()->check() && in_array(auth()->user()->user_group_id, $pageRoleIds))) {
                $page->access = true;
            }
        }
        $item = Page::where('id', $request->menu_id)->first();
        if ($request->menu_title) {
            $item = Page::where('id', $request->menu_id)->first(['id', 'title', 'menu_name', 'parent_menu_id']);
            return response()->json($item, 200);
        }
        return view('web.jquery_live.sub_menu_items', compact('items', 'item'));
    }
    public function getMenuDepth($menuItem)
    {
        // If no children, depth is 1
        if ($menuItem->children->isEmpty()) {
            return 1;
        }
        // If children exist, calculate depth of each child
        $maxDepth = 0;
        foreach ($menuItem->children as $child) {
            $maxDepth = max($maxDepth, $this->getMenuDepth($child));
        }
        // Return depth + 1 for the current level
        return 1 + $maxDepth;
    }
    public function sendDiscount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'discount_email' => 'nullable|email|unique:sended_discounts,email', // Ensure email is unique in the 'sended_discounts' table
            'discount_phone' => 'nullable|string|max:20|regex:/^\+?[0-9\s\-()]*$/|unique:sended_discounts,phone', // Ensure phone is unique in the 'sended_discounts' table
        ]);
        //   dd($request->get('discount_phone'));
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ]);
        }
        // Check if at least one field (email or phone) is provided
        if (!$request->filled('discount_email') && !$request->filled('discount_phone')) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide an email address or phone number.',
            ]);
        }
        $promo = Promo::where('global', true)->first();
        $data = [
            'email' => $request->get('discount_email'),
            'phone' => $request->get('discount_phone'),
            'promo_id' => $promo->id,
        ];
        $discount = SentDiscount::create($data);
        // dd($discount->phone);
        $userEmailTemplatesGroup = EmailTemplateGroup::where('title', 'Discount')->first();
        if (!$userEmailTemplatesGroup) {
            return response()->json([
                'success' => false,
                'message' => 'No email templates found for the Discount group.',
            ]);
        }
        $userEmailTemplates = EmailTemplate::where('email_template_group_id', $userEmailTemplatesGroup->id)->get();
        if ($userEmailTemplates->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No email templates available under the Discount group.',
            ]);
        }
        foreach ($userEmailTemplates as $userEmailTemplate) {
            $emailBody = unserialize($userEmailTemplate->email_body);
            $smsBody = unserialize($userEmailTemplate->sms_body);
            $mailData = (object)[
                'promo_code' => $promo->promo_code,
                'promo_date' => $promo->to_date,
                'subject' => $userEmailTemplate->subject,
                'pre_header' => $userEmailTemplate->pre_header,
                'email_body' => $emailBody,
                'sms_status' => $userEmailTemplate->sms_status ? true : false,
                'sms_body' => $smsBody,
            ];
            $sms_message = $this->sendEmailData($mailData)->sms_body;
            $discount->phone = str_replace('+', '', $discount->phone);
            if ($discount->phone) {
                $this->checkWhatsappExist($userEmailTemplate->sms_status, $discount->phone, $sms_message);
            }
            $processedMailData = $this->sendEmailData($mailData);
            $mailData->subject = $processedMailData->subject;
            $mailData->pre_header = $processedMailData->pre_header;
            $mailData->content = $processedMailData->content;
            $mailData->sms_content = $processedMailData->sms_body;
            if (!empty($discount->email)) {
                Mail::to($discount->email)->later(
                    now()->addSeconds(30),
                    new SiteEmail($mailData)
                );
            }
        }
        return response()->json([
            'success' => true,
            'message' => 'Promo registration successful.',
            'data' => $data,
        ]);
    }
    public function trackOrder($order)
    {
        $tracking_code = $order->tracking_code;
        if ($tracking_code) {
            try {
                $trackingResponse = $this->dhlService->trackingParcel($tracking_code);
                if ($trackingResponse instanceof \Illuminate\Http\JsonResponse) {
                    $trackingData = $trackingResponse->json();
                } elseif (is_array($trackingResponse)) {
                    $trackingData = $trackingResponse;
                } else {
                    $trackingData = [];
                }
                $tracking = $trackingData['shipments'][0] ?? null;
            } catch (\Exception $e) {
                Log::error('DHL Tracking Error: ' . $e->getMessage());
                $tracking = null;
            }
        } else {
            $tracking = null;
        }
        // dd($tracking);
        if ($tracking) {
            return $TrackingDetails = (object) [
                'id' => $tracking['shipmentTrackingNumber'],
                'carrier' => $tracking['productCode'],
                'receiverDetails' => $tracking['receiverDetails'],
                'shipperDetails' => $tracking['shipperDetails'],
                'status' => $tracking['status'],
                'shipment_id' => $tracking['shipmentTrackingNumber'],
                // 'public_url' => $tracking->public_url,
            ];
        } else {
            return $TrackingDetails = null;
        }
    }
    public function helpFulCount(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'help_full' => 'nullable',
        ]);
        if ($validator->fails()) {
            return response()->json(['error' => 'Validation failed'], 422);
        }
        $userId = auth()->id();
        if (!$userId) {
            return response()->json(['error' => 'Your acount not Loggin in'], 404);
        }
        $review = Review::where('user_id', $userId)->find($id);
        if (!$review) {
            return response()->json(['error' => 'Review not found'], 404);
        }
        if ($request->help_full) {
            $review->help_full = ($review->help_full ?? 0) + 1;
        } else {
            $review->help_full = max(0, ($review->help_full ?? 0) - 1);
        }
        $review->save();
        return response()->json([
            'success' => true,
            'help_full' => $review->help_full,
            'message' => 'Vote recorded'
        ]);
    }
    public function showPage(Request $request, $slug = 'home', $selector = null)
    {

        $this->checkAbandonedCarts();
        //Session:flush();
        // dd("hi");
        $user = User::where('username', $slug)->first();
        $page = Page::whereSlug($slug)->where('status', 1)->first();
        $location_data = Session::get('shop_logged_data');
        $page ?? $slug = 'home';
        $page ?? $page = Page::whereSlug($slug)->where('status', 1)->first();
        if ($user) $page = Page::where('page_key', 'my_profile')->where('status', 1)->first();
        if (Auth()->user()) $page->user_menu = $this->getMenu('user_menu', $slug);
        $page->sections_before_main = $this->view_sections($page->id)->sections_before_main;
        $page->sections_after_main = $this->view_sections($page->id)->sections_after_main;
        $page->top_menu = $this->getMenu('top_menu', $slug);
        $page->menu = $this->getMenu('main_menu', $slug);
        $page->footer_menu = $this->getMenu('footer_menu', $slug);
        $page->footer_menu_b = $this->getMenu('footer_menu_b', $slug);
        $page->footer_menu_c = $this->getMenu('footer_menu_c', $slug);
        $profile_page = Page::where('page_key', 'my_profile')->first();
        $page->review_images = array();
        if (Session::has('review_images')) {
            $page->review_images = Session::get('review_images');
        }
        $page->refund_images = array();
        if (Session::has('refund_images')) {
            $page->refund_images = Session::get('refund_images');
        }
        $page->countries = $this->countries();
        $products_page = Page::where('page_key', 'products')->first();
        if (!empty($products_page)) {
            $page->view_products_url = route('page', $products_page->slug);
        }
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $allowedLocations = ($selectedCountry === $site_info->country) ? null : $selectedCountry;
        $storeIds = $this->getEligibleStores($selectedCountry);
        if (Session::has('cart')) {
            $cart = Session::get('cart');
            foreach ($cart->items as $value) {
                $value->condition = $this->makeCondition(unserialize($value->item_attributes));
                if (count($storeIds) > 0) {
                    $value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->whereIn('store_id', $storeIds)->get(), $value->cart_group);
                }
                // else {
                //     //$value->available_item = $this->stockCalculation(ProductItem::where('product_id', $value->product_id)->where('status', 1)->get(), $value->cart_group);
                // }
            }
            $page->cart = $cart;
            $checkout_page = Page::where('page_key', 'checkout')->first();
            $cart_page = Page::where('page_key', 'cart')->first();
            if (!empty($checkout_page)) {
                $page->buy_now_url = route('page', $checkout_page->slug);
            }
            if (!empty($cart_page)) {
                $page->view_cart_url = route('page', $cart_page->slug);
            }
            if (!empty($thank_page)) {
                $page->view_thank_you_url = route('page', $thank_page->slug);
            }
        }
        if (Auth()->user() && !empty($profile_page)) {
            $user_group_map = UserGroupMap::where('user_id', Auth()->user()->id)->first();
            $page->user_group_id = $user_group_map->user_group_id;
            $page->my_profile = route('page', $profile_page->slug);
            $orders = Order::where('customer_id', Auth()->user()->id)->get();
            $order_items = array();
            foreach ($orders as $value) {
                $orders_array[] = $value->id;
            }
            $reviews_items = Review::where([
                'user_id' => auth()->user()->id,
            ])
                ->where('parent_review_id', null)
                ->get(['product_item_id', 'product_sku_s']);
            $reviews_items_array = array();
            $product_sku_s_array = array();
            foreach ($reviews_items as $value) {
                $reviews_items_array[] = $value->product_item_id;
                $product_sku_s_array[] = $value->product_sku_s;
            }
            $items = OrderItem::join('orders', 'orders.id', 'order_items.order_id')
                ->select('order_items.*', 'orders.customer_id as customer_id', 'orders.order_no as order_no')
                ->where('orders.customer_id', auth()->user()->id)
                // ->whereNotIn('product_item_id', $reviews_items_array)
                ->get();
            $items = collect($this->getOrderItems($items))->whereNotIn('sku_details', $product_sku_s_array);
            $page->products = $items;
        }
        $storeinfo = Page::where('page_key', 'storeinfo')->first();
        if (!empty($storeinfo)) {
            $page->view_store_info_url = route('page', $storeinfo->slug);
        }
        $defaultCurrencyCode = getDefaultCurrencyCode();
        $defaultCurrencySymbol = getDefaultCurrencySymbol();
        if ($user) return $this->showProfilePage($slug, $page, $user->username);
        if ($page) {
            $countries = $this->countries();
            foreach ($countries as $key => $value) {
                $countries_data[$key] = $value . ' (' . $defaultCurrencyCode . ' ' . $defaultCurrencySymbol . ')';
            }
            $page->counties_currency = $countries_data;
            $shop_logged_data = (object)[
                'location_popup' => true,
                'country' => '',
                'country_name' => '',
                'currency' => '',
            ];
            $page->location_data = $shop_logged_data;
            // dd(Session::get('shop_logged_data'));
            if (Session::has('shop_logged_data')) {
                $location_data = Session::get('shop_logged_data');
                $shop_logged_data = (object)[
                    'location_popup' => false,
                    'country' => $location_data->country,
                    'country_name' => $location_data->country_name,
                    'currency' => $location_data->currency,
                    'currency_rate' => $location_data->currency_rate,
                    'currency_symbol' => $location_data->currency_symbol,
                    'country_code' => $location_data->country_code,
                    'country_short_code' => $location_data->country_short_code,
                    'banks' => $location_data->banks,
                    'rate_adjustment_last_updated' => $this->getLatestCurrencyAdjustmentTime($location_data->country_name)
                ];
                Session::put([
                    'shop_logged_data' => $shop_logged_data,
                ]);
                $page->location_data = Session::get('shop_logged_data');
            } else {
                $location_data = $request->ipinfo->all;
                $setting = Setting::where('status', true)->where('country', 'NG')->first();
                //   dd($setting);
                $countryName = $this->countries($setting->country ?? 'NG');
                if ($location_data && !empty($location_data['country_name'])) {
                    $shop_currency = '';
                    if ($location_data['country'] != 'US' && $location_data['country'] != 'us') {
                        $shop_currency = 'Payment option in <strong>' . $this->getLocalCurrencyName($location_data['country']) . '</strong> available at checkout';
                    }
                    $shop_logged_data = (object)[
                        'location_popup' => true,
                        'country' => $location_data['country'],
                        'country_name' => 'Your location is set to ' . $location_data['country_name'],
                        'currency' => $shop_currency,
                        'currency_rate' => $this->getLocalCurrencyRate($location_data['country']),
                        'currency_symbol' => $this->getLocalCurrencySymbol($location_data['country']),
                        'country_code' => $this->getLocalCountryCode($location_data['country']),
                        'country_short_code' => $this->getLocalCountryShortCode($location_data['country']),
                        'banks' => $this->getBanks($location_data['country']),
                        'rate_adjustment_last_updated' => $this->getLatestCurrencyAdjustmentTime($location_data['country'])
                    ];
                    Session::put([
                        'shop_logged_data' => $shop_logged_data,
                    ]);
                    $page->location_data = Session::get('shop_logged_data');
                    $page->currency_rate = $this->getLocalCurrencyRate($location_data['country']);
                }
            }
            $page->series = ProductSeries::whereStatus(true)->get();
            // dd($page->brands);
            $product_items = count($this->productItemsData());
            $random_count = 1;
            if ($product_items > 4) {
                $random_count = 5;
            }
            if ($product_items > 0) {
                $page->recommended_items = collect($this->productItemsData())->random($random_count)->take(20);
            }
            //dd($page->page_key);

            if ($page->page_type == 'build_page') {

                return $this->showBuildPage($slug, $page, $selector, $request);
            } elseif ($page->page_type == 'component_page') {
                return $this->showComponent($slug, $page, $selector);
            } else {

                return $page->checkAccessLabel($slug) ? view('web.page', compact('page')) : abort(403);
            }
        }
        return redirect('/');
    }
    public function ProductNotAvailable($slug, $page, $selector, $request)
    {
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selected_country = $site_data->country_name ?? $this->getCountryNameByCode($site_info->country);
        return view('web.product_not_found',  compact('selected_country', 'page'));
    }

    public function getCountryNameByCode($code)
    {
        $countriesFile = storage_path('countries.json');
        $countryName = '';

        if (file_exists($countriesFile)) {
            $countriesJson = file_get_contents($countriesFile);
            $countriesArray = json_decode($countriesJson, true);

            // Create a map of code => name
            foreach ($countriesArray as $country) {
                if ($country['code'] == $code) {
                    $countryName = $country['name'];
                    break;
                }
            }
        }
        return $countryName;
    }
    public function getLocalCostAndCurrency($payment_method, $location_data)
    {
        $allowedCurrencies = explode(',', $payment_method->allowed_currency);

        // Trim any whitespace
        $allowedCurrencies = array_map('trim', $allowedCurrencies);
        $finalAmount = $cart->grand_total;
        if (in_array($location_data->country_code, $allowedCurrencies) && getDefaultCurrencyCode() != $location_data->country_code) {
            $selectedCurrency = $location_data->country_code;
            $finalAmount =  $finalAmount * $location_data->currency_rate;
        } else {
            $selectedCurrency = getDefaultCurrencyCode(); // Default fallback if empty
        }
        if (!in_array($selectedCurrency, $allowedCurrencies)) {
            return response()->json([
                'status' => false,
                'message' => 'Selected currency is not allowed for this payment method.',
            ], 422);
        }
        return ['finalAmount' => $finalAmount, 'currency' => $selectedCurrency];
    }
}
