<?php

// Add these methods to your existing CartController or create a new CouponCartController

namespace App\Http\Controllers;

use App\Services\CouponService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Exception;

class CouponCartController extends Controller
{
    protected $couponService;

    public function __construct(CouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    /**
     * Apply coupon to cart
     */
    public function applyCoupon(Request $request)
    {
        try {
            $request->validate([
                'coupon_code' => 'required|string|max:255'
            ]);

            $user = Auth::user();
            $couponCode = strtoupper(trim($request->coupon_code));

            $result = $this->couponService->applyCouponToCart($couponCode, $user);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result,
                    'cart_totals' => $this->getCartTotals()
                ]);
            }

            return back()->with('success', $result['message']);

        } catch (Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 400);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove coupon from cart
     */
    public function removeCoupon(Request $request)
    {
        try {
            $result = $this->couponService->removeCouponFromCart();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result,
                    'cart_totals' => $this->getCartTotals()
                ]);
            }

            return back()->with('success', $result['message']);

        } catch (Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 400);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get promo popup coupon
     */
    public function getPromoPopupCoupon()
    {
        $coupon = $this->couponService->getPromoPopupCoupon();
        
        if (!$coupon) {
            return response()->json(['coupon' => null]);
        }

        return response()->json([
            'coupon' => [
                'code' => $coupon->code,
                'name' => $coupon->name,
                'description' => $coupon->description,
                'discount_text' => $coupon->getDiscountText(),
                'condition_description' => $coupon->getConditionDescription(),
                'image' => $coupon->image ? asset('storage/' . $coupon->image) : null,
                'expires_at' => $coupon->to_date ? $coupon->to_date->format('M j, Y') : null
            ]
        ]);
    }

    /**
     * Get current cart totals for AJAX responses
     */
    protected function getCartTotals(): array
    {
        $cart = Session::get('cart');
        
        if (!$cart) {
            return [];
        }

        $location_data = Session::get('shop_logged_data');
        
        return [
            'subtotal' => $cart->total ?? 0,
            'discount' => $cart->totalDiscount ?? 0,
            'grand_total' => $cart->grand_total ?? 0,
            'subtotal_local' => $cart->total_local ?? 0,
            'discount_local' => $cart->totalDiscount_local ?? 0,
            'grand_total_local' => $cart->grand_total_local ?? 0,
            'currency_symbol' => getDefaultCurrencySymbol(),
            'local_currency_symbol' => $location_data->currency_symbol ?? '',
            'applied_coupon' => $cart->applied_coupon ?? null,
            'free_shipping' => $cart->freeShipping ?? false
        ];
    }

    /**
     * Validate coupon without applying (for preview)
     */
    public function validateCoupon(Request $request)
    {
        try {
            $request->validate([
                'coupon_code' => 'required|string|max:255'
            ]);

            $user = Auth::user();
            $couponCode = strtoupper(trim($request->coupon_code));

            $coupon = $this->couponService->validateCoupon($couponCode, $user);

            return response()->json([
                'valid' => true,
                'coupon' => [
                    'code' => $coupon->code,
                    'name' => $coupon->name,
                    'description' => $coupon->description,
                    'discount_text' => $coupon->getDiscountText(),
                    'condition_description' => $coupon->getConditionDescription()
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'valid' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}