<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCarrierOptionCountryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       Schema::create('carrier_option_country', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('carrier_option_id');
        $table->string('country_code', 2); // ISO 3166-1 alpha-2

        $table->foreign('carrier_option_id')
              ->references('id')->on('carrier_options')
              ->onDelete('cascade');
    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('carrier_option_country');
    }
}
