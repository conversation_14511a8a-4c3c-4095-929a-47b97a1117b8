<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProdcutCategoriesParentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the table doesn't already exist
        if (!Schema::hasTable('prodcut_categories_parents')) {
            Schema::create('prodcut_categories_parents', function (Blueprint $table) {
                $table->id();

                if (!Schema::hasColumn('prodcut_categories_parents', 'title')) {
                    $table->string('title');
                }

                if (!Schema::hasColumn('prodcut_categories_parents', 'description')) {
                    $table->string('description');
                }

                if (!Schema::hasColumn('prodcut_categories_parents', 'slug')) {
                    $table->string('slug');
                }

                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop only if the table exists
        if (Schema::hasTable('prodcut_categories_parents')) {
            Schema::dropIfExists('prodcut_categories_parents');
        }
    }
}
