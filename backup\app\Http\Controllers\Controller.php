<?php



namespace App\Http\Controllers;



use Carbon\Carbon;

use App\Models\Page;

use App\Models\Order;

use App\Models\Reward;

use App\Models\Collect;

use App\Models\Product;

use App\Models\Setting;

use Twilio\Rest\Client;

use App\Models\OrderItem;

use App\Models\UserGroup;

use App\Models\UserGroupMap;

use App\Models\AccessLabel;

use App\Models\ProductItem;

use App\Models\UserBilling;

use App\Models\ProductBrand;

use App\Models\UserShipping;

use App\Models\CustomerPromo;

use App\Models\ProductSeries;

use App\Models\ProductCategory;

use App\Models\TransactionMethod;

use Illuminate\Support\Facades\File;

use Illuminate\Support\Facades\Http;

use Illuminate\Support\Facades\Artisan;

use Illuminate\Support\Facades\Storage;

use Illuminate\Foundation\Bus\DispatchesJobs;

use Illuminate\Routing\Controller as BaseController;

use Illuminate\Foundation\Validation\ValidatesRequests;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Cache;

use Illuminate\Support\Facades\DB;

use Illuminate\Support\Facades\Session;

use App\Models\User;

use App\Models\PointSystem;



class Controller extends BaseController

{

    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;



    protected $sessionFolderPath = 'session_files/';

    protected $itemPerPage = 25;

    protected $itemPerPagePublic = 15;

    protected $maxFileSize = '2000';

    protected $maxFileSizeVideo = '10000';

    protected $reward_value_per_usd = 0.5;

    protected $cash_value_per_points = 500;

    protected $review_rewards_points = 50;



    public function folderName()

    {

        $folder_path = Carbon::now()->format('Y') . '/' . Carbon::now()->format('m-Y');

        // $folder_path = Carbon::now()->format('Y') . '/' . Carbon::now()->format('m-Y') . '/';

        return $folder_path;

    }



    public static function country_data($option = null, $symbol = null)

    {

        $options = array(

            array('name' => 'Afghanistan', 'iso_alpha2' => 'AF', 'iso_alpha3' => 'AFG', 'iso_numeric' => '4', 'calling_code' => '93', 'currency_code' => 'AFN', 'currency_name' => 'Afghani', 'currency_symbol' => '؋'),

            array('name' => 'Albania', 'iso_alpha2' => 'AL', 'iso_alpha3' => 'ALB', 'iso_numeric' => '8', 'calling_code' => '355', 'currency_code' => 'ALL', 'currency_name' => 'Lek', 'currency_symbol' => 'Lek'),

            array('name' => 'Algeria', 'iso_alpha2' => 'DZ', 'iso_alpha3' => 'DZA', 'iso_numeric' => '12', 'calling_code' => '213', 'currency_code' => 'DZD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'American Samoa', 'iso_alpha2' => 'AS', 'iso_alpha3' => 'ASM', 'iso_numeric' => '16', 'calling_code' => '1684', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Andorra', 'iso_alpha2' => 'AD', 'iso_alpha3' => 'AND', 'iso_numeric' => '20', 'calling_code' => '376', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Angola', 'iso_alpha2' => 'AO', 'iso_alpha3' => 'AGO', 'iso_numeric' => '24', 'calling_code' => '244', 'currency_code' => 'AOA', 'currency_name' => 'Kwanza', 'currency_symbol' => 'Kz'),

            array('name' => 'Anguilla', 'iso_alpha2' => 'AI', 'iso_alpha3' => 'AIA', 'iso_numeric' => '660', 'calling_code' => '1264', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Antarctica', 'iso_alpha2' => 'AQ', 'iso_alpha3' => 'ATA', 'iso_numeric' => '10', 'calling_code' => '672', 'currency_code' => '', 'currency_name' => '', 'currency_symbol' => ''),

            array('name' => 'Antigua and Barbuda', 'iso_alpha2' => 'AG', 'iso_alpha3' => 'ATG', 'iso_numeric' => '28', 'calling_code' => '1268', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Argentina', 'iso_alpha2' => 'AR', 'iso_alpha3' => 'ARG', 'iso_numeric' => '32', 'calling_code' => '54', 'currency_code' => 'ARS', 'currency_name' => 'Peso', 'currency_symbol' => '$'),

            array('name' => 'Armenia', 'iso_alpha2' => 'AM', 'iso_alpha3' => 'ARM', 'iso_numeric' => '51', 'calling_code' => '374', 'currency_code' => 'AMD', 'currency_name' => 'Dram', 'currency_symbol' => ''),

            array('name' => 'Aruba', 'iso_alpha2' => 'AW', 'iso_alpha3' => 'ABW', 'iso_numeric' => '533', 'calling_code' => '297', 'currency_code' => 'AWG', 'currency_name' => 'Guilder', 'currency_symbol' => 'ƒ'),

            array('name' => 'Australia', 'iso_alpha2' => 'AU', 'iso_alpha3' => 'AUS', 'iso_numeric' => '36', 'calling_code' => '61', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Austria', 'iso_alpha2' => 'AT', 'iso_alpha3' => 'AUT', 'iso_numeric' => '40', 'calling_code' => '43', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Azerbaijan', 'iso_alpha2' => 'AZ', 'iso_alpha3' => 'AZE', 'iso_numeric' => '31', 'calling_code' => '994', 'currency_code' => 'AZN', 'currency_name' => 'Manat', 'currency_symbol' => 'ман'),

            array('name' => 'Bahamas', 'iso_alpha2' => 'BS', 'iso_alpha3' => 'BHS', 'iso_numeric' => '44', 'calling_code' => '1242', 'currency_code' => 'BSD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Bahrain', 'iso_alpha2' => 'BH', 'iso_alpha3' => 'BHR', 'iso_numeric' => '48', 'calling_code' => '973', 'currency_code' => 'BHD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Bangladesh', 'iso_alpha2' => 'BD', 'iso_alpha3' => 'BGD', 'iso_numeric' => '50', 'calling_code' => '880', 'currency_code' => 'BDT', 'currency_name' => 'Taka', 'currency_symbol' => ''),

            array('name' => 'Barbados', 'iso_alpha2' => 'BB', 'iso_alpha3' => 'BRB', 'iso_numeric' => '52', 'calling_code' => '1246', 'currency_code' => 'BBD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Belarus', 'iso_alpha2' => 'BY', 'iso_alpha3' => 'BLR', 'iso_numeric' => '112', 'calling_code' => '375', 'currency_code' => 'BYR', 'currency_name' => 'Ruble', 'currency_symbol' => 'p.'),

            array('name' => 'Belgium', 'iso_alpha2' => 'BE', 'iso_alpha3' => 'BEL', 'iso_numeric' => '56', 'calling_code' => '32', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Belize', 'iso_alpha2' => 'BZ', 'iso_alpha3' => 'BLZ', 'iso_numeric' => '84', 'calling_code' => '501', 'currency_code' => 'BZD', 'currency_name' => 'Dollar', 'currency_symbol' => 'BZ$'),

            array('name' => 'Benin', 'iso_alpha2' => 'BJ', 'iso_alpha3' => 'BEN', 'iso_numeric' => '204', 'calling_code' => '229', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Bermuda', 'iso_alpha2' => 'BM', 'iso_alpha3' => 'BMU', 'iso_numeric' => '60', 'calling_code' => '1441', 'currency_code' => 'BMD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Bhutan', 'iso_alpha2' => 'BT', 'iso_alpha3' => 'BTN', 'iso_numeric' => '64', 'calling_code' => '975', 'currency_code' => 'BTN', 'currency_name' => 'Ngultrum', 'currency_symbol' => ''),

            array('name' => 'Bolivia', 'iso_alpha2' => 'BO', 'iso_alpha3' => 'BOL', 'iso_numeric' => '68', 'calling_code' => '591', 'currency_code' => 'BOB', 'currency_name' => 'Boliviano', 'currency_symbol' => '$b'),

            array('name' => 'Bosnia and Herzegovina', 'iso_alpha2' => 'BA', 'iso_alpha3' => 'BIH', 'iso_numeric' => '70', 'calling_code' => '387', 'currency_code' => 'BAM', 'currency_name' => 'Marka', 'currency_symbol' => 'KM'),

            array('name' => 'Botswana', 'iso_alpha2' => 'BW', 'iso_alpha3' => 'BWA', 'iso_numeric' => '72', 'calling_code' => '267', 'currency_code' => 'BWP', 'currency_name' => 'Pula', 'currency_symbol' => 'P'),

            array('name' => 'Bouvet Island', 'iso_alpha2' => 'BV', 'iso_alpha3' => 'BVT', 'iso_numeric' => '74', 'calling_code' => '', 'currency_code' => 'NOK', 'currency_name' => 'Krone', 'currency_symbol' => 'kr'),

            array('name' => 'Brazil', 'iso_alpha2' => 'BR', 'iso_alpha3' => 'BRA', 'iso_numeric' => '76', 'calling_code' => '55', 'currency_code' => 'BRL', 'currency_name' => 'Real', 'currency_symbol' => 'R$'),

            array('name' => 'British Indian Ocean Territory', 'iso_alpha2' => 'IO', 'iso_alpha3' => 'IOT', 'iso_numeric' => '86', 'calling_code' => '', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'British Virgin Islands', 'iso_alpha2' => 'VG', 'iso_alpha3' => 'VGB', 'iso_numeric' => '92', 'calling_code' => '1284', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Brunei', 'iso_alpha2' => 'BN', 'iso_alpha3' => 'BRN', 'iso_numeric' => '96', 'calling_code' => '673', 'currency_code' => 'BND', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Bulgaria', 'iso_alpha2' => 'BG', 'iso_alpha3' => 'BGR', 'iso_numeric' => '100', 'calling_code' => '359', 'currency_code' => 'BGN', 'currency_name' => 'Lev', 'currency_symbol' => 'лв'),

            array('name' => 'Burkina Faso', 'iso_alpha2' => 'BF', 'iso_alpha3' => 'BFA', 'iso_numeric' => '854', 'calling_code' => '226', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Burundi', 'iso_alpha2' => 'BI', 'iso_alpha3' => 'BDI', 'iso_numeric' => '108', 'calling_code' => '257', 'currency_code' => 'BIF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Cambodia', 'iso_alpha2' => 'KH', 'iso_alpha3' => 'KHM', 'iso_numeric' => '116', 'calling_code' => '855', 'currency_code' => 'KHR', 'currency_name' => 'Riels', 'currency_symbol' => '៛'),

            array('name' => 'Cameroon', 'iso_alpha2' => 'CM', 'iso_alpha3' => 'CMR', 'iso_numeric' => '120', 'calling_code' => '237', 'currency_code' => 'XAF', 'currency_name' => 'Franc', 'currency_symbol' => 'FCF'),

            array('name' => 'Canada', 'iso_alpha2' => 'CA', 'iso_alpha3' => 'CAN', 'iso_numeric' => '124', 'calling_code' => '1', 'currency_code' => 'CAD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Cape Verde', 'iso_alpha2' => 'CV', 'iso_alpha3' => 'CPV', 'iso_numeric' => '132', 'calling_code' => '238', 'currency_code' => 'CVE', 'currency_name' => 'Escudo', 'currency_symbol' => ''),

            array('name' => 'Cayman Islands', 'iso_alpha2' => 'KY', 'iso_alpha3' => 'CYM', 'iso_numeric' => '136', 'calling_code' => '1345', 'currency_code' => 'KYD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Central African Republic', 'iso_alpha2' => 'CF', 'iso_alpha3' => 'CAF', 'iso_numeric' => '140', 'calling_code' => '236', 'currency_code' => 'XAF', 'currency_name' => 'Franc', 'currency_symbol' => 'FCF'),

            array('name' => 'Chad', 'iso_alpha2' => 'TD', 'iso_alpha3' => 'TCD', 'iso_numeric' => '148', 'calling_code' => '235', 'currency_code' => 'XAF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Chile', 'iso_alpha2' => 'CL', 'iso_alpha3' => 'CHL', 'iso_numeric' => '152', 'calling_code' => '56', 'currency_code' => 'CLP', 'currency_name' => 'Peso', 'currency_symbol' => ''),

            array('name' => 'China', 'iso_alpha2' => 'CN', 'iso_alpha3' => 'CHN', 'iso_numeric' => '156', 'calling_code' => '86', 'currency_code' => 'CNY', 'currency_name' => 'YuanRenminbi', 'currency_symbol' => '¥'),

            array('name' => 'Christmas Island', 'iso_alpha2' => 'CX', 'iso_alpha3' => 'CXR', 'iso_numeric' => '162', 'calling_code' => '61', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Cocos Islands', 'iso_alpha2' => 'CC', 'iso_alpha3' => 'CCK', 'iso_numeric' => '166', 'calling_code' => '61', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Colombia', 'iso_alpha2' => 'CO', 'iso_alpha3' => 'COL', 'iso_numeric' => '170', 'calling_code' => '57', 'currency_code' => 'COP', 'currency_name' => 'Peso', 'currency_symbol' => '$'),

            array('name' => 'Comoros', 'iso_alpha2' => 'KM', 'iso_alpha3' => 'COM', 'iso_numeric' => '174', 'calling_code' => '269', 'currency_code' => 'KMF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Cook Islands', 'iso_alpha2' => 'CK', 'iso_alpha3' => 'COK', 'iso_numeric' => '184', 'calling_code' => '682', 'currency_code' => 'NZD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Costa Rica', 'iso_alpha2' => 'CR', 'iso_alpha3' => 'CRI', 'iso_numeric' => '188', 'calling_code' => '506', 'currency_code' => 'CRC', 'currency_name' => 'Colon', 'currency_symbol' => '₡'),

            array('name' => 'Croatia', 'iso_alpha2' => 'HR', 'iso_alpha3' => 'HRV', 'iso_numeric' => '191', 'calling_code' => '385', 'currency_code' => 'HRK', 'currency_name' => 'Kuna', 'currency_symbol' => 'kn'),

            array('name' => 'Cuba', 'iso_alpha2' => 'CU', 'iso_alpha3' => 'CUB', 'iso_numeric' => '192', 'calling_code' => '53', 'currency_code' => 'CUP', 'currency_name' => 'Peso', 'currency_symbol' => '₱'),

            array('name' => 'Cyprus', 'iso_alpha2' => 'CY', 'iso_alpha3' => 'CYP', 'iso_numeric' => '196', 'calling_code' => '357', 'currency_code' => 'CYP', 'currency_name' => 'Pound', 'currency_symbol' => ''),

            array('name' => 'Czech Republic', 'iso_alpha2' => 'CZ', 'iso_alpha3' => 'CZE', 'iso_numeric' => '203', 'calling_code' => '420', 'currency_code' => 'CZK', 'currency_name' => 'Koruna', 'currency_symbol' => 'Kč'),

            array('name' => 'Democratic Republic of the Congo', 'iso_alpha2' => 'CD', 'iso_alpha3' => 'COD', 'iso_numeric' => '180', 'calling_code' => '243', 'currency_code' => 'CDF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Denmark', 'iso_alpha2' => 'DK', 'iso_alpha3' => 'DNK', 'iso_numeric' => '208', 'calling_code' => '45', 'currency_code' => 'DKK', 'currency_name' => 'Krone', 'currency_symbol' => 'kr'),

            array('name' => 'Djibouti', 'iso_alpha2' => 'DJ', 'iso_alpha3' => 'DJI', 'iso_numeric' => '262', 'calling_code' => '253', 'currency_code' => 'DJF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Dominica', 'iso_alpha2' => 'DM', 'iso_alpha3' => 'DMA', 'iso_numeric' => '212', 'calling_code' => '1767', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Dominican Republic', 'iso_alpha2' => 'DO', 'iso_alpha3' => 'DOM', 'iso_numeric' => '214', 'calling_code' => '1809', 'currency_code' => 'DOP', 'currency_name' => 'Peso', 'currency_symbol' => 'RD$'),

            array('name' => 'East Timor', 'iso_alpha2' => 'TL', 'iso_alpha3' => 'TLS', 'iso_numeric' => '626', 'calling_code' => '670', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Ecuador', 'iso_alpha2' => 'EC', 'iso_alpha3' => 'ECU', 'iso_numeric' => '218', 'calling_code' => '593', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Egypt', 'iso_alpha2' => 'EG', 'iso_alpha3' => 'EGY', 'iso_numeric' => '818', 'calling_code' => '20', 'currency_code' => 'EGP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'El Salvador', 'iso_alpha2' => 'SV', 'iso_alpha3' => 'SLV', 'iso_numeric' => '222', 'calling_code' => '503', 'currency_code' => 'SVC', 'currency_name' => 'Colone', 'currency_symbol' => '$'),

            array('name' => 'Equatorial Guinea', 'iso_alpha2' => 'GQ', 'iso_alpha3' => 'GNQ', 'iso_numeric' => '226', 'calling_code' => '240', 'currency_code' => 'XAF', 'currency_name' => 'Franc', 'currency_symbol' => 'FCF'),

            array('name' => 'Eritrea', 'iso_alpha2' => 'ER', 'iso_alpha3' => 'ERI', 'iso_numeric' => '232', 'calling_code' => '291', 'currency_code' => 'ERN', 'currency_name' => 'Nakfa', 'currency_symbol' => 'Nfk'),

            array('name' => 'Estonia', 'iso_alpha2' => 'EE', 'iso_alpha3' => 'EST', 'iso_numeric' => '233', 'calling_code' => '372', 'currency_code' => 'EEK', 'currency_name' => 'Kroon', 'currency_symbol' => 'kr'),

            array('name' => 'Ethiopia', 'iso_alpha2' => 'ET', 'iso_alpha3' => 'ETH', 'iso_numeric' => '231', 'calling_code' => '251', 'currency_code' => 'ETB', 'currency_name' => 'Birr', 'currency_symbol' => ''),

            array('name' => 'Falkland Islands', 'iso_alpha2' => 'FK', 'iso_alpha3' => 'FLK', 'iso_numeric' => '238', 'calling_code' => '500', 'currency_code' => 'FKP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'Faroe Islands', 'iso_alpha2' => 'FO', 'iso_alpha3' => 'FRO', 'iso_numeric' => '234', 'calling_code' => '298', 'currency_code' => 'DKK', 'currency_name' => 'Krone', 'currency_symbol' => 'kr'),

            array('name' => 'Fiji', 'iso_alpha2' => 'FJ', 'iso_alpha3' => 'FJI', 'iso_numeric' => '242', 'calling_code' => '679', 'currency_code' => 'FJD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Finland', 'iso_alpha2' => 'FI', 'iso_alpha3' => 'FIN', 'iso_numeric' => '246', 'calling_code' => '358', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'France', 'iso_alpha2' => 'FR', 'iso_alpha3' => 'FRA', 'iso_numeric' => '250', 'calling_code' => '33', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'French Guiana', 'iso_alpha2' => 'GF', 'iso_alpha3' => 'GUF', 'iso_numeric' => '254', 'calling_code' => '', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'French Polynesia', 'iso_alpha2' => 'PF', 'iso_alpha3' => 'PYF', 'iso_numeric' => '258', 'calling_code' => '689', 'currency_code' => 'XPF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'French Southern Territories', 'iso_alpha2' => 'TF', 'iso_alpha3' => 'ATF', 'iso_numeric' => '260', 'calling_code' => '', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Gabon', 'iso_alpha2' => 'GA', 'iso_alpha3' => 'GAB', 'iso_numeric' => '266', 'calling_code' => '241', 'currency_code' => 'XAF', 'currency_name' => 'Franc', 'currency_symbol' => 'FCF'),

            array('name' => 'Gambia', 'iso_alpha2' => 'GM', 'iso_alpha3' => 'GMB', 'iso_numeric' => '270', 'calling_code' => '220', 'currency_code' => 'GMD', 'currency_name' => 'Dalasi', 'currency_symbol' => 'D'),

            array('name' => 'Georgia', 'iso_alpha2' => 'GE', 'iso_alpha3' => 'GEO', 'iso_numeric' => '268', 'calling_code' => '995', 'currency_code' => 'GEL', 'currency_name' => 'Lari', 'currency_symbol' => ''),

            array('name' => 'Germany', 'iso_alpha2' => 'DE', 'iso_alpha3' => 'DEU', 'iso_numeric' => '276', 'calling_code' => '49', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Ghana', 'iso_alpha2' => 'GH', 'iso_alpha3' => 'GHA', 'iso_numeric' => '288', 'calling_code' => '233', 'currency_code' => 'GHC', 'currency_name' => 'Cedi', 'currency_symbol' => '¢'),

            array('name' => 'Gibraltar', 'iso_alpha2' => 'GI', 'iso_alpha3' => 'GIB', 'iso_numeric' => '292', 'calling_code' => '350', 'currency_code' => 'GIP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'Greece', 'iso_alpha2' => 'GR', 'iso_alpha3' => 'GRC', 'iso_numeric' => '300', 'calling_code' => '30', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Greenland', 'iso_alpha2' => 'GL', 'iso_alpha3' => 'GRL', 'iso_numeric' => '304', 'calling_code' => '299', 'currency_code' => 'DKK', 'currency_name' => 'Krone', 'currency_symbol' => 'kr'),

            array('name' => 'Grenada', 'iso_alpha2' => 'GD', 'iso_alpha3' => 'GRD', 'iso_numeric' => '308', 'calling_code' => '1473', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Guadeloupe', 'iso_alpha2' => 'GP', 'iso_alpha3' => 'GLP', 'iso_numeric' => '312', 'calling_code' => '', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Guam', 'iso_alpha2' => 'GU', 'iso_alpha3' => 'GUM', 'iso_numeric' => '316', 'calling_code' => '1671', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Guatemala', 'iso_alpha2' => 'GT', 'iso_alpha3' => 'GTM', 'iso_numeric' => '320', 'calling_code' => '502', 'currency_code' => 'GTQ', 'currency_name' => 'Quetzal', 'currency_symbol' => 'Q'),

            array('name' => 'Guinea', 'iso_alpha2' => 'GN', 'iso_alpha3' => 'GIN', 'iso_numeric' => '324', 'calling_code' => '224', 'currency_code' => 'GNF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Guinea-Bissau', 'iso_alpha2' => 'GW', 'iso_alpha3' => 'GNB', 'iso_numeric' => '624', 'calling_code' => '245', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Guyana', 'iso_alpha2' => 'GY', 'iso_alpha3' => 'GUY', 'iso_numeric' => '328', 'calling_code' => '592', 'currency_code' => 'GYD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Haiti', 'iso_alpha2' => 'HT', 'iso_alpha3' => 'HTI', 'iso_numeric' => '332', 'calling_code' => '509', 'currency_code' => 'HTG', 'currency_name' => 'Gourde', 'currency_symbol' => 'G'),

            array('name' => 'Heard Island and McDonald Islands', 'iso_alpha2' => 'HM', 'iso_alpha3' => 'HMD', 'iso_numeric' => '334', 'calling_code' => '', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Honduras', 'iso_alpha2' => 'HN', 'iso_alpha3' => 'HND', 'iso_numeric' => '340', 'calling_code' => '504', 'currency_code' => 'HNL', 'currency_name' => 'Lempira', 'currency_symbol' => 'L'),

            array('name' => 'Hong Kong', 'iso_alpha2' => 'HK', 'iso_alpha3' => 'HKG', 'iso_numeric' => '344', 'calling_code' => '852', 'currency_code' => 'HKD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Hungary', 'iso_alpha2' => 'HU', 'iso_alpha3' => 'HUN', 'iso_numeric' => '348', 'calling_code' => '36', 'currency_code' => 'HUF', 'currency_name' => 'Forint', 'currency_symbol' => 'Ft'),

            array('name' => 'Iceland', 'iso_alpha2' => 'IS', 'iso_alpha3' => 'ISL', 'iso_numeric' => '352', 'calling_code' => '354', 'currency_code' => 'ISK', 'currency_name' => 'Krona', 'currency_symbol' => 'kr'),

            array('name' => 'India', 'iso_alpha2' => 'IN', 'iso_alpha3' => 'IND', 'iso_numeric' => '356', 'calling_code' => '91', 'currency_code' => 'INR', 'currency_name' => 'Rupee', 'currency_symbol' => '₹'),

            array('name' => 'Indonesia', 'iso_alpha2' => 'ID', 'iso_alpha3' => 'IDN', 'iso_numeric' => '360', 'calling_code' => '62', 'currency_code' => 'IDR', 'currency_name' => 'Rupiah', 'currency_symbol' => 'Rp'),

            array('name' => 'Iran', 'iso_alpha2' => 'IR', 'iso_alpha3' => 'IRN', 'iso_numeric' => '364', 'calling_code' => '98', 'currency_code' => 'IRR', 'currency_name' => 'Rial', 'currency_symbol' => '﷼'),

            array('name' => 'Iraq', 'iso_alpha2' => 'IQ', 'iso_alpha3' => 'IRQ', 'iso_numeric' => '368', 'calling_code' => '964', 'currency_code' => 'IQD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Ireland', 'iso_alpha2' => 'IE', 'iso_alpha3' => 'IRL', 'iso_numeric' => '372', 'calling_code' => '353', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Israel', 'iso_alpha2' => 'IL', 'iso_alpha3' => 'ISR', 'iso_numeric' => '376', 'calling_code' => '972', 'currency_code' => 'ILS', 'currency_name' => 'Shekel', 'currency_symbol' => '₪'),

            array('name' => 'Italy', 'iso_alpha2' => 'IT', 'iso_alpha3' => 'ITA', 'iso_numeric' => '380', 'calling_code' => '39', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Ivory Coast', 'iso_alpha2' => 'CI', 'iso_alpha3' => 'CIV', 'iso_numeric' => '384', 'calling_code' => '225', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Jamaica', 'iso_alpha2' => 'JM', 'iso_alpha3' => 'JAM', 'iso_numeric' => '388', 'calling_code' => '1876', 'currency_code' => 'JMD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Japan', 'iso_alpha2' => 'JP', 'iso_alpha3' => 'JPN', 'iso_numeric' => '392', 'calling_code' => '81', 'currency_code' => 'JPY', 'currency_name' => 'Yen', 'currency_symbol' => '¥'),

            array('name' => 'Jordan', 'iso_alpha2' => 'JO', 'iso_alpha3' => 'JOR', 'iso_numeric' => '400', 'calling_code' => '962', 'currency_code' => 'JOD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Kazakhstan', 'iso_alpha2' => 'KZ', 'iso_alpha3' => 'KAZ', 'iso_numeric' => '398', 'calling_code' => '7', 'currency_code' => 'KZT', 'currency_name' => 'Tenge', 'currency_symbol' => 'лв'),

            array('name' => 'Kenya', 'iso_alpha2' => 'KE', 'iso_alpha3' => 'KEN', 'iso_numeric' => '404', 'calling_code' => '254', 'currency_code' => 'KES', 'currency_name' => 'Shilling', 'currency_symbol' => ''),

            array('name' => 'Kiribati', 'iso_alpha2' => 'KI', 'iso_alpha3' => 'KIR', 'iso_numeric' => '296', 'calling_code' => '686', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Kuwait', 'iso_alpha2' => 'KW', 'iso_alpha3' => 'KWT', 'iso_numeric' => '414', 'calling_code' => '965', 'currency_code' => 'KWD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Kyrgyzstan', 'iso_alpha2' => 'KG', 'iso_alpha3' => 'KGZ', 'iso_numeric' => '417', 'calling_code' => '996', 'currency_code' => 'KGS', 'currency_name' => 'Som', 'currency_symbol' => 'лв'),

            array('name' => 'Laos', 'iso_alpha2' => 'LA', 'iso_alpha3' => 'LAO', 'iso_numeric' => '418', 'calling_code' => '856', 'currency_code' => 'LAK', 'currency_name' => 'Kip', 'currency_symbol' => '₭'),

            array('name' => 'Latvia', 'iso_alpha2' => 'LV', 'iso_alpha3' => 'LVA', 'iso_numeric' => '428', 'calling_code' => '371', 'currency_code' => 'LVL', 'currency_name' => 'Lat', 'currency_symbol' => 'Ls'),

            array('name' => 'Lebanon', 'iso_alpha2' => 'LB', 'iso_alpha3' => 'LBN', 'iso_numeric' => '422', 'calling_code' => '961', 'currency_code' => 'LBP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'Lesotho', 'iso_alpha2' => 'LS', 'iso_alpha3' => 'LSO', 'iso_numeric' => '426', 'calling_code' => '266', 'currency_code' => 'LSL', 'currency_name' => 'Loti', 'currency_symbol' => 'L'),

            array('name' => 'Liberia', 'iso_alpha2' => 'LR', 'iso_alpha3' => 'LBR', 'iso_numeric' => '430', 'calling_code' => '231', 'currency_code' => 'LRD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Libya', 'iso_alpha2' => 'LY', 'iso_alpha3' => 'LBY', 'iso_numeric' => '434', 'calling_code' => '218', 'currency_code' => 'LYD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Liechtenstein', 'iso_alpha2' => 'LI', 'iso_alpha3' => 'LIE', 'iso_numeric' => '438', 'calling_code' => '423', 'currency_code' => 'CHF', 'currency_name' => 'Franc', 'currency_symbol' => 'CHF'),

            array('name' => 'Lithuania', 'iso_alpha2' => 'LT', 'iso_alpha3' => 'LTU', 'iso_numeric' => '440', 'calling_code' => '370', 'currency_code' => 'LTL', 'currency_name' => 'Litas', 'currency_symbol' => 'Lt'),

            array('name' => 'Luxembourg', 'iso_alpha2' => 'LU', 'iso_alpha3' => 'LUX', 'iso_numeric' => '442', 'calling_code' => '352', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Macao', 'iso_alpha2' => 'MO', 'iso_alpha3' => 'MAC', 'iso_numeric' => '446', 'calling_code' => '853', 'currency_code' => 'MOP', 'currency_name' => 'Pataca', 'currency_symbol' => 'MOP'),

            array('name' => 'Macedonia', 'iso_alpha2' => 'MK', 'iso_alpha3' => 'MKD', 'iso_numeric' => '807', 'calling_code' => '389', 'currency_code' => 'MKD', 'currency_name' => 'Denar', 'currency_symbol' => 'ден'),

            array('name' => 'Madagascar', 'iso_alpha2' => 'MG', 'iso_alpha3' => 'MDG', 'iso_numeric' => '450', 'calling_code' => '261', 'currency_code' => 'MGA', 'currency_name' => 'Ariary', 'currency_symbol' => ''),

            array('name' => 'Malawi', 'iso_alpha2' => 'MW', 'iso_alpha3' => 'MWI', 'iso_numeric' => '454', 'calling_code' => '265', 'currency_code' => 'MWK', 'currency_name' => 'Kwacha', 'currency_symbol' => 'MK'),

            array('name' => 'Malaysia', 'iso_alpha2' => 'MY', 'iso_alpha3' => 'MYS', 'iso_numeric' => '458', 'calling_code' => '60', 'currency_code' => 'MYR', 'currency_name' => 'Ringgit', 'currency_symbol' => 'RM'),

            array('name' => 'Maldives', 'iso_alpha2' => 'MV', 'iso_alpha3' => 'MDV', 'iso_numeric' => '462', 'calling_code' => '960', 'currency_code' => 'MVR', 'currency_name' => 'Rufiyaa', 'currency_symbol' => 'Rf'),

            array('name' => 'Mali', 'iso_alpha2' => 'ML', 'iso_alpha3' => 'MLI', 'iso_numeric' => '466', 'calling_code' => '223', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Malta', 'iso_alpha2' => 'MT', 'iso_alpha3' => 'MLT', 'iso_numeric' => '470', 'calling_code' => '356', 'currency_code' => 'MTL', 'currency_name' => 'Lira', 'currency_symbol' => ''),

            array('name' => 'Marshall Islands', 'iso_alpha2' => 'MH', 'iso_alpha3' => 'MHL', 'iso_numeric' => '584', 'calling_code' => '692', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Martinique', 'iso_alpha2' => 'MQ', 'iso_alpha3' => 'MTQ', 'iso_numeric' => '474', 'calling_code' => '', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Mauritania', 'iso_alpha2' => 'MR', 'iso_alpha3' => 'MRT', 'iso_numeric' => '478', 'calling_code' => '222', 'currency_code' => 'MRO', 'currency_name' => 'Ouguiya', 'currency_symbol' => 'UM'),

            array('name' => 'Mauritius', 'iso_alpha2' => 'MU', 'iso_alpha3' => 'MUS', 'iso_numeric' => '480', 'calling_code' => '230', 'currency_code' => 'MUR', 'currency_name' => 'Rupee', 'currency_symbol' => '₨'),

            array('name' => 'Mayotte', 'iso_alpha2' => 'YT', 'iso_alpha3' => 'MYT', 'iso_numeric' => '175', 'calling_code' => '262', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Mexico', 'iso_alpha2' => 'MX', 'iso_alpha3' => 'MEX', 'iso_numeric' => '484', 'calling_code' => '52', 'currency_code' => 'MXN', 'currency_name' => 'Peso', 'currency_symbol' => '$'),

            array('name' => 'Micronesia', 'iso_alpha2' => 'FM', 'iso_alpha3' => 'FSM', 'iso_numeric' => '583', 'calling_code' => '691', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Moldova', 'iso_alpha2' => 'MD', 'iso_alpha3' => 'MDA', 'iso_numeric' => '498', 'calling_code' => '373', 'currency_code' => 'MDL', 'currency_name' => 'Leu', 'currency_symbol' => ''),

            array('name' => 'Monaco', 'iso_alpha2' => 'MC', 'iso_alpha3' => 'MCO', 'iso_numeric' => '492', 'calling_code' => '377', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Mongolia', 'iso_alpha2' => 'MN', 'iso_alpha3' => 'MNG', 'iso_numeric' => '496', 'calling_code' => '976', 'currency_code' => 'MNT', 'currency_name' => 'Tugrik', 'currency_symbol' => '₮'),

            array('name' => 'Montserrat', 'iso_alpha2' => 'MS', 'iso_alpha3' => 'MSR', 'iso_numeric' => '500', 'calling_code' => '1664', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Morocco', 'iso_alpha2' => 'MA', 'iso_alpha3' => 'MAR', 'iso_numeric' => '504', 'calling_code' => '212', 'currency_code' => 'MAD', 'currency_name' => 'Dirham', 'currency_symbol' => ''),

            array('name' => 'Mozambique', 'iso_alpha2' => 'MZ', 'iso_alpha3' => 'MOZ', 'iso_numeric' => '508', 'calling_code' => '258', 'currency_code' => 'MZN', 'currency_name' => 'Meticail', 'currency_symbol' => 'MT'),

            array('name' => 'Myanmar', 'iso_alpha2' => 'MM', 'iso_alpha3' => 'MMR', 'iso_numeric' => '104', 'calling_code' => '95', 'currency_code' => 'MMK', 'currency_name' => 'Kyat', 'currency_symbol' => 'K'),

            array('name' => 'Namibia', 'iso_alpha2' => 'NA', 'iso_alpha3' => 'NAM', 'iso_numeric' => '516', 'calling_code' => '264', 'currency_code' => 'NAD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Nauru', 'iso_alpha2' => 'NR', 'iso_alpha3' => 'NRU', 'iso_numeric' => '520', 'calling_code' => '674', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Nepal', 'iso_alpha2' => 'NP', 'iso_alpha3' => 'NPL', 'iso_numeric' => '524', 'calling_code' => '977', 'currency_code' => 'NPR', 'currency_name' => 'Rupee', 'currency_symbol' => '₨'),

            array('name' => 'Netherlands', 'iso_alpha2' => 'NL', 'iso_alpha3' => 'NLD', 'iso_numeric' => '528', 'calling_code' => '31', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Netherlands Antilles', 'iso_alpha2' => 'AN', 'iso_alpha3' => 'ANT', 'iso_numeric' => '530', 'calling_code' => '599', 'currency_code' => 'ANG', 'currency_name' => 'Guilder', 'currency_symbol' => 'ƒ'),

            array('name' => 'New Caledonia', 'iso_alpha2' => 'NC', 'iso_alpha3' => 'NCL', 'iso_numeric' => '540', 'calling_code' => '687', 'currency_code' => 'XPF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'New Zealand', 'iso_alpha2' => 'NZ', 'iso_alpha3' => 'NZL', 'iso_numeric' => '554', 'calling_code' => '64', 'currency_code' => 'NZD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Nicaragua', 'iso_alpha2' => 'NI', 'iso_alpha3' => 'NIC', 'iso_numeric' => '558', 'calling_code' => '505', 'currency_code' => 'NIO', 'currency_name' => 'Cordoba', 'currency_symbol' => 'C$'),

            array('name' => 'Niger', 'iso_alpha2' => 'NE', 'iso_alpha3' => 'NER', 'iso_numeric' => '562', 'calling_code' => '227', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Nigeria', 'iso_alpha2' => 'NG', 'iso_alpha3' => 'NGA', 'iso_numeric' => '566', 'calling_code' => '234', 'currency_code' => 'NGN', 'currency_name' => 'Naira', 'currency_symbol' => '₦'),

            array('name' => 'Niue', 'iso_alpha2' => 'NU', 'iso_alpha3' => 'NIU', 'iso_numeric' => '570', 'calling_code' => '683', 'currency_code' => 'NZD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Norfolk Island', 'iso_alpha2' => 'NF', 'iso_alpha3' => 'NFK', 'iso_numeric' => '574', 'calling_code' => '', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'North Korea', 'iso_alpha2' => 'KP', 'iso_alpha3' => 'PRK', 'iso_numeric' => '408', 'calling_code' => '850', 'currency_code' => 'KPW', 'currency_name' => 'Won', 'currency_symbol' => '₩'),

            array('name' => 'Northern Mariana Islands', 'iso_alpha2' => 'MP', 'iso_alpha3' => 'MNP', 'iso_numeric' => '580', 'calling_code' => '1670', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Norway', 'iso_alpha2' => 'NO', 'iso_alpha3' => 'NOR', 'iso_numeric' => '578', 'calling_code' => '47', 'currency_code' => 'NOK', 'currency_name' => 'Krone', 'currency_symbol' => 'kr'),

            array('name' => 'Oman', 'iso_alpha2' => 'OM', 'iso_alpha3' => 'OMN', 'iso_numeric' => '512', 'calling_code' => '968', 'currency_code' => 'OMR', 'currency_name' => 'Rial', 'currency_symbol' => '﷼'),

            array('name' => 'Pakistan', 'iso_alpha2' => 'PK', 'iso_alpha3' => 'PAK', 'iso_numeric' => '586', 'calling_code' => '92', 'currency_code' => 'PKR', 'currency_name' => 'Rupee', 'currency_symbol' => '₨'),

            array('name' => 'Palau', 'iso_alpha2' => 'PW', 'iso_alpha3' => 'PLW', 'iso_numeric' => '585', 'calling_code' => '680', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Palestinian Territory', 'iso_alpha2' => 'PS', 'iso_alpha3' => 'PSE', 'iso_numeric' => '275', 'calling_code' => '', 'currency_code' => 'ILS', 'currency_name' => 'Shekel', 'currency_symbol' => '₪'),

            array('name' => 'Panama', 'iso_alpha2' => 'PA', 'iso_alpha3' => 'PAN', 'iso_numeric' => '591', 'calling_code' => '507', 'currency_code' => 'PAB', 'currency_name' => 'Balboa', 'currency_symbol' => 'B/.'),

            array('name' => 'Papua New Guinea', 'iso_alpha2' => 'PG', 'iso_alpha3' => 'PNG', 'iso_numeric' => '598', 'calling_code' => '675', 'currency_code' => 'PGK', 'currency_name' => 'Kina', 'currency_symbol' => ''),

            array('name' => 'Paraguay', 'iso_alpha2' => 'PY', 'iso_alpha3' => 'PRY', 'iso_numeric' => '600', 'calling_code' => '595', 'currency_code' => 'PYG', 'currency_name' => 'Guarani', 'currency_symbol' => 'Gs'),

            array('name' => 'Peru', 'iso_alpha2' => 'PE', 'iso_alpha3' => 'PER', 'iso_numeric' => '604', 'calling_code' => '51', 'currency_code' => 'PEN', 'currency_name' => 'Sol', 'currency_symbol' => 'S/.'),

            array('name' => 'Philippines', 'iso_alpha2' => 'PH', 'iso_alpha3' => 'PHL', 'iso_numeric' => '608', 'calling_code' => '63', 'currency_code' => 'PHP', 'currency_name' => 'Peso', 'currency_symbol' => 'Php'),

            array('name' => 'Pitcairn', 'iso_alpha2' => 'PN', 'iso_alpha3' => 'PCN', 'iso_numeric' => '612', 'calling_code' => '870', 'currency_code' => 'NZD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Poland', 'iso_alpha2' => 'PL', 'iso_alpha3' => 'POL', 'iso_numeric' => '616', 'calling_code' => '48', 'currency_code' => 'PLN', 'currency_name' => 'Zloty', 'currency_symbol' => 'zł'),

            array('name' => 'Portugal', 'iso_alpha2' => 'PT', 'iso_alpha3' => 'PRT', 'iso_numeric' => '620', 'calling_code' => '351', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Puerto Rico', 'iso_alpha2' => 'PR', 'iso_alpha3' => 'PRI', 'iso_numeric' => '630', 'calling_code' => '1', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Qatar', 'iso_alpha2' => 'QA', 'iso_alpha3' => 'QAT', 'iso_numeric' => '634', 'calling_code' => '974', 'currency_code' => 'QAR', 'currency_name' => 'Rial', 'currency_symbol' => '﷼'),

            array('name' => 'Republic of the Congo', 'iso_alpha2' => 'CG', 'iso_alpha3' => 'COG', 'iso_numeric' => '178', 'calling_code' => '242', 'currency_code' => 'XAF', 'currency_name' => 'Franc', 'currency_symbol' => 'FCF'),

            array('name' => 'Reunion', 'iso_alpha2' => 'RE', 'iso_alpha3' => 'REU', 'iso_numeric' => '638', 'calling_code' => '', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Romania', 'iso_alpha2' => 'RO', 'iso_alpha3' => 'ROU', 'iso_numeric' => '642', 'calling_code' => '40', 'currency_code' => 'RON', 'currency_name' => 'Leu', 'currency_symbol' => 'lei'),

            array('name' => 'Russia', 'iso_alpha2' => 'RU', 'iso_alpha3' => 'RUS', 'iso_numeric' => '643', 'calling_code' => '7', 'currency_code' => 'RUB', 'currency_name' => 'Ruble', 'currency_symbol' => 'руб'),

            array('name' => 'Rwanda', 'iso_alpha2' => 'RW', 'iso_alpha3' => 'RWA', 'iso_numeric' => '646', 'calling_code' => '250', 'currency_code' => 'RWF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Saint Helena', 'iso_alpha2' => 'SH', 'iso_alpha3' => 'SHN', 'iso_numeric' => '654', 'calling_code' => '290', 'currency_code' => 'SHP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'Saint Kitts and Nevis', 'iso_alpha2' => 'KN', 'iso_alpha3' => 'KNA', 'iso_numeric' => '659', 'calling_code' => '1869', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Saint Lucia', 'iso_alpha2' => 'LC', 'iso_alpha3' => 'LCA', 'iso_numeric' => '662', 'calling_code' => '1758', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Saint Pierre and Miquelon', 'iso_alpha2' => 'PM', 'iso_alpha3' => 'SPM', 'iso_numeric' => '666', 'calling_code' => '508', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Saint Vincent and the Grenadines', 'iso_alpha2' => 'VC', 'iso_alpha3' => 'VCT', 'iso_numeric' => '670', 'calling_code' => '1784', 'currency_code' => 'XCD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Samoa', 'iso_alpha2' => 'WS', 'iso_alpha3' => 'WSM', 'iso_numeric' => '882', 'calling_code' => '685', 'currency_code' => 'WST', 'currency_name' => 'Tala', 'currency_symbol' => 'WS$'),

            array('name' => 'San Marino', 'iso_alpha2' => 'SM', 'iso_alpha3' => 'SMR', 'iso_numeric' => '674', 'calling_code' => '378', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Sao Tome and Principe', 'iso_alpha2' => 'ST', 'iso_alpha3' => 'STP', 'iso_numeric' => '678', 'calling_code' => '239', 'currency_code' => 'STD', 'currency_name' => 'Dobra', 'currency_symbol' => 'Db'),

            array('name' => 'Saudi Arabia', 'iso_alpha2' => 'SA', 'iso_alpha3' => 'SAU', 'iso_numeric' => '682', 'calling_code' => '966', 'currency_code' => 'SAR', 'currency_name' => 'Rial', 'currency_symbol' => '﷼'),

            array('name' => 'Senegal', 'iso_alpha2' => 'SN', 'iso_alpha3' => 'SEN', 'iso_numeric' => '686', 'calling_code' => '221', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Serbia and Montenegro', 'iso_alpha2' => 'CS', 'iso_alpha3' => 'SCG', 'iso_numeric' => '891', 'calling_code' => '', 'currency_code' => 'RSD', 'currency_name' => 'Dinar', 'currency_symbol' => 'Дин'),

            array('name' => 'Seychelles', 'iso_alpha2' => 'SC', 'iso_alpha3' => 'SYC', 'iso_numeric' => '690', 'calling_code' => '248', 'currency_code' => 'SCR', 'currency_name' => 'Rupee', 'currency_symbol' => '₨'),

            array('name' => 'Sierra Leone', 'iso_alpha2' => 'SL', 'iso_alpha3' => 'SLE', 'iso_numeric' => '694', 'calling_code' => '232', 'currency_code' => 'SLL', 'currency_name' => 'Leone', 'currency_symbol' => 'Le'),

            array('name' => 'Singapore', 'iso_alpha2' => 'SG', 'iso_alpha3' => 'SGP', 'iso_numeric' => '702', 'calling_code' => '65', 'currency_code' => 'SGD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Slovakia', 'iso_alpha2' => 'SK', 'iso_alpha3' => 'SVK', 'iso_numeric' => '703', 'calling_code' => '421', 'currency_code' => 'SKK', 'currency_name' => 'Koruna', 'currency_symbol' => 'Sk'),

            array('name' => 'Slovenia', 'iso_alpha2' => 'SI', 'iso_alpha3' => 'SVN', 'iso_numeric' => '705', 'calling_code' => '386', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Solomon Islands', 'iso_alpha2' => 'SB', 'iso_alpha3' => 'SLB', 'iso_numeric' => '90', 'calling_code' => '677', 'currency_code' => 'SBD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Somalia', 'iso_alpha2' => 'SO', 'iso_alpha3' => 'SOM', 'iso_numeric' => '706', 'calling_code' => '252', 'currency_code' => 'SOS', 'currency_name' => 'Shilling', 'currency_symbol' => 'S'),

            array('name' => 'South Africa', 'iso_alpha2' => 'ZA', 'iso_alpha3' => 'ZAF', 'iso_numeric' => '710', 'calling_code' => '27', 'currency_code' => 'ZAR', 'currency_name' => 'Rand', 'currency_symbol' => 'R'),

            array('name' => 'South Georgia and the South Sandwich Islands', 'iso_alpha2' => 'GS', 'iso_alpha3' => 'SGS', 'iso_numeric' => '239', 'calling_code' => '', 'currency_code' => 'GBP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'South Korea', 'iso_alpha2' => 'KR', 'iso_alpha3' => 'KOR', 'iso_numeric' => '410', 'calling_code' => '82', 'currency_code' => 'KRW', 'currency_name' => 'Won', 'currency_symbol' => '₩'),

            array('name' => 'Spain', 'iso_alpha2' => 'ES', 'iso_alpha3' => 'ESP', 'iso_numeric' => '724', 'calling_code' => '34', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Sri Lanka', 'iso_alpha2' => 'LK', 'iso_alpha3' => 'LKA', 'iso_numeric' => '144', 'calling_code' => '94', 'currency_code' => 'LKR', 'currency_name' => 'Rupee', 'currency_symbol' => '₨'),

            array('name' => 'Sudan', 'iso_alpha2' => 'SD', 'iso_alpha3' => 'SDN', 'iso_numeric' => '736', 'calling_code' => '249', 'currency_code' => 'SDD', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Suriname', 'iso_alpha2' => 'SR', 'iso_alpha3' => 'SUR', 'iso_numeric' => '740', 'calling_code' => '597', 'currency_code' => 'SRD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Svalbard and Jan Mayen', 'iso_alpha2' => 'SJ', 'iso_alpha3' => 'SJM', 'iso_numeric' => '744', 'calling_code' => '', 'currency_code' => 'NOK', 'currency_name' => 'Krone', 'currency_symbol' => 'kr'),

            array('name' => 'Swaziland', 'iso_alpha2' => 'SZ', 'iso_alpha3' => 'SWZ', 'iso_numeric' => '748', 'calling_code' => '268', 'currency_code' => 'SZL', 'currency_name' => 'Lilangeni', 'currency_symbol' => ''),

            array('name' => 'Sweden', 'iso_alpha2' => 'SE', 'iso_alpha3' => 'SWE', 'iso_numeric' => '752', 'calling_code' => '46', 'currency_code' => 'SEK', 'currency_name' => 'Krona', 'currency_symbol' => 'kr'),

            array('name' => 'Switzerland', 'iso_alpha2' => 'CH', 'iso_alpha3' => 'CHE', 'iso_numeric' => '756', 'calling_code' => '41', 'currency_code' => 'CHF', 'currency_name' => 'Franc', 'currency_symbol' => 'CHF'),

            array('name' => 'Syria', 'iso_alpha2' => 'SY', 'iso_alpha3' => 'SYR', 'iso_numeric' => '760', 'calling_code' => '963', 'currency_code' => 'SYP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'Taiwan', 'iso_alpha2' => 'TW', 'iso_alpha3' => 'TWN', 'iso_numeric' => '158', 'calling_code' => '886', 'currency_code' => 'TWD', 'currency_name' => 'Dollar', 'currency_symbol' => 'NT$'),

            array('name' => 'Tajikistan', 'iso_alpha2' => 'TJ', 'iso_alpha3' => 'TJK', 'iso_numeric' => '762', 'calling_code' => '992', 'currency_code' => 'TJS', 'currency_name' => 'Somoni', 'currency_symbol' => ''),

            array('name' => 'Tanzania', 'iso_alpha2' => 'TZ', 'iso_alpha3' => 'TZA', 'iso_numeric' => '834', 'calling_code' => '255', 'currency_code' => 'TZS', 'currency_name' => 'Shilling', 'currency_symbol' => ''),

            array('name' => 'Thailand', 'iso_alpha2' => 'TH', 'iso_alpha3' => 'THA', 'iso_numeric' => '764', 'calling_code' => '66', 'currency_code' => 'THB', 'currency_name' => 'Baht', 'currency_symbol' => '฿'),

            array('name' => 'Togo', 'iso_alpha2' => 'TG', 'iso_alpha3' => 'TGO', 'iso_numeric' => '768', 'calling_code' => '228', 'currency_code' => 'XOF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Tokelau', 'iso_alpha2' => 'TK', 'iso_alpha3' => 'TKL', 'iso_numeric' => '772', 'calling_code' => '690', 'currency_code' => 'NZD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Tonga', 'iso_alpha2' => 'TO', 'iso_alpha3' => 'TON', 'iso_numeric' => '776', 'calling_code' => '676', 'currency_code' => 'TOP', 'currency_name' => 'Paanga', 'currency_symbol' => 'T$'),

            array('name' => 'Trinidad and Tobago', 'iso_alpha2' => 'TT', 'iso_alpha3' => 'TTO', 'iso_numeric' => '780', 'calling_code' => '1868', 'currency_code' => 'TTD', 'currency_name' => 'Dollar', 'currency_symbol' => 'TT$'),

            array('name' => 'Tunisia', 'iso_alpha2' => 'TN', 'iso_alpha3' => 'TUN', 'iso_numeric' => '788', 'calling_code' => '216', 'currency_code' => 'TND', 'currency_name' => 'Dinar', 'currency_symbol' => ''),

            array('name' => 'Turkey', 'iso_alpha2' => 'TR', 'iso_alpha3' => 'TUR', 'iso_numeric' => '792', 'calling_code' => '90', 'currency_code' => 'TRY', 'currency_name' => 'Lira', 'currency_symbol' => 'YTL'),

            array('name' => 'Turkmenistan', 'iso_alpha2' => 'TM', 'iso_alpha3' => 'TKM', 'iso_numeric' => '795', 'calling_code' => '993', 'currency_code' => 'TMM', 'currency_name' => 'Manat', 'currency_symbol' => 'm'),

            array('name' => 'Turks and Caicos Islands', 'iso_alpha2' => 'TC', 'iso_alpha3' => 'TCA', 'iso_numeric' => '796', 'calling_code' => '1649', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Tuvalu', 'iso_alpha2' => 'TV', 'iso_alpha3' => 'TUV', 'iso_numeric' => '798', 'calling_code' => '688', 'currency_code' => 'AUD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'U.S. Virgin Islands', 'iso_alpha2' => 'VI', 'iso_alpha3' => 'VIR', 'iso_numeric' => '850', 'calling_code' => '1340', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Uganda', 'iso_alpha2' => 'UG', 'iso_alpha3' => 'UGA', 'iso_numeric' => '800', 'calling_code' => '256', 'currency_code' => 'UGX', 'currency_name' => 'Shilling', 'currency_symbol' => ''),

            array('name' => 'Ukraine', 'iso_alpha2' => 'UA', 'iso_alpha3' => 'UKR', 'iso_numeric' => '804', 'calling_code' => '380', 'currency_code' => 'UAH', 'currency_name' => 'Hryvnia', 'currency_symbol' => '₴'),

            array('name' => 'United Arab Emirates', 'iso_alpha2' => 'AE', 'iso_alpha3' => 'ARE', 'iso_numeric' => '784', 'calling_code' => '971', 'currency_code' => 'AED', 'currency_name' => 'Dirham', 'currency_symbol' => ''),

            array('name' => 'United Kingdom', 'iso_alpha2' => 'GB', 'iso_alpha3' => 'GBR', 'iso_numeric' => '826', 'calling_code' => '44', 'currency_code' => 'GBP', 'currency_name' => 'Pound', 'currency_symbol' => '£'),

            array('name' => 'United States', 'iso_alpha2' => 'US', 'iso_alpha3' => 'USA', 'iso_numeric' => '840', 'calling_code' => '1', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'United States Minor Outlying Islands', 'iso_alpha2' => 'UM', 'iso_alpha3' => 'UMI', 'iso_numeric' => '581', 'calling_code' => '', 'currency_code' => 'USD', 'currency_name' => 'Dollar', 'currency_symbol' => '$'),

            array('name' => 'Uruguay', 'iso_alpha2' => 'UY', 'iso_alpha3' => 'URY', 'iso_numeric' => '858', 'calling_code' => '598', 'currency_code' => 'UYU', 'currency_name' => 'Peso', 'currency_symbol' => '$U'),

            array('name' => 'Uzbekistan', 'iso_alpha2' => 'UZ', 'iso_alpha3' => 'UZB', 'iso_numeric' => '860', 'calling_code' => '998', 'currency_code' => 'UZS', 'currency_name' => 'Som', 'currency_symbol' => 'лв'),

            array('name' => 'Vanuatu', 'iso_alpha2' => 'VU', 'iso_alpha3' => 'VUT', 'iso_numeric' => '548', 'calling_code' => '678', 'currency_code' => 'VUV', 'currency_name' => 'Vatu', 'currency_symbol' => 'Vt'),

            array('name' => 'Vatican', 'iso_alpha2' => 'VA', 'iso_alpha3' => 'VAT', 'iso_numeric' => '336', 'calling_code' => '39', 'currency_code' => 'EUR', 'currency_name' => 'Euro', 'currency_symbol' => '€'),

            array('name' => 'Venezuela', 'iso_alpha2' => 'VE', 'iso_alpha3' => 'VEN', 'iso_numeric' => '862', 'calling_code' => '58', 'currency_code' => 'VEF', 'currency_name' => 'Bolivar', 'currency_symbol' => 'Bs'),

            array('name' => 'Vietnam', 'iso_alpha2' => 'VN', 'iso_alpha3' => 'VNM', 'iso_numeric' => '704', 'calling_code' => '84', 'currency_code' => 'VND', 'currency_name' => 'Dong', 'currency_symbol' => '₫'),

            array('name' => 'Wallis and Futuna', 'iso_alpha2' => 'WF', 'iso_alpha3' => 'WLF', 'iso_numeric' => '876', 'calling_code' => '681', 'currency_code' => 'XPF', 'currency_name' => 'Franc', 'currency_symbol' => ''),

            array('name' => 'Western Sahara', 'iso_alpha2' => 'EH', 'iso_alpha3' => 'ESH', 'iso_numeric' => '732', 'calling_code' => '', 'currency_code' => 'MAD', 'currency_name' => 'Dirham', 'currency_symbol' => ''),

            array('name' => 'Yemen', 'iso_alpha2' => 'YE', 'iso_alpha3' => 'YEM', 'iso_numeric' => '887', 'calling_code' => '967', 'currency_code' => 'YER', 'currency_name' => 'Rial', 'currency_symbol' => '﷼'),

            array('name' => 'Zambia', 'iso_alpha2' => 'ZM', 'iso_alpha3' => 'ZMB', 'iso_numeric' => '894', 'calling_code' => '260', 'currency_code' => 'ZMK', 'currency_name' => 'Kwacha', 'currency_symbol' => 'ZK'),

            array('name' => 'Zimbabwe', 'iso_alpha2' => 'ZW', 'iso_alpha3' => 'ZWE', 'iso_numeric' => '716', 'calling_code' => '263', 'currency_code' => 'ZWD', 'currency_name' => 'Dollar', 'currency_symbol' => 'Z$')

        );



        if ($option) {

            foreach ($options as $value) {

                if ($symbol) {

                    if ($value['currency_code'] == $option) {

                        return $value['currency_symbol'];

                    }

                }

            }

        }



        return $options;

    }



    public function currenciesList($type = null)

    {

        $options = (object)array(

            'ALL' => 'Albania Lek',

            'AFN' => 'Afghanistan Afghani',

            'ARS' => 'Argentina Peso',

            'AWG' => 'Aruba Guilder',

            'AUD' => 'Australia Dollar',

            'AZN' => 'Azerbaijan New Manat',

            'BSD' => 'Bahamas Dollar',

            'BBD' => 'Barbados Dollar',

            'BDT' => 'BD taka',

            'BYR' => 'Belarus Ruble',

            'BZD' => 'Belize Dollar',

            'BMD' => 'Bermuda Dollar',

            'BOB' => 'Bolivia Boliviano',

            'BAM' => 'Bosnia and Herzegovina Convertible Marka',

            'BWP' => 'Botswana Pula',

            'BGN' => 'Bulgaria Lev',

            'BRL' => 'Brazil Real',

            'BND' => 'Brunei Darussalam Dollar',

            'KHR' => 'Cambodia Riel',

            'CAD' => 'Canada Dollar',

            'KYD' => 'Cayman Islands Dollar',

            'CLP' => 'Chile Peso',

            'CNY' => 'China Yuan Renminbi',

            'COP' => 'Colombia Peso',

            'CRC' => 'Costa Rica Colon',

            'HRK' => 'Croatia Kuna',

            'CUP' => 'Cuba Peso',

            'CZK' => 'Czech Republic Koruna',

            'DKK' => 'Denmark Krone',

            'DOP' => 'Dominican Republic Peso',

            'XCD' => 'East Caribbean Dollar',

            'EGP' => 'Egypt Pound',

            'SVC' => 'El Salvador Colon',

            'EEK' => 'Estonia Kroon',

            'EUR' => 'Euro Member Countries',

            'FKP' => 'Falkland Islands (Malvinas) Pound',

            'FJD' => 'Fiji Dollar',

            'GHC' => 'Ghana Cedis',

            'GIP' => 'Gibraltar Pound',

            'GTQ' => 'Guatemala Quetzal',

            'GGP' => 'Guernsey Pound',

            'GYD' => 'Guyana Dollar',

            'HNL' => 'Honduras Lempira',

            'HKD' => 'Hong Kong Dollar',

            'HUF' => 'Hungary Forint',

            'ISK' => 'Iceland Krona',

            'INR' => 'India Rupee',

            'IDR' => 'Indonesia Rupiah',

            'IRR' => 'Iran Rial',

            'IMP' => 'Isle of Man Pound',

            'ILS' => 'Israel Shekel',

            'JMD' => 'Jamaica Dollar',

            'JPY' => 'Japan Yen',

            'JEP' => 'Jersey Pound',

            'KZT' => 'Kazakhstan Tenge',

            'KPW' => 'Korea (North) Won',

            'KRW' => 'Korea (South) Won',

            'KGS' => 'Kyrgyzstan Som',

            'LAK' => 'Laos Kip',

            'LVL' => 'Latvia Lat',

            'LBP' => 'Lebanon Pound',

            'LRD' => 'Liberia Dollar',

            'LTL' => 'Lithuania Litas',

            'MKD' => 'Macedonia Denar',

            'MYR' => 'Malaysia Ringgit',

            'MUR' => 'Mauritius Rupee',

            'MXN' => 'Mexico Peso',

            'MNT' => 'Mongolia Tughrik',

            'MZN' => 'Mozambique Metical',

            'NAD' => 'Namibia Dollar',

            'NPR' => 'Nepal Rupee',

            'ANG' => 'Netherlands Antilles Guilder',

            'NZD' => 'New Zealand Dollar',

            'NIO' => 'Nicaragua Cordoba',

            'NGN' => 'Nigeria Naira',

            'NOK' => 'Norway Krone',

            'OMR' => 'Oman Rial',

            'PKR' => 'Pakistan Rupee',

            'PAB' => 'Panama Balboa',

            'PYG' => 'Paraguay Guarani',

            'PEN' => 'Peru Nuevo Sol',

            'PHP' => 'Philippines Peso',

            'PLN' => 'Poland Zloty',

            'QAR' => 'Qatar Riyal',

            'RON' => 'Romania New Leu',

            'RUB' => 'Russia Ruble',

            'SHP' => 'Saint Helena Pound',

            'SAR' => 'Saudi Arabia Riyal',

            'RSD' => 'Serbia Dinar',

            'SCR' => 'Seychelles Rupee',

            'SGD' => 'Singapore Dollar',

            'SBD' => 'Solomon Islands Dollar',

            'SOS' => 'Somalia Shilling',

            'ZAR' => 'South Africa Rand',

            'LKR' => 'Sri Lanka Rupee',

            'SEK' => 'Sweden Krona',

            'CHF' => 'Switzerland Franc',

            'SRD' => 'Suriname Dollar',

            'SYP' => 'Syria Pound',

            'TWD' => 'Taiwan New Dollar',

            'THB' => 'Thailand Baht',

            'TTD' => 'Trinidad and Tobago Dollar',

            'TRY' => 'Turkey Lira',

            'TRL' => 'Turkey Lira',

            'TVD' => 'Tuvalu Dollar',

            'UAH' => 'Ukraine Hryvna',

            'GBP' => 'United Kingdom Pound',

            'USD' => 'United States Dollar',

            'UYU' => 'Uruguay Peso',

            'UZS' => 'Uzbekistan Som',

            'VEF' => 'Venezuela Bolivar',

            'VND' => 'Viet Nam Dong',

            'YER' => 'Yemen Rial',

            'ZWD' => 'Zimbabwe Dollar'

        );



        if ($type) {

            foreach ($options as $key => $option) {

                if ($key == $type) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function menuLocation()

    {

        $menu_location = (object)array(

            'main_menu' => 'Main menu',

            'mobile_menu' => 'Mobile menu',

            'user_menu' => 'User menu',

            'footer_menu' => 'About Us',

            'footer_menu_b' => 'Others',

        );



        return $menu_location;

    }



    public function frequency_types($type = null)

    {

        $options = (object)array(

            'date' => 'Specific Date',

            'days' => 'Specific Days',

            'daily' => 'Everyday',

        );



        if ($type) {

            foreach ($options as $key => $option) {

                if ($key == $type) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function system_email_groups($type = null)

    {

        $options = (object)array(

            // 'system' => 'System',

            // 'customers' => 'Customers',

            // 'admin' => 'Admin',

            // 'dynamic_cron' => 'Dynamic/Cron',

            'others' => 'Others',

            'order' => 'Orders',

            'review' => 'Reviews',

            'reward' => 'Rewards',

            'refund' => 'Refund',

            'promo' => 'Promo',

        );



        if ($type) {

            foreach ($options as $key => $option) {

                if ($key == $type) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function email_template_types($type = null)

    {

        $options = (object)array(

            'system' => 'System',

            // 'customers' => 'Customers',

            // 'admin' => 'Admin',

            'dynamic_cron' => 'Dynamic/Cron',

            // 'order' => 'Order',

        );



        if ($type) {

            foreach ($options as $key => $option) {

                if ($key == $type) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function email_system_types($type = null)

    {

        $options = (object)array(

            'pay_now' => 'Pay Now',

            'partial_pay' => 'Partial Pay',

            'send_review' => 'Send Review',

            'update_review' => 'Update Review',

            'update_profile' => 'Update Profile',

            'create_account' => 'Create Account',

            'redeem_cash_reward' => 'Redeem Cash Reward',

            'send_password_reset_link' => 'Send Password Reset Link',

            'convert_cash' => 'Convert to Cash',

            'update_password' => 'Update Password',

            'send_message' => 'Send Message',

            'request_return' => 'Request Return', //pending

        );



        if ($type) {

            foreach ($options as $key => $option) {

                if ($key == $type) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function orderStatus($type = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Incomplete',

                'key' => 'incomplete',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Confirmed',

                'key' => 'confirmed',

            ],

            '3' => (object)[

                'id' => '3',

                'title' => 'Partial',

                'key' => 'partial',

            ],

            '4' => (object)[

                'id' => '4',

                'title' => 'Processing',

                'key' => 'processing',

            ],

            '5' => (object)[

                'id' => '5',

                'title' => 'Shipped',

                'key' => 'shipped',

            ],

            '6' => (object)[

                'id' => '6',

                'title' => 'Delivered',

                'key' => 'delivered',

            ],

            '7' => (object)[

                'id' => '7',

                'title' => 'Delivered (With Returns)',

                'key' => 'delivered_returns',

            ],

            '8' => (object)[

                'id' => '8',

                'title' => 'Problem',

                'key' => 'problem',

            ],

            '9' => (object)[

                'id' => '9',

                'title' => 'Shipment Problem',

                'key' => 'shipment_problem',

            ],

            '10' => (object)[

                'id' => '10',

                'title' => 'Refund Issued',

                'key' => 'refund_issued',

            ],

            '11' => (object)[

                'id' => '11',

                'title' => 'Cancelled',

                'key' => 'cancelled',

            ],

        );



        if ($type) {

            foreach ($options as $option) {

                if ($type == $option->id) {

                    return $option->title;

                }

            }

        }



        return $options;

    }



    public function rewardTransferStatus($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Pending',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Processing',

            ],

            '3' => (object)[

                'id' => '3',

                'title' => 'Completed',

            ],

            '4' => (object)[

                'id' => '4',

                'title' => 'Completed (Checkout)',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($id == $option->id) {

                    return $option->title;

                }

            }

        }



        return $options;

    }



    public function product_box_types($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Open Box',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'With Box',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($id == $option->id) {

                    return $option->title;

                }

            }

        }



        return $options;

    }



    public function reviewStatus($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Pending',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Approved',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($id == $option->id) {

                    return $option->title;

                }

            }

        }



        return $options;

    }



    public function email_constants()

    {

        $options = [

            '{$current_date_time}',

            '{$site_name}',

            '{$site_url}',

            '{$site_email}',

            '{$site_phone}',

            '{$company_name}',

            '{$company_address}',

            '{$company_city}',

            '{$company_state}',

            '{$company_postcode}',

            '{$company_country}',

            '{$company_email}',

            '{$company_phone}',

            '{$user_name}',

            '{$first_name}',

            '{$last_name}',

            '{$reset_password_link}',

            '{$account_verification_link}',

            '{$admin_email}',

            '{$admin_username}',

            '{$admin_first_name}',

            '{$admin_last_name}',

            '{$order_list}',

            '{$order_number_id}',

            '{$order_date_time}',

            '{$order_tracking_number}',

            '{$order_status}',

            '{$partial_order_amount}',

            '{$order_balance}',

            '{$shipping_from_name}',

            '{$shipping_to_name}',

            '{$shipping_from_email}',

            '{$shipping_to_email}',

            '{$shipping_from_phone}',

            '{$shipping_to_phone}',

            '{$review_status}',

            '{$review_rating}',

            '{$review_reward_points}',

            '{$reward_points}',

            '{$instant_reward_points}',

            '{$reward_cash_value}',

            '{$order_billing_address}',

            '{$order_billing_phone}',

            '{$order_shipping_address}',

            '{$order_shipping_phone}',

            '{$order_payment_method}',

            '{$shipping_method}',

            '{$contact_first_name}',

            '{$contact_last_name}',

            '{$contact_email}',

            '{$contact_how_can_help}',

            '{$contact_subject}',

            '{$contact_message}',

            '{$contact_phone}',

            '{$tracking_code}',

            '{$order_id}',

            '{$your_company_name}',

            '{$product_url}',

            '{$product_price}',

            '{$product_name}',

            '{$customer_email}',

            '{$Customer_Name}',

            '{$Product_Name}',

            '{$Quantity}',

            '{$Price}',

            '{$promo_code}',

            '{$promo_date}',

            // '{$promo_name}',

            // '{$promo_image}',

            // '{$promo_code}',

            // '{$promo_start_date}',

            // '{$promo_expire_date}',

            // '{$promo_discount_amount}',

        ];

        return $options;

    }



    public function makeCondition($items)

    {

        foreach ($items as $value) {

            if ($value->attribute_type_id == 1) {

                $name = $value->data->title;

            }

        }

        return $name;

    }



    public function makeAttributeData($items, $item)

    {

        $title_array = array();

        $sub_title_array = array();

        $condition_text = array();

        foreach (collect($items)->sortBy('ordering') as $value) {

            if ($value->input_type_id == 1 && $value->show_on_title) {

                $title_array[] = $value->data->title;

            }

            if ($value->input_type_id == 1 && $value->show_on_sub_title) {

                $sub_title_array[] = $value->data->title;

            }

            if ($value->input_type_id == 1 && $value->attribute_type_id != 1) {

                $group_array[] = preg_replace('/\s*/', '', strtolower($value->data->title));

                $option_keys_array[] = $value->data->title . $item->product_id;

                $attribute_option_keys_array[] = $value->data->title;

            }

            if ($value->input_type_id == 2) {

                foreach ($value->data as $v) {

                    $attribute_option_keys_array[] = $v->title;

                }

            }

            if ($value->attribute_type_id == 1 && !empty($value->data->description)) {

                $condition_text[] = $value->data->description;

            }

            if ($value->attribute_type_id == 1 && $value->input_type_id) {

                $condition = $value->data->title;

            }

        }



        $item->condition_text = count($condition_text) > 0 ? implode(',', $condition_text) : false;

        $item->extra_title = count($title_array) > 0 ? implode(', ', array_unique($title_array)) : false;

        $item->extra_sub_title = count($sub_title_array) > 0 ?  implode(', ', array_unique($sub_title_array)) : false;

        $item->cart_group = implode(',', array_unique($option_keys_array));

        $item->group = implode('-', array_unique($group_array));

        $item->attribute_options = implode(',', array_unique($attribute_option_keys_array));

        $item->cart_group = preg_replace('/\s*/', '', strtolower($item->group . '-' . $condition));

        // dd($item->cart_group);

    }



    public function makeDetails($items)

    {

        // dd($items);

        $sku_details_url = array();

        foreach ($items as $value) {

            $sku_details_url[] = $value->sku;

        }

        return implode(', ', $sku_details_url);

    }



    public function itemTotalPrice($items)

    {

        $total = 0;

        foreach ($items as $value) {

            $total += $value->price;

        }



        return $total;

    }



    public function sendEmailData($mail_data)

    {

        // dd($mail_data->product_url);

        $user_name = '';

        $first_name = '';

        $last_name = '';

        if (!empty($mail_data->user)) {

            $first_name = ucfirst($mail_data->user->first_name);

            $last_name = ucfirst($mail_data->user->last_name);

            $user_name = $mail_data->user->username;

        }

        $reset_password_link = '';

        if (!empty($mail_data->reset_password_link)) {

            $reset_password_link = '<a style="word-break:break-all;" href="' . $mail_data->reset_password_link . '">Reset Password</a>';

        }

        $account_verification_link = '';

        if (!empty($mail_data->account_verification_link)) {

            $account_verification_link = '<a style="word-break:break-all;" href="' . $mail_data->account_verification_link . '">Verify</a>';

        }

        $admin_email = '';

        $admin_username = '';

        $admin_first_name = '';

        $admin_last_name = '';

        if (auth()->check()) {

            $admin_email = auth()->user()->email;

            $admin_username = auth()->user()->username;

            $admin_first_name = auth()->user()->first_name;

            $admin_last_name = auth()->user()->last_name;

        }

        $order_tracking_number = '';

        $partial_order_amount = 0;

        $order_balance = 0;

        $order_list = '';

        $order_number_id = '';

        $order_status = '';

        $order_date_time = '';

        $shipping_from_name = '';

        $shipping_to_name = '';

        $shipping_from_email = '';

        $shipping_to_email = '';

        $shipping_from_phone = '';

        $shipping_to_phone = '';

        $shipping_to_phone = '';

        $shipping_method = '';

        $order_billing_address = '';

        $order_billing_phone = '';

        $order_payment_method = '';

        $order_shipping_address = '';

        $order_shipping_phone = '';

        $tracking_code = '';

        $your_company_name = '';

        $product_url = '';

        $product_price = '';

        $product_name = '';

        $customer_email = '';

        $order_id = '';

        $Customer_Name = '';

        $Product_Name = '';

        $Quantity = '';

        $Price = "";

        $promo_code = "";

        $promo_date = '';



        if (!empty($mail_data->order)) {

            $order = Order::where('id', $mail_data->order->id)->first();

            // dd($order);

            $order_items = OrderItem::where('order_id', $mail_data->order->id)->get();

            foreach ($order_items as $value) {

                $value->product = Product::find($value->product_id);

                $value->item = ProductItem::find($value->product_item_id);

            }



            $options = [];

            foreach ($order_items as $value) {

                $this->makeAttributeData(unserialize($value->item->item_attributes), $value);

                $options[] = $value->cart_group;

            }

            $items_data = array_unique($options);

            $items = array();

            foreach ($items_data as $value) {

                $count = collect($order_items)->where('cart_group', $value)->count();

                if ($count == 1) {

                    $data = collect($order_items)->where('cart_group', $value)->all();

                    $item = collect($order_items)->where('cart_group', $value)->first();

                    $product = Product::find($item->product_id);

                    $item->product_title = $product->title;

                    if ($item->title) {

                        $item->title = $item->extra_title . ' (' . $item->title . ')';

                    } else {

                        $item->title = $item->extra_title;

                    }

                    $item->condition = $this->makeCondition(unserialize($item->item->item_attributes));

                    $item->sku_details = $this->makeDetails($data);

                    $item->quantity = $count;

                    $item->item_price = $this->itemTotalPrice($data);

                    $brand = ProductBrand::find($product->product_brand_id);

                    $item->product_brand_title = $brand->title;

                    if (empty($item->product_item_image)) {

                        $item->product_image = asset('storage/products/' . $product->product_image);

                    }

                    $attributes = collect(unserialize($item->item->item_attributes))->sortBy('ordering');

                    foreach ($attributes as $value) {

                        if ($value->attribute_type_id == 12) {

                            if (!empty($value->data->file_path)) {

                                $item->product_image =  asset('storage/products/' . $value->data->file_path);

                            }

                        }

                    }



                    $items[] = $item;

                }

                if ($count > 1) {

                    $data = collect($order_items)->where('cart_group', $value)->all();

                    $item = collect($order_items)->where('cart_group', $value)->first();

                    $product = Product::find($item->product_id);

                    $item->product_title = $product->title;

                    if ($item->title) {

                        $item->title = $item->extra_title . ' (' . $item->title . ')';

                    } else {

                        $item->title = $item->extra_title;

                    }

                    $item->condition = $this->makeCondition(unserialize($item->item->item_attributes));

                    $item->sku_details = $this->makeDetails($data);

                    $item->quantity = $count;

                    $item->item_price = $this->itemTotalPrice($data);

                    $brand = ProductBrand::find($product->product_brand_id);

                    $item->product_brand_title = $brand->title;

                    if (empty($item->product_item_image)) {

                        $item->product_image = asset('storage/products/' . $product->product_image);

                    }

                    $attributes = collect(unserialize($item->item->item_attributes))->sortBy('ordering');

                    foreach ($attributes as $value) {

                        if ($value->attribute_type_id == 12) {

                            if (!empty($value->data->file_path)) {

                                $item->product_image =  asset('storage/products/' . $value->data->file_path);

                            }

                        }

                    }

                    $items[] = $item;

                }

            }



            $total_collects = 0;

            $local_total_collects = 0;

            $currency_symbol = getDefaultCurrencySymbol();

            $local_currency_symbol = '';

            $currency_rate = 0;

            // if ($order->status == 3) {

            // dd('here');

            $collects = Collect::where('order_id', $mail_data->order->id)->get(['amount', 'currency', 'local_amount', 'currency_rate']);

            foreach ($collects as $value) {

                $total_collects += $value->amount;

                if ($value->local_amount) {

                    $local_total_collects += $value->local_amount;

                    $local_currency_symbol = $this->country_data($value->currency, 'symbol');

                    $currency_rate = $value->currency_rate;

                }

            }

            $partial_order_amount = $currency_symbol . '' . number_format($total_collects, 2);

            $order_balance = $currency_symbol . '' . number_format(abs($mail_data->order->total_amount - $total_collects), 2);



            if ($local_total_collects > 0) {

                $local_partial_order_amount = $local_currency_symbol . '' . number_format($local_total_collects, 2);

                $local_order_balance = $local_currency_symbol . '' . number_format(abs(($mail_data->order->total_amount * $currency_rate) - $local_total_collects), 2);



                $partial_order_amount = $partial_order_amount . ' (' . $local_partial_order_amount . ')';

                $order_balance = $order_balance . ' (' . $local_order_balance . ')';

            }

            // }

            // dd($partial_order_amount, $order_balance);

            $order_list = '<table border="0" cellpadding="0" cellspacing="0" width="100%" style="font-family: Montserrat, Arial, sans-serif; border-collapse: collapse; width: 100%; background-color: #ffffff; border: 1px solid #e0e0e0;">';



            $order_list .= '<tr style="background-color: #f6f6f6;">';

            $order_list .= '<th colspan="2" style="padding: 10px; text-align: left; font-weight: bold; font-size: 14px; border-bottom: 1px solid #ddd;">Details</th>';

            $order_list .= '<th style="padding: 10px; text-align: center; font-weight: bold; font-size: 14px; border-bottom: 1px solid #ddd;">Quantity</th>';

            $order_list .= '<th style="padding: 10px; text-align: right; font-weight: bold; font-size: 14px; border-bottom: 1px solid #ddd;">Amount</th>';

            $order_list .= '</tr>';



            foreach ($items as $item) {

                $order_list .= '<tr>';

                $order_list .= '<td style="padding: 10px; vertical-align: top; width: 60px;">';

                $order_list .= '<img src="' . $item->product_image . '" alt="' . $item->product_title . '" style="width: 50px; height: auto; border-radius: 4px;">';

                $order_list .= '</td>';

                $order_list .= '<td style="padding: 10px; vertical-align: top; font-size: 13px; color: #333;">';

                $order_list .= '<strong>' . $item->product_brand_title . '</strong><br>' . $item->product_title . '<br>' . $item->title . ' (' . $item->condition . ')<br><small style="color: #999;">SKU: ' . $item->sku_details . '</small>';

                $order_list .= '</td>';

                $order_list .= '<td style="padding: 10px; text-align: center; font-size: 13px; color: #333;">' . $item->quantity . '</td>';

                $order_list .= '<td style="padding: 10px; text-align: right; font-size: 13px; color: #333;">';

                $order_list .= getDefaultCurrencySymbol() . number_format(round($item->item_price, 2), 2);

                if ($local_total_collects > 0) {

                    $order_list .= '<br><small style="color: #999;">' . $local_currency_symbol . number_format(round($item->item_price * $currency_rate, 2), 2) . '</small>';

                }

                $order_list .= '</td>';

                $order_list .= '</tr>';

            }



            $order_list .= '<tr style="background-color: #f9f9f9;">';

            $order_list .= '<td colspan="3" style="padding: 10px; text-align: right; font-weight: bold; font-size: 13px;">SUBTOTAL</td>';

            $order_list .= '<td style="padding: 10px; text-align: right; font-size: 13px;">$' . $mail_data->order->sub_total_amount . '</td>';

            $order_list .= '</tr>';



            $order_list .= '<tr style="background-color: #f9f9f9;">';

            $order_list .= '<td colspan="3" style="padding: 10px; text-align: right; font-weight: bold; font-size: 13px;">SHIPPING</td>';

            $order_list .= '<td style="padding: 10px; text-align: right; font-size: 13px;">$' . $mail_data->order->shipping_amount . '</td>';

            $order_list .= '</tr>';



            if ($mail_data->order->transaction_discount > 0) {

                $order_list .= '<tr style="background-color: #f9f9f9;">';

                $order_list .= '<td colspan="3" style="padding: 10px; text-align: right; font-weight: bold; font-size: 13px;">Transaction Discount</td>';

                $order_list .= '<td style="padding: 10px; text-align: right; font-size: 13px;">$' . $mail_data->order->transaction_discount . '</td>';

                $order_list .= '</tr>';

            }



            $order_list .= '<tr style="background-color: #efefef;">';

            $order_list .= '<td colspan="3" style="padding: 12px; text-align: right; font-weight: bold; font-size: 14px; color: #000;">TOTAL</td>';

            $order_list .= '<td style="padding: 12px; text-align: right; font-size: 14px; font-weight: bold; color: #000;">';

            $order_list .= getDefaultCurrencySymbol() . $mail_data->order->total_amount;

            if ($local_total_collects > 0) {

                $order_list .= '<br><small style="color: #999;">' . $local_currency_symbol . number_format(round($mail_data->order->total_amount * $currency_rate, 2), 2) . '</small>';

            }

            $order_list .= '</td>';

            $order_list .= '</tr>';



            $order_list .= '</table>';



            $order_number_id = $mail_data->order->order_no;

            $order_id = $mail_data->order->order_no;

            $order_date_time = Carbon::parse($mail_data->order->created_at)->format('M, d Y g:i A');

            $order_status = $this->orderStatus($mail_data->order->status);

            $tracking_code = $mail_data->tracking_code ?? '';



            // dd($mail_data->$product_url);



            $billing = UserBilling::find($mail_data->order->user_billing_id);



            $order_billing_address = $billing->address ?? "";

            $order_billing_phone = $billing->tel_cell ?? "";

            // $order_billing_phone = $billing->tel_cell_country_code.$billing->tel_cell;

            $order_payment_method = '';



            $shipping = UserShipping::find($mail_data->order->user_shipping_id);

            if (!empty($shipping)) {

                // dd($mail_data->order, $shipping);

                $shipping_from_name = site_info('site_name');

                $shipping_to_name = $shipping->first_name . ' ' . $shipping->last_name;

                $shipping_from_email = site_info('email');

                $shipping_to_email = $shipping->email;

                $shipping_from_phone = site_info('tel_cell');

                // $shipping_from_phone = site_info('tel_cell_country_code') . site_info('tel_cell');

                $shipping_to_phone = $shipping->tel_cell;

                // $shipping_to_phone = $shipping->tel_cell_country_code. $shipping->tel_cell;

            }

        }



        $your_company_name = $mail_data->your_company_name ?? '';

        $product_url = $mail_data->product_url ?? '';

        $product_price = $mail_data->product_price ?? '';

        $product_name = $mail_data->product_name ?? '';

        $customer_email = $mail_data->customer_email ?? '';

        $Customer_Name = $mail_data->Customer_Name ?? '';

        $Product_Name = $mail_data->Product_Name ?? "";

        $Price = $mail_data->Price ?? '';

        $Quantity = $mail_data->Quantity ?? '';

        $promo_code = $mail_data->promo_code ?? '';

        $promo_date = $mail_data->promo_date ?? '';

        // dd($mail_data->customer_email);





        $review_status = '';

        $review_rating = '';

        $review_reward_points = '';

        if (!empty($mail_data->review)) {

            $review_reward = Reward::where([

                'review_id' => $mail_data->review->id,

            ])->first();

            $review_status = $this->reviewStatus($mail_data->review->status);

            $review_rating = $mail_data->review->rating;

            if (!empty($review_reward)) {

                $review_reward_points = (string)$review_reward->points;

            }

        }



        $reward_points = '';

        $instant_reward_points = '';

        $reward_cash_value = '';

        if (!empty($mail_data->cash_reward)) {

            $reward_points = $mail_data->cash_reward->points;

            $reward_cash_value = $mail_data->cash_reward->amount;

        }



        $contact_first_name = '';

        $contact_last_name = '';

        $contact_email = '';

        $contact_how_can_help = '';

        $contact_subject = '';

        $contact_message = '';

        $contact_phone = '';

        if (!empty($mail_data->contact)) {

            $contact_first_name = $mail_data->contact->contact_first_name;

            $contact_last_name = $mail_data->contact->contact_last_name;

            $contact_email = $mail_data->contact->contact_email;

            $contact_how_can_help = $mail_data->contact->contact_how_can_help;

            $contact_subject = $mail_data->contact->contact_subject;

            $contact_message = $mail_data->contact->contact_message;

            $contact_phone = $mail_data->contact->contact_phone;

        }



        $data = [

            Carbon::now()->format('M, d Y g:i A'),

            site_info('site_name'),

            url('/'),

            site_info('email'),

            site_info('tel_cell'),

            // site_info('tel_cell_country_code'). site_info('tel_cell'),

            site_info('meta_title'),

            site_info('address') . site_info('address_2'),

            site_info('city'),

            site_info('state'),

            site_info('zip_code'),

            site_info('country'),

            site_info('email'),

            site_info('tel_cell'),

            // site_info('tel_cell_country_code') . site_info('tel_cell'),

            $user_name,

            $first_name,

            $last_name,

            $reset_password_link,

            $account_verification_link,

            $admin_email,

            $admin_username,

            $admin_first_name,

            $admin_last_name,

            $order_list,

            $order_number_id,

            $order_date_time,

            $order_tracking_number,

            $order_status,

            $partial_order_amount,

            $order_balance,

            $shipping_from_name,

            $shipping_to_name,

            $shipping_from_email,

            $shipping_to_email,

            $shipping_from_phone,

            $shipping_to_phone,

            $review_status,

            $review_rating,

            $review_reward_points,

            $reward_points,

            $instant_reward_points,

            $reward_cash_value,

            $order_billing_address,

            $order_billing_phone,

            $order_shipping_address,

            $order_shipping_phone,

            $order_payment_method,

            $shipping_method,

            $contact_first_name,

            $contact_last_name,

            $contact_email,

            $contact_how_can_help,

            $contact_subject,

            $contact_message,

            $contact_phone,

            $tracking_code,

            $order_id,

            $your_company_name,

            $product_url,

            $product_price,

            $product_name,

            $customer_email,

            $Customer_Name,

            $Product_Name,

            $Quantity,

            $Price,

            $promo_code,

            $promo_date,

        ];

        // dd($Customer_Name);

        $content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, $mail_data->email_body);

        // dd(app('App\Http\Controllers\Controller')->email_constants(), $data);

        $subject = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, $mail_data->subject);

        $pre_header = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, $mail_data->pre_header);

        $sms_body = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, $mail_data->sms_body);

        $usedVars = $this->extractUsedVariableValues($mail_data->sms_body, app('App\Http\Controllers\Controller')->email_constants(), $data);

        $sms_email_data = (object)[

            'content' => $content,

            'subject' => $subject,

            'pre_header' => $pre_header,

            'sms_body' => $sms_body,

            'used_vars' => $usedVars,

            'user' => $mail_data->user

        ];



        return $sms_email_data;

    }

    function extractUsedVariableValues($template = null, array $constants, array $data): array

    {

        $usedValues = [];

        $variablePositions = [];



        // Find all variables and their positions in the template

        foreach ($constants as $index => $variable) {

            $position = strpos($template, $variable);

            if ($position !== false) {

                $variablePositions[] = [

                    'position' => $position,

                    'index' => $index,

                    'variable' => $variable

                ];

            }

        }

        // Sort by position in template

        usort($variablePositions, function ($a, $b) {

            return $a['position'] <=> $b['position'];

        });



        // Extract values in the order they appear in template

        foreach ($variablePositions as $varInfo) {

            $usedValues[] = $data[$varInfo['index']] ?? null;

        }

        return $usedValues;

    }

    function getMenu($menu_location, $slug)

    {

        $pages = $this->pages($menu_location, $slug);

        return $pages;

    }



    public function pages($menu_location = null, $slug = null)

    {

        $pages = Page::where([

            ['parent_menu_id', null],

        ])

            ->orderBy('ordering', 'asc')

            ->get();



        if ($menu_location) {

            $pages = Page::where([

                ['parent_menu_id', null],

                ['status', '=', '1'],

                ['menu_location', '=', $menu_location]

            ])

                ->orderBy('ordering', 'asc')

                ->get();

        }



        foreach ($pages as $key => $page) {

            $page->index_no = ++$key;

            $page->path = $page->slug;

            if ($menu_location) {

                $page->items = $this->childPages($page->menuChildren, $slug, $menu_location, $page->index_no, $page->slug);

            } else {

                $page->items = $this->childPages($page->children, $slug, $menu_location, $page->index_no, $page->slug);

            }



            if (!is_null($page->access_label_id)) {

                $pageRoleIds = AccessLabel::findOrFail($page->access_label_id)->user_group_id;

                $pageRoleIds = explode(',', $pageRoleIds);

            }

            $page->access = false;

            if (($page->access_label_id == 1) || (auth()->check() && in_array(auth()->user()->user_group_id, $pageRoleIds))) {

                $page->access = true;

            }



            $page->menu_active = false;

            if ($page->slug == $slug) {

                $page->menu_active = true;

            }



            foreach ($page->items as $item) {

                if ($item->menu_active) {

                    $page->menu_active = true;

                }

            }

        }

        // dd($pages);



        return $pages;

    }



    public function childPages($items, $slug, $menu_location, $index_no, $path)

    {

        foreach ($items as $key => $item) {

            $item->index_no = $index_no . '.' . ++$key;

            $item->path = $path . '/' . $item->slug;



            if ($menu_location) {

                $item->items = $this->childPages($item->menuChildren, $slug, $menu_location, $item->index_no, $item->path);

            } else {

                $item->items = $this->childPages($item->children, $slug, $menu_location, $item->index_no, $item->path);

            }



            if (!is_null($item->access_label_id)) {

                $pageRoleIds = AccessLabel::findOrFail($item->access_label_id)->user_group_id;

                $pageRoleIds = explode(',', $pageRoleIds);

            }

            $item->access = false;

            if (($item->access_label_id == 1) || (auth()->check() && in_array(auth()->user()->user_group_id, $pageRoleIds))) {

                $item->access = true;

            }

            $item->menu_active = false;

            if ($item->slug == $slug) {

                $item->menu_active = true;

            }



            foreach ($item->items as $item_data) {

                if ($item_data->menu_active) {

                    $item->menu_active = true;

                }

            }

        }

        return $items;

    }



    public function getSeries($brand_id, $option_id = null)

    {

        $items = ProductSeries::where('product_brand_id', $brand_id)->get();



        return view('admin.jquery_live.options', compact('items', 'option_id'));

    }



    public function getLiveCategories($brand_id, $option_id = null)

    {

        $item = ProductBrand::find($brand_id);

        $array = explode(',', $item->product_categories_id);

        foreach ($array as $value) {

            $items[] = ProductCategory::find($value);

        }

        // dd($categories);

        return view('admin.jquery_live.options', compact('items', 'option_id'));

    }



    public function deleteFile($folder, $path)

    {

        $fullPath = $folder . '/' . $path;

        if ($path && Storage::exists($fullPath)) {

            Storage::delete($fullPath);

        }



        $insideFolder = explode('/', $path)[0];



        $dir = storage_path('app/' . $folder . $insideFolder);



        if ((count(glob($dir . '/*')) <= 0) && Storage::exists($dir)) {

            Storage::deleteDirectory($dir);

        }

    }



    function removeEmptyDir($path)

    {

        $dir = storage_path('app/' . $path);

        if ((count(glob($dir . '/*')) <= 0)) {

            Storage::deleteDirectory($dir);

        }

    }



    public function attribute_types($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Condition',

                'key' => 'condition',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Carrier',

                'key' => 'carrier',

            ],

            '3' => (object)[

                'id' => '3',

                'title' => 'Carrier Type',

                'key' => 'carrier_type',

            ],

            '4' => (object)[

                'id' => '4',

                'title' => 'Storage',

                'key' => 'storage',

            ],

            '5' => (object)[

                'id' => '5',

                'title' => 'Processor',

                'key' => 'processor',

            ],

            '6' => (object)[

                'id' => '6',

                'title' => 'Screen size',

                'key' => 'screen_size',

            ],

            '7' => (object)[

                'id' => '7',

                'title' => 'Year',

                'key' => 'year',

            ],

            '8' => (object)[

                'id' => '8',

                'title' => 'RAM',

                'key' => 'ram',

            ],

            '9' => (object)[

                'id' => '9',

                'title' => 'Connectivity',

                'key' => 'connectivity',

            ],

            '10' => (object)[

                'id' => '10',

                'title' => 'Case Type',

                'key' => 'case_type',

            ],

            '11' => (object)[

                'id' => '11',

                'title' => 'Case Size',

                'key' => 'case_size',

            ],

            '12' => (object)[

                'id' => '12',

                'title' => 'Color',

                'key' => 'color',

            ],

            '13' => (object)[

                'id' => '13',

                'title' => 'Other',

                'key' => 'other',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($option->id == $id) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function input_types($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Radio',

                'key' => 'radio',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Checkbox',

                'key' => 'checkbox',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($option->id == $id) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function refund_status($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Refund Requested',

                'key' => 'requested',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Refund Completed',

                'key' => 'completed',

            ],

            '3' => (object)[

                'id' => '3',

                'title' => 'Refund Denied',

                'key' => 'denied',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($option->id == $id) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function module_types($id = null)

    {

        $options = (object)array(

            '1' => (object)[

                'id' => '1',

                'title' => 'Raw',

                'key' => 'raw',

            ],

            '2' => (object)[

                'id' => '2',

                'title' => 'Custom',

                'key' => 'custom',

            ],

            '3' => (object)[

                'id' => '3',

                'title' => 'Slideshow',

                'key' => 'slideshow',

            ],

            '4' => (object)[

                'id' => '4',

                'title' => 'Categories',

                'key' => 'categories',

            ],

            '5' => (object)[

                'id' => '5',

                'title' => 'Brands',

                'key' => 'brands',

            ],

            '6' => (object)[

                'id' => '6',

                'title' => 'Series',

                'key' => 'series',

            ],

            '7' => (object)[

                'id' => '7',

                'title' => 'Recommended',

                'key' => 'recommended',

            ],

            '8' => (object)[

                'id' => '8',

                'title' => 'Reviews',

                'key' => 'reviews',

            ],

        );



        if ($id) {

            foreach ($options as $option) {

                if ($option->id == $id) {

                    return $option;

                }

            }

        }



        return $options;

    }



    public function userGroups()

    {

        $user_groups = UserGroup::where([

            'user_group_parent_id' => null

        ])->get();

        foreach ($user_groups as $user_group) {

            $user_group->path = '-&nbsp;';

            $user_group->child = $this->childUserGroup($user_group->children, $user_group->path);

        }



        return $user_groups;

    }



    public function childUserGroup($items, $path)

    {

        foreach ($items as $item) {

            $item->path = $path . '-&nbsp;';

            $item->child = $this->childUserGroup($item->children, $item->path);

        }

        return $items;

    }



    public function getCommonUserGroups($items)

    {

        $user_group_roles = array();

        foreach ($items as $item) {

            $user_group = UserGroup::where('id', $item->user_group_id)->first();

            if ($user_group) {

                $user_group_roles[] = $user_group->id;

            }

        }

        return $user_group_roles;

    }



    public function getUserGroupTitles($items)

    {

        $user_group_titles = array();

        foreach ($items as $item) {

            $user_group = UserGroup::findOrFail($item->user_group_id);

            $user_group_titles[] = $user_group->title;

        }

        $user_group_titles = implode(', ', $user_group_titles);



        return $user_group_titles;

    }



    public function getUserAccess($items)

    {

        $access = false;

        foreach ($items as $item) {

            $access = in_array($item, auth()->user()->getAuthUserChildArray());

        }

        if (auth()->user()->access('master_login')) return true;

        return $access;

    }



    public function fa_icons()

    {

        $fa_icons = (object)array(

            'fab fa-500px' => '500px',

            'fab fa-accessible-icon' => 'accessible-icon',

            'fab fa-accusoft' => 'accusoft',

            'fas fa-address-book' => 'address-book',

            'far fa-address-book' => 'address-book',

            'fas fa-address-card' => 'address-card',

            'far fa-address-card' => 'address-card',

            'fas fa-adjust' => 'adjust',

            'fab fa-adn' => 'adn',

            'fab fa-adversal' => 'adversal',

            'fab fa-affiliatetheme' => 'affiliatetheme',

            'fab fa-algolia' => 'algolia',

            'fas fa-align-center' => 'align-center',

            'fas fa-align-justify' => 'align-justify',

            'fas fa-align-left' => 'align-left',

            'fas fa-align-right' => 'align-right',

            'fas fa-allergies' => 'allergies',

            'fab fa-amazon' => 'amazon',

            'fab fa-amazon-pay' => 'amazon-pay',

            'fas fa-ambulance' => 'ambulance',

            'fas fa-american-sign-language-interpreting' => 'american-sign-language-interpreting',

            'fab fa-amilia' => 'amilia',

            'fas fa-anchor' => 'anchor',

            'fab fa-android' => 'android',

            'fab fa-angellist' => 'angellist',

            'fas fa-angle-double-down' => 'angle-double-down',

            'fas fa-angle-double-left' => 'angle-double-left',

            'fas fa-angle-double-right' => 'angle-double-right',

            'fas fa-angle-double-up' => 'angle-double-up',

            'fas fa-angle-down' => 'angle-down',

            'fas fa-angle-left' => 'angle-left',

            'fas fa-angle-right' => 'angle-right',

            'fas fa-angle-up' => 'angle-up',

            'fab fa-angrycreative' => 'angrycreative',

            'fab fa-angular' => 'angular',

            'fab fa-app-store' => 'app-store',

            'fab fa-app-store-ios' => 'app-store-ios',

            'fab fa-apper' => 'apper',

            'fab fa-apple' => 'apple',

            'fab fa-apple-pay' => 'apple-pay',

            'fas fa-archive' => 'archive',

            'fas fa-arrow-alt-circle-down' => 'arrow-alt-circle-down',

            'far fa-arrow-alt-circle-down' => 'arrow-alt-circle-down',

            'fas fa-arrow-alt-circle-left' => 'arrow-alt-circle-left',

            'far fa-arrow-alt-circle-left' => 'arrow-alt-circle-left',

            'fas fa-arrow-alt-circle-right' => 'arrow-alt-circle-right',

            'far fa-arrow-alt-circle-right' => 'arrow-alt-circle-right',

            'fas fa-arrow-alt-circle-up' => 'arrow-alt-circle-up',

            'far fa-arrow-alt-circle-up' => 'arrow-alt-circle-up',

            'fas fa-arrow-circle-down' => 'arrow-circle-down',

            'fas fa-arrow-circle-left' => 'arrow-circle-left',

            'fas fa-arrow-circle-right' => 'arrow-circle-right',

            'fas fa-arrow-circle-up' => 'arrow-circle-up',

            'fas fa-arrow-down' => 'arrow-down',

            'fas fa-arrow-left' => 'arrow-left',

            'fas fa-arrow-right' => 'arrow-right',

            'fas fa-arrow-up' => 'arrow-up',

            'fas fa-arrows-alt' => 'arrows-alt',

            'fas fa-arrows-alt-h' => 'arrows-alt-h',

            'fas fa-arrows-alt-v' => 'arrows-alt-v',

            'fas fa-assistive-listening-systems' => 'assistive-listening-systems',

            'fas fa-asterisk' => 'asterisk',

            'fab fa-asymmetrik' => 'asymmetrik',

            'fas fa-at' => 'at',

            'fab fa-audible' => 'audible',

            'fas fa-audio-description' => 'audio-description',

            'fab fa-autoprefixer' => 'autoprefixer',

            'fab fa-avianex' => 'avianex',

            'fab fa-aviato' => 'aviato',

            'fab fa-aws' => 'aws',

            'fas fa-backward' => 'backward',

            'fas fa-balance-scale' => 'balance-scale',

            'fas fa-ban' => 'ban',

            'fas fa-band-aid' => 'band-aid',

            'fab fa-bandcamp' => 'bandcamp',

            'fas fa-barcode' => 'barcode',

            'fas fa-bars' => 'bars',

            'fas fa-baseball-ball' => 'baseball-ball',

            'fas fa-basketball-ball' => 'basketball-ball',

            'fas fa-bath' => 'bath',

            'fas fa-battery-empty' => 'battery-empty',

            'fas fa-battery-full' => 'battery-full',

            'fas fa-battery-half' => 'battery-half',

            'fas fa-battery-quarter' => 'battery-quarter',

            'fas fa-battery-three-quarters' => 'battery-three-quarters',

            'fas fa-bed' => 'bed',

            'fas fa-beer' => 'beer',

            'fab fa-behance' => 'behance',

            'fab fa-behance-square' => 'behance-square',

            'fas fa-bell' => 'bell',

            'far fa-bell' => 'bell',

            'fas fa-bell-slash' => 'bell-slash',

            'far fa-bell-slash' => 'bell-slash',

            'fas fa-bicycle' => 'bicycle',

            'fab fa-bimobject' => 'bimobject',

            'fas fa-binoculars' => 'binoculars',

            'fas fa-birthday-cake' => 'birthday-cake',

            'fab fa-bitbucket' => 'bitbucket',

            'fab fa-bitcoin' => 'bitcoin',

            'fab fa-bity' => 'bity',

            'fab fa-black-tie' => 'black-tie',

            'fab fa-blackberry' => 'blackberry',

            'fas fa-blind' => 'blind',

            'fab fa-blogger' => 'blogger',

            'fab fa-blogger-b' => 'blogger-b',

            'fab fa-bluetooth' => 'bluetooth',

            'fab fa-bluetooth-b' => 'bluetooth-b',

            'fas fa-bold' => 'bold',

            'fas fa-bolt' => 'bolt',

            'fas fa-bomb' => 'bomb',

            'fas fa-book' => 'book',

            'fas fa-bookmark' => 'bookmark',

            'far fa-bookmark' => 'bookmark',

            'fas fa-bowling-ball' => 'bowling-ball',

            'fas fa-box' => 'box',

            'fas fa-box-open' => 'box-open',

            'fas fa-boxes' => 'boxes',

            'fas fa-braille' => 'braille',

            'fas fa-briefcase' => 'briefcase',

            'fas fa-briefcase-medical' => 'briefcase-medical',

            'fab fa-btc' => 'btc',

            'fas fa-bug' => 'bug',

            'fas fa-building' => 'building',

            'far fa-building' => 'building',

            'fas fa-bullhorn' => 'bullhorn',

            'fas fa-bullseye' => 'bullseye',

            'fas fa-burn' => 'burn',

            'fab fa-buromobelexperte' => 'buromobelexperte',

            'fas fa-bus' => 'bus',

            'fab fa-buysellads' => 'buysellads',

            'fas fa-calculator' => 'calculator',

            'fas fa-calendar' => 'calendar',

            'far fa-calendar' => 'calendar',

            'fas fa-calendar-alt' => 'calendar-alt',

            'far fa-calendar-alt' => 'calendar-alt',

            'fas fa-calendar-check' => 'calendar-check',

            'far fa-calendar-check' => 'calendar-check',

            'fas fa-calendar-minus' => 'calendar-minus',

            'far fa-calendar-minus' => 'calendar-minus',

            'fas fa-calendar-plus' => 'calendar-plus',

            'far fa-calendar-plus' => 'calendar-plus',

            'fas fa-calendar-times' => 'calendar-times',

            'far fa-calendar-times' => 'calendar-times',

            'fas fa-camera' => 'camera',

            'fas fa-camera-retro' => 'camera-retro',

            'fas fa-capsules' => 'capsules',

            'fas fa-car' => 'car',

            'fas fa-caret-down' => 'caret-down',

            'fas fa-caret-left' => 'caret-left',

            'fas fa-caret-right' => 'caret-right',

            'fas fa-caret-square-down' => 'caret-square-down',

            'far fa-caret-square-down' => 'caret-square-down',

            'fas fa-caret-square-left' => 'caret-square-left',

            'far fa-caret-square-left' => 'caret-square-left',

            'fas fa-caret-square-right' => 'caret-square-right',

            'far fa-caret-square-right' => 'caret-square-right',

            'fas fa-caret-square-up' => 'caret-square-up',

            'far fa-caret-square-up' => 'caret-square-up',

            'fas fa-caret-up' => 'caret-up',

            'fas fa-cart-arrow-down' => 'cart-arrow-down',

            'fas fa-cart-plus' => 'cart-plus',

            'fab fa-cc-amazon-pay' => 'cc-amazon-pay',

            'fab fa-cc-amex' => 'cc-amex',

            'fab fa-cc-apple-pay' => 'cc-apple-pay',

            'fab fa-cc-diners-club' => 'cc-diners-club',

            'fab fa-cc-discover' => 'cc-discover',

            'fab fa-cc-jcb' => 'cc-jcb',

            'fab fa-cc-mastercard' => 'cc-mastercard',

            'fab fa-cc-paypal' => 'cc-paypal',

            'fab fa-cc-stripe' => 'cc-stripe',

            'fab fa-cc-visa' => 'cc-visa',

            'fab fa-centercode' => 'centercode',

            'fas fa-certificate' => 'certificate',

            'fas fa-chart-area' => 'chart-area',

            'fas fa-chart-bar' => 'chart-bar',

            'far fa-chart-bar' => 'chart-bar',

            'fas fa-chart-line' => 'chart-line',

            'fas fa-chart-pie' => 'chart-pie',

            'fas fa-check' => 'check',

            'fas fa-check-circle' => 'check-circle',

            'far fa-check-circle' => 'check-circle',

            'fas fa-check-square' => 'check-square',

            'far fa-check-square' => 'check-square',

            'fas fa-chess' => 'chess',

            'fas fa-chess-bishop' => 'chess-bishop',

            'fas fa-chess-board' => 'chess-board',

            'fas fa-chess-king' => 'chess-king',

            'fas fa-chess-knight' => 'chess-knight',

            'fas fa-chess-pawn' => 'chess-pawn',

            'fas fa-chess-queen' => 'chess-queen',

            'fas fa-chess-rook' => 'chess-rook',

            'fas fa-chevron-circle-down' => 'chevron-circle-down',

            'fas fa-chevron-circle-left' => 'chevron-circle-left',

            'fas fa-chevron-circle-right' => 'chevron-circle-right',

            'fas fa-chevron-circle-up' => 'chevron-circle-up',

            'fas fa-chevron-down' => 'chevron-down',

            'fas fa-chevron-left' => 'chevron-left',

            'fas fa-chevron-right' => 'chevron-right',

            'fas fa-chevron-up' => 'chevron-up',

            'fas fa-child' => 'child',

            'fab fa-chrome' => 'chrome',

            'fas fa-circle' => 'circle',

            'far fa-circle' => 'circle',

            'fas fa-circle-notch' => 'circle-notch',

            'fas fa-clipboard' => 'clipboard',

            'far fa-clipboard' => 'clipboard',

            'fas fa-clipboard-check' => 'clipboard-check',

            'fas fa-clipboard-list' => 'clipboard-list',

            'fas fa-clock' => 'clock',

            'far fa-clock' => 'clock',

            'fas fa-clone' => 'clone',

            'far fa-clone' => 'clone',

            'fas fa-closed-captioning' => 'closed-captioning',

            'far fa-closed-captioning' => 'closed-captioning',

            'fas fa-cloud' => 'cloud',

            'fas fa-cloud-download-alt' => 'cloud-download-alt',

            'fas fa-cloud-upload-alt' => 'cloud-upload-alt',

            'fab fa-cloudscale' => 'cloudscale',

            'fab fa-cloudsmith' => 'cloudsmith',

            'fab fa-cloudversify' => 'cloudversify',

            'fas fa-code' => 'code',

            'fas fa-code-branch' => 'code-branch',

            'fab fa-codepen' => 'codepen',

            'fab fa-codiepie' => 'codiepie',

            'fas fa-coffee' => 'coffee',

            'fas fa-cog' => 'cog',

            'fas fa-cogs' => 'cogs',

            'fas fa-columns' => 'columns',

            'fas fa-comment' => 'comment',

            'far fa-comment' => 'comment',

            'fas fa-comment-alt' => 'comment-alt',

            'far fa-comment-alt' => 'comment-alt',

            'fas fa-comment-dots' => 'comment-dots',

            'fas fa-comment-slash' => 'comment-slash',

            'fas fa-comments' => 'comments',

            'far fa-comments' => 'comments',

            'fas fa-compass' => 'compass',

            'far fa-compass' => 'compass',

            'fas fa-compress' => 'compress',

            'fab fa-connectdevelop' => 'connectdevelop',

            'fab fa-contao' => 'contao',

            'fas fa-copy' => 'copy',

            'far fa-copy' => 'copy',

            'fas fa-copyright' => 'copyright',

            'far fa-copyright' => 'copyright',

            'fas fa-couch' => 'couch',

            'fab fa-cpanel' => 'cpanel',

            'fab fa-creative-commons' => 'creative-commons',

            'fas fa-credit-card' => 'credit-card',

            'far fa-credit-card' => 'credit-card',

            'fas fa-crop' => 'crop',

            'fas fa-crosshairs' => 'crosshairs',

            'fab fa-css3' => 'css3',

            'fab fa-css3-alt' => 'css3-alt',

            'fas fa-cube' => 'cube',

            'fas fa-cubes' => 'cubes',

            'fas fa-cut' => 'cut',

            'fab fa-cuttlefish' => 'cuttlefish',

            'fab fa-d-and-d' => 'd-and-d',

            'fab fa-dashcube' => 'dashcube',

            'fas fa-database' => 'database',

            'fas fa-deaf' => 'deaf',

            'fab fa-delicious' => 'delicious',

            'fab fa-deploydog' => 'deploydog',

            'fab fa-deskpro' => 'deskpro',

            'fas fa-desktop' => 'desktop',

            'fab fa-deviantart' => 'deviantart',

            'fas fa-diagnoses' => 'diagnoses',

            'fab fa-digg' => 'digg',

            'fab fa-digital-ocean' => 'digital-ocean',

            'fab fa-discord' => 'discord',

            'fab fa-discourse' => 'discourse',

            'fas fa-dna' => 'dna',

            'fab fa-dochub' => 'dochub',

            'fab fa-docker' => 'docker',

            'fas fa-dollar-sign' => 'dollar-sign',

            'fas fa-dolly' => 'dolly',

            'fas fa-dolly-flatbed' => 'dolly-flatbed',

            'fas fa-donate' => 'donate',

            'fas fa-dot-circle' => 'dot-circle',

            'far fa-dot-circle' => 'dot-circle',

            'fas fa-dove' => 'dove',

            'fas fa-download' => 'download',

            'fab fa-draft2digital' => 'draft2digital',

            'fab fa-dribbble' => 'dribbble',

            'fab fa-dribbble-square' => 'dribbble-square',

            'fab fa-dropbox' => 'dropbox',

            'fab fa-drupal' => 'drupal',

            'fab fa-dyalog' => 'dyalog',

            'fab fa-earlybirds' => 'earlybirds',

            'fab fa-edge' => 'edge',

            'fas fa-edit' => 'edit',

            'far fa-edit' => 'edit',

            'fas fa-eject' => 'eject',

            'fab fa-elementor' => 'elementor',

            'fas fa-ellipsis-h' => 'ellipsis-h',

            'fas fa-ellipsis-v' => 'ellipsis-v',

            'fab fa-ember' => 'ember',

            'fab fa-empire' => 'empire',

            'fas fa-envelope' => 'envelope',

            'far fa-envelope' => 'envelope',

            'fas fa-envelope-open' => 'envelope-open',

            'far fa-envelope-open' => 'envelope-open',

            'fas fa-envelope-square' => 'envelope-square',

            'fab fa-envira' => 'envira',

            'fas fa-eraser' => 'eraser',

            'fab fa-erlang' => 'erlang',

            'fab fa-ethereum' => 'ethereum',

            'fab fa-etsy' => 'etsy',

            'fas fa-euro-sign' => 'euro-sign',

            'fas fa-exchange-alt' => 'exchange-alt',

            'fas fa-exclamation' => 'exclamation',

            'fas fa-exclamation-circle' => 'exclamation-circle',

            'fas fa-exclamation-triangle' => 'exclamation-triangle',

            'fas fa-expand' => 'expand',

            'fas fa-expand-arrows-alt' => 'expand-arrows-alt',

            'fab fa-expeditedssl' => 'expeditedssl',

            'fas fa-external-link-alt' => 'external-link-alt',

            'fas fa-external-link-square-alt' => 'external-link-square-alt',

            'fas fa-eye' => 'eye',

            'fas fa-eye-dropper' => 'eye-dropper',

            'fas fa-eye-slash' => 'eye-slash',

            'far fa-eye-slash' => 'eye-slash',

            'fab fa-facebook' => 'facebook',

            'fab fa-facebook-f' => 'facebook-f',

            'fab fa-facebook-messenger' => 'facebook-messenger',

            'fab fa-facebook-square' => 'facebook-square',

            'fas fa-fast-backward' => 'fast-backward',

            'fas fa-fast-forward' => 'fast-forward',

            'fas fa-fax' => 'fax',

            'fas fa-female' => 'female',

            'fas fa-fighter-jet' => 'fighter-jet',

            'fas fa-file' => 'file',

            'far fa-file' => 'file',

            'fas fa-file-alt' => 'file-alt',

            'far fa-file-alt' => 'file-alt',

            'fas fa-file-archive' => 'file-archive',

            'far fa-file-archive' => 'file-archive',

            'fas fa-file-audio' => 'file-audio',

            'far fa-file-audio' => 'file-audio',

            'fas fa-file-code' => 'file-code',

            'far fa-file-code' => 'file-code',

            'fas fa-file-excel' => 'file-excel',

            'far fa-file-excel' => 'file-excel',

            'fas fa-file-image' => 'file-image',

            'far fa-file-image' => 'file-image',

            'fas fa-file-medical' => 'file-medical',

            'fas fa-file-medical-alt' => 'file-medical-alt',

            'fas fa-file-pdf' => 'file-pdf',

            'far fa-file-pdf' => 'file-pdf',

            'fas fa-file-powerpoint' => 'file-powerpoint',

            'far fa-file-powerpoint' => 'file-powerpoint',

            'fas fa-file-video' => 'file-video',

            'far fa-file-video' => 'file-video',

            'fas fa-file-word' => 'file-word',

            'far fa-file-word' => 'file-word',

            'fas fa-film' => 'film',

            'fas fa-filter' => 'filter',

            'fas fa-fire' => 'fire',

            'fas fa-fire-extinguisher' => 'fire-extinguisher',

            'fab fa-firefox' => 'firefox',

            'fas fa-first-aid' => 'first-aid',

            'fab fa-first-order' => 'first-order',

            'fab fa-firstdraft' => 'firstdraft',

            'fas fa-flag' => 'flag',

            'far fa-flag' => 'flag',

            'fas fa-flag-checkered' => 'flag-checkered',

            'fas fa-flask' => 'flask',

            'fab fa-flickr' => 'flickr',

            'fab fa-flipboard' => 'flipboard',

            'fab fa-fly' => 'fly',

            'fas fa-folder' => 'folder',

            'far fa-folder' => 'folder',

            'fas fa-folder-open' => 'folder-open',

            'far fa-folder-open' => 'folder-open',

            'fas fa-font' => 'font',

            'fab fa-font-awesome' => 'font-awesome',

            'fab fa-font-awesome-alt' => 'font-awesome-alt',

            'fab fa-font-awesome-flag' => 'font-awesome-flag',

            'fab fa-fonticons' => 'fonticons',

            'fab fa-fonticons-fi' => 'fonticons-fi',

            'fas fa-football-ball' => 'football-ball',

            'fab fa-fort-awesome' => 'fort-awesome',

            'fab fa-fort-awesome-alt' => 'fort-awesome-alt',

            'fab fa-forumbee' => 'forumbee',

            'fas fa-forward' => 'forward',

            'fab fa-foursquare' => 'foursquare',

            'fab fa-free-code-camp' => 'free-code-camp',

            'fab fa-freebsd' => 'freebsd',

            'fas fa-frown' => 'frown',

            'far fa-frown' => 'frown',

            'fas fa-futbol' => 'futbol',

            'far fa-futbol' => 'futbol',

            'fas fa-gamepad' => 'gamepad',

            'fas fa-gavel' => 'gavel',

            'fas fa-gem' => 'gem',

            'far fa-gem' => 'gem',

            'fas fa-genderless' => 'genderless',

            'fab fa-get-pocket' => 'get-pocket',

            'fab fa-gg' => 'gg',

            'fab fa-gg-circle' => 'gg-circle',

            'fas fa-gift' => 'gift',

            'fab fa-git' => 'git',

            'fab fa-git-square' => 'git-square',

            'fab fa-github' => 'github',

            'fab fa-github-alt' => 'github-alt',

            'fab fa-github-square' => 'github-square',

            'fab fa-gitkraken' => 'gitkraken',

            'fab fa-gitlab' => 'gitlab',

            'fab fa-gitter' => 'gitter',

            'fas fa-glass-martini' => 'glass-martini',

            'fab fa-glide' => 'glide',

            'fab fa-glide-g' => 'glide-g',

            'fas fa-globe' => 'globe',

            'fab fa-gofore' => 'gofore',

            'fas fa-golf-ball' => 'golf-ball',

            'fab fa-goodreads' => 'goodreads',

            'fab fa-goodreads-g' => 'goodreads-g',

            'fab fa-google' => 'google',

            'fab fa-google-drive' => 'google-drive',

            'fab fa-google-play' => 'google-play',

            'fab fa-google-plus' => 'google-plus',

            'fab fa-google-plus-g' => 'google-plus-g',

            'fab fa-google-plus-square' => 'google-plus-square',

            'fab fa-google-wallet' => 'google-wallet',

            'fas fa-graduation-cap' => 'graduation-cap',

            'fab fa-gratipay' => 'gratipay',

            'fab fa-grav' => 'grav',

            'fab fa-gripfire' => 'gripfire',

            'fab fa-grunt' => 'grunt',

            'fab fa-gulp' => 'gulp',

            'fas fa-h-square' => 'h-square',

            'fab fa-hacker-news' => 'hacker-news',

            'fab fa-hacker-news-square' => 'hacker-news-square',

            'fas fa-hand-holding' => 'hand-holding',

            'fas fa-hand-holding-heart' => 'hand-holding-heart',

            'fas fa-hand-holding-usd' => 'hand-holding-usd',

            'fas fa-hand-lizard' => 'hand-lizard',

            'far fa-hand-lizard' => 'hand-lizard',

            'fas fa-hand-paper' => 'hand-paper',

            'far fa-hand-paper' => 'hand-paper',

            'fas fa-hand-peace' => 'hand-peace',

            'far fa-hand-peace' => 'hand-peace',

            'fas fa-hand-point-down' => 'hand-point-down',

            'far fa-hand-point-down' => 'hand-point-down',

            'fas fa-hand-point-left' => 'hand-point-left',

            'far fa-hand-point-left' => 'hand-point-left',

            'fas fa-hand-point-right' => 'hand-point-right',

            'far fa-hand-point-right' => 'hand-point-right',

            'fas fa-hand-point-up' => 'hand-point-up',

            'far fa-hand-point-up' => 'hand-point-up',

            'fas fa-hand-pointer' => 'hand-pointer',

            'far fa-hand-pointer' => 'hand-pointer',

            'fas fa-hand-rock' => 'hand-rock',

            'far fa-hand-rock' => 'hand-rock',

            'fas fa-hand-scissors' => 'hand-scissors',

            'far fa-hand-scissors' => 'hand-scissors',

            'fas fa-hand-spock' => 'hand-spock',

            'far fa-hand-spock' => 'hand-spock',

            'fas fa-hands' => 'hands',

            'fas fa-hands-helping' => 'hands-helping',

            'fas fa-handshake' => 'handshake',

            'far fa-handshake' => 'handshake',

            'fas fa-hashtag' => 'hashtag',

            'fas fa-hdd' => 'hdd',

            'far fa-hdd' => 'hdd',

            'fas fa-heading' => 'heading',

            'fas fa-headphones' => 'headphones',

            'fas fa-heart' => 'heart',

            'far fa-heart' => 'heart',

            'fas fa-heartbeat' => 'heartbeat',

            'fab fa-hips' => 'hips',

            'fab fa-hire-a-helper' => 'hire-a-helper',

            'fas fa-history' => 'history',

            'fas fa-hockey-puck' => 'hockey-puck',

            'fas fa-home' => 'home',

            'fab fa-hooli' => 'hooli',

            'fas fa-hospital' => 'hospital',

            'far fa-hospital' => 'hospital',

            'fas fa-hospital-alt' => 'hospital-alt',

            'fas fa-hospital-symbol' => 'hospital-symbol',

            'fab fa-hotjar' => 'hotjar',

            'fas fa-hourglass' => 'hourglass',

            'far fa-hourglass' => 'hourglass',

            'fas fa-hourglass-end' => 'hourglass-end',

            'fas fa-hourglass-half' => 'hourglass-half',

            'fas fa-hourglass-start' => 'hourglass-start',

            'fab fa-houzz' => 'houzz',

            'fab fa-html5' => 'html5',

            'fab fa-hubspot' => 'hubspot',

            'fas fa-i-cursor' => 'i-cursor',

            'fas fa-id-badge' => 'id-badge',

            'far fa-id-badge' => 'id-badge',

            'fas fa-id-card' => 'id-card',

            'far fa-id-card' => 'id-card',

            'fas fa-id-card-alt' => 'id-card-alt',

            'fas fa-image' => 'image',

            'far fa-image' => 'image',

            'fas fa-images' => 'images',

            'far fa-images' => 'images',

            'fab fa-imdb' => 'imdb',

            'fas fa-inbox' => 'inbox',

            'fas fa-indent' => 'indent',

            'fas fa-industry' => 'industry',

            'fas fa-info' => 'info',

            'fas fa-info-circle' => 'info-circle',

            'fab fa-instagram' => 'instagram',

            'fab fa-internet-explorer' => 'internet-explorer',

            'fab fa-ioxhost' => 'ioxhost',

            'fas fa-italic' => 'italic',

            'fab fa-itunes' => 'itunes',

            'fab fa-itunes-note' => 'itunes-note',

            'fab fa-java' => 'java',

            'fab fa-jenkins' => 'jenkins',

            'fab fa-joget' => 'joget',

            'fab fa-joomla' => 'joomla',

            'fab fa-js' => 'js',

            'fab fa-js-square' => 'js-square',

            'fab fa-jsfiddle' => 'jsfiddle',

            'fas fa-key' => 'key',

            'fas fa-keyboard' => 'keyboard',

            'far fa-keyboard' => 'keyboard',

            'fab fa-keycdn' => 'keycdn',

            'fab fa-kickstarter' => 'kickstarter',

            'fab fa-kickstarter-k' => 'kickstarter-k',

            'fab fa-korvue' => 'korvue',

            'fas fa-language' => 'language',

            'fas fa-laptop' => 'laptop',

            'fab fa-laravel' => 'laravel',

            'fab fa-lastfm' => 'lastfm',

            'fab fa-lastfm-square' => 'lastfm-square',

            'fas fa-leaf' => 'leaf',

            'fab fa-leanpub' => 'leanpub',

            'fas fa-lemon' => 'lemon',

            'far fa-lemon' => 'lemon',

            'fab fa-less' => 'less',

            'fas fa-level-down-alt' => 'level-down-alt',

            'fas fa-level-up-alt' => 'level-up-alt',

            'fas fa-life-ring' => 'life-ring',

            'far fa-life-ring' => 'life-ring',

            'fas fa-lightbulb' => 'lightbulb',

            'far fa-lightbulb' => 'lightbulb',

            'fab fa-line' => 'line',

            'fas fa-link' => 'link',

            'fab fa-linkedin' => 'linkedin',

            'fab fa-linkedin-in' => 'linkedin-in',

            'fab fa-linode' => 'linode',

            'fab fa-linux' => 'linux',

            'fas fa-lira-sign' => 'lira-sign',

            'fas fa-list' => 'list',

            'fas fa-list-alt' => 'list-alt',

            'far fa-list-alt' => 'list-alt',

            'fas fa-list-ol' => 'list-ol',

            'fas fa-list-ul' => 'list-ul',

            'fas fa-location-arrow' => 'location-arrow',

            'fas fa-lock' => 'lock',

            'fas fa-lock-open' => 'lock-open',

            'fas fa-long-arrow-alt-down' => 'long-arrow-alt-down',

            'fas fa-long-arrow-alt-left' => 'long-arrow-alt-left',

            'fas fa-long-arrow-alt-right' => 'long-arrow-alt-right',

            'fas fa-long-arrow-alt-up' => 'long-arrow-alt-up',

            'fas fa-low-vision' => 'low-vision',

            'fab fa-lyft' => 'lyft',

            'fab fa-magento' => 'magento',

            'fas fa-magic' => 'magic',

            'fas fa-magnet' => 'magnet',

            'fas fa-male' => 'male',

            'fas fa-map' => 'map',

            'far fa-map' => 'map',

            'fas fa-map-marker' => 'map-marker',

            'fas fa-map-marker-alt' => 'map-marker-alt',

            'fas fa-map-pin' => 'map-pin',

            'fas fa-map-signs' => 'map-signs',

            'fas fa-mars' => 'mars',

            'fas fa-mars-double' => 'mars-double',

            'fas fa-mars-stroke' => 'mars-stroke',

            'fas fa-mars-stroke-h' => 'mars-stroke-h',

            'fas fa-mars-stroke-v' => 'mars-stroke-v',

            'fab fa-maxcdn' => 'maxcdn',

            'fab fa-medapps' => 'medapps',

            'fab fa-medium' => 'medium',

            'fab fa-medium-m' => 'medium-m',

            'fas fa-medkit' => 'medkit',

            'fab fa-medrt' => 'medrt',

            'fab fa-meetup' => 'meetup',

            'fas fa-meh' => 'meh',

            'far fa-meh' => 'meh',

            'fas fa-mercury' => 'mercury',

            'fas fa-microchip' => 'microchip',

            'fas fa-microphone' => 'microphone',

            'fas fa-microphone-slash' => 'microphone-slash',

            'fab fa-microsoft' => 'microsoft',

            'fas fa-minus' => 'minus',

            'fas fa-minus-circle' => 'minus-circle',

            'fas fa-minus-square' => 'minus-square',

            'far fa-minus-square' => 'minus-square',

            'fab fa-mix' => 'mix',

            'fab fa-mixcloud' => 'mixcloud',

            'fab fa-mizuni' => 'mizuni',

            'fas fa-mobile' => 'mobile',

            'fas fa-mobile-alt' => 'mobile-alt',

            'fab fa-modx' => 'modx',

            'fab fa-monero' => 'monero',

            'fas fa-money-bill-alt' => 'money-bill-alt',

            'far fa-money-bill-alt' => 'money-bill-alt',

            'fas fa-moon' => 'moon',

            'far fa-moon' => 'moon',

            'fas fa-motorcycle' => 'motorcycle',

            'fas fa-mouse-pointer' => 'mouse-pointer',

            'fas fa-music' => 'music',

            'fab fa-napster' => 'napster',

            'fas fa-neuter' => 'neuter',

            'fas fa-newspaper' => 'newspaper',

            'far fa-newspaper' => 'newspaper',

            'fab fa-nintendo-switch' => 'nintendo-switch',

            'fab fa-node' => 'node',

            'fab fa-node-js' => 'node-js',

            'fas fa-notes-medical' => 'notes-medical',

            'fab fa-npm' => 'npm',

            'fab fa-ns8' => 'ns8',

            'fab fa-nutritionix' => 'nutritionix',

            'fas fa-object-group' => 'object-group',

            'far fa-object-group' => 'object-group',

            'fas fa-object-ungroup' => 'object-ungroup',

            'far fa-object-ungroup' => 'object-ungroup',

            'fab fa-odnoklassniki' => 'odnoklassniki',

            'fab fa-odnoklassniki-square' => 'odnoklassniki-square',

            'fab fa-opencart' => 'opencart',

            'fab fa-openid' => 'openid',

            'fab fa-opera' => 'opera',

            'fab fa-optin-monster' => 'optin-monster',

            'fab fa-osi' => 'osi',

            'fas fa-outdent' => 'outdent',

            'fab fa-page4' => 'page4',

            'fab fa-pagelines' => 'pagelines',

            'fas fa-paint-brush' => 'paint-brush',

            'fab fa-palfed' => 'palfed',

            'fas fa-pallet' => 'pallet',

            'fas fa-paper-plane' => 'paper-plane',

            'far fa-paper-plane' => 'paper-plane',

            'fas fa-paperclip' => 'paperclip',

            'fas fa-parachute-box' => 'parachute-box',

            'fas fa-paragraph' => 'paragraph',

            'fas fa-paste' => 'paste',

            'fab fa-patreon' => 'patreon',

            'fas fa-pause' => 'pause',

            'fas fa-pause-circle' => 'pause-circle',

            'far fa-pause-circle' => 'pause-circle',

            'fas fa-paw' => 'paw',

            'fab fa-paypal' => 'paypal',

            'fas fa-pen-square' => 'pen-square',

            'fas fa-pencil-alt' => 'pencil-alt',

            'fas fa-people-carry' => 'people-carry',

            'fas fa-percent' => 'percent',

            'fab fa-periscope' => 'periscope',

            'fab fa-phabricator' => 'phabricator',

            'fab fa-phoenix-framework' => 'phoenix-framework',

            'fas fa-phone' => 'phone',

            'fas fa-phone-slash' => 'phone-slash',

            'fas fa-phone-square' => 'phone-square',

            'fas fa-phone-volume' => 'phone-volume',

            'fab fa-php' => 'php',

            'fab fa-pied-piper' => 'pied-piper',

            'fab fa-pied-piper-alt' => 'pied-piper-alt',

            'fab fa-pied-piper-hat' => 'pied-piper-hat',

            'fab fa-pied-piper-pp' => 'pied-piper-pp',

            'fas fa-piggy-bank' => 'piggy-bank',

            'fas fa-pills' => 'pills',

            'fab fa-pinterest' => 'pinterest',

            'fab fa-pinterest-p' => 'pinterest-p',

            'fab fa-pinterest-square' => 'pinterest-square',

            'fas fa-plane' => 'plane',

            'fas fa-play' => 'play',

            'fas fa-play-circle' => 'play-circle',

            'far fa-play-circle' => 'play-circle',

            'fab fa-playstation' => 'playstation',

            'fas fa-plug' => 'plug',

            'fas fa-plus' => 'plus',

            'fas fa-plus-circle' => 'plus-circle',

            'fas fa-plus-square' => 'plus-square',

            'far fa-plus-square' => 'plus-square',

            'fas fa-podcast' => 'podcast',

            'fas fa-poo' => 'poo',

            'fas fa-pound-sign' => 'pound-sign',

            'fas fa-power-off' => 'power-off',

            'fas fa-prescription-bottle' => 'prescription-bottle',

            'fas fa-prescription-bottle-alt' => 'prescription-bottle-alt',

            'fas fa-print' => 'print',

            'fas fa-procedures' => 'procedures',

            'fab fa-product-hunt' => 'product-hunt',

            'fab fa-pushed' => 'pushed',

            'fas fa-puzzle-piece' => 'puzzle-piece',

            'fab fa-python' => 'python',

            'fab fa-qq' => 'qq',

            'fas fa-qrcode' => 'qrcode',

            'fas fa-question' => 'question',

            'fas fa-question-circle' => 'question-circle',

            'far fa-question-circle' => 'question-circle',

            'fas fa-quidditch' => 'quidditch',

            'fab fa-quinscape' => 'quinscape',

            'fab fa-quora' => 'quora',

            'fas fa-quote-left' => 'quote-left',

            'fas fa-quote-right' => 'quote-right',

            'fas fa-random' => 'random',

            'fab fa-ravelry' => 'ravelry',

            'fab fa-react' => 'react',

            'fab fa-readme' => 'readme',

            'fab fa-rebel' => 'rebel',

            'fas fa-recycle' => 'recycle',

            'fab fa-red-river' => 'red-river',

            'fab fa-reddit' => 'reddit',

            'fab fa-reddit-alien' => 'reddit-alien',

            'fab fa-reddit-square' => 'reddit-square',

            'fas fa-redo' => 'redo',

            'fas fa-redo-alt' => 'redo-alt',

            'fas fa-registered' => 'registered',

            'far fa-registered' => 'registered',

            'fab fa-rendact' => 'rendact',

            'fab fa-renren' => 'renren',

            'fas fa-reply' => 'reply',

            'fas fa-reply-all' => 'reply-all',

            'fab fa-replyd' => 'replyd',

            'fab fa-resolving' => 'resolving',

            'fas fa-retweet' => 'retweet',

            'fas fa-ribbon' => 'ribbon',

            'fas fa-road' => 'road',

            'fas fa-rocket' => 'rocket',

            'fab fa-rocketchat' => 'rocketchat',

            'fab fa-rockrms' => 'rockrms',

            'fas fa-rss' => 'rss',

            'fas fa-rss-square' => 'rss-square',

            'fas fa-ruble-sign' => 'ruble-sign',

            'fas fa-rupee-sign' => 'rupee-sign',

            'fab fa-safari' => 'safari',

            'fab fa-sass' => 'sass',

            'fas fa-save' => 'save',

            'far fa-save' => 'save',

            'fab fa-schlix' => 'schlix',

            'fab fa-scribd' => 'scribd',

            'fas fa-search' => 'search',

            'fas fa-search-minus' => 'search-minus',

            'fas fa-search-plus' => 'search-plus',

            'fab fa-searchengin' => 'searchengin',

            'fas fa-seedling' => 'seedling',

            'fab fa-sellcast' => 'sellcast',

            'fab fa-sellsy' => 'sellsy',

            'fas fa-server' => 'server',

            'fab fa-servicestack' => 'servicestack',

            'fas fa-share' => 'share',

            'fas fa-share-alt' => 'share-alt',

            'fas fa-share-alt-square' => 'share-alt-square',

            'fas fa-share-square' => 'share-square',

            'far fa-share-square' => 'share-square',

            'fas fa-shekel-sign' => 'shekel-sign',

            'fas fa-shield-alt' => 'shield-alt',

            'fas fa-ship' => 'ship',

            'fas fa-shipping-fast' => 'shipping-fast',

            'fab fa-shirtsinbulk' => 'shirtsinbulk',

            'fas fa-shopping-bag' => 'shopping-bag',

            'fas fa-shopping-basket' => 'shopping-basket',

            'fas fa-shopping-cart' => 'shopping-cart',

            'fas fa-shower' => 'shower',

            'fas fa-sign' => 'sign',

            'fas fa-sign-in-alt' => 'sign-in-alt',

            'fas fa-sign-language' => 'sign-language',

            'fas fa-sign-out-alt' => 'sign-out-alt',

            'fas fa-signal' => 'signal',

            'fab fa-simplybuilt' => 'simplybuilt',

            'fab fa-sistrix' => 'sistrix',

            'fas fa-sitemap' => 'sitemap',

            'fab fa-skyatlas' => 'skyatlas',

            'fab fa-skype' => 'skype',

            'fab fa-slack' => 'slack',

            'fab fa-slack-hash' => 'slack-hash',

            'fas fa-sliders-h' => 'sliders-h',

            'fab fa-slideshare' => 'slideshare',

            'fas fa-smile' => 'smile',

            'far fa-smile' => 'smile',

            'fas fa-smoking' => 'smoking',

            'fab fa-snapchat' => 'snapchat',

            'fab fa-snapchat-ghost' => 'snapchat-ghost',

            'fab fa-snapchat-square' => 'snapchat-square',

            'fas fa-snowflake' => 'snowflake',

            'far fa-snowflake' => 'snowflake',

            'fas fa-sort' => 'sort',

            'fas fa-sort-alpha-down' => 'sort-alpha-down',

            'fas fa-sort-alpha-up' => 'sort-alpha-up',

            'fas fa-sort-amount-down' => 'sort-amount-down',

            'fas fa-sort-amount-up' => 'sort-amount-up',

            'fas fa-sort-down' => 'sort-down',

            'fas fa-sort-numeric-down' => 'sort-numeric-down',

            'fas fa-sort-numeric-up' => 'sort-numeric-up',

            'fas fa-sort-up' => 'sort-up',

            'fab fa-soundcloud' => 'soundcloud',

            'fas fa-space-shuttle' => 'space-shuttle',

            'fab fa-speakap' => 'speakap',

            'fas fa-spinner' => 'spinner',

            'fab fa-spotify' => 'spotify',

            'fas fa-square' => 'square',

            'far fa-square' => 'square',

            'fas fa-square-full' => 'square-full',

            'fab fa-stack-exchange' => 'stack-exchange',

            'fab fa-stack-overflow' => 'stack-overflow',

            'fas fa-star' => 'star',

            'far fa-star' => 'star',

            'fas fa-star-half' => 'star-half',

            'far fa-star-half' => 'star-half',

            'fab fa-staylinked' => 'staylinked',

            'fab fa-steam' => 'steam',

            'fab fa-steam-square' => 'steam-square',

            'fab fa-steam-symbol' => 'steam-symbol',

            'fas fa-step-backward' => 'step-backward',

            'fas fa-step-forward' => 'step-forward',

            'fas fa-stethoscope' => 'stethoscope',

            'fab fa-sticker-mule' => 'sticker-mule',

            'fas fa-sticky-note' => 'sticky-note',

            'far fa-sticky-note' => 'sticky-note',

            'fas fa-stop' => 'stop',

            'fas fa-stop-circle' => 'stop-circle',

            'far fa-stop-circle' => 'stop-circle',

            'fas fa-stopwatch' => 'stopwatch',

            'fab fa-strava' => 'strava',

            'fas fa-street-view' => 'street-view',

            'fas fa-strikethrough' => 'strikethrough',

            'fab fa-stripe' => 'stripe',

            'fab fa-stripe-s' => 'stripe-s',

            'fab fa-studiovinari' => 'studiovinari',

            'fab fa-stumbleupon' => 'stumbleupon',

            'fab fa-stumbleupon-circle' => 'stumbleupon-circle',

            'fas fa-subscript' => 'subscript',

            'fas fa-subway' => 'subway',

            'fas fa-suitcase' => 'suitcase',

            'fas fa-sun' => 'sun',

            'far fa-sun' => 'sun',

            'fab fa-superpowers' => 'superpowers',

            'fas fa-superscript' => 'superscript',

            'fab fa-supple' => 'supple',

            'fas fa-sync' => 'sync',

            'fas fa-sync-alt' => 'sync-alt',

            'fas fa-syringe' => 'syringe',

            'fas fa-table' => 'table',

            'fas fa-table-tennis' => 'table-tennis',

            'fas fa-tablet' => 'tablet',

            'fas fa-tablet-alt' => 'tablet-alt',

            'fas fa-tablets' => 'tablets',

            'fas fa-tachometer-alt' => 'tachometer-alt',

            'fas fa-tag' => 'tag',

            'fas fa-tags' => 'tags',

            'fas fa-tape' => 'tape',

            'fas fa-tasks' => 'tasks',

            'fas fa-taxi' => 'taxi',

            'fab fa-telegram' => 'telegram',

            'fab fa-telegram-plane' => 'telegram-plane',

            'fab fa-tencent-weibo' => 'tencent-weibo',

            'fas fa-terminal' => 'terminal',

            'fas fa-text-height' => 'text-height',

            'fas fa-text-width' => 'text-width',

            'fas fa-th' => 'th',

            'fas fa-th-large' => 'th-large',

            'fas fa-th-list' => 'th-list',

            'fab fa-themeisle' => 'themeisle',

            'fas fa-thermometer' => 'thermometer',

            'fas fa-thermometer-empty' => 'thermometer-empty',

            'fas fa-thermometer-full' => 'thermometer-full',

            'fas fa-thermometer-half' => 'thermometer-half',

            'fas fa-thermometer-quarter' => 'thermometer-quarter',

            'fas fa-thermometer-three-quarters' => 'thermometer-three-quarters',

            'fas fa-thumbs-down' => 'thumbs-down',

            'far fa-thumbs-down' => 'thumbs-down',

            'fas fa-thumbs-up' => 'thumbs-up',

            'far fa-thumbs-up' => 'thumbs-up',

            'fas fa-thumbtack' => 'thumbtack',

            'fas fa-ticket-alt' => 'ticket-alt',

            'fas fa-times' => 'times',

            'fas fa-times-circle' => 'times-circle',

            'far fa-times-circle' => 'times-circle',

            'fas fa-tint' => 'tint',

            'fas fa-toggle-off' => 'toggle-off',

            'fas fa-toggle-on' => 'toggle-on',

            'fas fa-trademark' => 'trademark',

            'fas fa-train' => 'train',

            'fas fa-transgender' => 'transgender',

            'fas fa-transgender-alt' => 'transgender-alt',

            'fas fa-trash' => 'trash',

            'fas fa-trash-alt' => 'trash-alt',

            'far fa-trash-alt' => 'trash-alt',

            'fas fa-tree' => 'tree',

            'fab fa-trello' => 'trello',

            'fab fa-tripadvisor' => 'tripadvisor',

            'fas fa-trophy' => 'trophy',

            'fas fa-truck' => 'truck',

            'fas fa-truck-loading' => 'truck-loading',

            'fas fa-truck-moving' => 'truck-moving',

            'fas fa-tty' => 'tty',

            'fab fa-tumblr' => 'tumblr',

            'fab fa-tumblr-square' => 'tumblr-square',

            'fas fa-tv' => 'tv',

            'fab fa-twitch' => 'twitch',

            'fab fa-twitter' => 'twitter',

            'fab fa-twitter-square' => 'twitter-square',

            'fab fa-typo3' => 'typo3',

            'fab fa-uber' => 'uber',

            'fab fa-uikit' => 'uikit',

            'fas fa-umbrella' => 'umbrella',

            'fas fa-underline' => 'underline',

            'fas fa-undo' => 'undo',

            'fas fa-undo-alt' => 'undo-alt',

            'fab fa-uniregistry' => 'uniregistry',

            'fas fa-universal-access' => 'universal-access',

            'fas fa-university' => 'university',

            'fas fa-unlink' => 'unlink',

            'fas fa-unlock' => 'unlock',

            'fas fa-unlock-alt' => 'unlock-alt',

            'fab fa-untappd' => 'untappd',

            'fas fa-upload' => 'upload',

            'fab fa-usb' => 'usb',

            'fas fa-user' => 'user',

            'far fa-user' => 'user',

            'fas fa-user-circle' => 'user-circle',

            'far fa-user-circle' => 'user-circle',

            'fas fa-user-md' => 'user-md',

            'fas fa-user-plus' => 'user-plus',

            'fas fa-user-secret' => 'user-secret',

            'fas fa-user-times' => 'user-times',

            'fas fa-users' => 'users',

            'fab fa-ussunnah' => 'ussunnah',

            'fas fa-utensil-spoon' => 'utensil-spoon',

            'fas fa-utensils' => 'utensils',

            'fab fa-vaadin' => 'vaadin',

            'fas fa-venus' => 'venus',

            'fas fa-venus-double' => 'venus-double',

            'fas fa-venus-mars' => 'venus-mars',

            'fab fa-viacoin' => 'viacoin',

            'fab fa-viadeo' => 'viadeo',

            'fab fa-viadeo-square' => 'viadeo-square',

            'fas fa-vial' => 'vial',

            'fas fa-vials' => 'vials',

            'fab fa-viber' => 'viber',

            'fas fa-video' => 'video',

            'fas fa-video-slash' => 'video-slash',

            'fab fa-vimeo' => 'vimeo',

            'fab fa-vimeo-square' => 'vimeo-square',

            'fab fa-vimeo-v' => 'vimeo-v',

            'fab fa-vine' => 'vine',

            'fab fa-vk' => 'vk',

            'fab fa-vnv' => 'vnv',

            'fas fa-volleyball-ball' => 'volleyball-ball',

            'fas fa-volume-down' => 'volume-down',

            'fas fa-volume-off' => 'volume-off',

            'fas fa-volume-up' => 'volume-up',

            'fab fa-vuejs' => 'vuejs',

            'fas fa-warehouse' => 'warehouse',

            'fab fa-weibo' => 'weibo',

            'fas fa-weight' => 'weight',

            'fab fa-weixin' => 'weixin',

            'fab fa-whatsapp' => 'whatsapp',

            'fab fa-whatsapp-square' => 'whatsapp-square',

            'fas fa-wheelchair' => 'wheelchair',

            'fab fa-whmcs' => 'whmcs',

            'fas fa-wifi' => 'wifi',

            'fab fa-wikipedia-w' => 'wikipedia-w',

            'fas fa-window-close' => 'window-close',

            'far fa-window-close' => 'window-close',

            'fas fa-window-maximize' => 'window-maximize',

            'far fa-window-maximize' => 'window-maximize',

            'fas fa-window-minimize' => 'window-minimize',

            'far fa-window-minimize' => 'window-minimize',

            'fas fa-window-restore' => 'window-restore',

            'far fa-window-restore' => 'window-restore',

            'fab fa-windows' => 'windows',

            'fas fa-wine-glass' => 'wine-glass',

            'fas fa-won-sign' => 'won-sign',

            'fab fa-wordpress' => 'wordpress',

            'fab fa-wordpress-simple' => 'wordpress-simple',

            'fab fa-wpbeginner' => 'wpbeginner',

            'fab fa-wpexplorer' => 'wpexplorer',

            'fab fa-wpforms' => 'wpforms',

            'fas fa-wrench' => 'wrench',

            'fas fa-x-ray' => 'x-ray',

            'fab fa-xbox' => 'xbox',

            'fab fa-xing' => 'xing',

            'fab fa-xing-square' => 'xing-square',

            'fab fa-y-combinator' => 'y-combinator',

            'fab fa-yahoo' => 'yahoo',

            'fab fa-yandex' => 'yandex',

            'fab fa-yandex-international' => 'yandex-international',

            'fab fa-yelp' => 'yelp',

            'fas fa-yen-sign' => 'yen-sign',

            'fab fa-yoast' => 'yoast',

            'fab fa-youtube' => 'youtube',

            'fab fa-youtube-square' => 'youtube-square',

        );

        return $fa_icons;

    }



    public function formatDate($date)

    {

        if ($date) {

            $date = explode('/', $date);

            $date = $date[2] . '-' . $date[0] . '-' . $date[1];

        }

        return $date ?? null;

    }



    public function undoFormatDate($date)

    {

        if ($date) {

            $date_time = explode(' ', $date);

            $date = explode('-', $date_time[0]);

            $date = $date[1] . '/' . $date[2] . '/' . $date[0];

        }

        return $date ?? null;

    }



    public function getClientIpAddress()

    {

        if (isset($_SERVER["HTTP_CF_CONNECTING_IP"])) {

            $_SERVER['REMOTE_ADDR'] = $_SERVER["HTTP_CF_CONNECTING_IP"];

            $_SERVER['HTTP_CLIENT_IP'] = $_SERVER["HTTP_CF_CONNECTING_IP"];

        }

        $client  = @$_SERVER['HTTP_CLIENT_IP'];

        $forward = @$_SERVER['HTTP_X_FORWARDED_FOR'];

        $remote  = $_SERVER['REMOTE_ADDR'] ?? null;



        if (filter_var($client, FILTER_VALIDATE_IP)) {

            $clientIp = $client;

        } elseif (filter_var($forward, FILTER_VALIDATE_IP)) {

            $clientIp = $forward;

        } else {

            $clientIp = $remote;

        }



        return $clientIp;

    }



    public function countries($option = null)

    {

        $options = [

            "BD" => "Bangladesh",

            "BE" => "Belgium",

            "BF" => "Burkina Faso",

            "BG" => "Bulgaria",

            "BA" => "Bosnia and Herzegovina",

            "BB" => "Barbados",

            "WF" => "Wallis and Futuna",

            "BL" => "Saint Barthelemy",

            "BM" => "Bermuda",

            "BN" => "Brunei",

            "BO" => "Bolivia",

            "BH" => "Bahrain",

            "BI" => "Burundi",

            "BJ" => "Benin",

            "BT" => "Bhutan",

            "JM" => "Jamaica",

            "BV" => "Bouvet Island",

            "BW" => "Botswana",

            "WS" => "Samoa",

            "BQ" => "Bonaire, Saint Eustatius and Saba ",

            "BR" => "Brazil",

            "BS" => "Bahamas",

            "JE" => "Jersey",

            "BY" => "Belarus",

            "BZ" => "Belize",

            "RU" => "Russia",

            "RW" => "Rwanda",

            "RS" => "Serbia",

            "TL" => "East Timor",

            "RE" => "Reunion",

            "TM" => "Turkmenistan",

            "TJ" => "Tajikistan",

            "RO" => "Romania",

            "TK" => "Tokelau",

            "GW" => "Guinea-Bissau",

            "GU" => "Guam",

            "GT" => "Guatemala",

            "GS" => "South Georgia and the South Sandwich Islands",

            "GR" => "Greece",

            "GQ" => "Equatorial Guinea",

            "GP" => "Guadeloupe",

            "JP" => "Japan",

            "GY" => "Guyana",

            "GG" => "Guernsey",

            "GF" => "French Guiana",

            "GE" => "Georgia",

            "GD" => "Grenada",

            "GB" => "United Kingdom",

            "GA" => "Gabon",

            "SV" => "El Salvador",

            "GN" => "Guinea",

            "GM" => "Gambia",

            "GL" => "Greenland",

            "GI" => "Gibraltar",

            "GH" => "Ghana",

            "OM" => "Oman",

            "TN" => "Tunisia",

            "JO" => "Jordan",

            "HR" => "Croatia",

            "HT" => "Haiti",

            "HU" => "Hungary",

            "HK" => "Hong Kong",

            "HN" => "Honduras",

            "HM" => "Heard Island and McDonald Islands",

            "VE" => "Venezuela",

            "PR" => "Puerto Rico",

            "PS" => "Palestinian Territory",

            "PW" => "Palau",

            "PT" => "Portugal",

            "SJ" => "Svalbard and Jan Mayen",

            "PY" => "Paraguay",

            "IQ" => "Iraq",

            "PA" => "Panama",

            "PF" => "French Polynesia",

            "PG" => "Papua New Guinea",

            "PE" => "Peru",

            "PK" => "Pakistan",

            "PH" => "Philippines",

            "PN" => "Pitcairn",

            "PL" => "Poland",

            "PM" => "Saint Pierre and Miquelon",

            "ZM" => "Zambia",

            "EH" => "Western Sahara",

            "EE" => "Estonia",

            "EG" => "Egypt",

            "ZA" => "South Africa",

            "EC" => "Ecuador",

            "IT" => "Italy",

            "VN" => "Vietnam",

            "SB" => "Solomon Islands",

            "ET" => "Ethiopia",

            "SO" => "Somalia",

            "ZW" => "Zimbabwe",

            "SA" => "Saudi Arabia",

            "ES" => "Spain",

            "ER" => "Eritrea",

            "ME" => "Montenegro",

            "MD" => "Moldova",

            "MG" => "Madagascar",

            "MF" => "Saint Martin",

            "MA" => "Morocco",

            "MC" => "Monaco",

            "UZ" => "Uzbekistan",

            "MM" => "Myanmar",

            "ML" => "Mali",

            "MO" => "Macao",

            "MN" => "Mongolia",

            "MH" => "Marshall Islands",

            "MK" => "Macedonia",

            "MU" => "Mauritius",

            "MT" => "Malta",

            "MW" => "Malawi",

            "MV" => "Maldives",

            "MQ" => "Martinique",

            "MP" => "Northern Mariana Islands",

            "MS" => "Montserrat",

            "MR" => "Mauritania",

            "IM" => "Isle of Man",

            "UG" => "Uganda",

            "TZ" => "Tanzania",

            "MY" => "Malaysia",

            "MX" => "Mexico",

            "IL" => "Israel",

            "FR" => "France",

            "IO" => "British Indian Ocean Territory",

            "SH" => "Saint Helena",

            "FI" => "Finland",

            "FJ" => "Fiji",

            "FK" => "Falkland Islands",

            "FM" => "Micronesia",

            "FO" => "Faroe Islands",

            "NI" => "Nicaragua",

            "NL" => "Netherlands",

            "NO" => "Norway",

            "NA" => "Namibia",

            "VU" => "Vanuatu",

            "NC" => "New Caledonia",

            "NE" => "Niger",

            "NF" => "Norfolk Island",

            "NG" => "Nigeria",

            "NZ" => "New Zealand",

            "NP" => "Nepal",

            "NR" => "Nauru",

            "NU" => "Niue",

            "CK" => "Cook Islands",

            "XK" => "Kosovo",

            "CI" => "Ivory Coast",

            "CH" => "Switzerland",

            "CO" => "Colombia",

            "CN" => "China",

            "CM" => "Cameroon",

            "CL" => "Chile",

            "CC" => "Cocos Islands",

            "CA" => "Canada",

            "CG" => "Republic of the Congo",

            "CF" => "Central African Republic",

            "CD" => "Democratic Republic of the Congo",

            "CZ" => "Czech Republic",

            "CY" => "Cyprus",

            "CX" => "Christmas Island",

            "CR" => "Costa Rica",

            "CW" => "Curacao",

            "CV" => "Cape Verde",

            "CU" => "Cuba",

            "SZ" => "Swaziland",

            "SY" => "Syria",

            "SX" => "Sint Maarten",

            "KG" => "Kyrgyzstan",

            "KE" => "Kenya",

            "SS" => "South Sudan",

            "SR" => "Suriname",

            "KI" => "Kiribati",

            "KH" => "Cambodia",

            "KN" => "Saint Kitts and Nevis",

            "KM" => "Comoros",

            "ST" => "Sao Tome and Principe",

            "SK" => "Slovakia",

            "KR" => "South Korea",

            "SI" => "Slovenia",

            "KP" => "North Korea",

            "KW" => "Kuwait",

            "SN" => "Senegal",

            "SM" => "San Marino",

            "SL" => "Sierra Leone",

            "SC" => "Seychelles",

            "KZ" => "Kazakhstan",

            "KY" => "Cayman Islands",

            "SG" => "Singapore",

            "SE" => "Sweden",

            "SD" => "Sudan",

            "DO" => "Dominican Republic",

            "DM" => "Dominica",

            "DJ" => "Djibouti",

            "DK" => "Denmark",

            "VG" => "British Virgin Islands",

            "DE" => "Germany",

            "YE" => "Yemen",

            "DZ" => "Algeria",

            "US" => "United States",

            "UY" => "Uruguay",

            "YT" => "Mayotte",

            "UM" => "United States Minor Outlying Islands",

            "LB" => "Lebanon",

            "LC" => "Saint Lucia",

            "LA" => "Laos",

            "TV" => "Tuvalu",

            "TW" => "Taiwan",

            "TT" => "Trinidad and Tobago",

            "TR" => "Turkey",

            "LK" => "Sri Lanka",

            "LI" => "Liechtenstein",

            "LV" => "Latvia",

            "TO" => "Tonga",

            "LT" => "Lithuania",

            "LU" => "Luxembourg",

            "LR" => "Liberia",

            "LS" => "Lesotho",

            "TH" => "Thailand",

            "TF" => "French Southern Territories",

            "TG" => "Togo",

            "TD" => "Chad",

            "TC" => "Turks and Caicos Islands",

            "LY" => "Libya",

            "VA" => "Vatican",

            "VC" => "Saint Vincent and the Grenadines",

            "AE" => "United Arab Emirates",

            "AD" => "Andorra",

            "AG" => "Antigua and Barbuda",

            "AF" => "Afghanistan",

            "AI" => "Anguilla",

            "VI" => "U.S. Virgin Islands",

            "IS" => "Iceland",

            "IR" => "Iran",

            "AM" => "Armenia",

            "AL" => "Albania",

            "AO" => "Angola",

            "AS" => "American Samoa",

            "AR" => "Argentina",

            "AU" => "Australia",

            "AT" => "Austria",

            "AW" => "Aruba",

            "IN" => "India",

            "AX" => "Aland Islands",

            "AZ" => "Azerbaijan",

            "IE" => "Ireland",

            "ID" => "Indonesia",

            "UA" => "Ukraine",

            "QA" => "Qatar",

            "MZ" => "Mozambique",

        ];

        // $client = new Client();

        // $response = $client->get('https://restcountries.com/v3.1/all');

        // $options = json_decode($response->getBody(), true);



        foreach ($options as $key => $value) {

            if ($option == $key) {

                return $value;

            }

        }

        return $options;

    }



    public function timezones()

    {

        static $regions = array(

            \DateTimeZone::AFRICA,

            \DateTimeZone::AMERICA,

            \DateTimeZone::ANTARCTICA,

            \DateTimeZone::ASIA,

            \DateTimeZone::ATLANTIC,

            \DateTimeZone::AUSTRALIA,

            \DateTimeZone::EUROPE,

            \DateTimeZone::INDIAN,

            \DateTimeZone::PACIFIC,

        );



        $timezones = array();

        foreach ($regions as $region) {

            $timezones = array_merge($timezones, \DateTimeZone::listIdentifiers($region));

        }



        $timezone_offsets = array();

        foreach ($timezones as $timezone) {

            $tz = new \DateTimeZone($timezone);

            $timezone_offsets[$timezone] = $tz->getOffset(new \DateTime);

        }



        // sort timezone by offset

        asort($timezone_offsets);



        $timezone_list = array();

        foreach ($timezone_offsets as $timezone => $offset) {

            $offset_prefix = $offset < 0 ? '-' : '+';

            $offset_formatted = gmdate('H:i', abs($offset));



            $pretty_offset = 'UTC ' . $offset_prefix . $offset_formatted;



            $timezone_list[$timezone] = '(' . $pretty_offset . ') ' . $timezone;

        }



        return $timezone_list;

    }



    public function sendMessage($message, $recipients)

    {

        $account_sid = config('app.twilio_sid');

        $auth_token = config('app.twilio_auth_token');

        $twilio_number = config('app.twilio_number');



        try {

            $client = new Client($account_sid, $auth_token);

            // Log the recipient to debug

            \Log::info("Sending message to: " . $recipients);



            $response = $client->messages->create(

                $recipients,

                [

                    'from' => $twilio_number,

                    'body' => $message,

                ]

            );



            \Log::info("Message sent successfully to: " . $recipients);

            return $response; // Return the response if needed

        } catch (\Exception $e) {

            // Use a single `$` for the variable

            \Log::error("Error sending message to: " . $recipients . " with error: " . $e->getMessage());

        }

    }



    // public function getLocalCurrencyRate($country = null){

    //     $currency_rate = 0;



    //     if($country == 'NG'){

    //         //This is Nigeria

    //         $urlScrape = "https://abokiforex.app/_ah-dt1.json";

    //         // $urlScrape = "https://abokiforex.app/_ah-myrates.json";

    //         $urlScrape1 = "https://www.ngnrates.com/market/exchange-rates/us-dollar-to-naira/";

    //         $httpClient = new \GuzzleHttp\Client();

    //         $response = $httpClient->get($urlScrape);

    //         $response1 = $httpClient->get($urlScrape1);

    //         $htmlArray = json_decode((string) $response->getBody(), TRUE);

    //         $urlRate = intval($htmlArray['value2']);

    //         $htmlString1 = (string) $response1->getBody();

    //         libxml_use_internal_errors(true);

    //         $doc1 = new \DOMDocument();

    //         $doc1->loadHTML($htmlString1);

    //         $xpath1 = new \DOMXPath($doc1);

    //         $exchangeRatesUrl1 = $xpath1->evaluate("*//table[contains(@class,'table') and contains(@class,'table-condensed')

    //                                 and contains(@class,'table-striped')][1]//tbody//tr//td[2]");

    //         $exchangeRatesUrl1Dates = $xpath1->evaluate("*//table[contains(@class,'table') and contains(@class,'table-condensed')

    //                                 and contains(@class,'table-striped')][1]//tbody//tr//td[3]");

    //         $url1Rate = 0;

    //         foreach ($exchangeRatesUrl1 as $key => $rate) {

    //             $rateStr = (string)str_replace(["\n", "\r"], '', explode(" ", $rate->textContent . PHP_EOL)[1]);

    //             $rateDate = (string)str_replace(["\n", "\r"], '', $exchangeRatesUrl1Dates[$key]->textContent);

    //             if (Carbon::hasFormat($rateDate, 'd/m/Y')) {

    //                 $date = Carbon::createFromFormat('d/m/Y', $rateDate)->format('d/m/Y');

    //                 if (intval($rateStr) > $url1Rate && $date == Carbon::now()->format('d/m/Y')) {

    //                     $url1Rate = intval($rateStr);

    //                 }

    //             }

    //         }

    //         $currency_rate = ($urlRate > $url1Rate) ? $urlRate : $url1Rate;

    //     }

    //     // dd($currency_rate);

    //     return $currency_rate;

    // }



    public function getLocalCurrencyRate($country = null)

    {

        $currency_rate = 0;

        $currencyCode = $this->getLocalCurrencyCode($country);

        $site_data = \Session::get('shop_logged_data');

        $defaultCurrency = getDefaultCurrencyCode();

        $httpClient = new \GuzzleHttp\Client();

        if ($defaultCurrency == $currencyCode) {

            return 0;

        }

        // Primary source: Wise API

        try {

            $wiseToken = 'db41957a-248a-4e43-8e44-a0e52aeb4ff0';

            $wiseUrl = "https://api.transferwise.com/v1/rates?source={$defaultCurrency}&target={$currencyCode}";



            $response = $httpClient->get($wiseUrl, [

                'headers' => [

                    'Authorization' => "Bearer {$wiseToken}",

                    'Accept'        => 'application/json',

                ]

            ]);

            $wiseData = json_decode((string) $response->getBody(), true);



            if (isset($wiseData[0]['rate'])) {

                $currency_rate = floatval($wiseData[0]['rate']);

            }

        } catch (\Exception $e) {

            \Log::error('Wise API Error: ' . $e->getMessage());

        }



        // Fallback: CurrencyBeacon if Wise fails

        if ($currency_rate == 0) {

            try {

                $apiKey = '30DSMPbc0wG1wFGTQixWFNHxUmcmlxKI';

                $url = "https://api.currencybeacon.com/v1/latest?api_key={$apiKey}&base={$defaultCurrency}&symbols={$currencyCode}";



                $response = $httpClient->get($url);

                $data = json_decode((string) $response->getBody(), true);



                if (isset($data['rates'][$currencyCode])) {

                    $currency_rate = floatval($data['rates'][$currencyCode]);

                }

            } catch (\Exception $e) {

                \Log::error('CurrencyBeacon API Error: ' . $e->getMessage());

            }

        }

        $rate = $currency_rate;



        $from = $defaultCurrency;

        $to = $currencyCode;



        $adjustment = \App\Models\CurrencyAdjustment::where('from_currency', $from)

            ->where('to_currency', $to)

            ->value('adjustment_percent');



        if (!is_null($adjustment)) {

            $rate = $rate + ($rate * ($adjustment / 100));

        }



        return $rate;

    }

    function getLatestCurrencyAdjustmentTime($country = null)

    {

        $currency_rate = 0;

        $toCurrency = $this->getLocalCurrencyCode($country);

        $site_data = \Session::get('shop_logged_data');

        $fromCurrency = getDefaultCurrencyCode();

        $httpClient = new \GuzzleHttp\Client();

        if ($fromCurrency == $toCurrency) {

                return Carbon::now();

        }

        $query = \App\Models\CurrencyAdjustment::query();



        if ($fromCurrency) {

            $query->where('from_currency', $fromCurrency);

        }



        if ($toCurrency) {

            $query->where('to_currency', $toCurrency);

        }



        return $query->latest('updated_at')->value('updated_at');

    }

    public function readJson($path)

    {

        if (File::exists($path)) {

            $json = File::get($path);

            $data = json_decode($json, true);

            return collect($data);

        } else {

            return response()->json(['error' => 'File not found.'], 404);

        }

    }

    public function getCurrencySymbol($currency = null)

    {

        $countries = $this->readJson(storage_path('countries.json'));

        foreach ($countries as $value) {

            // dd($value['currency']['code']);

            if ($value['currency']['code'] == $currency) {

                return $value['currency']['symbol'];

            }

        }

    }



    public function getLocalCurrencySymbol($country = null)

    {



        // $client = new \GuzzleHttp\Client();

        // $response = $client->get('https://restcountries.com/v3.1/all');

        // $countries = json_decode($response->getBody(), true);

        $countries = $this->readJson(storage_path('countries.json'));

        foreach ($countries as $value) {

            if ($value['code'] == $country) {

                // if ($value['cca2'] == $country) {

                return $value['currency']['symbol'];

                // return $value['currencies'][key($value['currencies'])]['symbol'];

            }

        }

    }



    public function getLocalCurrencyCode($country = null)

    {

        // $client = new \GuzzleHttp\Client();

        // $response = $client->get('https://restcountries.com/v3.1/all');

        // $countries = json_decode($response->getBody(), true);

        $countries = $this->readJson(storage_path('countries.json'));

        foreach ($countries as $value) {

            if ($value['code'] == $country) {

                // if($value['cca2'] == $country){

                // foreach($value['currencies'] as $key => $currency){

                //     return $key;

                // }

                return $value['currency']['code'];

            }

        }

    }



    public function getLocalCountryCode($country = null,)

    {

        $countries = $this->readJson(storage_path('countries.json'));

        foreach ($countries as $value) {

            if ($value['code'] == $country) {

                return $value['currency']['code'];

            }

        }

        // dd('getLocalCountryCode');

        // $client = new \GuzzleHttp\Client();

        // $apiUrl = "https://restcountries.com/v3/alpha/$country";

        // $response = $client->get($apiUrl)->getBody();

        // if ($response === FALSE) {

        //     die('Error occurred while fetching data.');

        // }

        // $data = json_decode($response, true);

        // if (isset($data['status']) && $data['status'] === 404) {

        //     die('Country not found.');

        // }

        // $data = is_array($data) ? reset($data) : null;



        // return $data['idd']['root'].$data['idd']['suffixes'][0];

    }



    public function getLocalCountryShortCode($country = null,)

    {

        $countries = $this->readJson(storage_path('countries.json'));

        foreach ($countries as $value) {

            if ($value['code'] == $country) {

                return $value['currency']['code'];

            }

        }

        // dd('getLocalCountryShortCode');

        // $client = new \GuzzleHttp\Client();

        // $apiUrl = "https://restcountries.com/v3/alpha/$country";

        // $response = $client->get($apiUrl)->getBody();

        // if ($response === FALSE) {

        //     die('Error occurred while fetching data.');

        // }

        // $data = json_decode($response, true);

        // if (isset($data['status']) && $data['status'] === 404) {

        //     die('Country not found.');

        // }

        // $data = is_array($data) ? reset($data) : null;

        // foreach($data['currencies'] as $key =>$value){

        //     return $key;

        // }

    }



    public function getLocalCurrencyName($country = null)

    {

        // $client = new \GuzzleHttp\Client();

        // $response = $client->get('https://restcountries.com/v3.1/all');

        // $countries = json_decode($response->getBody(), true);

        $countries = $this->readJson(storage_path('countries.json'));

        foreach ($countries as $value) {

            if ($value['code'] == $country) {

                // if ($value['cca2'] == $country) {

                // foreach ($value['currencies'] as $currency) {

                //     return $currency['name'];

                // }

                return $value['currency']['name'];

            }

        }

    }



    public function transactionMethod($option)

    {

        $transaction_method = TransactionMethod::where('method_key', $option)->first();



        return $transaction_method;

    }



    public function stores()

    {

        $items = Setting::where('parent_store_id', null)

            ->with('childStores')

            ->orderBy('ordering')

            ->get();



        foreach ($items as $key => $value) {

            $value->index_no = ++$key;

            $value->details = $value->site_name . '<br />' . $value->email . ' ' . $value->tel_cell;

            // $value->details = $value->site_name . '<br />' . $value->email . ' ' . $value->tel_cell_country_code . $value->tel_cell;

            if (count($value->childStores) > 0) {

                foreach ($value->childStores as $key => $child_value) {

                    $child_value->index_no = $value->index_no . '.' . ++$key;

                    $child_value->details = $child_value->site_name . '<br />' . $child_value->email . ' ' . $child_value->tel_cell;

                    // $child_value->details = $child_value->site_name . '<br />' . $child_value->email . ' ' . $child_value->tel_cell_country_code . $child_value->tel_cell;

                }

            }

        }

        // dd($items);

        return $items;

    }

    public function SendMessageToUser($phoneNumber, $message)

    {

        $accessToken = config('services.whatsapp.access_token');

        $phoneNumberId = config('services.whatsapp.phone_number');

        $formattedMessage = preg_replace("/[\r\n\t]+/", " ", $message); // Replace new-lines and tabs with a single space

        $formattedMessage = preg_replace("/ {2,}/", " ", $formattedMessage);     // Replace more than one space with a single space

        $formattedMessage = trim($formattedMessage);



        // API Endpoint

        $url = "https://graph.facebook.com/v21.0/{$phoneNumberId}/messages";



        // Payload

        $payload = [

            'messaging_product' => 'whatsapp',

            'to' => $phoneNumber, // Recipient phone number (include country code, e.g., +1234567890)

            'type' => 'template',

            'template' => [

                'name' => 'testing_message', // Template name as defined in WhatsApp Business Manager

                'language' => [

                    'code' => 'en' // Language code as configured in the template

                ],

                'components' => [

                    [

                        'type' => 'body', // Matches the "type": "BODY"

                        'parameters' => [

                            [

                                'type' => 'text', // Parameter type as defined

                                'text' => $formattedMessage, // This replaces {{1}}

                            ]

                        ]

                    ]

                ]

            ]

        ];



        // Make the POST Request

        $response = Http::withHeaders([

            'Authorization' => 'Bearer ' . $accessToken,

            'Content-Type' => 'application/json',

        ])->post($url, $payload);



        return $response->getStatusCode();

    }



    public function checkWhatsappExist($status, $number, $message)

    {

        $controller = new Controller();

        if ($status) {

            $whatsapp = $this->SendMessageToUser($number, $message);



            if ($whatsapp == 401) {

                $token = $this->renewAccessToken();

                if ($token->statusCode == 200) {

                    $whatsapp = $this->SendMessageToUser($number, $message);

                }

            }





            if ($whatsapp != 200) {

                $number = '+' . $number;

                $controller->sendMessage($message, $number);

            }

        }

    }





    public function renewAccessToken(): ?string

    {

        $appId = env('META_APP_ID');

        $appSecret = env('META_APP_SECRET');

        $currentToken = env('META_WHATSAPP_TOKEN');



        try {

            $response = Http::get('https://graph.facebook.com/v16.0/oauth/access_token', [

                'grant_type' => 'fb_exchange_token',

                'client_id' => $appId,

                'client_secret' => $appSecret,

                'fb_exchange_token' => $currentToken,

            ]);



            if ($response->successful()) {

                $newToken = $response->json()['access_token'] ?? null;



                if ($newToken) {

                    $this->updateEnvFile('META_WHATSAPP_TOKEN', $newToken);

                    $this->optimizeEnv(); // clears cache if needed

                    return $newToken; // return the string token only

                }

            }



            Log::error('Failed to renew access token: ' . json_encode($response->json()));

            return null;

        } catch (\Exception $e) {

            Log::error('Error during token renewal: ' . $e->getMessage());

            return null;

        }

    }





    private function updateEnvFile($key, $value)

    {

        $envPath = base_path('.env');



        if (file_exists($envPath)) {

            $envContent = file_get_contents($envPath);



            if (preg_match("/^{$key}=.*/m", $envContent)) {

                $envContent = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $envContent);

            } else {

                $envContent .= PHP_EOL . "{$key}={$value}";

            }



            file_put_contents($envPath, $envContent);

            echo "Updated .env with {$key}={$value}";

        } else {

            echo ".env file not found.";

        }

    }



    private function optimizeEnv()

    {



        Artisan::call('config:clear');

        Artisan::call('config:cache');

    }



    function sendViaMetaWhatsApp($phone, $text = [], $template = 'verify_user', $language = 'en_US')

    {

        Log::error('Meta WhatsApp message intit: ');



        try {

            $dbPhoneValue = str_replace('+', '', $phone);



            DB::table('message_statuses')->insert([

                'phone' => $dbPhoneValue,

                'status' => 'pending',

                'created_at' => now(),

                'updated_at' => now(),

            ]);

            $response = $this->sendWhatsAppTemplate($phone, env('META_WHATSAPP_TOKEN'), $template, $text, $language);



            if ($response->status() === 401 || $this->isTokenExpired($response)) {

                // Token expired, attempt to renew

                $newToken = $this->renewAccessToken();



                if ($newToken) {

                    // Retry message with new token

                    $response = $this->sendWhatsAppTemplate($phone, $newToken, $template, $text, $language);

                }

            }



            // Polling

            $maxWaitSeconds = 20;

            $start = time();

            $status = 'pending';



            while ((time() - $start) < $maxWaitSeconds) {

                $currentStatus = DB::table('message_statuses')

                    ->where('phone', $dbPhoneValue)

                    ->orderByDesc('id')

                    ->value('status');

                //Log::error('Meta WhatsApp message failed from webhook: ' . $currentStatus);



                if ($currentStatus && $currentStatus !== 'pending') {

                    $status = $currentStatus;

                    break;

                }



                usleep(500000); // Wait 0.5 seconds

            }



            // Delete record after processing

            DB::table('message_statuses')->where('phone', $dbPhoneValue)->delete();



            if ($status === 'failed') {

                Log::error('Meta WhatsApp message failed from webhook:');

                return false;

            }



            if (!$response->successful()) {



                Log::error('Meta WhatsApp message failed: ' . $response->body());

                return false;

            } else {

                Log::error('Meta WhatsApp message success: ' . json_encode($response));

            }



            return $response->successful();

        } catch (\Exception $e) {

            Log::error('Meta WhatsApp message failed: ' . $e->getMessage());

            return false;

        }

    }



    private function sendWhatsAppTemplate($phone, $accessToken, $template, $variables = [], $language = 'en_US')

    {

        $payload = [

            'messaging_product' => 'whatsapp',

            'to' => $phone,

            'type' => 'template',

            'template' => [

                'name' => $template,

                'language' => ['code' => $language],

            ]

        ];

        $components = [

            [

                'type' => 'body',

                'parameters' => []

            ]

        ];

        if (!empty($variables)) {





            foreach ($variables as $value) {

                if (!is_string($value) || trim($value) === '') {

                    throw new \InvalidArgumentException("All template variables must be non-empty strings.");

                }



                $components[0]['parameters'][] = [

                    'type' => 'text',

                    'text' => $value

                ];

            }



            $payload['template']['components'] = $components;

        }



        return Http::withToken($accessToken)->post(

            'https://graph.facebook.com/v22.0/' . env('META_PHONE_ID') . '/messages',

            $payload

        );

    }







    private function isTokenExpired($response)

    {

        $data = $response->json();



        return isset($data['error']['code']) && $data['error']['code'] === 190;

    }

    function sendViaTwilioSms($phone, $text, $mode = 'otp', $twilioSenderId)

    {

        Log::error('Failed to send SMS', [

            'phone' => $phone,

            'text' => $text,

            'sender_id' => $twilioSenderId

        ]);

        try {

            $twilio = new Client($twilioSenderId, env('TWILIO_TOKEN'));



            $twilio->messages->create($phone, [

                'from' => env('TWILIO_SMS_FROM'),

                'body' => $text

            ]);

            Log::error('Twilio message success: ' . $phone);



            return true;

        } catch (\Exception $e) {

            Log::error('Twilio message failed: ' . $e->getMessage());

            return false;

        }

    }



    function getCountryList()

    {

        $countriesFile = storage_path('countries.json');

        $countriesList = [];



        if (file_exists($countriesFile)) {

            $countriesJson = file_get_contents($countriesFile);

            $countriesArray = json_decode($countriesJson, true);



            foreach ($countriesArray as $country) {

                $countriesList[$country['code']] = $country['name'];

            }

        }

        return $countriesList;

    }



    public function generateOtpSession()

    {

        $otp = rand(100000, 999999);



        session([

            'otp_code' => $otp,

            'otp_expires_at' => now()->addMinutes(5),

        ]);



        return $otp;

    }

    public  function makeCollection($intent_pay, $pay_id, $transaction_id, $status, $discount_amt)

    {

        $location_data = Session::get('shop_logged_data');

        $checkout_order = Session::get('checkout_order');

        $cart = Session::get('cart');

        if ($cart->order_create) {

            $order = Order::find($cart->order->id);

        }

        $cart->discount_amt = $discount_amt ?? 0;

        if (Session::has('checkout_order') && $cart->order_create) {

            $currency = ($checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') ? 'NGN' : 'USD';

            if (Session::has('shop_logged_data') && $checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') {

                $currency = $this->getLocalCurrencyCode($location_data->country);

            }

            // $order = Order::find($cart->order->id);

            $collect = new Collect;

            $collect->collection_date = Carbon::now();

            $collect->customer_id = $cart->user->id;

            $collect->order_id = $order->id;

            $collect->transaction_method_id = $pay_id;

            $collect->transaction_id = $transaction_id;

            if (Session::has('shop_logged_data') && $checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') {

                $collect->amount = $intent_pay / $location_data->currency_rate;

                $collect->local_amount = $intent_pay;

            } else {

                $collect->amount = $intent_pay;

                $collect->local_amount = null;

            }

            $collect->currency = $currency;

            $collect->currency_rate = $location_data->currency_rate ?? null;

            $collect->save();

            if ($cart->order_create) {

                $order->transaction_discount = $discount_amt;

                $order->order_items = serialize(OrderItem::where('order_id', $order->id)->get());

                $order->order = serialize($order);

                $order->status = $status;

                $order->update();

            }

            $cart->currency = $currency;

            // dd($cart);

            Session::put(['cart' => $cart]);

            $total_reward_points = 0;

            if ($cart->use_reward_point_status) {

                $rewards = Reward::where([

                    'user_id' => auth()->user()->id,

                    'status' => true,

                ])->get();

                foreach ($rewards as $value) {

                    $total_reward_points += $value->points;

                    $reward_update = Reward::findOrFail($value->id);

                    $reward_update->status = false;

                    $reward_update->update();

                }

            }

            if ($cart->use_cash_value_status) {

                $cash_rewards = CashReward::where([

                    'user_id' => auth()->user()->id,

                    'status' => true,

                ])->get();

                $cash_total_reward = 0;

                $total_cash_reward_points = 0;

                foreach ($cash_rewards as $value) {

                    $cash_total_reward += $value->amount;

                    $total_cash_reward_points += $value->points;

                    $cash_reward_update = CashReward::findOrFail($value->id);

                    $cash_reward_update->status = false;

                    $cash_reward_update->update();

                }

            }

            $transfer_meta = (object)[

                'order_id' => $order->id,

            ];

            if ($cart->use_reward_point_status || $cart->use_cash_value_status) {

                $redeem_cash_reward = new RedeemCashReward;

                $redeem_cash_reward->user_id = auth()->user()->id;

                $redeem_cash_reward->points = $total_reward_points + $total_cash_reward_points;

                $redeem_cash_reward->amount = round(($total_reward_points / $this->cash_value_per_points) + $cash_total_reward, 2);

                if ($location_data->country != 'US' || $location_data->country != 'us') {

                    $redeem_cash_reward->currency_rate = $this->getLocalCurrencyRate($location_data->country);

                    $redeem_cash_reward->country = $location_data->country;

                }

                $redeem_cash_reward->transfer_type = 'by_order';

                $redeem_cash_reward->transfer_meta = serialize($transfer_meta);

                $redeem_cash_reward->transfer_status = 4;

                $redeem_cash_reward->status = 1;

                $redeem_cash_reward->save();

            }

            if (isset($cart) && isset($cart->user->id)) {

                $user = User::find($cart->user->id);

                $user_country = $user->country;

                // dd($user_country);

                $reward_value_per_usd_country = PointSystem::where('country', $user_country)->first();

                if ($reward_value_per_usd_country != null) {

                    $reward_value_per_usd_country = $reward_value_per_usd_country->order_points;

                }

            }

            $reward_value_per_usd = $reward_value_per_usd_country ?? $this->reward_value_per_usd;

            // dd($reward_value_per_usd);

            $reward = new Reward;

            $reward->user_id = $cart->user->id;

            $reward->collect_id = $collect->id;

            $reward->points = round(($collect->amount * $reward_value_per_usd), 0);

            $reward->save();

        }

        return Session::put('cart', $cart);

    }

    public function addGuestUser($email, $phone)

    {

        $name = explode('@', $email)[0];

        $user = new User();

        $user->email = $email;

        $user->tel_cell =  $phone;

        $user->first_name =  $name;

        $user->verified = 1;

        $user->save();

        $group_map = new UserGroupMap();

        $group_map->user_id = $user->id;

        $group_map->user_group_id = 2;

        $group_map->save();

    }

}

