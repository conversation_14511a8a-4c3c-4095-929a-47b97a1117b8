<?php



namespace App\Http\Controllers\Admin;



use Session;

use Validator;

use Carbon\Carbon;

use App\Models\Product;

use App\Models\ProductItem;

use Illuminate\Support\Str;

use App\Models\ProductBrand;

use Illuminate\Http\Request;

use App\Models\ProductSeries;

use App\Models\ProductCategory;

use Illuminate\Validation\Rule;

use App\Models\ProductAttribute;

use Illuminate\Support\Collection;

use Illuminate\Support\Facades\DB;

use App\Http\Controllers\Controller;

use App\Models\ProductAttributeItem;

use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Storage;

use App\Models\GeneralSetting;


class ProductController extends Controller

{

    private $folderPath = 'products/';

    private $folderPathItem = 'product_item_images/';

    /**

     * Display a listing of the resource.

     *

     * @return \Illuminate\Http\Response

     */

    public function index(Request $request)

    {

        $admin_page_title = 'Products';

        if ($request->action == 'search') {

            if (empty($request->categories_id) && empty($request->brand_id)) {

                $this->validate($request, [

                    'query' => 'required|min:3',

                ]);
            }

            $keywords = explode(' ', $request->input('query'));

            $search_fields = ['title', 'slug'];



            $query = Product::query();

            foreach ($keywords as $keyword) {

                $query->orWhere(function ($query) use ($search_fields, $keyword) {

                    foreach ($search_fields as $field) {

                        $query->orWhere($field, 'LIKE', '%' . $keyword . '%');
                    }
                });
            }

            if ($request->categories_id) {

                foreach ($request->categories_id as $category_id) {

                    $query->where('products.product_categories_id', $category_id);

                    // $query->where('products.product_categories_id', 'LIKE', '%' . $category_id . '%');

                }
            }

            if ($request->brand_id) {

                $query->where('products.product_brand_id', $request->brand_id);
            }

            if ($request->series_id) {

                $query->where('products.product_series_id', $request->series_id);
            }



            $query->orderBy('products.status', 'asc');

            $query->orderBy('products.id', 'desc');

            $items = $query->get();



            foreach ($items as $item) {

                $product_brand = ProductBrand::find($item->product_brand_id);

                $item->product_brand_title = $product_brand->title;
            }
        } else {

            $items = DB::table('products')

                ->join('product_brands', 'product_brands.id', 'products.product_brand_id')

                ->join('product_series', 'product_series.id', 'products.product_series_id')

                ->select('products.*', 'product_brands.title as product_brand_title', 'product_series.title as product_series_title')

                ->orderBy('products.status', 'asc')

                ->orderBy('products.id', 'desc')

                ->get();
        }



        if ($request->action == 'clear') {

            return redirect()->route('admin.products.index');
        }



        $items = $this->products($items);



        $exist_brands = ProductBrand::whereStatus(true)->first();

        if (!$exist_brands) {

            $message = 'Please create the brands first';

            return redirect()->route('admin.product-brands.create')->with('warning', $message);
        }



        $exist_categories = ProductCategory::whereStatus(true)->first();

        if (!$exist_categories) {

            $message = 'Please create the category first';

            return redirect()->route('admin.product-categories.create')->with('warning', $message);
        }



        $collection  = new Collection($items);

        $items =  $collection->paginate($this->itemPerPage);

        // $items =  $collection->sortBy('ordering')->paginate($this->itemPerPage);

        // $items =  $collection->where('in_stock', '=', true)->sortBy('ordering')->paginate($this->itemPerPage);

        $sl = SLGenerator($items);



        $categories = ProductCategory::whereStatus(true)->get();

        $brands = ProductBrand::whereStatus(true)->get();



        if ($request->view == 'html') {

            // dd($items->total() > 0);

            return view('admin.jquery_live.products', compact('items', 'sl', 'admin_page_title'));
        }



        return view('admin.product.index', compact('sl', 'items', 'admin_page_title', 'categories', 'brands'));
    }



    public function getCategories($categories)

    {

        $category_titles  = array();

        foreach ($categories as $category) {

            $category = ProductCategory::whereStatus(true)->where('id', $category)->first();

            if ($category) {

                $category_titles[] = $category->title;
            }
        }



        return $categories = implode(',', $category_titles);
    }



    public function getAttributes($items)

    {

        foreach ($items as $item) {

            // dd(collect(unserialize($item->item_attributes))->sortBy('ordering'));

            $attributes[] = collect(unserialize($item->item_attributes))->sortBy('ordering')->toArray();
        }



        $attributes_array = [];

        foreach ($attributes as $attribute) {

            $attribute = $this->getAttribute($attribute);

            $attributes_array = array_merge($attributes_array, $attribute);
        }



        $attributes_options = array();

        foreach ($attributes_array as $attribute_array) {

            if ($attribute_array->id == $attribute_array->id) {

                $attributes_options[$attribute_array->id]['id'] = $attribute_array->id;

                $attributes_options[$attribute_array->id]['title'] = $attribute_array->title;

                $attributes_options[$attribute_array->id]['ordering'] = $attribute_array->ordering;

                $attributes_options[$attribute_array->id]['options'][] = $attribute_array->options;
            }
        }

        foreach ($attributes_options as $attributes_option) {

            $options[] = (object)$attributes_option;
        }

        foreach ($options as $option) {

            $option->options = implode(',', array_unique(explode(',', implode(',', $option->options))));
        }



        return $options;
    }



    public function getAttribute($items)

    {



        foreach ($items as $item) {

            if ($item->input_type_id == 1) {

                $item->options = $item->data->title;
            } else {

                $item->options = $this->getOption($item->data);
            }
        }

        return $items;
    }



    public function getOption($items)

    {

        foreach ($items as $item) {

            $titles[] = $item->title;
        }

        return implode(',', $titles);
    }



    /**

     * Show the form for creating a new resource.

     *

     * @return \Illuminate\Http\Response

     */

    public function create()

    {

        $admin_page_title = 'Create Product';

        $product_image_info = Session::get('product_image');



        $imagesCollection = collect(Session::get('product_images'));

        $images = $imagesCollection->sortBy('file_order');



        $categories = ProductCategory::whereStatus(true)->get();

        foreach ($categories as $category) {

            $categoriesID[] = explode(',', $category->id);
        }



        $brands = ProductBrand::whereStatus(true)->get();

        $attributes = ProductAttribute::all();



        $items = Session::get('product_attribute_options');

        $product_attributes = collect($items)->sortBy('ordering')->paginate($this->itemPerPage);

        // dd($product_attributes);

        $sl = SLGenerator($product_attributes);

        $tab_sl = SLGenerator($product_attributes);



        $product_option_icon_info = Session::get('product_option_icon');

        $product_option_text_icon_info = Session::get('option_text_icon');



        $items = Session::get('product_specifications');

        $specifications = collect($items)->paginate($this->itemPerPage);

        // dd($specifications);

        $spec_sl = SLGenerator($specifications);

        $inventory_status = Product::inventoryStatusOptions();
$shippingUnits = (object) [
            'weight_unit' => GeneralSetting::where('key', 'weight_unit')->value('value') ?? 'kg',
            'dimension_unit' => GeneralSetting::where('key', 'dimension_unit')->value('value') ?? 'cm',
        ];
        $weight_label = GeneralSetting::getUnitLabel('weight', $shippingUnits->weight_unit); // e.g. "Pound (lb)"
        $dimension_label = GeneralSetting::getUnitLabel('dimension', $shippingUnits->dimension_unit); // e.g. "Inch (in)"
        return view('admin.product.create', compact('weight_label','dimension_label','product_image_info', 'product_attributes', 'sl', 'tab_sl', 'specifications', 'spec_sl', 'product_option_icon_info', 'product_option_text_icon_info', 'images', 'attributes', 'categories', 'categoriesID', 'brands', 'admin_page_title', 'inventory_status'));
    }



    /**

     * Store a newly created resource in storage.

     *

     * @param  \Illuminate\Http\Request  $request

     * @return \Illuminate\Http\Response

     */

    public function store(Request $request)

    {

        if (!Auth::user()->permissions('product', 'add')) return redirect('/' . Auth::user()->username)->with('warning', 'You are not authorized this area');



        $this->validate($request, [

            'product_categories_id' => 'required|exists:product_categories,id',

            'product_brand_id' => 'required|exists:product_brands,id',

            'product_series_id' => 'required|exists:product_series,id',

            'product_attribute_id' => 'nullable|exists:product_attributes,id',

            'title' => 'required|string',

            'short_description' => 'nullable|string',

            'description' => 'nullable|string',

            'specifications' => 'nullable|string',

            'shipping' => 'nullable|string',

            'warranty_returns' => 'nullable|string',

            'meta_title' => 'nullable|string',

            'meta_description' => 'nullable|string',

            'meta_keyword' => 'nullable|string',

            'remark' => 'nullable|string',

            'status' => 'nullable|boolean',

            'ordering' => 'nullable|numeric',

        ]);



        if ($request->slug) {

            $item_slug = Str::slug($request->slug, '-');
        } else {

            $item_slug = Str::slug($request->title, '-');
        }



        $validator = Validator::make(['slug' => $item_slug], [

            'slug' => 'required|string|alpha_dash|unique:products,slug'

        ]);



        if ($validator->fails()) {

            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }



        $item = new Product;

        $item->product_categories_id = $request->get('product_categories_id');

        // $item->product_categories_id = implode(',', $request->get('product_categories_id'));

        $item->product_brand_id = $request->get('product_brand_id');

        $item->product_series_id = $request->get('product_series_id');

        $item->product_attribute_id = $request->get('product_attribute_id');

        $item->title = $request->get('title');

        $item->slug = $item_slug;

        $item->short_description = $request->get('short_description');

        $item->description = $request->get('description');

        $item->inventory_status = $request->get('inventory_status') ?? 'in_stock';

        // $item->specifications = $request->get('specifications');

        $item->shipping = $request->get('shipping');



        $item->box_with_shipping_weight = $request->get('box_with_shipping_weight');

        $item->box_with_shipping_length = $request->get('box_with_shipping_length');

        $item->box_with_shipping_height = $request->get('box_with_shipping_height');

        $item->box_with_shipping_width = $request->get('box_with_shipping_width');



        $item->open_box_shipping_weight = $request->get('open_box_shipping_weight');

        $item->open_box_shipping_length = $request->get('open_box_shipping_length');

        $item->open_box_shipping_height = $request->get('open_box_shipping_height');

        $item->open_box_shipping_width = $request->get('open_box_shipping_width');



        $item->warranty_returns = $request->get('warranty_returns');

        $item->meta_title = $request->get('meta_title');

        $item->meta_description = $request->get('meta_description');

        $item->meta_keyword = $request->get('meta_keyword');

        $item->remark = $request->get('remark');

        $item->status = $request->get('status') ?? 0;

        $item->ordering = $request->get('ordering') ?? 0;

        $item->save();



        $product = Product::findOrFail($item->id);

        $product_image_info = Session::get('product_image');

        if ($product_image_info) {

            $image_name = 'product_image_' . rand(99, 999999999) . '.' . $product_image_info->extension;

            $folderName = $this->folderName() . '/' . $item->id . '/';

            $product->product_image = $folderName . '/' . $image_name;

            Storage::move($product_image_info->product_image, $this->folderPath . $folderName . '/' . $image_name);
        }

        if ($request->hasFile('product_image')) {

            $folderName = $this->folderName() . $item->id . '/';

            $imgName = 'product_image_' . uniqid() . '.' . $request->product_image->extension();

            $request->product_image->storeAs($this->folderPath . $folderName, $imgName);

            $product->product_image = $folderName . '/' . $imgName;
        }

        if (Session::has('product_attribute_options')) {

            $attributes = Session::get('product_attribute_options');

            // dd($attributes);

            foreach ($attributes as $attribute) {

                $this->saveFile($attribute->attribute_options, $item->id, 'attribute_options', 'option_icon');

                $attribute->id = rand(10, 10000000);
            }

            $product->product_attributes = serialize($attributes);
        }

        if (Session::has('product_specifications')) {

            $specifications = Session::get('product_specifications');

            $this->saveFile($specifications, $item->id, 'specifications', 'specification_icon');

            $product->specifications = serialize($specifications);
        }

        // dd($item);

        $product->update();



        Session::forget('product_image');

        Session::forget('product_attribute_options');

        Session::forget('product_specifications');



        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.products.index'))->with('success', 'Product created successful.');
    }



    public function saveFile($items, $item_id, $folder_name, $icon_name)

    {

        if (!empty($items)) {

            // dd($items);

            foreach ($items as $item) {

                if (!empty($item->file_path)) {

                    $image_name = $icon_name . '_' . rand(99, 999999999) . '.' . $item->extension;

                    $folderName = $this->folderName() . '/' . $item_id . '/' . $folder_name;

                    // dd($item->file_current);

                    Storage::copy($item->file_current, $this->folderPath . $folderName . '/' . $image_name);

                    $item->file_current = $this->folderPath . $folderName . '/' . $image_name;

                    $item->file_path = $folderName . '/' . $image_name;

                    $item->file_root = asset('storage/products/');

                    $item->id = rand(10, 10000000);

                    // dd($this->folderPath . $folderName. '/'. $image_name);

                }

                if (!empty($item->text_file_path)) {

                    $image_name = $icon_name . '_' . rand(99, 999999999) . '.' . $item->text_extension;

                    $folderName = $this->folderName() . '/' . $item_id . '/' . $folder_name;

                    Storage::copy($item->text_file_current, $this->folderPath . $folderName . '/' . $image_name);

                    $item->text_file_current = $this->folderPath . $folderName . '/' . $image_name;

                    $item->text_file_path = $folderName . '/' . $image_name;

                    $item->text_file_root = asset('storage/products/');

                    $item->id = rand(10, 10000000);
                }
            }
        }
    }



    /**

     * Display the specified resource.

     *

     * @param  \App\Models\Product  $product

     * @return \Illuminate\Http\Response

     */

    public function show(Product $product)

    {

        //

    }



    /**

     * Show the form for editing the specified resource.

     *

     * @param  \App\Models\Product  $product

     * @return \Illuminate\Http\Response

     */

    public function edit(Product $product)

    {

        $admin_page_title = 'Edit Product';

        $item = Product::findOrFail($product->id);



        if ($item->product_images) {

            $imagesCollection = collect(unserialize($item->product_images));

            $item->product_images = $imagesCollection->sortBy('file_order');
        }

        $sl = null;

        $tab_sl = null;

        $spec_sl = null;



        if (Session::has('product_attribute_options')) {

            $product_attributes = unserialize($item->product_attributes);



            $session_attributes = Session::get('product_attribute_options');

            if (!$product_attributes) {

                $item->product_attributes = collect($session_attributes)->sortBy('ordering')->paginate($this->itemPerPage);
            } else {

                $collection_attributes = collect($product_attributes->merge($session_attributes));

                $item->product_attributes = $collection_attributes->sortBy('ordering')->paginate($this->itemPerPage);
            }
        } else {

            // dd(unserialize($item->product_attributes));

            if ($item->product_attributes) {

                $item->product_attributes = collect(unserialize($item->product_attributes))->sortBy('ordering')->paginate($this->itemPerPage);
            }
        }

        // dd($item->product_attributes);

        if (Session::has('product_attribute_options') || $item->product_attributes) {

            $sl = SLGenerator($item->product_attributes);

            $tab_sl = SLGenerator($item->product_attributes);
        }





        if (Session::has('product_specifications')) {

            if ($item->specifications) {

                $specifications = collect(unserialize($item->specifications));

                $session_specifications = collect(Session::get('product_specifications'));

                $collection = collect($specifications->merge($session_specifications));
            } else {

                $collection = collect(Session::get('product_specifications'));
            }

            // dd($session_specifications, $specifications);



            $item->specifications = $collection->paginate($this->itemPerPage);

            $spec_sl = SLGenerator($item->specifications);
        } else {

            if ($item->specifications) {

                $item->specifications = collect(unserialize($item->specifications))->paginate($this->itemPerPage);

                $spec_sl = SLGenerator($item->specifications);
            }
        }

        // dd($item->specifications);

        $categories = ProductCategory::whereStatus(true)->get();

        $categoriesID = explode(',', $item->product_categories_id);

        $brands = ProductBrand::whereStatus(true)->get();

        $series = ProductSeries::whereStatus(true)->get();

        $attributes = ProductAttribute::all();
        $inventory_status = Product::inventoryStatusOptions();
        $shippingUnits = (object) [
            'weight_unit' => GeneralSetting::where('key', 'weight_unit')->value('value') ?? 'kg',
            'dimension_unit' => GeneralSetting::where('key', 'dimension_unit')->value('value') ?? 'cm',
        ];
        $weight_label = GeneralSetting::getUnitLabel('weight', $shippingUnits->weight_unit); // e.g. "Pound (lb)"
        $dimension_label = GeneralSetting::getUnitLabel('dimension', $shippingUnits->dimension_unit); // e.g. "Inch (in)"
        return view('admin.product.edit', compact('dimension_label', 'weight_label', 'item', 'sl', 'tab_sl', 'spec_sl', 'brands', 'categories', 'attributes', 'categoriesID', 'series', 'admin_page_title', 'inventory_status'));
    }



    /**

     * Update the specified resource in storage.

     *

     * @param  \Illuminate\Http\Request  $request

     * @param  \App\Models\Product  $product

     * @return \Illuminate\Http\Response

     */

    public function update(Request $request, $id)

    {

        $categories = ProductCategory::get();

        foreach ($categories as $category) {

            $categories_id[] =  $category->id;
        }



        $this->validate($request, [

            'product_categories_id' => 'required|exists:product_categories,id',

            // 'product_categories_id.*' => ['required', Rule::in($categories_id)],

            'product_brand_id' => 'required|exists:product_brands,id',

            'product_series_id' => 'required|exists:product_series,id',

            'product_attribute_id' => 'nullable|exists:product_attributes,id',

            'title' => 'required|string',

            'short_description' => 'nullable|string',

            'description' => 'nullable|string',

            'specifications' => 'nullable|string',

            'shipping' => 'nullable|string',

            'warranty_returns' => 'nullable|string',

            'meta_title' => 'nullable|string',

            'meta_description' => 'nullable|string',

            'meta_keyword' => 'nullable|string',

            'remark' => 'nullable|string',

            'status' => 'nullable|boolean',

            'ordering' => 'nullable|numeric',

        ]);



        $product = Product::findOrFail($id);

        if ($request->slug) {

            $item_slug = Str::slug($request->slug, '-');
        } else {

            $item_slug = Str::slug($request->title, '-');
        }



        $validator = Validator::make(['slug' => $item_slug], [

            'slug' => 'required|string|alpha_dash|unique:products,slug,' . $product->id,

        ]);



        if ($validator->fails()) {

            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }



        $product->product_categories_id = $request->get('product_categories_id');

        // $product->product_categories_id = implode(',', $request->get('product_categories_id'));

        $product->product_brand_id = $request->get('product_brand_id');

        $product->product_series_id = $request->get('product_series_id');

        // $product->product_attribute_id = $request->get('product_attribute_id');

        $product->title = $request->get('title');

        $product->slug = $item_slug;

        $product->short_description = $request->get('short_description');

        $product->description = $request->get('description');

        // $product->specifications = $request->get('specifications');

        $product->shipping = $request->get('shipping');



        $product->box_with_shipping_weight = $request->get('box_with_shipping_weight');

        $product->box_with_shipping_length = $request->get('box_with_shipping_length');

        $product->box_with_shipping_height = $request->get('box_with_shipping_height');

        $product->box_with_shipping_width = $request->get('box_with_shipping_width');



        $product->open_box_shipping_weight = $request->get('open_box_shipping_weight');

        $product->open_box_shipping_length = $request->get('open_box_shipping_length');

        $product->open_box_shipping_height = $request->get('open_box_shipping_height');

        $product->open_box_shipping_width = $request->get('open_box_shipping_width');



        $product->warranty_returns = $request->get('warranty_returns');

        $product->meta_title = $request->get('meta_title');

        $product->meta_description = $request->get('meta_description');

        $product->meta_keyword = $request->get('meta_keyword');

        $product->remark = $request->get('remark');

        $product->status = $request->get('status') ?? 0;

        $product->ordering = $request->get('ordering') ?? 0;
        $product->inventory_status = $request->get('inventory_status') ?? 'in_stock';



        if (Session::has('product_attribute_options')) {

            $attributes = Session::get('product_attribute_options');



            foreach ($attributes as $attribute) {

                if ($attribute->attribute_options) {

                    $this->saveFile($attribute->attribute_options, $product->id, 'attribute_options', 'option_icon');
                }

                $attribute->id = rand(10, 10000000);
            }



            if ($product->product_attributes) {

                $attributes_collect = collect($attributes);

                $product_attributes = collect(unserialize($product->product_attributes));

                $product->product_attributes = serialize(collect($product_attributes->merge($attributes_collect)));
            } else {

                $product->product_attributes = serialize(collect($attributes));
            }
        }



        if (Session::has('product_specifications')) {

            $specifications = Session::get('product_specifications');

            $this->saveFile($specifications, $product->id, 'specifications', 'specification_icon');



            if ($product->specifications) {

                $specifications_collect = collect($specifications);

                $product_specifications = collect(unserialize($product->specifications));

                $product->specifications = serialize(collect($product_specifications->merge($specifications_collect)));
            } else {

                $product->specifications = serialize(collect($specifications));
            }
        }



        $product->update();



        Session::forget('product_attribute_options');

        Session::forget('product_specifications');



        $message = 'Product successfully updated.';

        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.products.index')->with('success', $message);
    }



    /**

     * Remove the specified resource from storage.

     *

     * @param  \App\Models\Product  $product

     * @return \Illuminate\Http\Response

     */

    public function destroy(Product $product)

    {

        $product_items = ProductItem::where('product_id', $product->id)->get();

        foreach ($product_items as $product_item) {

            if ($product_item->product_item_image) {

                $get_folder_name = explode('/', $product_item->product_item_image);

                Storage::deleteDirectory($this->folderPathItem . $get_folder_name[0] . '/' . $get_folder_name[1] . '/' . $product_item->id);
            }

            if ($product_item->product_item_images) {

                $images = unserialize($product_item->product_item_images);

                foreach ($images as $image) {

                    $get_folder_name = explode('/', $image->product_item_images);

                    Storage::deleteDirectory($this->folderPathItem . $get_folder_name[0] . '/' . $get_folder_name[1] . '/' . $product_item->id);

                    break;
                }
            }

            $product_item->delete();
        }



        if ($product->product_image) {

            $get_folder_name = explode('/', $product->product_image);

            Storage::deleteDirectory($this->folderPath . $get_folder_name[0] . '/' . $get_folder_name[1] . '/' . $product->id);
        }

        $product->delete();



        $message = '<strong>' . $product->title . '</strong> delete successful';

        return response()->json([

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function products($items)

    {

        foreach ($items as $item) {

            $item->in_stock = ProductItem::Where([

                'product_id' => $item->id,

                'stock_status' => true,

                'status' => true

            ])->exists();



            $stock_count =  ProductItem::Where([

                'product_id' => $item->id,

                'stock_status' => true,

                'status' => true

            ])

                ->orderBy('ordering', 'asc')

                ->get();

            $item->attributes = array();

            $item->price = $this->selectMaxMinPrice($stock_count);

            $item->stock_count = $this->stockCount($stock_count);



            $item->categories = $this->getCategories(explode(',', $item->product_categories_id));

            if (count($stock_count) > 0) {

                $item->attributes = $this->getAttributes($stock_count);
            }
        }



        return $items;
    }



    public function fileUpload(Request $request, $target, $id)

    {

        $validator = Validator::make($request->all(), [

            $target => 'file|mimes:jpg,jpeg,gif,png,svg,webp,mp4,avi,mov,wmv'

        ]);



        if ($validator->fails()) {

            foreach ($validator->messages()->all() as $key => $message) {

                return response()->json([

                    'status' => 'failed',

                    'message' =>  $message

                ], 200);

                break;
            }
        }



        $item = Product::findOrFail($id);



        $this->deleteFile($this->folderPath, $item->$target);



        $imgName = $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();

        $folderName = $this->folderName() . '/' . $item->id . '/';

        $request->$target->storeAs($this->folderPath . '/' . $folderName, $imgName);



        $item->$target = $folderName . '/' . $imgName;

        $item->update();



        $url = url('/storage/products/' . $folderName . '/' . $imgName);



        return response()->json(

            [

                'status' => 'success',

                'file_url' => $url

            ],

            200

        );
    }



    public function fileRemove($target, $id)

    {

        $item = Product::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target);

        $item->$target = null;

        $item->update();



        return response()->json('success', 200);
    }



    public function stockCount($items)

    {

        return count($items);
    }



    public function selectMaxMinPrice($items)

    {

        foreach ($items as $item) {

            // $item->price = $item->discount_price ??  $item->sale_price;

            if ($item->discount_price && $item->discount_type) {

                if ($item->discount_type == 1) {

                    $discount_amt = ($item->sale_price * $item->discount_price) / 100;
                } else {

                    $discount_amt = $item->discount_price;
                }

                $item->price = number_format($item->sale_price - $discount_amt, 2);
            } else {

                $item->price = $item->sale_price;
            }
        }

        $collection  = new Collection($items);

        $max_price =  $collection->max('price');

        $min_price =  $collection->min('price');

        $price = $max_price = $max_price;

        if (count($items) > 1 & $max_price != $min_price) {

            $price = $min_price . ' - ' . $max_price;
        }

        return $price;
    }



    public function destroyAttributeOption($option_id, $attribute_id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_attributes = collect(unserialize($product->product_attributes));

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();



            $items = $product_attribute->attribute_options;

            foreach ($items as $key => $item) {

                if ($item->id == $option_id) {

                    if ($item->file_current) {

                        Storage::delete($item->file_current);
                    }

                    if ($item->text_file_current) {

                        Storage::delete($item->text_file_current);
                    }

                    unset($items[$key]);

                    $message = '<strong>' . $item->title . '</strong> deleted successful';

                    break;
                }
            }

            $product_attribute->attribute_options = collect($items);

            $product->product_attributes = serialize($product_attributes);

            $product->update();
        }



        if (Session::has('product_attribute_options')) {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();

            $items = $product_attribute->attribute_options;

            foreach ($items as $key => $item) {

                if ($item->id == $option_id) {

                    unset($items[$key]);

                    $message = '<strong>' . $item->title . '</strong> deleted successful';

                    break;
                }
            }

            Session::put([

                'product_attribute_options' => $product_attributes,

            ]);
        }





        return response()->json([

            'status' => 'success',

            'message' => $message,

        ], 200);
    }



    public function attributeOptions($attribute_id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::find($product_id);

            $product_attributes = unserialize($product->product_attributes);

            $session_attributes = Session::get('product_attribute_options');

            $items_collect = collect($product_attributes->merge($session_attributes));

            $product_attribute = $items_collect->where('id', $attribute_id)->first();

            $items = $product_attribute->attribute_options;
        } else {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();

            $items = $product_attribute->attribute_options;
        }

        // dd($items);

        if (count($items) > 0) {

            return view('admin.jquery_live.product_attribute_items_options', compact('items', 'attribute_id', 'product_id'));
        }
    }



    public function addAttributeOption(Request $request, $attribute_id, $product_id = null)

    {

        $validator = Validator::make($request->all(), [

            'option_title' => 'required|string|max:100',

            'option_description' => 'nullable|string|max:300',

            'option_ordering' => 'nullable|numeric',

            'option_status' => 'nullable|boolean',

        ]);



        if ($validator->fails()) {

            return response()->json([

                'status' => 'failed',

                'messages' => $validator->messages(),

            ], 200);
        }

        $option_icon = Session::get('product_option_icon');

        $option_text_icon = Session::get('product_option_text_icon');



        $item = (object)([

            'id' => uniqid(),

            'attribute_id' => $attribute_id,



            'file_current' => $option_icon->product_option_icon ?? null,

            'file_path' => $option_icon->file_path ?? null,

            'extension' => $option_icon->extension ?? null,

            'file_root' => $option_icon->file_root ?? 0,



            'text_file_current' => $option_text_icon->product_option_text_icon ?? null,

            'text_file_path' => $option_text_icon->file_path ?? null,

            'text_extension' => $option_text_icon->extension ?? null,

            'text_file_root' => $option_text_icon->file_root ?? 0,



            'title' => $request->get('option_title'),

            'description' => $request->get('option_description'),

            'ordering' =>  $request->get('option_ordering') ?? 0,

            'status' => $request->get('option_status') ?? 0,



        ]);



        $exist_attribute = ProductAttributeItem::where('id', $attribute_id)->exists();



        if ($product_id && !$exist_attribute && !Session::has('product_attribute_options')) {

            $product = Product::findOrFail($product_id);

            if (!empty($item->file_path)) {

                $image_name = 'option_icon_' . rand(99, 999999999) . '.' . $item->extension;

                $folderName = $this->folderName() . '/' . $product->id . '/products/attribute_options';



                Storage::copy($item->file_current, $this->folderPath . $folderName . '/' . $image_name);



                $item->file_current = $this->folderPath . $folderName . '/' . $image_name;

                $item->file_path = $folderName . '/' . $image_name;

                $item->file_root = asset('storage/products/');

                $item->id = rand(10, 10000000);

                // dd($this->folderPath . $folderName. '/'. $image_name);

            }

            if (!empty($item->text_file_path)) {

                $image_name = 'option_icon_' . rand(99, 999999999) . '.' . $item->extension;

                $folderName = $this->folderName() . '/' . $product->id . '/products/attribute_options';



                Storage::copy($item->text_file_current, $this->folderPath . $folderName . '/' . $image_name);



                $item->text_file_current = $this->folderPath . $folderName . '/' . $image_name;

                $item->text_file_path = $folderName . '/' . $image_name;

                $item->text_file_root = asset('storage/products/');

                $item->id = rand(10, 10000000);
            }



            $product_attributes = unserialize($product->product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();

            $items_collect = $product_attribute->attribute_options;



            if (!empty($items_collect)) {

                $items = $items_collect->toArray();

                if (count($items) > 10) {

                    return response()->json([

                        'status' => 'failed',

                        'message' =>  'limit end'

                    ], 200);
                }

                array_push($items, $item);
            }



            if (empty($items_collect)) {

                $items = array($item);
            }



            $product_attribute->attribute_options = collect($items);

            $product->product_attributes = serialize($product_attributes);

            $product->update();
        }



        if (Session::has('product_attribute_options')) {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();

            if ($product_attribute) {

                $items_collect = $product_attribute->attribute_options;

                if (!empty($items_collect)) {

                    $items = $items_collect->toArray();

                    if (count($items) > 10) {

                        return response()->json(

                            [

                                'status' => 'failed',

                                'message' =>  'limit end'

                            ],

                            200

                        );
                    }

                    array_push($items, $item);
                } else {

                    $items = array($item);
                }

                $product_attribute->attribute_options = collect($items);



                Session::put([

                    'product_attribute_options' => $product_attributes,

                ]);
            }
        }



        Session::forget('product_option_icon');

        Session::forget('product_option_text_icon');



        $message = '<strong>' . $item->title . '</strong> added successful';



        return response()->json([

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function updateAttributeOption(Request $request, $option_id, $attribute_id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_attributes = unserialize($product->product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();

            $items = $product_attribute->attribute_options;

            foreach ($items as $item) {

                if ($item->id == $option_id) {

                    $item->title = $request->option_title;

                    $item->description = $request->option_description;

                    $item->ordering = $request->option_ordering;

                    $item->status = $request->option_status ?? 0;



                    $message = '<strong>' . $item->title . '</strong> update successful';

                    break;
                }
            }

            $product_attribute->attribute_options = $items;

            $product->product_attributes = serialize($product_attributes);

            $product->update();
        }



        if (Session::has('product_attribute_options')) {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();

            $items = $product_attribute->attribute_options;

            foreach ($items as $item) {

                if ($item->id == $option_id) {

                    $item->title = $request->option_title;

                    $item->description = $request->option_description;

                    $item->ordering = $request->option_ordering;

                    $item->status = $request->option_status ?? 0;



                    $message = '<strong>' . $item->title . '</strong> update successful';

                    break;
                }
            }

            Session::put([

                'product_attribute_options' => $product_attributes,

            ]);
        }





        return response()->json(

            [

                'status' => 'success',

                'message' => $message

            ],

            200

        );
    }



    public function editAttributeOption($option_id, $attribute_id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::find($product_id);

            $product_attributes = unserialize($product->product_attributes);

            $session_attributes = Session::get('product_attribute_options');

            $collection = collect($product_attributes->merge($session_attributes));

            $product_attribute = $collection->where('id', $attribute_id)->first();
        } else {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();
        }

        $items = $product_attribute->attribute_options;

        foreach ($items as $item) {

            if ($item->id == $option_id) {



                // $item->file_root = asset('storage/product_attributes/');

                $item->file_img_upload_url = route('admin.attribute.item.file.upload', ['product_option_icon', $option_id, $attribute_id, $product_id]);

                $item->file_img_remove_url = route('admin.attribute.item.file.remove', ['product_option_icon', $option_id, $attribute_id, $product_id]);



                // $item->text_file_root = asset('storage/product_attributes/');

                $item->text_file_img_upload_url = route('admin.attribute.item.file.upload', ['product_option_text_icon', $option_id, $attribute_id, $product_id]);

                $item->text_file_img_remove_url = route('admin.attribute.item.file.remove', ['product_option_text_icon', $option_id, $attribute_id, $product_id]);



                if ($item->file_path) {

                    $item->file_root = asset('storage/products/');

                    $item->img_url = $item->file_root . '/' . $item->file_path;
                }

                if (!empty($item->text_file_path)) {

                    $item->text_file_root = asset('storage/products/');

                    $item->text_img_url = $item->text_file_root . '/' . $item->text_file_path;
                }

                return response()->json($item, 200);

                break;
            }
        }
    }



    public function attributeOrderUpdate($items, $index, $new_position)
    {

        // dd($index, $new_position, $items);

        foreach ($items as $item) {

            if ($index == $item->id) {

                // dd('here');

                $item->ordering = $new_position;
            }
        }

        return $items;
    }



    public function updateAttributes(Request $request, $attribute_id, $product_id = null)

    {

        // dd($request->all());

        if ($request->update == 1) {

            foreach ($request->positions as $position) {

                $index = $position[0];

                $new_position = $position[1];

                $product_id = null;

                if (count($position) == 3) {

                    $product_id = $position[2];
                }



                if ($product_id) {

                    $product = Product::findOrFail($product_id);

                    $product_attributes = unserialize($product->product_attributes);

                    if ($product->product_attributes) {

                        $product_attributes = $this->attributeOrderUpdate($product_attributes, $index, $new_position);

                        $product->product_attributes = serialize($product_attributes);

                        $product->update();
                    }
                }

                if (Session::has('product_attribute_options')) {

                    $product_attributes = Session::get('product_attribute_options');

                    $new_product_attributes = $this->attributeOrderUpdate($product_attributes, $index, $new_position);

                    // dd($new_product_attributes);

                    Session::put([

                        'product_attribute_options' => $new_product_attributes,

                    ]);
                }
            }
        }

        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_attributes = unserialize($product->product_attributes);

            if ($product->product_attributes) {

                foreach ($product_attributes as $product_attribute) {

                    if ($product_attribute->id == $attribute_id) {

                        $product_attribute->title = $request->attribute_title[$attribute_id];

                        $product_attribute->input_type_id = $request->input_type_id[$attribute_id];

                        $product_attribute->attribute_type_id = $request->attribute_type_id[$attribute_id];

                        $product_attribute->input_required = $request->input_required[$attribute_id] ?? 0;

                        break;
                    }
                }

                $product->product_attributes = serialize($product_attributes);

                $product->update();
            }
        }

        if (Session::has('product_attribute_options')) {

            $product_attributes = Session::get('product_attribute_options');

            if ($product_attributes) {

                $product_attributes = collect($product_attributes);

                foreach ($product_attributes as $product_attribute) {

                    if ($product_attribute->id == $attribute_id) {

                        $product_attribute->title = $request->attribute_title[$attribute_id];

                        $product_attribute->input_type_id = $request->input_type_id[$attribute_id];

                        $product_attribute->attribute_type_id = $request->attribute_type_id[$attribute_id];

                        $product_attribute->input_required = $request->input_required[$attribute_id] ?? 0;

                        break;
                    }
                }

                Session::put([

                    'product_attribute_options' => $product_attributes,

                ]);
            }
        }



        return response()->json('success', 200);
    }



    public function attributeItemUpload(Request $request, $target, $option_id, $attribute_id, $product_id = null)

    {

        $validator = Validator::make($request->all(), [

            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,

        ]);



        if ($validator->fails()) {

            foreach ($validator->messages()->all() as $key => $message) {

                return response()->json([

                    'status' => 'failed',

                    'message' =>  $message

                ], 200);

                break;
            }
        }



        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_attributes = collect(unserialize($product->product_attributes));

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();



            if (!empty($product_attribute->attribute_options)) {

                $items = $product_attribute->attribute_options;

                foreach ($items as $item) {

                    if ($item->id == $option_id) {

                        $image_name = 'option_icon_' . rand(99, 999999999) . '.' . $request->$target->extension();

                        $folderName = $this->folderName() . '/' . $product->id . '/attribute_options';



                        if ($target == 'product_option_text_icon') {

                            Storage::delete($item->text_file_current);

                            $request->$target->storeAs($this->folderPath . $folderName, $image_name);



                            $item->text_extension = $request->$target->extension();

                            $item->text_file_current = $this->folderPath . $folderName . '/' . $image_name;

                            $item->text_file_path = $folderName . '/' . $image_name;

                            $item->text_file_root = asset('storage/products/');



                            $url = $item->file_root . '/' . $item->file_path;
                        }

                        if ($target == 'product_option_icon') {

                            Storage::delete($item->file_current);



                            $request->$target->storeAs($this->folderPath . $folderName, $image_name);



                            $item->extension = $request->$target->extension();

                            $item->file_current = $this->folderPath . $folderName . '/' . $image_name;

                            $item->file_path = $folderName . '/' . $image_name;

                            $item->file_root = asset('storage/products/');



                            $url = $item->file_root . '/' . $item->file_path;
                        }

                        break;
                    }
                }

                $product_attribute->attribute_options = $items;

                $product->product_attributes = serialize($product_attributes);

                $product->update();
            }
        }



        if (Session::has('product_attribute_options')) {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();



            $items = $product_attribute->attribute_options;

            foreach ($items as $item) {

                if ($item->id == $option_id) {

                    $imgName = $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();

                    $folderName = $this->folderName();

                    $auth_user = Auth()->user();



                    if ($target == 'product_option_text_icon') {

                        $request->$target->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);

                        $item->text_file_path = $folderName  . '/' . $imgName;

                        $item->text_file_current = $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName  . '/' . $imgName;

                        $item->text_extension = $request->$target->extension();

                        $item->text_file_root = asset('storage/session_files/' . $auth_user->username . '/' . $target . '/');
                    }



                    if ($target == 'product_option_icon') {

                        $request->$target->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);

                        $item->file_path = $folderName  . '/' . $imgName;

                        $item->file_current = $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName  . '/' . $imgName;

                        $item->extension = $request->$target->extension();

                        $item->file_root = asset('storage/session_files/' . $auth_user->username . '/' . $target . '/');
                    }



                    $url = url('storage/session_files/' . $auth_user->username . '/' . $target . '/' . $folderName . '/' . $imgName);

                    break;
                }
            }

            $product_attribute->attribute_options = $items;

            Session::put([

                'product_attribute_options' => $product_attributes,

            ]);
        }

        return response()->json(

            [

                'status' => 'success',

                'file_url' => $url

            ],

            200

        );
    }



    public function attributeItemRemove($target, $option_id, $attribute_id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_attributes = collect(unserialize($product->product_attributes));

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();



            $items = $product_attribute->attribute_options;

            foreach ($items as $item) {

                if ($item->id == $option_id) {

                    if ($target == 'product_option_text_icon') {

                        Storage::delete($item->text_file_current);

                        $item->text_file_path = null;

                        $item->text_file_current = null;

                        $item->text_extension = null;

                        $item->text_file_root = null;
                    }

                    if ($target == 'product_option_icon') {

                        Storage::delete($item->file_current);

                        $item->file_path = null;

                        $item->file_current = null;

                        $item->extension = null;

                        $item->file_root = null;
                    }

                    break;
                }
            }

            $product_attribute->attribute_options = $items;

            $product->product_attributes = serialize($product_attributes);

            $product->update();
        }



        if (Session::has('product_attribute_options')) {

            $product_attributes = Session::get('product_attribute_options');

            $product_attributes = collect($product_attributes);

            $product_attribute = $product_attributes->where('id', $attribute_id)->first();



            $items = $product_attribute->attribute_options;

            foreach ($items as $item) {

                if ($item->id == $option_id) {

                    $auth_user = Auth()->user();



                    if ($target == 'product_option_text_icon') {

                        $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $item->text_file_path);

                        $item->text_file_path = null;

                        $item->text_file_current = null;

                        $item->text_extension = null;

                        $item->text_file_root = null;
                    }

                    if ($target == 'product_option_icon') {

                        $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $item->file_path);

                        $item->file_path = null;

                        $item->file_current = null;

                        $item->extension = null;

                        $item->file_root = null;
                    }

                    break;
                }
            }

            $product_attribute->attribute_options = $items;

            Session::put([

                'product_attribute_options' => $product_attributes,

            ]);
        }



        return response()->json('success', 200);
    }



    public function destroyAttribute($attribute_id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::find($product_id);

            $items = unserialize($product->product_attributes);

            foreach ($items as $key => $item) {

                if ($item->id == $attribute_id) {

                    if ($item->attribute_options) {

                        $this->deleteAttributeFile($item->attribute_options);
                    }

                    unset($items[$key]);

                    $message = '<strong>' . $item->title . '</strong> deleted successful';

                    break;
                }
            }

            $product->product_attributes = serialize($items);

            $product->update();
        }



        if (Session::has('product_attribute_options')) {

            $items = Session::get('product_attribute_options');

            foreach ($items as $key => $item) {

                if ($item->id == $attribute_id) {

                    unset($items[$key]);

                    $message = '<strong>' . $item->title . '</strong> deleted successful';

                    break;
                }
            }

            Session::put([

                'product_attribute_options' => $items,

            ]);
        }





        return response()->json([

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function deleteAttributeFile($items)
    {



        foreach ($items as $item) {

            if (!empty($item->file_current)) {

                Storage::delete($item->file_current);
            }

            if (!empty($item->text_file_current)) {

                Storage::delete($item->text_file_current);
            }
        }
    }



    public function addAttribute($product_id = null)

    {

        $attribute_data = Session::get('product_attribute_options');



        $items = array();

        if ($attribute_data) {

            $items = $attribute_data->toArray();
        }

        $item = (object)([

            'id' => rand(999, 9999999),

            'product_attribute_id' => rand(999, 9999999),

            'title' => 'new ' . uniqid(),

            'input_type_id' => 1,

            'attribute_type_id' => 1,

            'input_required' => 0,

            'attribute_options' => null,

            'ordering' => rand(999, 9999999),

            'created_at' => Carbon::now()->toDateTimeString(),

            'updated_at' => Carbon::now()->toDateTimeString(),

            'attribute_types' => $this->attribute_types(),

            'input_types' => $this->input_types(),

        ]);

        $message = $item->title . ' added successful';

        if (count($items) > 0) {

            array_push($items, $item);
        } else {

            $items = array($item);
        }



        foreach ($items as $item_data) {

            $items_data[] = (object) $item_data;
        }



        Session::put([

            'product_attribute_options' => collect($items_data),

        ]);



        return response()->json([

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function getProductAttribute($product_id = null)

    {

        if ($product_id) {

            $product = Product::find($product_id);

            // dd($product);

            if ($product->product_attributes) {

                $product_attributes = unserialize($product->product_attributes);

                $session_attributes = Session::get('product_attribute_options');

                $collection = collect($product_attributes->merge($session_attributes));
            } else {

                $session_attributes = Session::get('product_attribute_options');

                $collection = collect($session_attributes);
            }

            $attributes = $collection->paginate($this->itemPerPage);

            // dd($attributes);

        } else {

            $items = Session::get('product_attribute_options');

            $attributes = collect($items)->paginate($this->itemPerPage);
        }

        $sl = SLGenerator($attributes);

        $tab_sl = SLGenerator($attributes);



        if (count($attributes) > 0) {

            return view('admin.jquery_live.product_attribute_items', compact('attributes', 'sl', 'tab_sl', 'product_id'));
        }
    }



    public function getSpecifications($product_id = null)

    {

        if ($product_id) {

            $product = Product::find($product_id);

            if ($product->specifications) {

                $product_specifications = collect(unserialize($product->specifications));

                $session_specifications = collect(Session::get('product_specifications'));

                $collection = collect($product_specifications->merge($session_specifications));
            } else {

                $collection = collect(Session::get('product_specifications'));
            }



            $specifications = $collection->paginate($this->itemPerPage);
        } else {

            $specifications = collect(Session::get('product_specifications'))->paginate($this->itemPerPage);
        }



        $sl = SLGenerator($specifications);

        if (count($specifications) > 0) {

            return view('admin.jquery_live.product_specifications_items', compact('specifications', 'sl', 'product_id'));
        }
    }



    public function addSpecification(Request $request, $product_id = null)

    {

        $validator = Validator::make($request->all(), [

            'specification_title' => 'required|string|max:100',

            'specification_sub_title' => 'nullable|string|max:100',

            'specification_ordering' => 'nullable|numeric',

            'specification_status' => 'nullable|boolean',

        ]);



        if ($validator->fails()) {

            return response()->json([

                'status' => 'failed',

                'messages' => $validator->messages(),

            ], 200);
        }

        $specification_icon = Session::get('product_specification_icon');



        $item = (object)([

            'id' => uniqid(),

            'product_id' => $product_id,

            'title' => $request->get('specification_title'),

            'sub_title' => $request->get('specification_sub_title'),

            'ordering' =>  $request->get('specification_ordering') ?? 0,

            'status' => $request->get('specification_status') ?? 0,

            'file_current' => $specification_icon->product_specification_icon ?? null,

            'file_path' => $specification_icon->file_path ?? null,

            'extension' => $specification_icon->extension ?? null,

            'file_root' => $specification_icon->file_root ?? 0,

        ]);



        if (Session::has('product_specifications')) {

            $specifications = Session::get('product_specifications');

            $specifications_collect = collect($specifications);

            if (!empty($specifications_collect)) {

                $items = $specifications_collect->toArray();

                if (count($items) > 10) {

                    return response()->json(

                        [

                            'status' => 'failed',

                            'message' =>  'limit end'

                        ],

                        200

                    );
                }

                array_push($items, $item);
            }
        } else {

            $items = array($item);
        }



        $specifications_items = collect($items);



        Session::put([

            'product_specifications' => $specifications_items,

        ]);



        Session::forget('product_specification_icon');



        $message = '<strong>' . $item->title . '</strong> added successful';

        return response()->json([

            'items' => $items,

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function editSpecification($id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::find($product_id);

            $product_specifications = unserialize($product->specifications);

            if ($product_specifications) {

                $session_specifications = collect(Session::get('product_specifications'));

                $collection = collect($product_specifications->merge($session_specifications));
            } else {

                $collection = collect(Session::get('product_specifications'));
            }



            $specifications = $collection;
        } else {

            $specifications = Session::get('product_specifications');
        }



        $specifications_collect = collect($specifications);

        foreach ($specifications_collect as $item) {

            if ($item->id == $id) {

                if ($item->file_path) {

                    $item->img_url = $item->file_root . '/' . $item->file_path;
                }

                return response()->json($item, 200);

                break;
            }
        }
    }



    public function updateSpecification(Request $request, $id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_specifications = unserialize($product->specifications);

            foreach ($product_specifications as $product_specification) {

                foreach ($product_specifications as $product_specification) {

                    if ($product_specification->id == $id) {

                        $product_specification->title = $request->get('specification_title');

                        $product_specification->sub_title = $request->get('specification_sub_title');

                        $product_specification->ordering = $request->get('specification_ordering') ?? 0;

                        $product_specification->status = $request->get('specification_status') ?? 0;



                        $message = '<strong>' . $product_specification->title . '</strong> update successful';

                        break;
                    }
                }
            }

            $product->specifications = serialize($product_specifications);

            $product->update();
        }



        if (Session::has('product_specifications')) {

            $specifications = collect(Session::get('product_specifications'));

            foreach ($specifications as $specification) {

                if ($specification->id == $id) {

                    $specification->title = $request->get('specification_title');

                    $specification->sub_title = $request->get('specification_sub_title');

                    $specification->ordering = $request->get('specification_ordering') ?? 0;

                    $specification->status = $request->get('specification_status') ?? 0;



                    $message = '<strong>' . $specification->title . '</strong> update successful';

                    break;
                }
            }

            Session::put([

                'product_specifications' => $specifications,

            ]);
        }



        return response()->json([

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function deleteSpecification($id, $product_id = null)

    {

        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_specifications = unserialize($product->specifications);

            foreach ($product_specifications as $key => $product_specification) {

                foreach ($product_specifications as $product_specification) {

                    if ($product_specification->id == $id) {

                        if ($product_specification->file_current) {

                            Storage::delete($product_specification->file_current);
                        }

                        unset($product_specifications[$key]);

                        $message = '<strong>' . $product_specification->title . '</strong> deleted successful';

                        break;
                    }
                }
            }

            $product->specifications = serialize($product_specifications);

            $product->update();
        }



        if (Session::has('product_specifications')) {

            $specifications = collect(Session::get('product_specifications'));

            foreach ($specifications as $key => $specification) {

                if ($specification->id == $id) {

                    if ($specification->file_current) {

                        Storage::delete($specification->file_current);
                    }

                    unset($specifications[$key]);

                    $message = '<strong>' . $specification->title . '</strong> update successful';

                    break;
                }
            }

            Session::put([

                'product_specifications' => $specifications,

            ]);
        }



        return response()->json([

            'message' => $message,

            'status' => 'success',

        ], 200);
    }



    public function specificationFileUpload(Request $request, $target, $id, $product_id = null)

    {

        $validator = Validator::make($request->all(), [

            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,

        ]);



        if ($validator->fails()) {

            foreach ($validator->messages()->all() as $key => $message) {

                return response()->json([

                    'status' => 'failed',

                    'message' =>  $message

                ], 200);

                break;
            }
        }



        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_specifications = unserialize($product->specifications);

            foreach ($product_specifications as $product_specification) {

                if ($product_specification->id == $id) {



                    if ($product_specification->file_current) {

                        Storage::delete($product_specification->file_current);
                    }

                    $image_name = 'specification_icon_' . rand(99, 999999999) . '.' . $request->$target->extension();

                    $folderName = $this->folderName() . '/' . $product->id . '/specifications';



                    $request->$target->storeAs($this->folderPath . $folderName, $image_name);



                    $product_specification->extension = $request->$target->extension();

                    $product_specification->file_current = $this->folderPath . $folderName . '/' . $image_name;

                    $product_specification->file_path = $folderName . '/' . $image_name;

                    $product_specification->file_root = asset('storage/products/');



                    $url = $product_specification->file_root . '/' . $product_specification->file_path;



                    break;
                }
            }

            $product->specifications = serialize($product_specifications);

            $product->update();
        }





        if (Session::has('product_specifications')) {

            $specifications = collect(Session::get('product_specifications'));

            foreach ($specifications as $specification) {

                if ($specification->id == $id) {

                    $imgName = $target . "_" . $specification->id . uniqid() . '.' . $request->$target->extension();

                    $folderName = $this->folderName();

                    $auth_user = Auth()->user();



                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $specification->file_path);

                    $request->$target->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);

                    $specification->file_path = $folderName  . '/' . $imgName;

                    $specification->file_current = $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName  . '/' . $imgName;

                    $specification->extension = $request->$target->extension();

                    $specification->file_root = asset('storage/session_files/' . $auth_user->username . '/' . $target . '/');



                    $url = url('storage/session_files/' . $auth_user->username . '/' . $target . '/' . $folderName . '/' . $imgName);

                    break;
                }
            }

            Session::put([

                'product_specifications' => $specifications,

            ]);
        }



        return response()->json(

            [

                'status' => 'success',

                'file_url' => $url

            ],

            200

        );
    }



    public function specificationFileRemove($target, $id, $product_id = null)

    {



        if ($product_id) {

            $product = Product::findOrFail($product_id);

            $product_specifications = unserialize($product->specifications);

            foreach ($product_specifications as $key => $product_specification) {

                foreach ($product_specifications as $product_specification) {

                    if ($product_specification->id == $id) {

                        Storage::delete($product_specification->file_current);

                        $product_specification->file_path = null;

                        $product_specification->file_current = null;

                        $product_specification->extension = null;

                        $product_specification->file_root = null;

                        break;
                    }
                }
            }

            $product->specifications = serialize($product_specifications);

            $product->update();
        }



        if (Session::has('product_specifications')) {

            $specifications = collect(Session::get('product_specifications'));

            foreach ($specifications as $key => $specification) {

                if ($specification->id == $id) {

                    $auth_user = Auth()->user();

                    $this->deleteFile($this->sessionFolderPath . $auth_user->username . '/' . $target, $specification->file_path);

                    $specification->file_path = null;

                    $specification->file_current = null;

                    $specification->extension = null;

                    $specification->file_root = null;

                    break;
                }
            }

            Session::put([

                'product_specifications' => $specifications,

            ]);
        }



        return response()->json('success', 200);
    }

    public function product_inventory_status($id, Request $request)
    {



        $Product = Product::find($id);

        $request->validate([

            'inventory_status' => 'required',

            'estimated_delivery_days' => 'nullable',

        ]);

        // dd($ProductItem);


        $Product->inventory_status = $request->inventory_status;

        $Product->estimated_delivery_days = $request->inventory_status === 'in_stock_otm'
            ? ($request->estimated_delivery_days ?? 0)
            : null;




        $Product->save();

        return redirect('/admin/products')->with('success', 'Product updated successfully!');
    }
}
