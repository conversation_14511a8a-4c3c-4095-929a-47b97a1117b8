<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AlertPreference;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateGroup;
use Illuminate\Http\Request;

class EmailTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Email Templates';
        $items = EmailTemplate::orderBy('email_templates.id', 'desc')->paginate($this->itemPerPage);
        foreach ($items as $value) {
            $value->email_template_group = EmailTemplateGroup::find($value->email_template_group_id);
            if ($value->system_type) {
                $value->system_type = $this->email_system_types($value->system_type);
            }
            if ($value->alert_preference_id) {
                $value->alert_preference = AlertPreference::find($value->alert_preference_id);
            }
        }
        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.email_templates', compact('items', 'sl'));
        }
        // dd($items);

        return view('admin.email_template.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Email Template';
        $email_template_groups = EmailTemplateGroup::where('status', true)->get();
        $email_constants = $this->email_constants();
        $email_system_types = $this->email_system_types();
        $alert_preferences = AlertPreference::where('status', true)->get();

        $email_templates = EmailTemplate::get();
        $selected_templates = [];
        foreach ($email_templates as $value) {
            if ($value->system_type) {
                $selected_templates[] = $value->system_type;
            }
        }
        $whatsapp_templates = getSmsTemplates();
        return view('admin.email_template.create', compact('admin_page_title', 'email_template_groups', 'email_constants', 'alert_preferences', 'email_system_types', 'selected_templates', 'whatsapp_templates'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'email_template_group_id' => 'nullable|exists:email_template_groups,id',
            'alert_preference_id' => 'nullable|exists:alert_preferences,id',
            'subject' => 'required|string|max:255',
            'pre_header' => 'nullable|string|max:455',
            'email_body' => 'required|string',
            'email_system_type' => 'nullable|string',
            'sms_body' => 'nullable|string',
            'status' => 'nullable|boolean',
        ]);

        $item = new EmailTemplate;
        $item->email_template_group_id = $request->email_template_group_id;
        $item->alert_preference_id = $request->alert_preference_id;
        $item->subject = $request->subject;
        $item->pre_header = $request->pre_header;
        $item->system_type = $request->email_system_type;
        $item->email_body = serialize($request->email_body);
        $item->sms_body = serialize($request->sms_body);
        $item->sms_status = $request->sms_status ?? 0;
        $item->status = $request->status ?? 0;
        $item->save();

        $message = 'template successfully added.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.email_templates.index')->with('success', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmailTemplate  $emailTemplate
     * @return \Illuminate\Http\Response
     */
    public function show(EmailTemplate $emailTemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EmailTemplate  $emailTemplate
     * @return \Illuminate\Http\Response
     */
    public function edit(EmailTemplate $emailTemplate)
    {
        $admin_page_title = 'Edit Email Template';
        // $email_template_types = $this->email_template_types();
        $email_template_groups = EmailTemplateGroup::where('status', true)->get();
        foreach ($email_template_groups as $value) {
            $value->status_title = $this->orderStatus($value->order_status_id);
        }
        // dd($email_template_groups);
        $email_constants = $this->email_constants();
        $email_system_types = $this->email_system_types();
        $emailTemplate->email_body = unserialize($emailTemplate->email_body);
        $emailTemplate->sms_body = unserialize($emailTemplate->sms_body);
        $alert_preferences = AlertPreference::where('status', true)->get();
        $item = $emailTemplate;
        $email_templates = EmailTemplate::get();
        $selected_templates = [];
        foreach ($email_templates as $value) {
            if ($value->system_type) {
                $selected_templates[] = $value->system_type;
            }
        }
        $whatsapp_templates = getSmsTemplates();
        return view('admin.email_template.edit', compact('admin_page_title', 'email_template_groups', 'email_constants', 'alert_preferences', 'email_system_types', 'item', 'selected_templates', 'whatsapp_templates'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EmailTemplate  $emailTemplate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmailTemplate $emailTemplate)
    {
       
        $this->validate($request, [
            'email_template_group_id' => 'nullable|exists:email_template_groups,id',
            'alert_preference_id' => 'nullable|exists:alert_preferences,id',
            'subject' => 'required|string|max:255',
            'pre_header' => 'nullable|string|max:455',
            'email_body' => 'required|string',
            'sms_body' => 'nullable|string',
            'email_system_type' => 'nullable|string',
            'status' => 'nullable|boolean',
        ]);
        // dd($request->email_system_type);
        $emailTemplate->email_template_group_id = $request->email_template_group_id;
        $emailTemplate->alert_preference_id = $request->alert_preference_id;
        $emailTemplate->subject = $request->subject;
        $emailTemplate->system_type = $request->email_system_type ??  $emailTemplate->system_type;
        $emailTemplate->pre_header = $request->pre_header;
        $emailTemplate->email_body = serialize($request->email_body);
        $emailTemplate->sms_body = serialize([
            'body' => $request->sms_body,
            'template_id' => $request->whatsapp_template_id,
        ]);
        $emailTemplate->sms_status = $request->sms_status ?? 0;
        $emailTemplate->status = $request->status ?? 0;
        $emailTemplate->update();

        $message = 'template successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.email_templates.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmailTemplate  $emailTemplate
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmailTemplate $emailTemplate)
    {
        $type = $this->email_template_types($emailTemplate->type);
        $emailTemplate->delete();

        $message = '<strong>' . $type . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }
}
