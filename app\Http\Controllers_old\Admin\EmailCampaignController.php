<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateGroup;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Carbon\Carbon;

class EmailCampaignController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Email Campaigns';
        $items = EmailCampaign::orderBy('email_campaigns.id', 'desc')->paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.email_campaigns', compact('items', 'sl'));
        }

        return view('admin.email_template.campaign.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Email Campaign';
        $email_template_groups = EmailTemplateGroup::where([
            ['type', 'dynamic_cron'],
            'status' => true,
        ])
        ->get();
        $user_groups = UserGroup::where([
            ['id','!=',1],
            ['id','!=',2],
            ['id','!=',6],
            'status' => true
        ])->get();
        $frequency_types = $this->frequency_types();
        $email_constants = $this->email_constants();
        return view('admin.email_template.campaign.create', compact('admin_page_title', 'email_template_groups', 'frequency_types', 'email_constants', 'user_groups'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string|max:100',
            'user_groups*' => 'required|exists:users_groups,id',
            'frequency_type' => 'required|string|max:5',
            'frequency_date' => 'nullable|date',
            'frequency_time*' => 'required|date_format:h:i A',
            'expire_date' => 'nullable|date',
            'subject' => 'required|string|max:100',
            'pre_header' => 'required|string|max:100',
            'email_body' => 'required|string',
            'status' => 'nullable|boolean',
        ]);

        if ($request->frequency_type == 'date') {
            $this->validate($request,[
                'frequency_date' => 'required|date',
            ]);
        }

        if ($request->frequency_type == 'days') {
            $this->validate($request, [
                'frequency_days' => 'required',
                'expire_date' => 'required|date',
            ]);
        }

        if ($request->frequency_type == 'daily') {
            $this->validate($request, [
                'expire_date' => 'required|date',
            ]);
        }

        foreach ($request->frequency_time as $value) {
            $frequency_time[] = Carbon::parse($value)->format('H:i');
            // $frequency_time[] = Carbon::parse($value)->format('H:i:s');
        }

        $item = new EmailCampaign;
        $item->title = $request->title;
        $item->user_groups_id = implode(',', $request->user_groups);
        $item->frequency_type = $request->frequency_type;
        $item->frequency_date = $this->formatDate($request->get('frequency_date'));
        $item->frequency_time = implode(',', $frequency_time);
        // $item->frequency_time = Carbon::parse($request->frequency_time)->format('H:i:s');
        $item->expire_date = $this->formatDate($request->get('expire_date'));
        $item->frequency_days = $request->frequency_days;
        $item->subject = $request->subject;
        $item->pre_header = $request->pre_header;
        $item->email_body = serialize($request->email_body);
        $item->sms_body = serialize($request->sms_body);
        $item->sms_status = $request->sms_status ?? 0;
        $item->status = $request->status ?? 0;
        $item->save();

        $message = 'template successfully added.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.email_campaigns.index')->with('success', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmailCampaign  $emailCampaign
     * @return \Illuminate\Http\Response
     */
    public function show(EmailCampaign $emailCampaign)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EmailCampaign  $emailCampaign
     * @return \Illuminate\Http\Response
     */
    public function edit(EmailCampaign $emailCampaign)
    {
        $admin_page_title = 'Edit Email Campaign';
        $email_template_groups = EmailTemplateGroup::where([
            ['type', 'dynamic_cron'],
            'status' => true,
        ])
        ->get();
        $email_constants = $this->email_constants();
        $frequency_types = $this->frequency_types();
        $user_groups = UserGroup::where([
            ['id', '!=', 1],
            ['id', '!=', 2],
            ['id', '!=', 6],
            'status' => true
        ])->get();

        $emailCampaign->frequency_date = $this->undoFormatDate($emailCampaign->frequency_date);
        $emailCampaign->expire_date = $this->undoFormatDate($emailCampaign->expire_date);
        foreach(explode(',', $emailCampaign->frequency_time) as $value){
            if (!empty(($value))) {
                $frequency_time[] = Carbon::parse($value)->format('h:i A');
                // $frequency_time[] = Carbon::parse($value)->format('h:i A');
            }
        }
        $emailCampaign->frequency_time = $frequency_time;
        $emailCampaign->email_body = unserialize($emailCampaign->email_body);
        $emailCampaign->sms_body = unserialize($emailCampaign->sms_body);
        $emailCampaign->user_groups_id = explode(',',$emailCampaign->user_groups_id);
        $item = $emailCampaign;

        return view('admin.email_template.campaign.edit', compact('admin_page_title', 'email_template_groups', 'email_constants', 'frequency_types', 'user_groups', 'item'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EmailCampaign  $emailCampaign
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmailCampaign $emailCampaign)
    {
        $this->validate($request, [
            'title' => 'required|string|max:100',
            'user_groups*' => 'required|exists:users_groups,id',
            'frequency_type' => 'required|string|max:5',
            'frequency_date' => 'nullable|date',
            'frequency_time*' => 'required|date_format:h:i A',
            'expire_date' => 'nullable|date',
            'subject' => 'required|string|max:100',
            'pre_header' => 'required|string|max:100',
            'email_body' => 'required|string',
            'status' => 'nullable|boolean',
        ]);

        if ($request->frequency_type == 'date') {
            $this->validate($request, [
                'frequency_date' => 'required|date',
            ]);
        }

        if ($request->frequency_type == 'days') {
            $this->validate($request, [
                'frequency_days' => 'required',
                'expire_date' => 'required|date',
            ]);
        }

        if ($request->frequency_type == 'daily') {
            $this->validate($request, [
                'expire_date' => 'required|date',
            ]);
        }
        foreach($request->frequency_time as $value){
            if(!empty(($value))){
                $frequency_time[] = Carbon::parse($value)->format('H:i');
                // $frequency_time[] = Carbon::parse($value)->format('H:i:s');
            }
        }

        $emailCampaign->title = $request->title;
        $emailCampaign->user_groups_id = implode(',', $request->user_groups);
        $emailCampaign->frequency_type = $request->frequency_type;
        $emailCampaign->frequency_date = $this->formatDate($request->get('frequency_date'));
        $emailCampaign->frequency_time = implode(',', $frequency_time);
        // $emailCampaign->frequency_time = Carbon::parse($request->frequency_time)->format('H:i:s');
        $emailCampaign->expire_date = $this->formatDate($request->get('expire_date'));
        $emailCampaign->frequency_days = $request->frequency_days;
        $emailCampaign->subject = $request->subject;
        $emailCampaign->pre_header = $request->pre_header;
        $emailCampaign->email_body = serialize($request->email_body);
        $emailCampaign->status = $request->status ?? 0;
        $emailCampaign->sms_body = serialize($request->sms_body);
        $emailCampaign->sms_status = $request->sms_status ?? 0;
        $emailCampaign->update();

        $message = 'template successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.email_campaigns.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmailCampaign  $emailCampaign
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmailCampaign $emailCampaign)
    {
        $emailCampaign->delete();

        $message = '<strong>' . $emailCampaign->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }

    public function getEmailTemplates($id){
        $group = EmailTemplateGroup::find($id);
        $items  = EmailTemplate::where('email_template_group_id', $group->id)->get();

        return view('admin.jquery_live.email_template_options', compact('items'));
    }

    public function getEmailTemplatesOrderStatus($id){
        $email_template_groups = EmailTemplateGroup::where('order_status_id', $id)->get();
        foreach($email_template_groups as $value){
            $email_template_groups_array [] = $value->id;
        }
        $items  = EmailTemplate::whereIn('email_template_group_id', array_unique($email_template_groups_array))->get();

        return view('admin.jquery_live.email_template_options', compact('items'));
    }

    public function getEmailTemplate($id){
        $item  = EmailTemplate::find($id);
        if(!empty($item->email_body)){
            $item->email_body = unserialize($item->email_body);
        }
        if(!empty($item->sms_body)){
            $item->sms_body = unserialize($item->sms_body);
        }
        return response()->json($item, 200);
    }
}
