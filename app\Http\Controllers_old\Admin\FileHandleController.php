<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Session;
use Validator;
use Illuminate\Support\Facades\Storage;

class FileHandleController extends Controller
{
    public function fileUploadTemp(Request $request, $target)
    {
        $file = $request->file($target);
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }

        $image_path = $file->getPath() . '/' . $file->getFilename();
        $image_data = file_get_contents($image_path);
        $image_base_64_url = 'data:image/' . $file->extension() . ';base64,' . base64_encode($image_data);

        $auth_user = Auth()->user();
        $imgName = $target . '_' . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $path = $request->file($target)->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $folderName, $imgName);
        $file_path = $folderName.'/' . $imgName;
        $file_root = asset('storage/session_files/' . $auth_user->username . '/' . $target . '/');

        $image_info = (object)([
            $target => $path,
            'file_current' => $this->sessionFolderPath . $auth_user->username . '/' . $target . '/'.$file_path,
            'file_path' => $file_path,
            'file_root' => $file_root,
            'extension' => $file->extension(),
        ]);

        Session::put([
            $target => $image_info,
        ]);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $image_base_64_url
            ],
            200
        );
    }

    public function fileRemoveTemp($target)
    {
        $auth_user = Auth()->user();
        $image = Session::get($target);
        // Storage::deleteDirectory($this->sessionFolderPath . $auth_user->username . '/' . $target);
        Storage::delete($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $image->file_path);
        Session::forget($target);

        return response()->json('success', 200);
    }

    public function filesUploadTemp(Request $request, $target)
    {
        // dd($request->all());
        $store_folder = $target;
        $file = $request->file($target);

        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }

        $image_path = $file->getPath() . '/' . $file->getFilename();
        $image_data = file_get_contents($image_path);
        $image_base_64_url = 'data:image/' . $file->extension() . ';base64,' . base64_encode($image_data);

        $auth_user = Auth()->user();
        $imgName = $target . '_' . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $path = $request->file($target)->storeAs($this->sessionFolderPath . $auth_user->username . '/' . $target.'/' . $folderName, $imgName);
        $file_path = $folderName .'/'. $imgName;

        $image_info = (object)([
            'id' => uniqid(),
            'file_id' => uniqid(),
            $target => $path,
            'file_current' => $this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $file_path,
            'file_path' => $file_path,
            'extension' => $file->extension(),
            'file_caption' => null,
            'file_order' => time(),
            'layout' => $request->view,
        ]);
        // dd($image_info);
        if (Session::has($target)) {
            $images = Session::get($target);
            if (count($images) > 10) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  'upload limit end'
                    ],
                    200
                );
            }
            array_push($images, $image_info);
        } else {
            $images = array($image_info);
        }

        Session::put([
            $target => $images,
        ]);

        $imagesCollection = collect(Session::get($target));
        $images = $imagesCollection->sortBy('file_order');
        $item = null;

        return view('admin.jquery_live.images', compact('images', 'item', 'store_folder'));
    }

    public function filesRemoveTemp($target, $id)
    {
        $auth_user = Auth()->user();
        $images = Session::get($target);
        foreach ($images as $key => $image) {
            if ($image->file_id == $id) {
                Storage::delete($this->sessionFolderPath . $auth_user->username . '/' . $target . '/' . $image->file_path);
                unset($images[$key]);
                break;
            }
        }
        Session::put([
            $target => $images,
        ]);

        if (count($images) < 0) {
            Session::forget($target);
        }

        return response()->json('files_success', 200);
    }

    public function updateImageInfoTemp(Request $request, $target)
    {
        $store_folder = $target;
        if ($request->action == 'get_info') {
            $images = Session::get($target);
            foreach ($images as $image) {
                if ($image->file_id == $request->imageId) {
                    return response()->json($image);
                }
            }
        }

        $validator = Validator::make($request->all(), [
            'caption' => 'required|string',
            'file_ordering' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    'status' => 'failed',
                    'message' =>  $validator->messages()
                ],
                200
            );
        }

        $images = Session::get($target);
        foreach ($images as $image) {
            if ($image->file_id == $request->imageId) {
                $image->file_caption = $request->caption;
                $image->file_order = $request->file_ordering ?? 0;
            }
        }
        Session::put([
            $target => $images,
        ]);

        $imagesCollection = collect(Session::get($target));
        $images = $imagesCollection->sortBy('file_order');
        $item = null;

        return view('admin.jquery_live.images', compact('images', 'item', 'store_folder'));
    }

    public function updateFileInfoTemp(Request $request, $target)
    {

        if ($request->action == 'get_info') {
            $files = Session::get($target);
            foreach ($files as $image) {
                if ($image->file_id == $request->imageId) {
                    return response()->json($image);
                }
            }
        }

        $validator = Validator::make($request->all(), [
            'caption' => 'required|string',
            'file_order' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    'status' => 'failed',
                    'target' => $target,
                    'message' =>  $validator->messages()
                ],
                200
            );
        }

        $files = Session::get($target);
        foreach ($files as $image) {
            if ($image->file_id == $request->imageId) {
                $image->file_info = $request->caption;
                $image->file_order = $request->file_order ?? 0;
            }
        }
        Session::put([
            $target => $files,
        ]);

        $filesCollection = collect(Session::get($target));
        $files = $filesCollection->sortBy('file_order');
        $item = null;

        return view('admin.jquery_live.files', compact('files', 'item', 'target'));
    }
}
