<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShippingDetailsToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->after('product_attribute_id', function ($table) {
                $table->string('shipping_weight')->nullable();
                $table->string('shipping_length')->nullable();
                $table->string('shipping_height')->nullable();
                $table->string('shipping_width')->nullable();
                $table->integer('box_type')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('shipping_weight');
            $table->dropColumn('shipping_length');
            $table->dropColumn('shipping_height');
            $table->dropColumn('shipping_width');
            $table->dropColumn('box_type');
        });
    }
}
