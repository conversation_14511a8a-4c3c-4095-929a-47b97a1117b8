<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;
use Illuminate\Support\Facades\Auth;
use App\Models\AccessLabel;
use App\Models\Section;
use App\Models\SlideCategory;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;

class PageController extends Controller
{
    private $folderPath = 'pages/';

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Pages';
        if (!Auth::user()->permissions('page')) return redirect('/' . Auth::user()->username)->with('warning', 'You are un-authorized to access here');

        if ($request->action == 'search') {
            $menu_location = $request->input('menu_location');
            if (empty($menu_location)) {
                $this->validate($request, [
                    'query' => 'required|min:3',
                ]);
            }
            $keyword = $request->input('query');

            $collection  = new Collection($this->pages($menu_location, null, $keyword, true));
            $pages =  $collection->paginate($this->itemPerPage)->setPath('');
        } else {
            $collection  = new Collection($this->pages());
            $pages =  $collection->paginate($this->itemPerPage)->setPath('');
        }

        if ($request->action == 'clear') {
            return redirect()->route('admin.pages.index');
        }

        $menu_location = $this->menuLocation();
        $sl = SLGenerator($pages);

        if ($request->view == 'html') {
            return view('admin.jquery_live.pages', compact('items', 'sl'));
        }
        return view('admin.page.index', compact('sl', 'pages', 'menu_location', 'admin_page_title'));
    }



    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Page';
        $parent_menu_list = DB::table('pages')
        ->where('menu_name', '!=', '')
        ->orderBy('ordering', 'asc')
        ->get();

        $page_type = (object)array(
            'build_page' => 'System page',
            'component_page' => 'Component',
        );

        $menu_location = $this->menuLocation();

        $accessLabels = AccessLabel::whereStatus(true)->get(['id', 'title']);
        $header_image_info = Session::get('header_image');
        $article_image_info = Session::get('article_image');
        $fa_icons = $this->fa_icons();

        $module_types = $this->module_types();

        $sections = array();
        $sections_sl = null;
        if (Session::has('page_sections')) {
            $sections = collect(Session::get('page_sections'))->sortBy('ordering')->paginate($this->itemPerPage);
            $sections_sl = SLGenerator($sections);
        }
        $slideshow = SlideCategory::get();
        // dd($sections);
        return view('admin.page.create', compact('fa_icons', 'sections', 'slideshow', 'sections_sl', 'module_types', 'parent_menu_list', 'accessLabels', 'header_image_info', 'article_image_info', 'menu_location', 'page_type', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if ($request->custom_url) {
            $this->validate($request, [
                'title' => 'required|string|max:255',
                'menu_location' => 'nullable|string',
                'slug' => 'required|string|url',
                'content' => 'nullable|string',
                'header_image' => 'nullable|image|max:' . $this->maxFileSize,
                'article_image' => 'nullable|image|max:' . $this->maxFileSize,
                'status' => 'nullable|boolean',
                'is_menu' => 'nullable|boolean',
                'url_type' => 'nullable|string',
                'menu_name' => 'nullable|string|max:150',
                'menu_icon' => 'nullable|string|max:150',
                'parent_menu_id' => 'nullable|integer',
                'ordering' => 'nullable|integer',
                'access_label_id' => 'required|integer',
                'meta_title' => 'nullable|string',
                'meta_description' => 'nullable|string',
                'meta_keyword' => 'nullable|string'
            ]);
        } else {
            $this->validate($request, [
                'title' => 'required|string|max:255',
                'menu_location' => 'nullable|string',
                'content' => 'nullable|string',
                'header_image' => 'nullable|image|max:' . $this->maxFileSize,
                'article_image' => 'nullable|image|max:' . $this->maxFileSize,
                'status' => 'nullable|boolean',
                'is_menu' => 'nullable|boolean',
                'url_type' => 'nullable|string',
                'menu_name' => 'nullable|string|max:150',
                'menu_icon' => 'nullable|string|max:150',
                'parent_menu_id' => 'nullable|integer',
                'ordering' => 'nullable|integer',
                'access_label_id' => 'required|integer',
                'meta_title' => 'nullable|string',
                'meta_description' => 'nullable|string',
                'meta_keyword' => 'nullable|string'
            ]);
        }

        $page = new Page;
        $page->title = $request->title;
        if ($request->get('access_label_id') != 'public') {
            $page->access_label_id = $request->get('access_label_id');
        }

        if ($request->url_type == 'custom_url') {
            $page->slug = $request->slug;
        } else {
            if ($request->slug) {
                $item_slug = Str::slug($request->slug, '-');
            } else {
                $item_slug = Str::slug($request->title, '-');
            }

            $validator = Validator::make(['slug' => $item_slug], [
                'slug' => 'required|unique:pages,slug'
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
            }
            $page->slug = $item_slug;
        }

        $page->content = $request->content;

        $header_image_info = Session::get('header_image');
        if ($header_image_info) {
            $image_name = $item_slug . '_' . 'header' . "_" . $page->id . uniqid() . '.' . $header_image_info->extension;
            $folderName = $this->folderName() . $item_slug . '/';
            Storage::move($header_image_info->header_image, $this->folderPath . $folderName . '/' . $image_name);
            $page->header_image = $folderName . '/' . $image_name;
            Session::forget('header_image');
        }

        if ($request->hasFile('header_image')) {
            $imgName = $item_slug . '_' . 'header_' . uniqid() . '.' . $request->header_image->extension();
            $request->header_image->storeAs($this->folderPath, $imgName);
            $page->header_image = $imgName;
        }

        $article_image_info = Session::get('article_image');
        if ($article_image_info) {
            $image_name = $item_slug . '_' . 'article' . "_" . $page->id . uniqid() . '.' . $article_image_info->extension;
            $folderName = $this->folderName() . $item_slug . '/';
            Storage::move($article_image_info->article_image, $this->folderPath . $folderName.'/' . $image_name);
            $page->article_image = $folderName.'/' . $image_name;
            Session::forget('article_image');
        }

        if ($request->hasFile('article_image')) {
            $imgName = $item_slug . '_' . 'article_' . uniqid() . '.' . $request->article_image->extension();
            $request->article_image->storeAs($this->folderPath, $imgName);
            $page->article_image = $imgName;
        }

        $page->page_type = $request->page_type;
        $page->page_key = $request->page_key;

        $page->menu_location = $request->menu_location;
        $page->meta_title = $request->meta_title;
        $page->meta_description = $request->meta_description;
        $page->meta_keyword = $request->meta_keyword;
        $page->menu_icon = $request->menu_icon;
        $page->is_menu = ($request->is_menu > 0 ? $request->is_menu : '0');
        $page->menu_name = $request->menu_name;
        $page->ordering = ($request->ordering > 0 ? $request->ordering : '0');
        $page->parent_menu_id = $request->parent_menu_id;
        $page->status = $request->status ?? 0;
        $page->url_type = $request->url_type;

        $page_meta = (object)[
            'component_category_id' => $request->category_id ? implode(',', $request->category_id) : [],
        ];
        $page->page_meta = serialize($page_meta);
        $page->save();

        if (Session::has('page_sections')) {
            $sections = Session::get('page_sections');
            foreach($sections as $value){
                $item = new Section();
                $item->title =  $value->title;
                $item->page_id = $page->id;
                $item->section_position = $value->section_position;
                $item->module_type = $value->module_type;
                $item->section_meta = serialize($value->section_meta);
                $item->remark = $value->remark ?? null;
                $item->ordering = $value->ordering ?? 0;
                $item->status = $value->status ?? 0;
                $item->save();
            }
        }

        Session::forget('page_sections');

        $message = 'Page successfully added.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.pages.index')->with('success', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Page  $page
     * @return \Illuminate\Http\Response
     */
    public function show(Page $page)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Page  $page
     * @return \Illuminate\Http\Response
     */
    public function edit(Page $page)
    {
        $admin_page_title = 'Edit Page';
        $fa_icons = $this->fa_icons();
        $parent_menu_list = DB::table('pages')
            ->where([
                ['pages.id', '!=', $page->id],
                ['menu_name', '!=', ''],
            ])
            ->orderBy('ordering', 'asc')
            ->get();
        $page_type = (object)array(
            'build_page' => 'System page',
            'component_page' => 'Component',
        );
        $accessLabels = AccessLabel::whereStatus(true)->get(['id', 'title']);
        $menu_location = $this->menuLocation();
        if ($page->page_meta) {
            $page->page_meta = unserialize($page->page_meta);
        }
        $page->module_types = $this->module_types();

        $sections = Section::where('page_id', $page->id)->orderBy('sections.ordering')->paginate($this->itemPerPage);
        foreach($sections as $value){
            $value->section_meta = unserialize($value->section_meta);
        }
        $sections_sl = SLGenerator($sections);
        $page->sections = $sections;
        $page->slideshow = SlideCategory::get();

        return view('admin.page.edit', compact('page', 'sections_sl', 'parent_menu_list', 'accessLabels', 'fa_icons', 'menu_location', 'page_type', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Page  $page
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Page $page)
    {
        if ($request->url_type == 'custom_url') {
            $this->validate($request, [
                'title' => 'required|string|max:255',
                'menu_location' => 'nullable|string',
                'slug' => 'required|string',
                'content' => 'nullable|string',
                'header_image' => 'nullable|image|max:' . $this->maxFileSize,
                'article_image' => 'nullable|image|max:' . $this->maxFileSize,
                'status' => 'nullable|boolean',
                'in_build_page' => 'nullable|string|alpha_dash',
                'is_menu' => 'nullable|boolean',
                'url_type' => 'nullable|string',
                'menu_name' => 'nullable|string|max:150',
                'menu_icon' => 'nullable|string|max:150',
                'parent_menu_id' => 'nullable|integer',
                'ordering' => 'nullable|integer',
                'access_label_id' => 'required|integer',
                'meta_title' => 'nullable|string',
                'meta_description' => 'nullable|string',
                'meta_keyword' => 'nullable|string'
            ]);
        } else {
            $this->validate($request, [
                'title' => 'required|string|max:255',
                'menu_location' => 'nullable|string',
                'slug' => 'required|string|alpha_dash|unique:pages,slug,' . $page->id,
                'content' => 'nullable|string',
                'header_image' => 'nullable|image|max:' . $this->maxFileSize,
                'article_image' => 'nullable|image|max:' . $this->maxFileSize,
                'status' => 'nullable|boolean',
                'in_build_page' => 'nullable|string|alpha_dash',
                'is_menu' => 'nullable|boolean',
                'url_type' => 'nullable|string',
                'menu_name' => 'nullable|string|max:150',
                'menu_icon' => 'nullable|string|max:150',
                'parent_menu_id' => 'nullable|integer',
                'ordering' => 'nullable|integer',
                'access_label_id' => 'required|integer',
                'meta_title' => 'nullable|string',
                'meta_description' => 'nullable|string',
                'meta_keyword' => 'nullable|string'
            ]);
        }

        $page = Page::findOrFail($page['id']);
        $page->title = $request->title;

        if ($request->get('access_label_id') != 'public') {
            $page->access_label_id = $request->get('access_label_id');
        }

        // if ($request->url_type == 'custom_url') {
        //     $page->slug = $request->slug;
        // } else {
        //     $page->slug = Str::slug($request->slug);
        // }

        if ($request->url_type == 'custom_url') {
            $page->slug = $request->slug;
        } else {
            if ($request->slug) {
                $item_slug = Str::slug($request->slug,'-');
            } else {
                $item_slug = Str::slug($request->title, '-');
            }

            $validator = Validator::make(['slug' => $item_slug], [
                'slug' => 'required|unique:pages,slug,' . $page->id
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
            }
            $page->slug = $item_slug;
        }

        $page_meta = (object)[
            'component_category_id' => $request->category_id ? implode(',',$request->category_id) : [],
        ];
        $page->page_meta = serialize($page_meta);
        $page->page_type = $request->page_type;
        $page->page_key = $request->page_key;
        $page->content = $request->content;
        $page->menu_location = $request->menu_location;
        $page->meta_title = $request->meta_title;
        $page->meta_description = $request->meta_description;
        $page->meta_keyword = $request->meta_keyword;
        $page->is_menu = ($request->is_menu > 0 ? $request->is_menu : '0');
        $page->menu_name = ($request->menu_name != "" ? $request->menu_name : '');
        $page->ordering = ($request->ordering > 0 ? $request->ordering : '0');
        $page->parent_menu_id = $request->parent_menu_id;
        $page->menu_icon = $request->menu_icon;
        $page->status = ($request->status > 0 ? $request->status : '0');
        $page->url_type = $request->url_type;
        $page->update();

        $message = 'Page successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.pages.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Page  $page
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $page = Page::findOrFail($id);
        if ($page->header_image) {
            $get_folder_name = explode('/', $page->header_image);
            Storage::deleteDirectory($this->folderPath . $get_folder_name[0] . '/' . $get_folder_name[1] . '/' . $page->slug);
        }

        $page->delete();

        $message = '<strong>' . $page->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }

    /**
     * Upload File Function For This Component
     */
    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }

        $page = Page::findOrFail($id);

        $this->deleteFile($this->folderPath, $page->$target);

        $imgName = $target . "_" . $page->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath .'/'. $folderName, $imgName);

        $page->$target = $folderName . '/' . $imgName;
        $page->update();

        $url = url('/storage/pages/' . $folderName. '/' . $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    /**
     * Remove File Function For This Component
     *
     * @param string target target key is table key
     * @return boolean
     */
    public function fileRemove($target, $id)
    {
        $page = Page::findOrFail($id);

        $this->deleteFile($this->folderPath, $page->$target);

        $page->$target = null;
        $page->update();

        return response()->json('success', 200);
    }

    public function pageTypes(Request $request)
    {
        $page_type = $request->page_type;
        $page_key = $request->page_key;
        if ($page_type == 'build_page') {
            $results = (object)array(
                'home' => 'Home',
                'my_profile' => 'Profile',
                'my_orders' => 'Orders',
                'change_password' => 'Change Password',
                'cart' => 'Cart',
                'products' => 'Products',
                'product_details' => 'Product Details',
                'order_details' => 'Order Details',
                'checkout' => 'Checkout',
                'thank_you' => 'Thank',
                'track_my_order' => 'Track my order',
            );

            return view('admin.jquery_live.page_types', compact('results', 'page_type', 'page_key'));
        }

        $results = DB::table('components')
            ->where([
                ['components.key', '!=', 'user'],
                ['components.key', '!=', 'page'],
                ['components.key', '!=', 'setting'],
                ['components.key', '!=', 'user_role'],
                ['components.key', '!=', 'access_label'],
                ['components.key', '!=', 'slideshow'],
                ['components.key', '!=', 'product_attribute'],
                ['components.key', '!=', 'promo'],
                ['components.key', '!=', 'email_template'],
                ['components.key', '!=', 'email_template_group'],
                ['components.key', '!=', 'email_campaign'],
                ['components.key', '!=', 'review'],
            ])
            ->get();

        return view('admin.jquery_live.page_types', compact('results', 'page_type', 'page_key'));
    }

    public function pageKeys(Request $request)
    {
        $items = array();
        $options_id = array();
        $option_id = '';

        $categories_id = explode(',', $request->categories_id);
        if (empty($request->categories_id)) {
            $page = Page::find($request->page_id);
            if ($page) {
                $page->page_meta = unserialize($page->page_meta);
                $options_id = $page->page_meta ? explode(',', $page->page_meta->component_category_id) : [];
            }
        }
        // dd($options_id);
        if ($request->page_key == 'post') {
            $items = DB::table('post_categories')
            ->where('status', 1)
            ->get();
        }
        if ($request->page_key == 'product_category') {
            $items = DB::table('product_categories')
            ->where('status', 1)
            ->get();
        }
        if ($request->page_key == 'product_brand') {
            $items = DB::table('product_brands')
            ->where('status', 1)
            ->get();
        }
        if ($request->page_key == 'product_series') {
            $items = DB::table('product_series')
            ->where('status', 1)
            ->get();
        }
        if ($request->page_key == 'product') {
            $items = DB::table('products')
            ->where('status', 1)
            ->get();
        }

        return view('admin.jquery_live.options', compact('items', 'option_id', 'options_id'));
    }

    public function search(Request $request)
    {
        if ($request->action == 'clear') {
            return redirect()->route('admin.pages.index');
        }
        $menu_location = $request->input('menu_location');
        if (empty($menu_location)) {
            $this->validate($request, [
                'query' => 'required|min:3',
            ]);
        }
        $keyword = $request->input('query');

        $pages = DB::table('pages')
            ->where('pages.menu_location', 'like', "%$menu_location%")
            ->where('pages.title', 'like', "%$keyword%")
            ->where('pages.slug', 'like', "%$keyword%")
            ->where('parent_menu_id', 0)
            ->orderBy('pages.ordering')
            ->paginate($this->itemPerPage)->setPath('');

        $sub_sl = null;
        foreach ($pages as $page) {
            $page->sub_pages = array();
            if ($page->parent_menu_id == 0) {
                $sub_pages = DB::table('pages')->orderBy('pages.ordering')
                    ->where('parent_menu_id',  $page->id)
                    ->paginate($this->itemPerPage);
                $page->sub_pages = $sub_pages;
                $sub_sl = SLGenerator($sub_pages);
            }
        }
        $menu_location = $this->menuLocation();
        $sl = SLGenerator($pages);
        return view('admin.page.index', compact('sl', 'pages', 'sub_sl', 'menu_location'));
    }
}
