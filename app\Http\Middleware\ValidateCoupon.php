<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Services\CouponService;

class ValidateCoupon
{
    protected $couponService;

    public function __construct(CouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    public function handle(Request $request, Closure $next)
    {
        $cart = Session::get('cart');
        
        if ($cart && isset($cart->applied_coupon)) {
            try {
                // Re-validate the applied coupon
                $this->couponService->validateCoupon(
                    $cart->applied_coupon['code'],
                    auth()->user()
                );
            } catch (\Exception $e) {
                // Remove invalid coupon
                $this->couponService->removeCouponFromCart();
                
                return redirect()->back()->with('warning', 
                    'Your applied coupon is no longer valid and has been removed: ' . $e->getMessage()
                );
            }
        }
        
        return $next($request);
    }
}