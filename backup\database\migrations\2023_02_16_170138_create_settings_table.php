<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('site_logo')->nullable();
            $table->string('admin_logo')->nullable();
            $table->string('favicon')->nullable();
            $table->string('site_name');
            $table->string('site_slogan')->nullable();
            $table->string('tel_cell_country_code')->nullable();
            $table->string('tel_cell')->nullable();
            $table->string('email')->nullable();
            $table->boolean('site_offline')->default(false);
            $table->longText('socials')->nullable();
            $table->longText('business_hours')->nullable();
            $table->longText('business_hours_description')->nullable();
            $table->string('zip_code')->nullable();
            $table->string('country')->nullable();
            $table->string('address')->nullable();
            $table->longText('setting_meta')->nullable();
            $table->string('meta_title')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('meta_description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settings');
    }
}
