<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProdcutCategoriesParent extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'slug',
    ];

    /**
     * Define a has-many relationship with ProductCategory.
     */
    public function productCategories()
    {
        return $this->hasMany(ProductCategory::class, 'parent_category_id');
    }
}
