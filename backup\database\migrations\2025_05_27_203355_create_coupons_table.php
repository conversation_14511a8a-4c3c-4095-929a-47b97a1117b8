<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCouponsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupons', function (Blueprint $table) {
    $table->id();
    $table->string('name'); // Internal name
    $table->string('coupon_type'); // general, conditional, shipping
    $table->string('code')->nullable(); // for specific coupon type
    $table->enum('value_type', ['discount', 'free_shipping']); // value type
    $table->enum('discount_type', ['fixed', 'percentage'])->nullable(); // for 'discount' only
    $table->decimal('discount_amount', 10, 2)->nullable();
    $table->text('description')->nullable();
    $table->enum('status', ['active', 'inactive'])->default('inactive');
    $table->json('customer_groups')->nullable(); // new, active
    $table->boolean('guest_checkout')->default(false);
    $table->enum('condition_type', ['none', 'subtotal', 'category', 'brand', 'product', 'sku'])->default('none');
    $table->decimal('min_subtotal', 10, 2)->nullable(); // only if condition_type is subtotal
    $table->integer('uses_per_coupon')->nullable();
    $table->integer('uses_per_customer')->nullable();
    $table->date('from_date')->nullable();
    $table->date('to_date')->nullable();
    $table->boolean('never_expire')->default(false);
    $table->string('image')->nullable();
    $table->timestamps();
});

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupons');
    }
}
