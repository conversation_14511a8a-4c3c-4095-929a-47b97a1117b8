<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory;
protected $guarded=[];
    public function user()
    {
        return $this->belongsTo(User::class, 'customer_id');
    }
  public function orderRefund(){
        return $this->hasOne(OrderRefund::class, 'order_id');
    }
     public function OrderItems()
    {
        return $this->hasMany(OrderItem::class);
    }
}
