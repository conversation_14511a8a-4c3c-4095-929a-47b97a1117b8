<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
// use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\AlertPreference;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateGroup;

class UserVerificationToken extends Mailable
{
    use Queueable, SerializesModels;

    // public $user;
    public $content = null;
    public $pre_header = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $email_template = EmailTemplate::where('type', 'signup')->first();
        $user = User::where('email', $email)->first();
        $account_verification_link = route('verify.mail', ['email' => $user->email, 'token' => $user->token]);

        $data = [
            ucfirst($user->first_name), ucfirst($user->last_name),
            $account_verification_link,
        ];
        $this->subject = $email_template->subject;
        $this->pre_header = $email_template->pre_header;
        $this->content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->email_body));
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)->view('email_templates.user_verification_token');
    }
}
