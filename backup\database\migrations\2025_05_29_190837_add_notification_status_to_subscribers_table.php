<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNotificationStatusToSubscribersTable extends Migration
{
    public function up()
    {
        Schema::table('subscribers', function (Blueprint $table) {
            $table->boolean('email_notifications_enabled')->default(true)->after('phone');
            $table->boolean('sms_notifications_enabled')->default(true)->after('email_notifications_enabled');
            $table->boolean('whatsapp_notifications_enabled')->default(true)->after('sms_notifications_enabled');
        });
    }

    public function down()
    {
        Schema::table('subscribers', function (Blueprint $table) {
            $table->dropColumn([
                'email_notifications_enabled',
                'sms_notifications_enabled',
                'whatsapp_notifications_enabled',
            ]);
        });
    }
}
