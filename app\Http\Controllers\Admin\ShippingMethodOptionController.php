<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShippingMethodOption;
use Illuminate\Http\Request;

class ShippingMethodOptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ShippingMethodOption  $shippingMethodOption
     * @return \Illuminate\Http\Response
     */
    public function show(ShippingMethodOption $shippingMethodOption)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ShippingMethodOption  $shippingMethodOption
     * @return \Illuminate\Http\Response
     */
    public function edit(ShippingMethodOption $shippingMethodOption)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ShippingMethodOption  $shippingMethodOption
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ShippingMethodOption $shippingMethodOption)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ShippingMethodOption  $shippingMethodOption
     * @return \Illuminate\Http\Response
     */
    public function destroy(ShippingMethodOption $shippingMethodOption)
    {
        //
    }
}
