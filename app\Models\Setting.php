<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    // Define the relationship to get the parent store
    public function parentStore()
    {
        return $this->belongsTo(Setting::class, 'parent_store_id');
    }

    // Define the relationship to get the children stores
    public function childStores()
    {
        return $this->hasMany(Setting::class, 'parent_store_id');
    }
}
