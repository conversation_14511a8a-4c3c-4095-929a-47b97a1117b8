<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products');
            $table->string("title")->nullable();
            $table->string("sub_title")->nullable();
            $table->string("product_item_sku");
            $table->decimal('sale_price', 20, 2);
            $table->integer('discount_type')->nullable();
            $table->decimal('discount_price', 20, 2)->nullable();
            $table->longText("item_attributes")->nullable();
            $table->longText("description")->nullable();
            $table->string('product_item_image')->nullable();
            $table->longText("product_item_images")->nullable();
            $table->boolean('stock')->default(true);
            $table->text('remark')->nullable();
            $table->integer('ordering')->default(0);
            $table->boolean('partial_sale')->default(false);
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_items');
    }
}
