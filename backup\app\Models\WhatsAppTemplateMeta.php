<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsAppTemplateMeta extends Model
{
        protected $table = 'whatsapp_template_metas';

    use HasFactory;
    protected $fillable = ['template_id', 'title', 'body', 'variables'];

    protected $casts = [
        'variables' => 'array',
    ];
    public function emailTemplates()
{
    return $this->hasMany(EmailTemplate::class, 'sms_template_id');
}
}
