<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use PayPal\Api\Payer;
use PayPal\Api\Amount;
use PayPal\Api\Transaction;
use PayPal\Api\RedirectUrls;
use PayPal\Api\Payment;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;
use PayPal\Exception\PayPalConnectionException;
use App\Models\Page;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use Illuminate\Support\Facades\URL;
use App\Services\BTCPayService;

class PaymentProcessingService
{
    protected $paymentMethod;
    protected $apiConfig;

    public function __construct(PaymentMethod $paymentMethod)
    {
        $paypal_conf = \Config::get('paypal');
        $this->btcPayService = new BTCPayService();

        $this->paymentMethod = $paymentMethod;
        $this->apiConfig = $paymentMethod->getApiConfig();
        $this->_api_context = new \PayPal\Rest\ApiContext(
            new \PayPal\Auth\OAuthTokenCredential(
                $paypal_conf['client_id'],
                $paypal_conf['secret']
            )
        );
        $this->_api_context->setConfig($paypal_conf['settings']);
    }

    /**
     * Process payment based on payment method configuration
     */
    public function processPayment(array $paymentData): array
    {

        Session::put('payment_reference', $paymentData['reference']);
        try {
            // Validate payment amount
            $amount = $paymentData['amount'];
            if (!$this->paymentMethod->supportsAmount($amount)) {
                throw new \Exception('Payment amount not supported by this method');
            }

            // Calculate fees and discounts
            $feeBreakdown = $this->paymentMethod->getFeeBreakdown($amount);
            $discountAmount = $this->paymentMethod->getDiscountAmount($amount, $paymentData['currency']);

            // Adjust amount for customer fees and discounts
            $finalAmount = $amount;

            // Check if split payment is requested
            if (isset($paymentData['split_payment']) && $paymentData['split_payment']) {
                return $this->processSplitPayment($paymentData, $finalAmount);
            }

            // Check if partial transactions are needed
            if ($this->paymentMethod->exceedsSingleTransactionLimit($finalAmount)) {
                return $this->processPartialTransactions($paymentData, $finalAmount);
            }
            // Process single transaction
            return $this->processSingleTransaction($paymentData, $finalAmount);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage(), [
                'payment_method' => $this->paymentMethod->name,
                'payment_data' => $paymentData
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }




    protected function verifyPayPalPayment(string $reference): array
    {
        try {
            $paypal_conf = \Config::get('paypal');
            $clientId = $paypal_conf['client_id'] ?? null;
            $clientSecret = $paypal_conf['secret'] ?? null;
            $mode = $this->apiConfig['mode'] ?? 'sandbox';

            if (!$clientId || !$clientSecret) {
                throw new \Exception('PayPal client credentials not configured');
            }

            // Initialize PayPal API Context
            $apiContext = new \PayPal\Rest\ApiContext(
                new \PayPal\Auth\OAuthTokenCredential($clientId, $clientSecret)
            );

            $apiContext->setConfig([
                'mode' => $mode,
                'log.LogEnabled' => true,
                'log.FileName' => storage_path('logs/paypal.log'),
                'log.LogLevel' => 'DEBUG',
                'cache.enabled' => false,
            ]);

            // Get PayPal payment ID - try multiple sources
            $paymentId = null;

            // 1. Try from URL parameters (common in PayPal returns)
            $paymentId = $_GET['paymentId'] ?? $_GET['payment_id'] ?? null;

            // 2. Try from session (stored during payment creation)
            if (!$paymentId) {
                $paymentId = Session::get('paypal_payment_id');
            }

            // 3. Try to extract from reference if it contains payment ID
            if (!$paymentId && strpos($reference, 'PAY-') === 0) {
                $paymentId = $reference;
            }

            // 4. Last resort - try to find by invoice number
            if (!$paymentId) {
                // This would require a different API call to search payments by invoice number
                // For now, we'll throw an exception
                throw new \Exception('PayPal payment ID not found. Reference: ' . $reference);
            }

            Log::info('PayPal payment verification attempt', [
                'payment_id' => $paymentId,
                'reference' => $reference,
                'mode' => $mode
            ]);

            // Get payment details from PayPal
            $payment = \PayPal\Api\Payment::get($paymentId, $apiContext);

            if (!$payment) {
                throw new \Exception('PayPal payment not found');
            }

            // Extract payment information
            $paymentState = $payment->getState(); // 'created', 'approved', 'failed', 'pending', etc.
            $transactions = $payment->getTransactions();
            $amountPaid = 0;
            $currency = 'USD';

            if (!empty($transactions)) {
                $transaction = $transactions[0];
                $amount = $transaction->getAmount();
                $amountPaid = floatval($amount->getTotal());
                $currency = $amount->getCurrency();
            }

            // Get payer info
            $payer = $payment->getPayer();
            $payerInfo = $payer ? $payer->getPayerInfo() : null;
            $payerEmail = $payerInfo ? $payerInfo->getEmail() : null;

            $isSuccess = false;
            $needsExecution = false;

            // Handle different PayPal payment states
            if ($paymentState === 'created') {
                // Payment created but not yet approved by user
                $isSuccess = true;
                Log::info('PayPal payment is in created state - user needs to approve');
            } elseif ($paymentState === 'approved') {
                // Payment approved by user but needs execution
                $needsExecution = true;
                Log::info('PayPal payment approved - attempting execution');

                try {
                    // Get payer ID from URL (PayPal returns this)
                    $payerId = $_GET['PayerID'] ?? $_GET['payer_id'] ?? null;

                    if ($payerId) {
                        // Execute the payment
                        $execution = new \PayPal\Api\PaymentExecution();
                        $execution->setPayerId($payerId);

                        $executedPayment = $payment->execute($execution, $apiContext);

                        // Check execution result
                        if ($executedPayment->getState() === 'approved') {
                            $isSuccess = true;
                            $payment = $executedPayment; // Update payment object
                            $paymentState = $executedPayment->getState();

                            Log::info('PayPal payment executed successfully', [
                                'payment_id' => $paymentId,
                                'payer_id' => $payerId
                            ]);
                        }
                    } else {
                        Log::warning('PayPal payment approved but no PayerID found for execution');
                    }
                } catch (\Exception $execException) {
                    Log::error('PayPal payment execution failed', [
                        'error' => $execException->getMessage(),
                        'payment_id' => $paymentId
                    ]);
                    // Don't throw here - we'll return the current state
                }
            } elseif ($paymentState === 'failed' || $paymentState === 'canceled') {
                // Payment failed or canceled
                $isSuccess = false;
                Log::info('PayPal payment failed or canceled', ['state' => $paymentState]);
            }

            // Double-check with sale transaction if payment was executed
            if ($isSuccess && !empty($transactions)) {
                $relatedResources = $transactions[0]->getRelatedResources();
                if (!empty($relatedResources)) {
                    $sale = $relatedResources[0]->getSale();
                    if ($sale) {
                        $saleState = $sale->getState();
                        $isSuccess = ($saleState === 'completed');
                        Log::info('PayPal sale state checked', ['sale_state' => $saleState]);
                    }
                }
            }

            Log::info('PayPal payment verification result', [
                'payment_id' => $paymentId,
                'state' => $paymentState,
                'is_success' => $isSuccess,
                'amount_paid' => $amountPaid,
                'currency' => $currency
            ]);

            // Enhanced response structure following the pattern of other verification methods
            $result = [
                'success' => true,
                'verified' => $isSuccess,
                'status' => $paymentState,
                'amount_paid' => $amountPaid,
                'currency' => $currency,
                'reference' => $reference,
                'payment_id' => $paymentId,
                'payer_email' => $payerEmail,
                'needs_execution' => $needsExecution,
                'provider_response' => $payment->toArray()
            ];

            // Add specific messaging based on state
            if ($paymentState === 'created') {
                $result['message'] = 'Payment created but not yet approved by user';
                $result['next_action'] = 'redirect_to_paypal';
            } elseif ($needsExecution && !$isSuccess) {
                $result['message'] = 'Payment approved but execution failed';
                $result['next_action'] = 'retry_execution';
            } elseif ($isSuccess) {
                $result['message'] = 'Payment completed successfully';
                $result['next_action'] = 'none';
            }

            // Check if this is part of a split payment (following the same pattern as other methods)
            $splitState = Session::get('split_payment_state');
            if ($splitState && $isSuccess) {
                if ($splitState['current_transaction'] == 1) {
                    $result['is_first_split_payment'] = true;
                    $result['remaining_amount'] = $splitState['split_calculation']['second_transaction']['amount'];
                } else {
                    $result['is_final_split_payment'] = true;
                    $result['total_amount_paid'] = $splitState['total_amount'];
                }
            }

            return $result;
        } catch (\PayPal\Exception\PayPalConnectionException $ex) {
            Log::error('PayPal verification connection error', [
                'error_message' => $ex->getMessage(),
                'error_data' => $ex->getData(),
                'reference' => $reference
            ]);

            return [
                'success' => false,
                'verified' => false,
                'error' => 'PayPal verification connection failed: ' . $ex->getMessage(),
                'reference' => $reference
            ];
        } catch (\PayPal\Exception\PayPalInvalidCredentialException $ex) {
            Log::error('PayPal verification credential error', [
                'error_message' => $ex->getMessage(),
                'reference' => $reference
            ]);

            return [
                'success' => false,
                'verified' => false,
                'error' => 'PayPal verification failed: Invalid credentials',
                'reference' => $reference
            ];
        } catch (\Exception $e) {
            Log::error('PayPal verification error', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'reference' => $reference
            ]);

            return [
                'success' => false,
                'verified' => false,
                'error' => 'PayPal payment verification failed: ' . $e->getMessage(),
                'reference' => $reference
            ];
        }
    }
    protected function processSplitPayment(array $paymentData, float $amount): array
    {
        if (!$this->paymentMethod->supports_split_payment) {
            throw new \Exception('Split payment not supported by this method');
        }

        $splitOption = $paymentData['split_option'] ?? '50-50';
        $splitCalculation = $this->paymentMethod->calculateSplitPayment($amount, $splitOption);
        //dd( $splitCalculation);
        // Check if this is a continuation (second transaction)
        $splitState = Session::get('split_payment_state');
        // dd($splitState);
        if ($splitState && $splitState['current_transaction'] == 2) {
            // Process second transaction
            return $this->processSecondSplitTransaction($paymentData, $splitCalculation, $splitState);
        } else {
            // Process first transaction and setup split state
            return $this->processFirstSplitTransaction($paymentData, $splitCalculation, $amount, $splitOption);
        }
    }

    /**
     * Process first split transaction
     */
    protected function processFirstSplitTransaction(array $paymentData, array $splitCalculation, float $totalAmount, string $splitOption): array
    {

        // Store split payment state in session
        Session::put('split_payment_state', [
            'total_amount' => $totalAmount,
            'split_option' => $splitOption,
            'split_calculation' => $splitCalculation,
            'current_transaction' => 1,
            'payment_method_id' => $this->paymentMethod->id,
            'original_payment_data' => $paymentData,
            'created_at' => now()
        ]);

        $cart = Session::get('cart');
        $shippingAmount = Session::get('shippingAmount', 0);
        $totalWithShipping = $totalAmount + $shippingAmount;
        // Process only the first transaction
        $firstTransactionAmount = $splitCalculation['first_transaction']['amount'];

        $firstTransactionData = array_merge($paymentData, [
            'amount' => $firstTransactionAmount, // Use calculated amount exactly
            'reference' => $paymentData['reference'] . '_SPLIT_1',
            'total_amount_with_shipping' => $totalWithShipping,

        ]);


        $result = $this->processSingleTransaction($firstTransactionData, $splitCalculation['first_transaction']['amount']);

        if ($result['success']) {
            // Add split payment metadata to result
            $result['split_payment'] = true;
            $result['split_info'] = $splitCalculation;
            $result['transaction_sequence'] = 1;
            $result['total_transactions'] = 2;
            $result['first_transaction_amount'] = $splitCalculation['first_transaction']['amount'];
            $result['remaining_amount'] = $splitCalculation['second_transaction']['amount'];
        }

        return $result;
    }

    /**
     * Process second split transaction
     */
    protected function processSecondSplitTransaction(array $paymentData, array $splitCalculation, array $splitState): array
    {

        // Process only the second transaction
        $splitState = Session::get('split_payment_state');
        $split_payment_continuation = Session::get('split_payment_continuation');

        $remainingAmount = $splitState['split_calculation']['second_transaction']['amount'];

        $secondTransactionData = array_merge($paymentData, [
            'amount' => $remainingAmount,  // Use the calculated remaining amount
            'reference' => $paymentData['reference'] . '_split_2'
        ]);

        $result = $this->processSingleTransaction($secondTransactionData, $remainingAmount);

        if ($result['success']) {
            // Clear split payment state - we're done
            Session::forget('split_payment_state');

            // Add completion metadata
            $result['split_payment'] = true;
            $result['split_payment_completed'] = true;
            $result['split_info'] = $splitCalculation;
            $result['transaction_sequence'] = 2;
            $result['total_transactions'] = 2;
            $result['total_amount_paid'] = $splitState['total_amount'];
        }

        return $result;
    }

    // ADD this new method to handle split payment callbacks:

    /**
     * Handle split payment callback
     */
    public function handleSplitPaymentCallback(string $reference): array
    {
        $splitState = Session::get('split_payment_state');
        if (!$splitState) {
            // No split payment state, treat as regular payment
            return $this->verifyPayment($reference);
        }

        // Verify the current transaction
        $verificationResult = $this->verifyPayment($reference);

        if (!$verificationResult['success'] || !$verificationResult['verified']) {
            // Current transaction failed - cleanup
            Session::forget('split_payment_state');
            return [
                'success' => false,
                'verified' => false,
                'error' => 'Split payment transaction verification failed'
            ];
        }

        if ($splitState['current_transaction'] == 1) {
            // First transaction completed successfully
            // Update state for second transaction
            $splitState['current_transaction'] = 2;
            $splitState['first_transaction_completed'] = true;
            $splitState['first_transaction_reference'] = $reference;
            Session::put('split_payment_state', $splitState);

            // CORRECTED: Return proper structure with split payment fields
            return [
                'success' => true,
                'verified' => true,
                'split_payment_continue' => true,
                'is_first_split_payment' => true,
                'amount_paid' => $splitState['split_calculation']['first_transaction']['amount'],
                'remaining_amount' => $splitState['split_calculation']['second_transaction']['amount'],
                'transaction_completed' => 1,
                'total_transactions' => 2,
                'reference' => $reference,
                'split_info' => $splitState['split_calculation']
            ];
        } else {
            // Second transaction completed - split payment done
            $totalAmountPaid = $splitState['total_amount'];
            Session::forget('split_payment_state');

            // CORRECTED: Return proper structure for completed split payment
            return [
                'success' => true,
                'verified' => true,
                'split_payment_completed' => true,
                'is_final_split_payment' => true,
                'amount_paid' => $splitState['split_calculation']['second_transaction']['amount'],
                'total_amount_paid' => $totalAmountPaid,
                'transaction_completed' => 2,
                'total_transactions' => 2,
                'reference' => $reference
            ];
        }
    }

    /**
     * Verify SquadPay payment - CORRECTED: Enhanced to include split payment context
     */
    protected function verifySquadPayment(string $reference): array
    {
        $apiKey = $this->apiConfig['secret_key'] ?? null;

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
        ])->get($this->getSquadApiUrl() . "/transaction/verify/{$reference}");

        if ($response->successful()) {
            $data = $response->json();
            $isSuccess = ($data['data']['transaction_status'] ?? '') === 'success';
            $amountPaid = $data['data']['amount'] ?? 0;

            // CORRECTED: Enhanced response structure
            $result = [
                'success' => true,
                'verified' => $isSuccess,
                'status' => $data['data']['transaction_status'] ?? 'unknown',
                'amount_paid' => $amountPaid,
                'reference' => $reference,
                'provider_response' => $data
            ];

            // Check if this is part of a split payment
            $splitState = Session::get('split_payment_state');
            if ($splitState && $isSuccess) {
                if ($splitState['current_transaction'] == 1) {
                    $result['is_first_split_payment'] = true;
                    $result['remaining_amount'] = $splitState['split_calculation']['second_transaction']['amount'];
                } else {
                    $result['is_final_split_payment'] = true;
                    $result['total_amount_paid'] = $splitState['total_amount'];
                }
            }

            return $result;
        }

        return [
            'success' => false,
            'verified' => false,
            'error' => 'Squad payment verification failed',
            'reference' => $reference
        ];
    }
    /**
     * Verify Flutterwave payment - CORRECTED: Enhanced to include split payment context
     */
    protected function verifyFlutterwavePayment(string $reference): array
    {
        $secretKey = $this->apiConfig['secret_key'] ?? null;

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $secretKey,
        ])->get($this->getFlutterwaveApiUrl() . "/transactions/verify_by_reference?tx_ref={$reference}");

        if ($response->successful()) {
            $data = $response->json();
            $isSuccess = ($data['data']['status'] ?? '') === 'successful';
            $amountPaid = $data['data']['amount'] ?? 0;

            // CORRECTED: Enhanced response structure
            $result = [
                'success' => true,
                'verified' => $isSuccess,
                'status' => $data['data']['status'] ?? 'unknown',
                'amount_paid' => $amountPaid,
                'reference' => $reference,
                'provider_response' => $data
            ];

            // Check if this is part of a split payment
            $splitState = Session::get('split_payment_state');
            if ($splitState && $isSuccess) {
                if ($splitState['current_transaction'] == 1) {
                    $result['is_first_split_payment'] = true;
                    $result['remaining_amount'] = $splitState['split_calculation']['second_transaction']['amount'];
                } else {
                    $result['is_final_split_payment'] = true;
                    $result['total_amount_paid'] = $splitState['total_amount'];
                }
            }

            return $result;
        }

        return [
            'success' => false,
            'verified' => false,
            'error' => 'Flutterwave payment verification failed',
            'reference' => $reference
        ];
    }

    /**
     * Verify Stripe payment - CORRECTED: Enhanced to include split payment context
     */
    protected function verifyStripePayment(string $reference): array
    {

        try {

            // For Stripe, reference might be a session ID or payment intent ID
            try {
                // Try as checkout session first
                $secretKey = $this->apiConfig['secret_key'] ?? null;

                if (!$secretKey) {
                    throw new \Exception('Stripe secret key not configured');
                }
                $sessionId = $_GET['session_id'] ?? null;

                // Initialize Stripe (you would need to include Stripe SDK)
                $stripe = new \Stripe\StripeClient($secretKey);
                $session = $stripe->checkout->sessions->retrieve($sessionId);
                $isSuccess = $session->payment_status === 'paid';
                $amountPaid = $session->amount_total / 100; // Convert from cents
                $result = [
                    'success' => true,
                    'verified' => $isSuccess,
                    'status' => $session->payment_status,
                    'amount_paid' => $amountPaid,
                    'reference' => $reference,
                    'provider_response' => $session->toArray()
                ];
            } catch (\Exception $e) {
                Log::error('Payment verification insdie', [
                    'mesage' => $e->getMessage()
                ]);
                // Try as payment intent
                $paymentIntent = $stripe->paymentIntents->retrieve($reference);
                $isSuccess = $paymentIntent->status === 'succeeded';
                $amountPaid = $paymentIntent->amount / 100; // Convert from cents

                $result = [
                    'success' => true,
                    'verified' => $isSuccess,
                    'status' => $paymentIntent->status,
                    'amount_paid' => $amountPaid,
                    'reference' => $reference,
                    'provider_response' => $paymentIntent->toArray()
                ];
            }

            // Check if this is part of a split payment
            $splitState = Session::get('split_payment_state');
            if ($splitState && $isSuccess) {
                if ($splitState['current_transaction'] == 1) {
                    $result['is_first_split_payment'] = true;
                    $result['remaining_amount'] = $splitState['split_calculation']['second_transaction']['amount'];
                } else {
                    $result['is_final_split_payment'] = true;
                    $result['total_amount_paid'] = $splitState['total_amount'];
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Payment verification insdie', [
                'mesage' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'verified' => false,
                'error' => 'Stripe payment verification failed: ' . $e->getMessage(),
                'reference' => $reference
            ];
        }
    }

    /**
     * Process multiple partial transactions for amount limits
     */
    protected function processPartialTransactions(array $paymentData, float $amount): array
    {
        if (!$this->paymentMethod->supports_partial_transactions) {
            throw new \Exception('Partial transactions not supported, but amount exceeds limit');
        }

        $partialTransactions = $this->paymentMethod->calculatePartialTransactions($amount);
        $results = [];
        $allSuccessful = true;

        foreach ($partialTransactions as $index => $transactionAmount) {
            $result = $this->processSingleTransaction(
                array_merge($paymentData, [
                    'amount' => $transactionAmount,
                    'partial_transaction_index' => $index + 1,
                    'total_partial_transactions' => count($partialTransactions)
                ]),
                $transactionAmount
            );

            $results['transactions'][] = $result;

            if (!$result['success']) {
                $allSuccessful = false;
                break; // Stop processing if any transaction fails
            }
        }

        $results['success'] = $allSuccessful;
        $results['partial_transaction_info'] = [
            'total_transactions' => count($partialTransactions),
            'original_amount' => $amount,
            'transactions' => $partialTransactions
        ];

        return $results;
    }

    /**
     * Process single transaction based on payment method
     */
    protected function processSingleTransaction(array $paymentData, float $amount): array
    {

        switch ($this->paymentMethod->name) {
            case 'squadpay':
                return $this->processSquadPayment($paymentData, $amount);
            case 'flutterwave':
                return $this->processFlutterwavePayment($paymentData, $amount);
            case 'stripe':
                return $this->processStripePayment($paymentData, $amount);
            case 'paypal':
                return $this->processPayPalPayment($paymentData, $amount);
            case 'btcpay':
                return $this->processBTCPayment($paymentData, $amount);
            case 'klarna':
                return $this->processKlarnaPayment($paymentData, $amount);
            case 'affirm':
                return $this->processAffirmPayment($paymentData, $amount);
            case 'afterpay':
                return $this->processAfterpayPayment($paymentData, $amount);
            default:
                return $this->processGenericPayment($paymentData, $amount);
        }
    }

    /**
     * Process SquadPay payment
     */
    protected function processSquadPayment(array $paymentData, float $amount): array
    {

        try {
            $apiKey = $this->apiConfig['secret_key'] ?? null;
            $publicKey = $this->apiConfig['public_key'] ?? null;
            if (!$apiKey || !$publicKey) {
                throw new \Exception('SquadPay API keys not configured');
            }

            // ** FIX: Handle customer data properly **
            $email = $paymentData['customer']['email'] ?? $paymentData['email'] ?? null;
            $customerId = $paymentData['customer']['id'] ?? $paymentData['customer_id'] ?? null;
            $customerName = $paymentData['customer']['name'] ?? $paymentData['customer_name'] ?? null;

            if (!$email) {
                throw new \Exception('Customer email is required for Squad payment');
            }

            // ** CRITICAL FIX: Proper amount handling to avoid decimals **
            // Round to 2 decimal places first to handle floating point precision
            $roundedAmount = round($amount, 2);
            // dd($roundedAmount);
            // Check if amount is already in kobo (if it's a large integer)
            if ($roundedAmount >= 100000) {
                // Likely already in kobo, use as-is but ensure it's a whole number
                $squadAmount = intval(round($roundedAmount));
                Log::info('Amount appears to be in kobo already', [
                    'original_amount' => $amount,
                    'rounded_amount' => $roundedAmount,
                    'final_squad_amount' => $squadAmount
                ]);
            } else {
                // Amount is in Naira, convert to kobo
                $squadAmount = intval(round($roundedAmount * 100));
                Log::info('Converting Naira to kobo', [
                    'naira_amount' => $roundedAmount,
                    'kobo_amount' => $squadAmount
                ]);
            }

            // ** Validate the final amount **
            if ($squadAmount <= 0) {
                throw new \Exception("Invalid Squad amount: {$squadAmount} (original: {$amount})");
            }

            if ($squadAmount < 100) { // Less than 1 Naira
                throw new \Exception("Amount too small: {$squadAmount} kobo (minimum: 100 kobo)");
            }

            if ($squadAmount > 1000000000) { // More than 10 million Naira
                throw new \Exception("Amount too large: {$squadAmount} kobo (maximum: 1,000,000,000 kobo)");
            }

            $payload = [
                'amount' => $squadAmount, // ** Use properly calculated whole number **
                'email' => $email,
                'currency' => $paymentData['currency'] ?? 'NGN',
                'transaction_ref' => $paymentData['reference'],
                'callback_url' => $paymentData['callback_url'] ?? null,
                'initiate_type' => 'inline',
                'payment_channels' => ['card', 'bank', 'ussd', 'transfer'],
                'pass_charge' => false,
                'metadata' => [
                    'order_id' => $paymentData['order_id'] ?? null,
                    'customer_id' => $customerId,
                    'customer_name' => $customerName,
                ]
            ];

            // ** Add customer_name to main payload if available **
            if ($customerName) {
                $payload['customer_name'] = $customerName;
            }

            Log::info('Squad payment payload prepared', [
                'amount_kobo' => $squadAmount,
                'amount_naira' => $squadAmount / 100,
                'email' => $email,
                'reference' => $paymentData['reference'],
                'currency' => $payload['currency']
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->getSquadApiUrl() . '/transaction/initiate', $payload);

            Log::info('Squad API response', [
                'status' => $response->status(),
                'successful' => $response->successful(),
                'response_preview' => substr($response->body(), 0, 300)
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'transaction_id' => $data['data']['transaction_ref'] ?? null,
                    'authorization_url' => $data['data']['checkout_url'] ?? null,
                    'reference' => $paymentData['reference'],
                    'provider_response' => $data,
                    'public_key' => $publicKey,
                    'authorization_key' => $publicKey,
                    'amount' => $squadAmount, // Return kobo amount
                    'amount_naira' => $squadAmount / 100, // Return naira for display
                    'email' => $email,
                    'customer_name' => $customerName,
                    'callback_url' => $paymentData['callback_url'] ?? null,
                    'currency' => $payload['currency'],
                    'initiate_type' => 'inline',
                    'payment_channels' => ['card', 'bank', 'ussd', 'transfer'],
                    'metadata' => $payload['metadata'],
                ];
            } else {
                $errorResponse = $response->json();
                $errorMessage = $errorResponse['message'] ?? 'SquadPay API error: ' . $response->body();

                Log::error('Squad API error', [
                    'status' => $response->status(),
                    'response' => $errorResponse,
                    'payload_sent' => $payload,
                    'amount_sent' => $squadAmount
                ]);

                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::error('Squad payment processing failed', [
                'error' => $e->getMessage(),
                'original_amount' => $amount,
                'payment_data_keys' => array_keys($paymentData)
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }


    /**
     * Process Flutterwave payment
     */
    protected function processFlutterwavePayment(array $paymentData, float $amount): array
    {
        try {
            $secretKey = $this->apiConfig['secret_key'] ?? null;

            if (!$secretKey) {
                throw new \Exception('Flutterwave secret key not configured');
            }
            $email = $paymentData['customer']['email'] ?? $paymentData['email'] ?? null;
            $phone = $paymentData['customer']['phone'] ?? $paymentData['phone_number'] ?? $paymentData['phone'] ?? null;
            $name = $paymentData['customer']['name'] ?? $paymentData['customer_name'] ?? null;

            $payload = [
                'tx_ref' => $paymentData['reference'],
                'amount' => $amount,
                'currency' => $paymentData['currency'],
                'redirect_url' => $paymentData['callback_url'] ?? '',
                'customer' => [
                    'email' => $email,
                    'phonenumber' => $phone,
                    'name' => $name
                ],
                'meta' => [
                    'order_id' => $paymentData['order_id'] ?? null,
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->getFlutterwaveApiUrl() . '/payments', $payload);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'transaction_id' => $data['data']['id'] ?? null,
                    'authorization_url' => $data['data']['link'] ?? null,
                    'reference' => $paymentData['reference'],
                    'provider_response' => $data,
                    'amount' => $amount
                ];
            } else {
                throw new \Exception('Flutterwave API error: ' . $response->body());
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process Stripe payment
     */
    protected function processStripePayment(array $paymentData, float $amount): array
    {
        try {
            $secretKey = $this->apiConfig['secret_key'] ?? null;

            if (!$secretKey) {
                throw new \Exception('Stripe secret key not configured');
            }

            // Initialize Stripe (you would need to include Stripe SDK)
            \Stripe\Stripe::setApiKey($secretKey);

            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => strtolower($paymentData['currency']),
                        'product_data' => [
                            'name' => 'Order Payment',
                        ],
                        'unit_amount' => $amount * 100, // Convert to cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => ($paymentData['callback_url'] ?? '') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => $paymentData['cancel_url'] ?? '',
                'metadata' => [
                    'order_id' => $paymentData['order_id'] ?? '',
                    'reference' => $paymentData['reference'],
                ],
            ]);

            return [
                'success' => true,
                'transaction_id' => $session->id,
                'authorization_url' => $session->url,
                'reference' => $paymentData['reference'],
                'provider_response' => $session->toArray(),
                'amount' => $amount
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process BNPL payment (generic implementation)
     */
    protected function processBNPLPayment(array $paymentData, float $amount, string $provider): array
    {
        try {
            // Generic BNPL processing logic
            $apiKey = $this->apiConfig['api_key'] ?? null;

            if (!$apiKey) {
                throw new \Exception("{$provider} API key not configured");
            }

            // This is a generic implementation - each BNPL provider would need specific logic
            $payload = [
                'amount' => $amount,
                'currency' => $paymentData['currency'],
                'reference' => $paymentData['reference'],
                'customer' => $paymentData['customer'],
                'return_url' => $paymentData['callback_url'] ?? '',
            ];

            // Each BNPL provider would have different endpoints and payload structures
            return [
                'success' => true,
                'requires_redirect' => true,
                'authorization_url' => $this->getBNPLAuthUrl($provider, $payload),
                'reference' => $paymentData['reference']
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process Klarna payment
     */
    protected function processKlarnaPayment(array $paymentData, float $amount): array
    {
        return $this->processBNPLPayment($paymentData, $amount, 'klarna');
    }

    /**
     * Process Affirm payment
     */
    protected function processAffirmPayment(array $paymentData, float $amount): array
    {
        return $this->processBNPLPayment($paymentData, $amount, 'affirm');
    }

    /**
     * Process Afterpay payment
     */
    protected function processAfterpayPayment(array $paymentData, float $amount): array
    {
        return $this->processBNPLPayment($paymentData, $amount, 'afterpay');
    }



    public function processPayPalPayment(array $paymentData, float $OrgAmount): array
    {
        // Retrieve essential data with null checks
        $checkout_page = Page::where('page_key', 'checkout')->firstOrFail();
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        // Validate critical data
        if (!$cart || !$checkout_order) {
            throw new \Exception('Cart or checkout order is missing.');
        }
        // $this->makeOrder();
        // Calculate cart amount with null-safe operations
        $cart_amount = max(0, $cart->grand_total - ($cart->paid_grand_total ?? 0));
        $item_array = [];
        $description = [];
        // Validate cart items
        if (empty($cart->items)) {
            throw new \Exception('No items in the cart.');
        }
        // Ensure cart->items is iterable
        if (!is_array($cart->items) && !$cart->items instanceof \Traversable) {
            throw new \Exception('Cart items are not iterable.');
        }
        // Create the item list with comprehensive error handling
        foreach ($cart->items as $value) {
            // Ensure all required fields exist
            $itemName = trim(
                ($value->product_item_sku ?? 'N/A') . '-' .
                    ($value->product_title ?? '') . ' ' .
                    ($value->title ?? '') . ' ' .
                    ($value->sub_title ?? '')
            );
            $description[] = $itemName;
            // Validate item price and quantity
            $itemPrice = max(0, floatval($value->price ?? 0));
            $itemQuantity = max(1, intval($value->quantity ?? 1));
            $item = new Item();
            $item->setName($itemName)
                ->setCurrency('USD')
                ->setQuantity(max(1, intval($value->quantity ?? 1)))
                ->setPrice(number_format(max(0, floatval($value->price ?? 0)), 2, '.', ''));
            $item_array[] = $item;
        }
        // Handle discounts with comprehensive checks
        $discount_amt = 0;
        $paymentMethod = PaymentMethod::find($checkout_order->payment->selected_method_id ?? null);

        
        // Validate shipping information
        $shippingAmount = max(0, floatval(optional($checkout_order->shipping)->shipping_amount ?? 0));
        // Prepare the payer
        $payer = new Payer();
        $payer->setPaymentMethod('paypal');
        // Prepare the amount details with strict validation
        $amountDetails = [
            'subtotal' => number_format(max(0, $cart->total ?? 0), 2, '.', ''),
            'tax' => '0.00',
            'shipping' => number_format($shippingAmount, 2, '.', ''),
            'discount' => number_format($discount_amt, 2, '.', ''),
        ];
        $totalAmount = max(0.01, round(
            ($cart->total ?? 0) + $shippingAmount - $discount_amt,
            2
        ));
        $amount = new Amount();
        $amount->setCurrency('USD')
            ->setDetails($amountDetails)
            ->setTotal(number_format($totalAmount, 2, '.', ''));
        // Prepare the item list
        $item_list = new ItemList();
        $item_list->setItems($item_array);
        // Create the transaction
        $transaction = new Transaction();
        $transaction->setAmount($amount)
            ->setItemList($item_list)
            ->setDescription(implode('|', array_filter($description)));
        // Set the redirect URLs
        $redirect_urls = new RedirectUrls();
        $redirect_urls->setReturnUrl($paymentData['callback_url'])
            ->setCancelUrl($paymentData['cancel_url']);
        // Create the payment
        $payment = new Payment();
        $payment->setIntent('sale')
            ->setPayer($payer)
            ->setRedirectUrls($redirect_urls)
            ->setTransactions([$transaction]);
        // Extensive logging for debugging
        \Log::info('PayPal Payment Request Details', [
            'cart_amount' => $cart_amount,
            'total_items' => count($item_array),
            'discount_amount' => $discount_amt,
            'shipping_amount' => $shippingAmount,
            'payment_details' => $payment->toArray()
        ]);
        // $payment->create($this->_api_context);
        // dd($payment);
        try {
            // Create payment with error handling
            $payment->create($this->_api_context);
            // Store PayPal payment ID in session
            Session::put('paypal_payment_id', $payment->getId());
            // Find approval URL
            // $redirect_url = null;
            foreach ($payment->getLinks() as $link) {
                if ($link->getRel() == 'approval_url') {
                    $redirect_url = $link->getHref();
                    break;
                }
            }
            //dd($redirect_url);
            // Redirect or handle errors
            // Ensure it's a string and redirecty
            return [
                'success' => true,
                'transaction_id' => $payment->getId(),
                'authorization_url' => $redirect_url,
                'reference' => $paymentData['reference'],
                'provider_response' => $payment->toArray(),
                'amount' => $OrgAmount
            ];
        } catch (PPConnectionException $ex) {
            // Detailed connection error logging
            \Log::error('PayPal Connection Error', [
                'error_message' => $ex->getMessage(),
                'error_data' => $ex->getData(),
                'debug_mode' => \Config::get('app.debug')
            ]);
        } catch (\Exception $ex) {
            // Catch-all for any other unexpected errors
            \Log::error('Unexpected PayPal Payment Error', [
                'error_message' => $ex->getMessage(),
                'error_trace' => $ex->getTraceAsString()
            ]);
        }
    }
    /**
     * Process PayPal payment
     */
    protected function processPayPalPaymentOld(array $paymentData, float $amount): array
    {
        try {
            $webpagecontroller = new \App\Http\Controllers\WebPageController();
            //dd($webpagecontroller->payWithPaypal());
            $clientId = $this->apiConfig['public_key'] ?? null;
            $clientSecret = $this->apiConfig['secret_key'] ?? null;
            $mode = $this->apiConfig['mode'] ?? 'sandbox'; // 'sandbox' or 'live'
            if (!$clientId || !$clientSecret) {
                throw new \Exception('PayPal client credentials not configured');
            }
            // Initialize PayPal API Context
            $apiContext = new \PayPal\Rest\ApiContext(
                new \PayPal\Auth\OAuthTokenCredential($clientId, $clientSecret)
            );

            $config = [
                'mode' => $mode,
                'log.LogEnabled' => true,
                'log.FileName' => storage_path('logs/paypal.log'),
                'log.LogLevel' => 'DEBUG',
                'cache.enabled' => false,
                'http.CURLOPT_CONNECTTIMEOUT' => 30,
                'http.CURLOPT_TIMEOUT' => 60,
            ];

            $apiContext->setConfig($config);

            // Create Payer
            $payer = new \PayPal\Api\Payer();
            $payer->setPaymentMethod('paypal');

            // Create Item for the order
            $item = new \PayPal\Api\Item();
            $item->setName($paymentData['description'] ?? 'Order Payment')
                ->setCurrency(strtoupper($paymentData['currency']))
                ->setQuantity(1)
                ->setPrice(number_format($amount, 2, '.', ''));

            // Create Item List
            $itemList = new \PayPal\Api\ItemList();
            $itemList->setItems([$item]);

            // Create Amount Details
            $details = new \PayPal\Api\Details();
            $details->setSubtotal(number_format($amount, 2, '.', ''))
                ->setTax('0.00')
                ->setShipping('0.00');

            // Create Amount
            $amountObj = new \PayPal\Api\Amount();
            $amountObj->setCurrency(strtoupper($paymentData['currency']))
                ->setTotal(number_format($amount, 2, '.', ''))
                ->setDetails($details);

            // Create Transaction
            $transaction = new \PayPal\Api\Transaction();
            $transaction->setAmount($amountObj)
                ->setItemList($itemList)
                ->setDescription($paymentData['description'] ?? 'Order Payment')
                ->setInvoiceNumber($paymentData['reference'] ?? uniqid())
                ->setCustom(json_encode([
                    'order_id' => $paymentData['order_id'] ?? '',
                    'reference' => $paymentData['reference']
                ]));

            // Create Redirect URLs
            $redirectUrls = new \PayPal\Api\RedirectUrls();
            $redirectUrls->setReturnUrl($paymentData['callback_url'] ?? '')
                ->setCancelUrl($paymentData['cancel_url'] ?? '');

            // Create Payment
            $payment = new \PayPal\Api\Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);

            // Log payment details for debugging
            \Log::info('PayPal Payment Request Details', [
                'amount' => $amount,
                'currency' => $paymentData['currency'],
                'reference' => $paymentData['reference'],
                'payment_details' => $payment->toArray()
            ]);

            // Create payment
            $payment->create($apiContext);

            // Find approval URL
            $approvalUrl = null;
            foreach ($payment->getLinks() as $link) {
                if ($link->getRel() === 'approval_url') {
                    $approvalUrl = $link->getHref();
                    break;
                }
            }

            if (!$approvalUrl) {
                throw new \Exception('Could not retrieve PayPal approval URL');
            }

            return [
                'success' => true,
                'transaction_id' => $payment->getId(),
                'authorization_url' => $approvalUrl,
                'reference' => $paymentData['reference'],
                'provider_response' => $payment->toArray(),
                'amount' => $amount
            ];
        } catch (\PayPal\Exception\PayPalConnectionException $ex) {
            // Detailed connection error logging
            \Log::error('PayPal Connection Error', [
                'error_message' => $ex->getMessage(),
                'error_data' => $ex->getData(),
                'debug_mode' => \Config::get('app.debug')
            ]);

            return [
                'success' => false,
                'error' => 'PayPal connection error: ' . $ex->getMessage()
            ];
        } catch (\Exception $e) {
            \Log::error('PayPal Payment Error', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Enhanced version with complex cart handling (based on your working code)
     */
    protected function processPayPalPaymentWithCart(array $paymentData, float $amount): array
    {
        try {
            $clientId = $this->apiConfig['public_key'] ?? null;
            $clientSecret = $this->apiConfig['secret_key'] ?? null;
            $mode = $this->apiConfig['mode'] ?? 'sandbox';

            if (!$clientId || !$clientSecret) {
                throw new \Exception('PayPal client credentials not configured');
            }

            // Initialize PayPal API Context (using your working approach)
            $apiContext = new \PayPal\Rest\ApiContext(
                new \PayPal\Auth\OAuthTokenCredential($clientId, $clientSecret)
            );

            $apiContext->setConfig([
                'mode' => $mode,
                'log.LogEnabled' => true,
                'log.FileName' => storage_path('logs/paypal.log'),
                'log.LogLevel' => 'DEBUG',
                'cache.enabled' => false,
            ]);

            // Create items array (adapted from your working code)
            $itemArray = [];
            $description = [];
            $cart = Session::get('cart');
            $checkout_order = Session::get('checkout_order');
            $paymentData['items'] = $cart->items;
            if (isset($paymentData['items']) && is_array($paymentData['items'])) {
                foreach ($paymentData['items'] as $cartItem) {
                    $itemName = trim(
                        ($cartItem['sku'] ?? 'N/A') . '-' .
                            ($cartItem['title'] ?? '') . ' ' .
                            ($cartItem['subtitle'] ?? '')
                    );
                    $description[] = $itemName;

                    $item = new \PayPal\Api\Item();
                    $item->setName($itemName)
                        ->setCurrency(strtoupper($paymentData['currency']))
                        ->setQuantity(max(1, intval($cartItem['quantity'] ?? 1)))
                        ->setPrice(number_format(max(0, floatval($cartItem['price'] ?? 0)), 2, '.', ''));

                    $itemArray[] = $item;
                }
            } else {
                // Single item fallback
                $item = new \PayPal\Api\Item();
                $item->setName($paymentData['description'] ?? 'Order Payment')
                    ->setCurrency(strtoupper($paymentData['currency']))
                    ->setQuantity(1)
                    ->setPrice(number_format($amount, 2, '.', ''));
                $itemArray[] = $item;
            }

            // Create Payer
            $payer = new \PayPal\Api\Payer();
            $payer->setPaymentMethod('paypal');

            // Calculate amounts (using your working logic)
            $subtotal = $paymentData['subtotal'] ?? $amount;
            $shipping = $paymentData['shipping'] ?? 0;
            $discount = $paymentData['discount'] ?? 0;
            $tax = $paymentData['tax'] ?? 0;

            // Create Amount Details
            $details = new \PayPal\Api\Details();
            $details->setSubtotal(number_format($subtotal, 2, '.', ''))
                ->setTax(number_format($tax, 2, '.', ''))
                ->setShipping(number_format($shipping, 2, '.', ''))
                ->setShippingDiscount(number_format($discount, 2, '.', ''));

            // Create Amount
            $amountObj = new \PayPal\Api\Amount();
            $amountObj->setCurrency(strtoupper($paymentData['currency']))
                ->setTotal(number_format($amount, 2, '.', ''))
                ->setDetails($details);

            // Create Item List
            $itemList = new \PayPal\Api\ItemList();
            $itemList->setItems($itemArray);

            // Create Transaction
            $transaction = new \PayPal\Api\Transaction();
            $transaction->setAmount($amountObj)
                ->setItemList($itemList)
                ->setDescription(implode('|', array_filter($description)))
                ->setInvoiceNumber($paymentData['reference'] ?? uniqid());

            // Create Redirect URLs
            $redirectUrls = new \PayPal\Api\RedirectUrls();
            $redirectUrls->setReturnUrl($paymentData['callback_url'] ?? '')
                ->setCancelUrl($paymentData['cancel_url'] ?? '');

            // Create Payment
            $payment = new \PayPal\Api\Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);

            // Log payment details (using your approach)
            \Log::info('PayPal Payment Request Details', [
                'cart_amount' => $amount,
                'total_items' => count($itemArray),
                'discount_amount' => $discount,
                'shipping_amount' => $shipping,
                'payment_details' => $payment->toArray()
            ]);

            // Create payment
            $payment->create($apiContext);

            // Find approval URL
            $approvalUrl = null;
            foreach ($payment->getLinks() as $link) {
                if ($link->getRel() === 'approval_url') {
                    $approvalUrl = $link->getHref();
                    break;
                }
            }

            if (!$approvalUrl) {
                throw new \Exception('Could not retrieve PayPal approval URL');
            }

            return [
                'success' => true,
                'transaction_id' => $payment->getId(),
                'authorization_url' => $approvalUrl,
                'reference' => $paymentData['reference'],
                'provider_response' => $payment->toArray(),
                'amount' => $amount
            ];
        } catch (\PayPal\Exception\PayPalConnectionException $ex) {
            \Log::error('PayPal Connection Error', [
                'error_message' => $ex->getMessage(),
                'error_data' => $ex->getData(),
                'debug_mode' => \Config::get('app.debug')
            ]);

            return [
                'success' => false,
                'error' => 'PayPal connection error: ' . $ex->getMessage()
            ];
        } catch (\Exception $ex) {
            \Log::error('Unexpected PayPal Payment Error', [
                'error_message' => $ex->getMessage(),
                'error_trace' => $ex->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $ex->getMessage()
            ];
        }
    }
    protected function processPayPalPayments(array $paymentData, float $amount): array
    {
        try {
            $clientId = $this->apiConfig['public_key'] ?? null;
            $clientSecret = $this->apiConfig['secret_key'] ?? null;
            $mode = $this->apiConfig['mode'] ?? 'sandbox'; // 'sandbox' or 'live'
            if (!$clientId || !$clientSecret) {
                throw new \Exception('PayPal client credentials not configured');
            }

            // Initialize PayPal API Context
            $apiContext = new \PayPal\Rest\ApiContext(
                new \PayPal\Auth\OAuthTokenCredential($clientId, $clientSecret)
            );
            $apiContext->setConfig([
                'mode' => $mode,
                'log.LogEnabled' => false,
                'log.FileName' => '',
                'log.LogLevel' => 'INFO',
                'cache.enabled' => false,
            ]);

            // Create Payer
            $payer = new \PayPal\Api\Payer();
            $payer->setPaymentMethod('paypal');

            // Create Amount
            $amountObj = new \PayPal\Api\Amount();
            $amountObj->setCurrency(strtoupper($paymentData['currency']))
                ->setTotal(number_format($amount, 2, '.', ''));

            // Create Transaction
            $transaction = new \PayPal\Api\Transaction();
            $transaction->setAmount($amountObj)
                ->setDescription('Order Payment')
                ->setInvoiceNumber($paymentData['reference'] ?? uniqid())
                ->setCustom(json_encode([
                    'order_id' => $paymentData['order_id'] ?? '',
                    'reference' => $paymentData['reference']
                ]));

            // Create Redirect URLs
            $redirectUrls = new \PayPal\Api\RedirectUrls();
            $redirectUrls->setReturnUrl($paymentData['callback_url'] ?? '')
                ->setCancelUrl($paymentData['cancel_url'] ?? '');

            // Create Payment
            $payment = new \PayPal\Api\Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);

            // Create payment
            $payment->create($apiContext);

            // Get approval URL
            $approvalUrl = null;
            foreach ($payment->getLinks() as $link) {
                if ($link->getRel() === 'approval_url') {
                    $approvalUrl = $link->getHref();
                    break;
                }
            }

            if (!$approvalUrl) {
                throw new \Exception('Could not retrieve PayPal approval URL');
            }

            return [
                'success' => true,
                'transaction_id' => $payment->getId(),
                'authorization_url' => $approvalUrl,
                'reference' => $paymentData['reference'],
                'provider_response' => $payment->toArray(),
                'amount' => $amount
            ];
        } catch (\PayPal\Exception\PayPalConnectionException $e) {
            return [
                'success' => false,
                'error' => 'PayPal connection error: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process BTC payment
     */
    protected function processBTCPayment(array $paymentData, float $amount): array
    {
        
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        foreach ($cart->items as $item) {
            $data = [
                'amount' => $amount,
                'currency' => getDefaultCurrencyCode(),
            ];
        }
        try {
            $invoice = $this->btcPayService->createInvoice($data, $paymentData['callback_url']);
            //dd($invoice);
            Session::put('btc_invoice_id', $invoice['id']);
              return [
                'success' => true,
                'transaction_id' => $invoice['id'],
                'authorization_url' => $invoice['checkoutLink'],
                'reference' => $paymentData['reference'],
                'provider_response' => $invoice,
                'amount' => $amount
            ];
        } catch (\Exception $e) {
            Log::error('BTCPay Error: ' . $e->getMessage());
           return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
      
    }

    /**
     * Generic payment processor for custom integrations
     */
    protected function processGenericPayment(array $paymentData, float $amount): array
    {
        // Fallback for custom payment methods
        throw new \Exception('Payment method not implemented: ' . $this->paymentMethod->name);
    }

    /**
     * Get SquadPay API URL based on environment
     */
    protected function getSquadApiUrl(): string
    {
        return $this->paymentMethod->sandbox_mode
            ? 'https://sandbox-api-d.squadco.com'
            : 'https://api-d.squadco.com';
    }

    /**
     * Get Flutterwave API URL based on environment
     */
    protected function getFlutterwaveApiUrl(): string
    {
        return $this->paymentMethod->sandbox_mode
            ? 'https://api.flutterwave.com/v3'
            : 'https://api.flutterwave.com/v3';
    }

    /**
     * Get BNPL authorization URL
     */
    protected function getBNPLAuthUrl(string $provider, array $payload): string
    {
        // This would be provider-specific URL generation
        return "https://{$provider}.com/checkout?" . http_build_query($payload);
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(string $reference): array
    {

        try {
            switch ($this->paymentMethod->name) {
                case 'squadpay':
                    return $this->verifySquadPayment($reference);
                case 'flutterwave':
                    return $this->verifyFlutterwavePayment($reference);
                case 'stripe':
                    return $this->verifyStripePayment($reference);
                case 'paypal':
                    return $this->verifyPayPalPayment($reference);
                case 'btcpay':
                    return $this->verifyBTCPayment($reference);
                default:
                    throw new \Exception('Payment verification not implemented for: ' . $this->paymentMethod->name);
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'verified' => false,
                'error' => $e->getMessage()
            ];
        }
    }



    /**
     * Process refund
     */
    public function processRefund(string $transactionId, float $amount, string $reason = ''): array
    {
        if (!$this->paymentMethod->supports_refunds) {
            return [
                'success' => false,
                'error' => 'Refunds not supported by this payment method'
            ];
        }

        try {
            switch ($this->paymentMethod->name) {
                case 'squadpay':
                    return $this->processSquadRefund($transactionId, $amount, $reason);
                case 'flutterwave':
                    return $this->processFlutterwaveRefund($transactionId, $amount, $reason);
                case 'stripe':
                    return $this->processStripeRefund($transactionId, $amount, $reason);
                default:
                    throw new \Exception('Refund not implemented for: ' . $this->paymentMethod->name);
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process SquadPay refund
     */
    protected function processSquadRefund(string $transactionId, float $amount, string $reason): array
    {
        // Implementation for SquadPay refund API
        // This would depend on SquadPay's refund API
        return [
            'success' => true,
            'refund_id' => 'squad_refund_' . time(),
            'status' => 'processing'
        ];
    }

    /**
     * Process Flutterwave refund
     */
    protected function processFlutterwaveRefund(string $transactionId, float $amount, string $reason): array
    {
        // Implementation for Flutterwave refund API
        return [
            'success' => true,
            'refund_id' => 'flw_refund_' . time(),
            'status' => 'processing'
        ];
    }

    /**
     * Process Stripe refund
     */
    protected function processStripeRefund(string $transactionId, float $amount, string $reason): array
    {
        // Implementation for Stripe refund using Stripe SDK
        return [
            'success' => true,
            'refund_id' => 'stripe_refund_' . time(),
            'status' => 'succeeded'
        ];
    }

    /**
 * Verify BTCPay Server payment - Enhanced to include split payment context
 */
protected function verifyBTCPayment(string $reference): array
{
    try {
        $serverUrl = $this->apiConfig['server_url'] ?? 'https://testnet.demo.btcpayserver.org';
        $apiKey = $this->apiConfig['api_key'] ?? null;
        $storeId = $this->apiConfig['store_id'] ?? null;
        if (!$apiKey) {
            throw new \Exception('BTCPay Server API key not configured');
        }

        if (!$storeId) {
            throw new \Exception('BTCPay Server store ID not configured');
        }

        // Get invoice ID - try multiple sources
        $invoiceId = null;
        
        // 1. Try from URL parameters
        $invoiceId = $_GET['invoiceId'] ?? $_GET['invoice_id'] ?? Session::get('btc_invoice_id');
        // 2. Try from session if stored during payment creation
        if (!$invoiceId) {
            $invoiceId = Session::get('btcpay_invoice_id');
        }
        
        // 3. Try to use reference as invoice ID
        if (!$invoiceId) {
            $invoiceId = $reference;
        }

        if (!$invoiceId) {
            throw new \Exception('BTCPay Server invoice ID not found. Reference: ' . $reference);
        }

        Log::info('BTCPay payment verification attempt', [
            'invoice_id' => $invoiceId,
            'reference' => $reference,
            'store_id' => $storeId
        ]);

        // Create HTTP client
        $client = new \GuzzleHttp\Client([
            'base_uri' => rtrim($serverUrl, '/') . '/',
            'headers' => [
                'Authorization' => 'token ' . $apiKey,
                'Content-Type' => 'application/json'
            ]
        ]);

        // Get invoice details
        $response = $client->get("api/v1/stores/{$storeId}/invoices/{$invoiceId}");

        if ($response->getStatusCode() !== 200) {
            throw new \Exception('BTCPay Server invoice not found');
        }

        $invoiceData = json_decode($response->getBody(), true);
        
        if (!$invoiceData) {
            throw new \Exception('BTCPay Server returned invalid invoice data');
        }

        // Extract invoice information
        $invoiceStatus = strtolower($invoiceData['status'] ?? 'unknown');
        $amountPaid = floatval($invoiceData['amount'] ?? 0);
        $currency = $invoiceData['currency'] ?? 'USD';
        $buyerEmail = $invoiceData['buyer']['email'] ?? null;
        
        Log::info('BTCPay invoice current status', [
            'invoice_id' => $invoiceId,
            'status' => $invoiceStatus,
            'amount' => $amountPaid,
            'currency' => $currency
        ]);

        // Determine verification status - be lenient like other payment methods
        // BTCPay Server invoice statuses: New, Paid, Confirmed, Complete, Expired, Invalid
        $successStates = ['paid', 'confirmed', 'complete', 'settled'];
        $failedStates = ['expired', 'invalid'];
        
        $isVerified = false;
        
        if (in_array($invoiceStatus, $successStates)) {
            // Payment successful states
            $isVerified = true;
            Log::info('BTCPay payment verified successfully', ['status' => $invoiceStatus]);
        } elseif (in_array($invoiceStatus, $failedStates)) {
            // Clear failure states
            $isVerified = false;
            Log::info('BTCPay payment failed', ['status' => $invoiceStatus]);
        } elseif ($invoiceStatus === 'new') {
            // Invoice created but not paid yet
            $isVerified = false;
            Log::info('BTCPay payment not completed - still new');
        } else {
            // Any other status - be optimistic like other payment methods
            $isVerified = true;
            Log::info('BTCPay payment verified with status: ' . $invoiceStatus);
        }

        Log::info('BTCPay payment verification final result', [
            'invoice_id' => $invoiceId,
            'status' => $invoiceStatus,
            'is_verified' => $isVerified,
            'amount' => $amountPaid
        ]);

        // Enhanced response structure matching other verification methods
        $result = [
            'success' => true,
            'verified' => $isVerified,
            'status' => $invoiceStatus,
            'amount_paid' => $amountPaid,
            'currency' => $currency,
            'reference' => $reference,
            'invoice_id' => $invoiceId,
            'buyer_email' => $buyerEmail,
            'checkout_link' => $invoiceData['checkoutLink'] ?? null,
            'created_time' => $invoiceData['createdTime'] ?? null,
            'expiration_time' => $invoiceData['expirationTime'] ?? null,
            'provider_response' => $invoiceData
        ];

        // Check if this is part of a split payment (like other methods)
        $splitState = Session::get('split_payment_state');
        if ($splitState && $isVerified) {
            if ($splitState['current_transaction'] == 1) {
                $result['is_first_split_payment'] = true;
                $result['remaining_amount'] = $splitState['split_calculation']['second_transaction']['amount'];
            } else {
                $result['is_final_split_payment'] = true;
                $result['total_amount_paid'] = $splitState['total_amount'];
            }
        }

        return $result;

    } catch (\GuzzleHttp\Exception\RequestException $e) {
        $errorBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : 'No response';
        
        Log::error('BTCPay verification request failed', [
            'error' => $e->getMessage(),
            'response_body' => $errorBody,
            'status_code' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : null,
            'reference' => $reference
        ]);

        return [
            'success' => false,
            'verified' => false,
            'error' => 'BTCPay Server verification failed: ' . $e->getMessage(),
            'reference' => $reference
        ];

    } catch (\Exception $e) {
        Log::error('BTCPay verification error', [
            'error_message' => $e->getMessage(),
            'reference' => $reference
        ]);

        return [
            'success' => false,
            'verified' => false,
            'error' => 'BTCPay payment verification failed: ' . $e->getMessage(),
            'reference' => $reference
        ];
    }
}
}
