<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCountryMessageSettingsTable extends Migration
{
      public function up()
    {
        Schema::create('country_message_settings', function (Blueprint $table) {
            $table->id();
            $table->string('country_code', 5)->unique(); // e.g. 'IN', 'US'
            $table->enum('mode', ['otp', 'reply'])->default('otp'); // messaging mode
            $table->string('twilio_sms_from')->nullable(); // Twilio sender number for this country
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('country_message_settings');
    }
}
