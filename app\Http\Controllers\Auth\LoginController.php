<?php

namespace App\Http\Controllers\Auth;

use Session;
use Validator;
use App\Models\Page;
use Illuminate\Http\Request;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;

use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Validator as FacadesValidator;

// use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    use ApiResponseHelpers;
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    private function redirectTo()
    {
        return isAdmin() ? '/admin/dashboard' : auth()->user()->username;
    }

    protected function validator(array $data)
    {
        return FacadesValidator::make($data, [
            'email' => 'required|string|email|max:255|exists:users,email',
            'password' => 'required|string|min:6',

        ]);
    }
    public function login(Request $request): JsonResponse
{

    $this->validator($request->all())->validate();


    if ($this->hasTooManyLoginAttempts($request)) {
        $this->fireLockoutEvent($request);
        return $this->sendLockoutResponse($request);
    }


    $my_profile_page = Page::where('page_key', 'my_profile')->where('status', 1)->first();


    if (!$my_profile_page) {
        return response()->json(['error' => 'Profile page not found'], 404);
    }


    if ($this->attemptLogin($request)) {
        $auth_user = auth()->user();

        if ($auth_user) {
            Storage::deleteDirectory($this->sessionFolderPath . $auth_user->username);
        }


        Session::forget('checkout_order');


        return response()->json([
            'url' => isAdmin() ? '/admin/dashboard' : $my_profile_page->slug,
        ], 200);
    }


    $this->incrementLoginAttempts($request);


    return $this->sendFailedLoginResponse($request);
}
protected function validateReCaptcha(Request $request)
{
    $recaptchaResponse = $request->input('g-recaptcha-response');
    $secret = config('services.recaptcha.secret');

    // Make a request to Google's reCAPTCHA verification API
    $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
        'secret' => $secret,
        'response' => $recaptchaResponse,
    ]);

    $data = $response->json();

    // If reCAPTCHA fails, return an error response
    if (!$data['success']) {
        throw \Illuminate\Validation\ValidationException::withMessages([
            'g-recaptcha-response' => ['reCAPTCHA verification failed. Please try again.'],
        ]);
    }
}

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function logout(Request $request)
    {
        $cart = Session::get('cart');
        $location_data = Session::get('shop_logged_data');

        $this->guard()->logout();
        $request->session()->invalidate();

        Session::forget('checkout_order');
        session()->put('cart', $cart);
        session()->put('shop_logged_data', $location_data);

        return $this->loggedOut($request) ?: redirect('/');
    }
}