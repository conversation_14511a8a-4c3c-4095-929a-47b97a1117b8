<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateStockColumnInProductItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('product_items')) {
            Schema::table('product_items', function (Blueprint $table) {
                // Drop the old 'stock' column only if it exists
                if (Schema::hasColumn('product_items', 'stock')) {
                    $table->dropColumn('stock');
                }

                // Add the new 'stock_status' column only if it doesn't exist
                if (!Schema::hasColumn('product_items', 'stock_status')) {
                    $table->enum('stock_status', ['yes', 'sold'])->default('yes')->after('product_item_images');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('product_items')) {
            Schema::table('product_items', function (Blueprint $table) {
                // Drop 'stock_status' if it exists
                if (Schema::hasColumn('product_items', 'stock_status')) {
                    $table->dropColumn('stock_status');
                }

                // Re-add the old 'stock' column only if it doesn't exist
                if (!Schema::hasColumn('product_items', 'stock')) {
                    $table->boolean('stock')->default(true)->after('product_item_images');
                }
            });
        }
    }
}
