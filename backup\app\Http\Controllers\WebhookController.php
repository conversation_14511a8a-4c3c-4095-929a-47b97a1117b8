<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Models\Promo;
use App\Models\Coupon;
use App\Models\SentDiscount;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Setting;
use App\Models\Subscriber;
use Illuminate\Support\Facades\Mail;
use App\Mail\PromoCodeEmail;
use App\Mail\SiteEmail;
use Illuminate\Support\Facades\Session;

class WebhookController extends Controller
{
    public function metaWebhook(Request $request)
    {
        Log::error('Incoming Meta: ' . json_encode($request->all()));

        // Verification
        if ($request->isMethod('get')) {
            if ($request->hub_verify_token === env('WHATSAPP_VERIFY_TOKEN')) {

                return response($request->hub_challenge, 200);
            }
            return response('Invalid token', 403);
        }
        $entries = $request->input('entry', []);
        foreach ($entries as $entry) {
            foreach ($entry['changes'] as $change) {
                $value = $change['value'];
                Log::error('Incoming Status: ');

                if (isset($value['statuses'])) {
                    $this->handleStatusUpdate($value['statuses']);
                }
                if (isset($value['messages'])) {
                    $this->handleIncomingMessage($request);
                }
            }
        }



        return response()->json(['status' => 'ok']);
    }




    public function handleIncomingMessage($request)
    {
        Log::error('Incoming Message: ' . json_encode($request->all()));

        // Message received
        $message = $request->input('entry.0.changes.0.value.messages.0');

        $phone = '+' . $message['from'] ?? null;
        $text = strtolower(trim($message['text']['body'] ?? ''));
        $discount = SentDiscount::where('phone', $phone)->first();

        $promo = Coupon::find($discount->promo_id);
        $promoCode = $promo->code;
        if ($discount && $discount->status !== 'sent' && $text == 'start') {
            $discount->status = 'sent';
            $discount->user_replied = true;
            $discount->save();

            $promoCode = Coupon::where('id', $discount->promo_id)->first();

            try {

                // $whatsappSent = $this->sendViaMetaWhatsApp($phone, [$promoCode->code], 'offer_code');
                // $sentDiscount = SentDiscount::where('phone', $phone)->first();

                // if ($sentDiscount) {
                //     Subscriber::create([
                //         'email' => $sentDiscount->email,
                //         'phone' => $phone,
                //     ]);
                // }
                $details = (object)[
                    'pre_header' => 'Here is your exclusive promo code!',
                    'subject' => 'Here is your promo code!',
                    'content' => '<p>Use promo code <strong>' . $promoCode->code . '</strong> at checkout to get your offer.</p>',
                ];

                Mail::to($discount->email)->send(new SiteEmail($details));
                $sentDiscount = SentDiscount::where('phone', $phone)->first();

                // if ($sentDiscount) {
                //     Subscriber::create([
                //         'email' => $sentDiscount->email,
                //         'phone' => $phone,
                //     ]);
                // }
                $this->addGuestUser($discount->email, $phone);
            } catch (\Exception $e) {
                Log::error('Meta WhatsApp message error: ' . $e->getMessage());
            }
        }

        if ($text == 'stop') {
            try {
                $subscriber = Subscriber::where('phone', $phone)->first();

                if ($subscriber) {
                    $subscriber->email_notification_status = false;
                    $subscriber->sms_notification_status = false;
                    $subscriber->whatsapp_notification_status = false;
                    $subscriber->save();
                }
                $user = User::where('tel_cell', $phone);

                // Deserialize
                $preferences = @unserialize($user->alert_preferences);

                if ($preferences && is_array($preferences)) {
                    if (isset($preferences['promotional_alerts'])) {
                        $preferences['promotional_alerts']['sms'] = '0';
                    }
                }
                $user->alert_preferences = serialize($preferences);
                $user->save();
                $whatsappSent = $this->sendViaMetaWhatsApp($phone, ['there', 'all'], 'unsubscribe_notification');
            } catch (\Exception $e) {
                Log::error('Meta WhatsApp message error: ' . $e->getMessage());
            }
        }
    }

    public function twilioWebhook(Request $request)
    {
        $phone = $request->input('From'); // Format: whatsapp:+123456789 or +123456789
        $message = strtolower($request->input('Body'));
        Log::error('twilioWebhook message: ' . $message);
        $phone = str_replace('whatsapp:', '', $phone);

        Log::error('twilioWebhook phone: ' . $phone);
        Log::error('twilioWebhook all: ' . json_encode($request->all()));

        $discount = SentDiscount::where('phone', $phone)->first();
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $twilioSenderId = \App\Models\CountryMessageSetting::where('country_code', $selectedCountry)->value('twilio_sms_from') ?? env('TWILIO_SID');
        $promo = Coupon::find($discount->promo_id);
        $promoCode = $promo->code;
        if ($discount && $discount->status !== 'sent' && $message == 'start') {
            Log::error('twilioWebhook inside: ' . json_encode($promoCode));
            $discount->user_replied = true;
            $discount->status = 'sent';
            $discount->save();

            $promoCode = Coupon::where('id', $discount->promo_id)->first();

            try {
                $details = (object)[
                    'pre_header' => 'Here is your exclusive promo code!',
                    'subject' => 'Here is your promo code!',
                    'content' => '<p>Use promo code <strong>' . $promoCode->code . '</strong> at checkout to get your offer.</p>',
                ];

                Mail::to($discount->email)->send(new SiteEmail($details));
                // $twSent = $this->sendViaTwilioSms($phone, 'Here is your promo code: ' . $promoCode->promo_code, 'reply', $twilioSenderId);
                $sentDiscount = SentDiscount::where('phone', $phone)->first();

                // if ($sentDiscount) {
                //     Subscriber::create([
                //         'email' => $sentDiscount->email,
                //         'phone' => $phone,
                //     ]);
                // }
                $this->addGuestUser($discount->email, $phone);
            } catch (\Exception $e) {
                Log::error('twil error: ' . $e->getMessage());
            }
        }
        if ($message == 'stop') {
            try {
                $subscriber = Subscriber::where('phone', $phone)->first();

                if ($subscriber) {
                    $subscriber->email_notification_status = false;
                    $subscriber->sms_notification_status = false;
                    $subscriber->whatsapp_notification_status = false;
                    $subscriber->save();
                }
                $user = User::where('tel_cell', $phone);

                // Deserialize
                $preferences = @unserialize($user->alert_preferences);

                if ($preferences && is_array($preferences)) {
                    if (isset($preferences['promotional_alerts'])) {
                        $preferences['promotional_alerts']['sms'] = '0';
                    }
                }
                $user->alert_preferences = serialize($preferences);
                $user->save();
                $twSent = $this->sendViaTwilioSms($phone, "Hi, we're sorry to see you go. You've successfully unsubscribed from all notifications.", 'reply', $twilioSenderId);
            } catch (\Exception $e) {
                Log::error('Meta WhatsApp message error: ' . $e->getMessage());
            }
        }

        return response("OK", 200);
    }
    protected function handleStatusUpdate(array $statuses)
    {
        foreach ($statuses as $status) {
            $phone = $status['recipient_id'];
            $state = $status['status'];
            DB::table('message_statuses')
                ->where('phone', $phone)
                ->update(['status' => $state]);
            Log::info("Delivery status for $phone: $state");
        }
    }
}
