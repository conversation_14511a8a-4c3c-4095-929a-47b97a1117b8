<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveReturenableFromProductItemsTable extends Migration
{
    public function up(): void
    {
        Schema::table('product_items', function (Blueprint $table) {
            if (Schema::hasColumn('product_items', 'returenable')) {
                $table->dropColumn('returenable');
            }
        });
    }

    public function down(): void
    {
        Schema::table('product_items', function (Blueprint $table) {
            $table->boolean('returenable')->default(false); // or whatever the original type was
        });
    }
}
