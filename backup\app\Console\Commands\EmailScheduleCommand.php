<?php

namespace App\Console\Commands;

use Exception;
use Carbon\Carbon;
use App\Models\Cart;
use App\Models\User;
use App\Models\Order;
use App\Mail\SiteEmail;
use App\Models\NotifyItem;
use App\Mail\EmailSchedule;
use App\Models\UserGroupMap;
use EasyPost\EasyPostClient;
use App\Models\EmailTemplate;
use Illuminate\Console\Command;
use App\Services\WhatsAppService;
use App\Models\EmailTemplateGroup;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use App\Models\Tracking;

class EmailScheduleCommand extends Command
{
    protected $whatsapp;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger-email-schedule';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $today = Carbon::now()->timezone(site_info('site_time_zone'));
        $now_time = Carbon::now()->timezone(site_info('site_time_zone'))->format('H:i');


        $this->info('Starting EmailCampaign...');

        $campaigns = DB::table('email_campaigns')
            ->where(function ($query) use ($today) {
                $query->whereNull('expire_date')->orWhereDate('expire_date', '>', $today);
            })
            ->orderByDesc('created_at')
            ->where('status', true)
            ->get();

        foreach ($campaigns as $camp) {

            $times = explode(',', $camp->frequency_time);
            if ($camp->title == 'AbandonedCarts') {
                dump($times);
                dump($now_time);
            }
            if (in_array($now_time, $times)) {
                $trigger = false;
                switch ($camp->frequency_type) {
                    case 'daily':
                        $trigger = true;
                        break;
                    case 'date':
                        $trigger = ($camp->frequency_date == $today->toDateString());
                        break;
                    case 'days':
                        $trigger = in_array($today->dayOfWeek, explode(',', $camp->frequency_days));
                        break;
                    default:
                        break;
                }

                if ($trigger) {
                    dump($trigger);
                    $this->info(">>> Triggering: $camp->title, [" . ($camp->frequency_type) . " @ $camp->frequency_time]");
                    switch ($camp->title) {
                        case 'TrackingStatusShippment':
                            $this->SendTrackShippmentEmail();
                            break;
                        case 'NotifyItem':
                            $this->NotifyUser();
                            break;
                        case 'Birthday':
                            $this->birdthdayWish();
                            break;
                        case 'AbandonedCarts':
                            $this->abandonedCarts();
                            break;
                        default:
                            $users_id = UserGroupMap::whereIn('user_group_id', explode(',', $camp->user_groups_id))->get();
                            $this->sendMail($camp->id, $users_id);
                            break;
                    }
                }
            } else {
                dump('error');
            }
        }
        $this->info('Completed.');
    }

    public function sendMail($camp_id, $users_id)
    {

        foreach ($users_id as $value) {

            $user = User::find($value->user_id);
            if ($user) {
                Mail::to($user->email)->send(new EmailSchedule($camp_id, $user));
            }
        }
    }
    public function SendTrackShippmentEmail()
    {
        $controller = new Controller();
        $orders = Order::where('status', '!=', 5)
            ->whereHas('user', function ($query) {
                $query->where('country', 'US');
            })
            ->with('user')
            ->get();
        foreach ($orders as $order) {
            $user_email_templates_group = EmailTemplateGroup::where('title', 'Order_Shipped_API')->first();
            $user_email_templates = EmailTemplate::where('email_template_group_id', $user_email_templates_group->id)->get();
            $tracking_code = $order->tracking_code;
            if ($tracking_code) {
                $client = new EasyPostClient(config('app.easypost_api'));
                $tracker = $client->tracker->create([
                    // 'tracking_code' => $order->tracking_code,
                    'tracking_code' => 'EZ2000000002',
                ]);


                foreach ($user_email_templates as $user_email_template) {
                    $user_email_template->email_body = unserialize($user_email_template->email_body);
                    $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                    $mail_data = (object)[
                        'user' => $order->user,
                        'order' => $order,
                        'subject' => $user_email_template->subject,
                        'pre_header' => $user_email_template->pre_header,
                        'email_body' => $user_email_template->email_body,
                        'sms_status' => $user_email_template->sms_status ? true : false,
                        'sms_body' => $user_email_template->sms_body,
                        'tracking_code' => $tracker->public_url
                    ];
                    $sms_message = $controller->sendEmailData($mail_data)->sms_body;
                    $order->user->tel_cell = str_replace('+', '', $order->user->tel_cell);
                    if ($order->user->tel_cell) {
                        //$this->checkWhatsappExist($user_email_template->sms_status, $order->user->tel_cell, $sms_message);
                    }
                    if ($tracker->status === 'in_transit') {
                        $order->shipped_at = now();
                        $order->status = 5;
                        $order->update();
                        $this->info("Order ID {$order->id} marked as Shipped.");
                        $mail_data->subject = $controller->sendEmailData($mail_data)->subject;
                        $mail_data->pre_header = $controller->sendEmailData($mail_data)->pre_header;
                        $mail_data->content = $controller->sendEmailData($mail_data)->content;
                        $mail_data->sms_content = $controller->sendEmailData($mail_data)->sms_body;
                        Mail::to($order->user->email)->send(new SiteEmail($mail_data));
                    }
                }
            }
        }
        $this->info('Shipping status check completed.');
    }

    public function NotifyUser()
    {
        dump('notify');
        $controller = new Controller();
        $notify_users = NotifyItem::whereHas('productItem', function ($query) {
            $query->where('stock_status', 'yes')
                ->whereHas('product', function ($query) {
                    $query->where('inventory_status', '!=', 'out_of_stock');
                });
        })->get();
        foreach ($notify_users as $notify_user) {
            $user_email_templates_group = EmailTemplateGroup::where('title', 'NotifyUser')->first();
            $user_email_templates = EmailTemplate::where('email_template_group_id', $user_email_templates_group->id)->get();
            foreach ($user_email_templates as $user_email_template) {
                $user_email_template->email_body = unserialize($user_email_template->email_body);
                $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                $mail_data = (object)[
                    'customer_email' => $notify_user->notify_email,
                    'product_name' => $notify_user->productItem->product->title,
                    'subject' => $user_email_template->subject,
                    'pre_header' => $user_email_template->pre_header,
                    'email_body' => $user_email_template->email_body,
                    'sms_status' => $user_email_template->sms_status ? true : false,
                    'sms_body' => $user_email_template->sms_body,
                    'product_price' => $notify_user->productItem->sale_price,
                    'product_url' => "https://buy.codeitjs.com/",
                    'your_company_name' => '1GuyGadjets',
                ];
                $mail_data->used_vars = $controller->sendEmailData($mail_data)->used_vars;
                $mail_data->store_location = $notify_user->productItem->store_id;
                $sms_message = $controller->sendEmailData($mail_data)->sms_body;
                $notify_user->notify_phone = str_replace('+', '', $notify_user->notify_phone);
                if ($notify_user->notify_phone) {
                    //$this->checkWhatsappExist($user_email_template->sms_status, $notify_user->notify_phone, $sms_message);

                    $sms_vars = $controller->sendEmailData($mail_data)->used_vars;
                    if (strpos($notify_user->notify_phone, '+') !== 0) {
                        $notify_user->notify_phone = '+' . $notify_user->notify_phone;
                    }
                    $this->sendMessage($notify_user->notify_phone, $sms_vars, null, null, $user_email_template->sms_body['template_id'], $sms_message, $controller);
                }
            }
            $users_email = $notify_user->notify_email;
            $mail_data->subject = $controller->sendEmailData($mail_data)->subject;
            $mail_data->pre_header = $controller->sendEmailData($mail_data)->pre_header;
            $mail_data->content = $controller->sendEmailData($mail_data)->content;
            $mail_data->sms_content = $controller->sendEmailData($mail_data)->sms_body;

            Mail::to($users_email)->later(now()->addSeconds(30), new SiteEmail($mail_data));
            dump('done');
        }
    }
    function sendMessage($phone, $vars = null, $mode = 'otp', $twilioSenderId = null, $template, $smsContent = null, $controller)
    {
        dump($phone);
        $whatsappSent = $controller->sendViaMetaWhatsApp($phone, $vars, $template, 'en_US');
        Log::error('Meta WhatsApp status: ' . $whatsappSent);
        if (empty($twilioSenderId)) {
            $twilioSenderId = env('TWILIO_SID');
        }

        if (!$whatsappSent) {

            $controller->sendViaTwilioSms($phone, $smsContent, $mode, $twilioSenderId);
            return true;
        }
    }
    public function birdthdayWish()
    {
        // dd(date('m-d'));
        $controller = new Controller();
        $users = User::all();
        foreach ($users as $user) {
            $DOB = $user->DOB;

            if ($DOB) {
                $today = date('m-d');
                $user_birthday = date('m-d', strtotime($DOB));
                if ($user_birthday == $today) {

                    $user_email_templates_group = EmailTemplateGroup::where('title', 'BIrdthday')->first();
                    $user_email_templates = EmailTemplate::where('email_template_group_id', $user_email_templates_group->id)->get();
                    foreach ($user_email_templates as $user_email_template) {
                        $user_email_template->email_body = unserialize($user_email_template->email_body);
                        $user_email_template->sms_body = unserialize($user_email_template->sms_body);
                        $mail_data = (object)[
                            'customer_email' => $user->email,
                            'Customer_Name' => $user->first_name,
                            'subject' => $user_email_template->subject,
                            'pre_header' => $user_email_template->pre_header,
                            'email_body' => $user_email_template->email_body,
                            'sms_status' => $user_email_template->sms_status ? true : false,
                            'sms_body' => $user_email_template->sms_body,
                            'your_company_name' => '1GuyGadjets',
                        ];

                        $sms_message = $controller->sendEmailData($mail_data)->sms_body;
                        $mail_data->used_vars = $controller->sendEmailData($mail_data)->used_vars;
                        $mail_data->store_location = null;
                        $sms_message = $controller->sendEmailData($mail_data)->sms_body;
                        $user->tel_cell = str_replace('+', '', $user->tel_cell);
                        if ($user->tel_cell) {
                            //$this->checkWhatsappExist($user_email_template->sms_status, $notify_user->notify_phone, $sms_message);

                            $sms_vars = $controller->sendEmailData($mail_data)->used_vars;
                            if (strpos($user->tel_cell, '+') !== 0) {
                                $user->tel_cell = '+' . $user->tel_cell;
                            }
                            $this->sendMessage($user->tel_cell, $sms_vars, null, null, $user_email_template->sms_body['template_id'], $sms_message, $controller);
                        }
                        //$this->checkWhatsappExist($user_email_template->sms_status, '923169089872', $sms_message);
                        // $mail_data->subject = $controller->sendEmailData($mail_data)->subject;
                        // $mail_data->pre_header = $controller->sendEmailData($mail_data)->pre_header;
                        // $mail_data->content = $controller->sendEmailData($mail_data)->content;
                        // $mail_data->sms_content = $controller->sendEmailData($mail_data)->sms_body;
                        // // dump($mail_data);


                        // Mail::to($user->email)->later(now()->addSeconds(30), new SiteEmail($mail_data));


                    }
                }
            }
        }
    }

    public function SendMessageToUser($phoneNumber, $message)
    {
        $accessToken = config('services.whatsapp.access_token');
        $phoneNumberId = config('services.whatsapp.phone_number');
        $formattedMessage = preg_replace("/[\r\n\t]+/", " ", $message); // Replace new-lines and tabs with a single space
        $formattedMessage = preg_replace("/ {2,}/", " ", $formattedMessage);     // Replace more than one space with a single space
        $formattedMessage = trim($formattedMessage);



        // API Endpoint
        $url = "https://graph.facebook.com/v21.0/{$phoneNumberId}/messages";

        // Payload
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $phoneNumber, // Recipient phone number (include country code, e.g., +1234567890)
            'type' => 'template',
            'template' => [
                'name' => 'testing_message', // Template name as defined in WhatsApp Business Manager
                'language' => [
                    'code' => 'en' // Language code as configured in the template
                ],
                'components' => [
                    [
                        'type' => 'body', // Matches the "type": "BODY"
                        'parameters' => [
                            [
                                'type' => 'text', // Parameter type as defined
                                'text' => $formattedMessage, // This replaces {{1}}
                            ]
                        ]
                    ]
                ]
            ]
        ];
        // Make the POST Request
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ])->post($url, $payload);
        // dd($response);

        return $response->getStatusCode();
    }

    public function checkWhatsappExist($status, $number, $message)
    {
        $controller = new Controller();
        if ($status) {
            $whatsapp = $this->SendMessageToUser($number, $message);

            if ($whatsapp == 401) {
                $token = $controller->renewAccessToken();
                if ($token->statusCode == 200) {
                    $whatsapp = $this->SendMessageToUser($number, $message);
                }
            }


            if ($whatsapp != 200) {
                $number = '+' . $number;
                $controller->sendMessage($message, $number);
            }
        }
    }
    public function abandonedCarts()
    {
        $controller = new Controller();
        $abandonedCarts = Cart::where('last_activity', '<', now()->subHour())->get();

        $user_email_templates_group = EmailTemplateGroup::where('title', 'AbandonedCarts')->first();

        if (!$user_email_templates_group) {
            return; // Stop execution if no email template group found
        }

        $user_email_templates = EmailTemplate::where('email_template_group_id', $user_email_templates_group->id)->get();

        foreach ($abandonedCarts as $cart) {
            if (!$cart->user_id) {
                continue; // Skip guest carts (or handle them differently)
            }

            $user = User::find($cart->user_id);
            if (!$user) {
                continue; // Skip if user not found
            }

            $cartItems = json_decode($cart->cart_items, true); // Decode JSON to array

            foreach ($user_email_templates as $user_email_template) {
                dump($user_email_template->email_body);
                                dump('ecah');

                $user_email_template->email_body = unserialize($user_email_template->email_body);
                $user_email_template->sms_body = unserialize($user_email_template->sms_body);

                $mail_data = (object) [
                    'customer_email' => $user->email,
                    'Customer_Name' => $user->first_name,
                    'Quantity' => $cartItems['quantity'] ?? 0,
                    'Price' => $cartItems['total'] ?? 0,
                    'subject' => $user_email_template->subject,
                    'pre_header' => $user_email_template->pre_header,
                    'email_body' => $user_email_template->email_body,
                    'sms_status' => (bool) $user_email_template->sms_status,
                    'sms_body' => $user_email_template->sms_body,
                    'product_url' => "https://buy.codeitjs.com/",
                    'your_company_name' => '1GuyGadjets',
                ];

                $emailData = $controller->sendEmailData($mail_data);
                $sms_message = $emailData->sms_body;
                        $mail_data->used_vars = $controller->sendEmailData($mail_data)->used_vars;
                        $mail_data->store_location = null;
                        $sms_message = $controller->sendEmailData($mail_data)->sms_body;
                        $user->tel_cell = str_replace('+', '', $user->tel_cell);
                        if ($user->tel_cell) {
                            //$this->checkWhatsappExist($user_email_template->sms_status, $notify_user->notify_phone, $sms_message);

                            $sms_vars = $controller->sendEmailData($mail_data)->used_vars;
                            if (strpos($user->tel_cell, '+') !== 0) {
                                $user->tel_cell = '+' . $user->tel_cell;
                            }
                            $this->sendMessage($user->tel_cell, $sms_vars, null, null, $user_email_template->sms_body['template_id'], $sms_message, $controller);
                        }
                

                // Send email
                Mail::to($user->email)->send(new SiteEmail($emailData));
            }
        }
    }
}
