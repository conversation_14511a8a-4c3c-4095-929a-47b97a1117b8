{{-- <ul class="main-mobile-menu">
    @foreach ($page->menu as $menu)
        @if($menu->access)
            <li class="{{ count($menu->menuChildren) > 0 ? 'parent' : '' }}">
                <a class="{{ $menu->menu_active ?'active ':'' }} @if(count($menu->menuChildren) > 0)have-sub-menu @endif" @if(count($menu->menuChildren) > 0)href="javascript:void(0)" @else href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}"@endif {{$menu->url_type=='custom_url'?'target="_blank"':''}} data-menu_id="{{ $menu->id }}">
                    @if($menu->menu_icon)<i class="{{ $menu->menu_icon }}"></i>@endif
                    <span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span>
                    @if(count($menu->menuChildren) > 0)
                    <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                        <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                    </svg>
                    @endif
                </a>
                @if(count($menu->menuChildren) > 0)
                    @include('layouts.utility.menu.sub_menu_mobile',[
                        'items' => $menu->menuChildren,
                    ])
                @endif
            </li>
        @endif
    @endforeach
</ul> --}}

<div class="page">
    <div class="block menu-accordion-mobile">
        <div class="menu-accordion-mobile-inner">
            <ul class="main-mobile-menu">
                @foreach ($page->menu as $menu)
                    @if($menu->access)
                        <li class="{{ count($menu->menuChildren) > 0 ? 'parent' : '' }}">
                            <a class="{{ $menu->menu_active ?'active ':'' }} @if(count($menu->menuChildren) > 0)have-sub-menu @endif" @if(count($menu->menuChildren) > 0)href="javascript:void(0)" @else href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}"@endif {{$menu->url_type=='custom_url'?'target="_blank"':''}} data-menu_id="{{ $menu->id }}">
                                @if($menu->menu_icon)<i class="{{ $menu->menu_icon }}"></i>@endif
                                <span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span>
                                @if(count($menu->menuChildren) > 0)
                                <svg viewBox="0 0 10 20" xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" tabindex="-1" disabled="disabled" clickable="false">
                                    <path d="M7.268 9.547L0 16l4-8-4-8 7.268 6.453C7.715 6.82 8 7.377 8 8c0 .623-.285 1.18-.732 1.547z"></path>
                                </svg>
                                @endif
                            </a>
                            @if(count($menu->menuChildren) > 0)
                                @include('layouts.utility.menu.sub_menu_mobile',[
                                    'items' => $menu->menuChildren,
                                    'menu' => $menu,
                                ])
                            @endif
                        </li>
                    @endif
                @endforeach
            </ul>
        </div>
    </div>
    <div class="block bottom-block p-0 m-0 d-xxl-none">
        <ul class="user-area-mobile-list">
            
            <li>
    @guest
    <!-- Show this if the user is not logged in -->
    <a data-toggle="modal" data-title="Customer Login" data-target="#userLoginModal" href="javascript:void(0)">
        <img src="media/images/icon/login.svg" alt="">
        <span>Login</span>
    </a>
    @endguest

    @auth
    <!-- Show this if the user is logged in -->
    <a href="https://buy.codeitjs.com/sign-out" onclick="event.preventDefault(); document.getElementById('logout_form').submit();">
        Logout
    </a>
    <form id="logout_form" action="https://buy.codeitjs.com/sign-out" method="POST" style="display: none;">
        @csrf
    </form>
    @endauth
</li>

            <li>   <a type="button"
                            target="{{ isset($setting) ? '_blank' : '' }}"
                            href="{{ isset($setting) ? $setting->site_link : 'javascript:void(0);' }}"
                            class="{{ isset($setting) ? '' : 'disabled' }}"
                            style="{{ isset($setting) ? '' : 'pointer-events: none;' }}">
                             <span class="badge rounded-pill {{ isset($setting) ? '' : 'disabled' }}"
                                   style="{{ isset($setting) ? '' : 'pointer-events: none; opacity: 0.5;' }}">
                                 Sell | Trade-in
                             </span>
                         </a></li>
            <li>
                <form action="#">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="floatingInputGroup1" placeholder="Search">
                        <span class="input-group-text"><i class="fa-solid fa-magnifying-glass"></i></span>
                    </div>
                </form>
            </li>
        </ul>
    </div>
</div>
