<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AccessLabel;
use Illuminate\Http\Request;
use App\Models\UserGroup;
use Validator;

class AccessLabelController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Access Labels';

        $user_groups = $this->userGroups();

        $items = AccessLabel::paginate($this->itemPerPage);
        $items->each(function ($item) {
            $account_groups_id = explode(',', $item->user_groups_id);
            $user_groups = UserGroup::whereIn('id', $account_groups_id)->get();
            $account_groups_title = '';
            foreach ($user_groups as $account_group) {
                $account_groups_title .= $account_group->title . ', ';
            }
            $item->account_groups_title = substr($account_groups_title, 0, -2) . '';
            $item->account_group_array = $account_groups_id;
        });

        $sl = SLGenerator($items);

        return view('admin.setting.access_label.index', compact('items', 'user_groups', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'user_group_id' => 'required|exists:user_groups,id',
            'status' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        $item = new AccessLabel();
        $item->title = $request->get('title');
        $item->user_groups_id = $request->get('user_group_id');
        $item->status = $request->get('status');
        $item->save();

        $user_groups = UserGroup::whereStatus(true)
            ->where('id', '!=', '1')
            ->paginate($this->itemPerPage);
        $items = AccessLabel::paginate($this->itemPerPage);
        $items->each(function ($item) {
            $account_groups_id = explode(',', $item->user_group_id);
            $user_groups = UserGroup::whereIn('id', $account_groups_id)->get();
            $account_groups_title = '';
            foreach ($user_groups as $account_group) {
                $account_groups_title .= $account_group->title . ', ';
            }
            $item->account_groups_title = substr($account_groups_title, 0, -2) . '';
            $item->account_group_array = $account_groups_id;
        });

        $sl = SLGenerator($items);

        return view('admin.jquery_live.access_labels', compact('items', 'sl'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AccessLabel  $accessLabel
     * @return \Illuminate\Http\Response
     */
    public function show(AccessLabel $accessLabel)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AccessLabel  $accessLabel
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $item = AccessLabel::findOrFail($request->id);
        $item->user_groups_id = explode(',', $item->user_groups_id);
        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AccessLabel  $accessLabel
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AccessLabel $accessLabel)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'user_group_id' => 'required|exists:user_groups,id',
            'status' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        $item = AccessLabel::findOrFail($request->id);
        $item->title = $request->get('title');
        $item->user_groups_id = $request->get('user_group_id');
        $item->status = $request->get('status');
        $item->update();

        $items = AccessLabel::paginate($this->itemPerPage);
        $items->each(function ($item) {
            $account_groups_id = explode(',', $item->user_groups_id);
            $user_groups = UserGroup::whereIn('id', $account_groups_id)->get();
            $account_groups_title = '';
            foreach ($user_groups as $account_group) {
                $account_groups_title .= $account_group->title . ', ';
            }
            $item->account_groups_title = substr($account_groups_title, 0, -2) . '';
            $item->account_group_array = $account_groups_id;
        });

        $sl = SLGenerator($items);

        return view('admin.jquery_live.access_labels', compact('items', 'sl'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AccessLabel  $accessLabel
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        AccessLabel::findOrFail($id)->delete();

        $user_groups = UserGroup::whereStatus(true)
            ->where('id', '!=', '1')
            ->paginate($this->itemPerPage);
        $items = AccessLabel::paginate($this->itemPerPage);
        $items->each(function ($item) {
            $account_groups_id = explode(',', $item->user_groups_id);
            $user_groups = UserGroup::whereIn('id', $account_groups_id)->get();
            $account_groups_title = '';
            foreach ($user_groups as $account_group) {
                $account_groups_title .= $account_group->title . ', ';
            }
            $item->account_groups_title = substr($account_groups_title, 0, -2) . '';
            $item->account_group_array = $account_groups_id;
        });

        $sl = SLGenerator($accessLabels);

        return view('admin.jquery_live.access_labels', compact('items', 'sl'));
    }
}
