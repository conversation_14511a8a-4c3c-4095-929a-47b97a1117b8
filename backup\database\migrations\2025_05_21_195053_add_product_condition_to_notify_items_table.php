<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProductConditionToNotifyItemsTable extends Migration
{
   public function up(): void
    {
        Schema::table('notify_items', function (Blueprint $table) {
            $table->string('product_condition')->nullable()->after('product_item_id'); // replace 'some_existing_column' with an actual column
        });
    }

    public function down(): void
    {
        Schema::table('notify_items', function (Blueprint $table) {
            $table->dropColumn('product_condition');
        });
    }
}
