<?php

namespace App\Http\Controllers;

use App\Models\NotifyItem;
use App\Mail\EmailSchedule;
use App\Models\ProductItem;
use App\Models\Product;
use App\Mail\NotifyUserMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use App\Mail\SiteEmail;
use Illuminate\Support\Facades\Log;
use App\Events\NotifyMeOrder;

class NotifyItemController extends Controller
{
    public function store(Request $request)
    {

        // Validate the incoming request
        $validatedData = $request->validate([
            'notify_email' => 'required|email|max:255',
            'notify_phone' => 'required|string|max:15',
            'product_item_id' => 'required',
            'product_condition' => 'required'
        ]);
        // Get the ProductItem to fetch its product_id
        $productItem = \App\Models\Product::find($validatedData['product_item_id']);
        if ($productItem) {
            $validatedData['product_id'] = $productItem->id;
        }
        // Create a new NotifyItem record
        NotifyItem::create($validatedData);

        // Return a success response
        return response()->json(['message' => 'NotifyItem created successfully!'], 201);
    }
    public function getData(Request $request, $id)
    {

        $query = NotifyItem::where('product_id', $id);


        $notifyItems = $query->skip($request->start)
            ->take($request->length)
            ->get();

        $totalFiltered = $query->count();


        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalFiltered,
            'recordsFiltered' => $totalFiltered,
            'data' => $notifyItems,
        ]);
    }


    public function notifyByEmail(Request $request, $id)
    {

        $notifyItem = NotifyItem::find($id);
        if (!$notifyItem) {
            return response()->json(['error' => 'NotifyItem not found'], 404);
        }
        $productItem = Product::find($notifyItem->product_id);

        event(new NotifyMeOrder($notifyItem->notify_email, $notifyItem->notify_phone, $productItem, $request->sms_status));
        if ($request->status_email) {
            $notifyItem->status_email = 1;
        }
        if ($request->status_phone) {
            $notifyItem->status_phone = 1;
        }
        $notifyItem->update();
        return response()->json(['sucess' => 'Email sent sucessfully'], 200);

        //         switch (true) {
        //             case $request->status_email:
        //                 $notifyItem->status_email = 1;
        //                 $productItem = Product::find($notifyItem->product_id);
        //                 $mail_data = new \stdClass();

        //                 $mail_data->subject = 'Good News! Your Desired Product is Back in Stock';
        //                 $mail_data->pre_header = 'The item you were waiting for is now available again. Order soon before it sells out!';
        //                 $mail_data->content = "
        //     <p>Hi there,</p>

        //     <p>We're excited to let you know that the product you showed interest in — 
        //     <strong>{$productItem->title}</strong> — is now <span style=\"color: green; font-weight: bold;\">back in stock</span> at our store.</p>

        //     <p>Don't miss out! Visit the store and place your order before it's gone again.</p>

        //     <p>Thank you for shopping with us!</p>
        // ";

        //                 $mail_data->store_location = $productItem->store_id;


        //                 if ($notifyItem->update()) {
        //                     Mail::to($notifyItem->notify_email)->send(new SiteEmail($mail_data));

        //                     return response()->json(['sucess' => 'Email sent sucessfully'], 200);
        //                 } else {
        //                     return response()->json(['error' => 'Product Item not found for email'], 404);
        //                 }
        //                 break;

        //             case $request->status_phone:
        //                 $notifyItem->status_phone = 1;
        //                 $productItem = Product::find($notifyItem->product_id);
        //                 if ($notifyItem->update()) {
        //                     $message = 'Hi there,

        // Good news! The product you were interested in — *' . $productItem->title . '* — is now back in stock at our store.

        // Dont miss out! Visit the store and place your order before it is gone again.

        // Thank you for shopping with us!';
        //                     $sms_vars = ['there', $productItem->title];
        //                     // return app('App\Http\Controllers\Controller')->sendMessage($message, $notifyItem->notify_phone);
        //                     return $this->sendReStockMessage($notifyItem->notify_phone, $sms_vars, null, null, 'product_back_in_stock', $message);
        //                 } else {
        //                     return response()->json(['error' => 'Product Item not found for SMS'], 404);
        //                 }
        //                 break;

        //             default:
        //                 return response()->json(['error' => 'No valid notification type provided'], 400);
        //         }
    }
    function sendReStockMessage($phone, $vars = null, $mode = 'otp', $twilioSenderId = null, $template, $smsContent = null)
    {
        $whatsappSent = $this->sendViaMetaWhatsApp($phone, $vars, $template, 'en_US');
        Log::error('Meta WhatsApp status: ' . $whatsappSent);
        if (empty($twilioSenderId)) {
            $twilioSenderId = env('TWILIO_SID');
        }

        if (!$whatsappSent) {

            $this->sendViaTwilioSms($phone, $smsContent, $mode, $twilioSenderId);
            return true;
        }
    }
}
