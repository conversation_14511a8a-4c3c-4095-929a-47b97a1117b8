<?php

namespace App\Listeners;

use App\Events\PaymentFailed;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendPaymentFailedNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(PaymentFailed $event)
    {
        $this->notifier->sendCustomerPaymentFailedNotification($event->user, $event->order);
    }
}
