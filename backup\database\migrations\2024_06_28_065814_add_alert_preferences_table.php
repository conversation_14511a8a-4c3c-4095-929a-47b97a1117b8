<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAlertPreferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('alert_preferences', function (Blueprint $table) {
            $table->after('sms_default', function ($table) {
                $table->boolean('email_force_checked')->default(false);
                $table->boolean('sms_force_checked')->default(false);
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('alert_preferences', function (Blueprint $table) {
            $table->dropColumn('email_force_checked');
            $table->dropColumn('sms_force_checked');
        });
    }
}
