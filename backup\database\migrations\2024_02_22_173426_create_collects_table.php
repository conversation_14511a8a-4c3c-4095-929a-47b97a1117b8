<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCollectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('collects', function (Blueprint $table) {
            $table->id();
            $table->date('collection_date')->nullable();
            $table->foreignId('order_id')->nullable()->constrained('orders');
            $table->foreignId('customer_id')->nullable()->constrained('users');
            $table->longText('collect_user')->nullable();
            $table->foreignId('transaction_method_id')->constrained('transaction_methods');
            $table->string('transaction_id');
            $table->decimal('amount', 20, 2);
            $table->decimal('local_amount', 20, 2)->nullable();
            $table->string('currency')->nullable();
            $table->decimal('currency_rate', 20, 2)->nullable();
            $table->text('remarks')->nullable();
            $table->longText("collection_meta")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('collects');
    }
}
