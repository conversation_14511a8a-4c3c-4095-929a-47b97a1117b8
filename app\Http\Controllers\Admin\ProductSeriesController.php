<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductSeries;
use Illuminate\Http\Request;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;
use Response;
use Illuminate\Support\Str;
use App\Models\ProductBrand;
use App\Models\ProductCategory;

class ProductSeriesController extends Controller
{
    private $folderPath = 'products/series/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Series';
        $items = ProductSeries::join('product_brands', 'product_brands.id', 'product_series.product_brand_id')->select('product_series.*', 'product_brands.title as product_brand_title')->paginate($this->itemPerPage);

        $sl = SLGenerator($items);

        return view('admin.product.series.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Series';
        $brands = ProductBrand::whereStatus(true)->get();
        $categories = ProductCategory::whereStatus(true)->get();
        $series_image_info = Session::get('series_image');
        return view('admin.product.series.create', compact('series_image_info', 'brands', 'categories', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'product_brand_id' => 'required|exists:product_brands,id',
            'product_category_id' => 'required|exists:product_categories,id',
            'title' => 'required|string',
            'ordering' => 'nullable|numeric',
            'series_link' => 'nullable|url',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|unique:product_series,slug'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        $item = new ProductSeries;
        $item->product_brand_id = $request->product_brand_id;
        $item->product_category_id = $request->product_category_id;
        $item->title = $request->title;
        $item->slug = $item_slug;
        $item->series_link = $request->series_link;
        $item->description = $request->description;
        $item->ordering = $request->ordering ?? 0;
        $item->status = $request->status ?? 0;
        $series_image_info = Session::get('series_image');
        if ($series_image_info) {
            $image_name = $item->slug . '_brand_' . uniqid() . '.' . $series_image_info->extension;
            $folderName = $this->folderName();
            Storage::move($series_image_info->series_image, $this->folderPath . $folderName . '/' . $image_name);
            $item->series_image = $folderName . '/' . $image_name;
            Session::forget('series_image');
        }
        if ($request->hasFile('series_image')) {
            $folderName = $this->folderName();
            $imgName = $item->slug . '_series_' . uniqid() . '.' . $request->series_image->extension();
            $request->series_image->storeAs($this->folderPath . $folderName, $imgName);
            $item->series_image = $folderName. '/'. $imgName;
        }
        $item->save();

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.product-series.index'))->with('success', 'Gallery category created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProductSeries  $productSeries
     * @return \Illuminate\Http\Response
     */
    public function show(ProductSeries $productSeries)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductSeries  $productSeries
     * @return \Illuminate\Http\Response
     */
    public function edit(ProductSeries $productSeries)
    {
        $admin_page_title = 'Edit Series';
        $item = $productSeries;
        $item->brands = ProductBrand::whereStatus(true)->get();
        $item->categories = ProductCategory::whereStatus(true)->get();
        return view('admin.product.series.edit', compact('item', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductSeries  $productSeries
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductSeries $productSeries)
    {
        $this->validate($request, [
            'product_brand_id' => 'required|exists:product_brands,id',
            'product_category_id' => 'required|exists:product_categories,id',
            'title' => 'required|string',
            'ordering' => 'nullable|string',
            'series_link' => 'nullable|url',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|string|alpha_dash|unique:product_series,slug,' . $productSeries->id,
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        $item = ProductSeries::findOrFail($productSeries->id);
        $item->product_brand_id = $request->product_brand_id;
        $item->product_category_id = $request->product_category_id;
        $item->title = $request->title;
        $item->slug = $item_slug;
        $item->series_link = $request->series_link;
        $item->description = $request->description;
        $item->ordering = $request->ordering ?? 0;
        $item->status = $request->status ?? 0;
        $item->save();

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.product-series.index'))->with('success', 'Post category updated successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductSeries  $productSeries
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProductSeries $productSeries)
    {
        $item = ProductSeries::findOrFail($productSeries->id);
        $this->deleteFile($this->folderPath, $item->series_image);
        $item->delete();

        $items = ProductSeries::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        return view('admin.jquery_live.series', compact('items', 'sl'));
    }

    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }

        $item = ProductSeries::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target);

        $imgName = $item->slug . '_' . $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath. '/' . $folderName, $imgName);

        $item->$target = $folderName . '/' . $imgName;
        $item->update();

        $url = url('/storage/products/series/' . $folderName .'/'. $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileRemove($target, $id)
    {
        $item = ProductSeries::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();

        return response()->json('success', 200);
    }
}
