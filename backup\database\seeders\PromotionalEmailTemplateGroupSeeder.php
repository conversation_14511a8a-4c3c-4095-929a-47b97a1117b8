<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PromotionalEmailTemplateGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
   DB::table('email_template_groups')->insert([
    [
        'title'            => 'Promotional',
        'system_group'     => 'promotional',
        'order_status_id'  => null,
        'status'           => 1,
        'type'             => 'system',
        'created_at'       => Carbon::now(),
        'updated_at'       => Carbon::now(),
    ],
    [
        'title'            => 'Promotional',
        'system_group'     => 'promotional',
        'order_status_id'  => null,
        'status'           => 1,
        'type'             => 'dynamic_cron',
        'created_at'       => Carbon::now(),
        'updated_at'       => Carbon::now(),
    ]
]);

    }
}
