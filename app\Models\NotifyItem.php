<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotifyItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'notify_email',
        'notify_phone',
        'product_item_id',
        'product_id',
        'product_condition'
    ];

    public function productItem(){

        return $this->belongsTo(ProductItem::class, 'product_item_id');
    }
}
