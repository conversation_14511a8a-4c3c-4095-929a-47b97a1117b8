<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Collect;
use App\Models\Tracking;
use App\Models\EmailTemplateGroup;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\ProductBrand;
use App\Models\ProductCategory;
use App\Models\TransactionMethod;
use App\Models\UserBilling;
use App\Models\UserShipping;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\SiteEmail;
use App\Models\OrderRefund;
use Session;
use App\Models\EstimatedDeliveryDate;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $admin_page_title = 'Orders';
        if (!Auth::user()->permissions('order')) return redirect('/' . Auth::user()->username)->with('warning', 'You are un-authorized to access here');

        $items = DB::table('orders')
        ->orderBy('orders.id', 'desc')
        ->paginate($this->itemPerPage);

        foreach($items as $value){
            $value->customer = User::find($value->customer_id);
            $value->order_date = $this->undoFormatDate($value->order_date);
            $value->order_items = $value->order_items;
            // dd($this->colletOrderAmount(Collect::where('order_id', $value->id)->get()));
            $value->collect_amount = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->total;
            $value->local_collect_amount = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->local_total;
            $value->local_currency = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->local_currency;
            $value->transaction_method_name = $this->colletOrderAmount(Collect::where('order_id', $value->id)->get())->transaction_method_name;
            $value->status = $this->orderStatus($value->status);
        }

        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.orders', compact('items', 'sl'));
        }
        // dd($items);
        return view('admin.order.index', compact('sl', 'items', 'admin_page_title'));
    }

    public function colletOrderAmount($items){
        $total = 0;
        $local_total = 0;
        $local_convert_usd = 0;
        $local_currency = array();
        $transaction_method_name = array();
        foreach($items as $value){
            $total+= $value->amount;
            if($value->local_amount){
                $local_total += $value->local_total;
                $local_convert_usd += ($value->local_total / $value->currency_rate);
            }
            if($value->currency){
                $local_currency[] = $value->currency;
            }
            if (!empty($value->transaction_method_id)) {
                $transaction_method_name[] = TransactionMethod::find($value->transaction_method_id)->title;
            }
        }

        return (object)[
            'total' => $total + $local_convert_usd,
            'local_total' => $local_total,
            'local_currency' => $local_currency,
            'transaction_method_name' => $transaction_method_name,
        ];
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\Response
     */
    public function show(Order $order)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\Response
     */
    public function edit(Order $order)
    {
        // $order->order_items = unserialize($order->order_items);
        // $order->order = unserialize($order->order);
        $admin_page_title = 'Edit '.$order->order_no;
        $order->customer = User::find($order->customer_id);

        $today = Carbon::now();

        $order_return_date = Carbon::parse($order->order_date)->addDays(site_info('order_return_days'));
        $thresholdDateReturn = Carbon::parse($order->order_date)->copy()->addDays(site_info('order_return_days'));
        $order->order_return = false;
        if ($today->lessThan($thresholdDateReturn)) {
            $order->order_return = true;
            $order->order_return_text = 'Return period ends ' . $order_return_date->format('M, d Y');
        } else {
            $order->order_return_text = 'Return period ended ' . $order_return_date->format('M, d Y');
        }

        $order_warranty_date = Carbon::parse($order->order_date)->addDays(site_info('order_warranty_days'));
        $thresholdDateWarranty = Carbon::parse($order->order_date)->copy()->addDays(site_info('order_warranty_days'));
        $order->order_warranty = false;
        if ($today->lessThan($thresholdDateWarranty)) {
            $order->order_warranty = true;
            $order->order_warranty_text = 'Warranty period ends ' . $order_warranty_date->format('M, d Y');
        } else {
            $order->order_warranty_text = 'Warranty period ended ' . $order_warranty_date->format('M, d Y');
        }

        $order->order_date = $this->undoFormatDate($order->order_date);
        $order->order = unserialize($order->order);
        // dd($order);
        $order_items = OrderItem::where('order_id', $order->id)->get();
        foreach($order_items as $value){
            $value->product = Product::find($value->product_id);
            $value->item = ProductItem::find($value->product_item_id);
        }

        $options = [];
        foreach ($order_items as $value) {
            $this->makeAttributeData(unserialize($value->item->item_attributes), $value);
            $options[] = $value->cart_group;
        }

        $items_data = array_unique($options);
        $items = array();
        foreach ($items_data as $value) {
            $count = collect($order_items)->where('cart_group', $value)->count();
            if ($count == 1) {
                $data = collect($order_items)->where('cart_group', $value)->all();

                $item = collect($order_items)->where('cart_group', $value)->first();
                $product = Product::find($item->product_id);
                if ($product) {
                    $item->attributes = $this->getAttributes($data, $product);
                    $this->getAttributeData($item->attributes, $item);
                }
                $item->product_title = $product->title;
                if ($item->title) {
                    $item->title = $item->extra_title . ' (' . $item->title . ')';
                } else {
                    $item->title = $item->extra_title;
                }
                if ($item->sub_title) {
                    $item->sub_title = $item->extra_sub_title . ' (' . $item->sub_title . ')';
                } else {
                    $item->sub_title = $item->extra_sub_title;
                }
                if (empty($item->product_item_image)) {
                    $item->product_image = asset('storage/products/' . $product->product_image);
                }
                $attributes = collect(unserialize($item->item->item_attributes))->sortBy('ordering');
                foreach ($attributes as $value) {
                    if ($value->attribute_type_id == 12) {
                        if (!empty($value->data->file_path)) {
                            $item->product_image =  asset('storage/products/' . $value->data->file_path);
                        }
                    }
                }

                $item->categories = $this->getCategories(explode(',', $product->product_categories_id));
                $brand = ProductBrand::find($product->product_brand_id);
                $item->product_brand_title = $brand->title;
                $item->product_series_id = $product->product_series_id;
                $item->attribute_options = $this->getAttributeID(explode(',', $item->attribute_options));
                $item->sort_title = ucfirst(mb_substr($item->product_title, 0, 1));
                $item->sku_details = $this->makeDetails($data);
                $item->sku_details_url = $this->makeDetailsUrl($data);
                $item->condition = $this->makeCondition(unserialize($item->item->item_attributes));
                $item->condition_type = strtolower(str_replace([' ', ','], '', $item->condition));
                $item->available_item = $this->stockCalculation(ProductItem::where('product_id', $item->product_id)->where('status', true)->where('stock_status', true)->get(), $item->cart_group);
                $item->quantity = $count;
                $item->item_price = $this->itemTotalPrice($data);
                $items[] = $item;
            }
            if ($count > 1) {
                $data = collect($order_items)->where('cart_group', $value)->all();
                // dd($data);
                $item = collect($order_items)->where('cart_group', $value)->first();
                $item->extra_title = $this->makeExtraTitle($data);
                $item->sub_title = $this->makeSubTitle($data);
                $product = Product::find($item->product_id);
                if ($product) {
                    $item->attributes = $this->getAttributes($data, $product);
                    $this->getAttributeData($item->attributes, $item);
                }
                $item->product_title = $product->title;
                if ($item->title) {
                    $item->title = $item->extra_title . ', ' . $item->title;
                } else {
                    $item->title = $item->extra_title;
                }
                if ($item->sub_title) {
                    $item->sub_title = $item->extra_sub_title . ', ' . $item->sub_title;
                } else {
                    $item->sub_title = $item->extra_sub_title;
                }
                if (empty($item->product_item_image)) {
                    $item->product_image = asset('storage/products/' . $product->product_image);
                }

                $attributes = collect(unserialize($item->item->item_attributes))->sortBy('ordering');
                foreach ($attributes as $value) {
                    if ($value->attribute_type_id == 12) {
                        if (!empty($value->data->file_path)) {
                            $item->product_image =  asset('storage/products/' . $value->data->file_path);
                        }
                    }
                }

                $item->categories = $this->getCategories(explode(',', $product->product_categories_id));
                $brand = ProductBrand::find($product->product_brand_id);
                $item->product_brand_title = $brand->title;
                $item->product_series_id = $product->product_series_id;
                $item->attribute_options = $this->getAttributeID(explode(',', $item->attribute_options));
                $item->sort_title = ucfirst(mb_substr($item->product_title, 0, 1));
                $item->sku_details = $this->makeDetails($data);
                $item->sku_details_url = $this->makeDetailsUrl($data);
                $item->condition = $this->makeCondition(unserialize($item->item->item_attributes));
                $item->condition_type = strtolower(str_replace([' ', ','], '', $item->condition));
                $item->available_item = $this->stockCalculation(ProductItem::where('product_id', $item->product_id)->where('status', true)->where('stock', true)->get(), $item->cart_group);
                $item->quantity = $count;
                $item->item_price = $this->itemTotalPrice($data);
                $items[] = $item;
                // dd($items);
            }
        }

        $order->order_items =  $items;
        $collect = Collect::where('order_id', $order->id)->get();
        foreach($collect as $value){
            $value->collection_time = Carbon::parse($value->created_at)->format('g:i A');
            $value->collection_date = $this->undoFormatDate($value->collection_date);
        }

        $order->collect = $collect;
        $order->billing = UserBilling::where('id', $order->user_billing_id)->first();
        $order->shipping = UserShipping::where('id', $order->user_shipping_id)->first();
        $order->email_template_groups = EmailTemplateGroup::where('status',true)->get();
        $order->order_status = $this->orderStatus();
        $order->email_constants = $this->email_constants();

        $order->local_currency = $this->colletOrderAmount(Collect::where('order_id', $item->id)->get())->local_currency;
        $order->transaction_method_name = $this->colletOrderAmount(Collect::where('order_id', $item->id)->get())->transaction_method_name;

        $send_sms_emails = Tracking::where('order_id', $order->id)->get();
        foreach ($send_sms_emails as $value) {
            $value->date_time = Carbon::parse($value->created_at)->format('F j, Y g.iA');
        }
        $order->send_sms_emails = $send_sms_emails;

        $item = $order;
        // dd($item);
        return view('admin.order.edit', compact('item', 'admin_page_title'));
    }

    public function itemTotalPrice($items){
        $total = 0;
        foreach($items as $value){
            $total += $value->price;
        }

        return $total;
    }

    public function makeAttributeData($items, $item)
    {
        $title_array = array();
        $sub_title_array = array();
        $condition_text = array();
        foreach (collect($items)->sortBy('ordering') as $value) {
            if ($value->input_type_id == 1 && $value->show_on_title) {
                $title_array[] = $value->data->title;
            }
            if ($value->input_type_id == 1 && $value->show_on_sub_title) {
                $sub_title_array[] = $value->data->title;
            }
            if ($value->input_type_id == 1 && $value->attribute_type_id != 1) {
                $group_array[] = preg_replace('/\s*/', '', strtolower($value->data->title));
                $option_keys_array[] = $value->data->title . $item->product_id;
                $attribute_option_keys_array[] = $value->data->title;
            }
            if ($value->input_type_id == 2) {
                foreach ($value->data as $v) {
                    $attribute_option_keys_array[] = $v->title;
                }
            }
            if ($value->attribute_type_id == 1 && !empty($value->data->description)) {
                $condition_text[] = $value->data->description;
            }
            if ($value->attribute_type_id == 1 && $value->input_type_id) {
                $condition = $value->data->title;
            }
        }

        $item->condition_text = count($condition_text) > 0 ? implode(',', $condition_text) : false;
        $item->extra_title = count($title_array) > 0 ? implode(', ', array_unique($title_array)) : false;
        $item->extra_sub_title = count($sub_title_array) > 0 ?  implode(', ', array_unique($sub_title_array)) : false;
        $item->cart_group = implode(',', array_unique($option_keys_array));
        $item->group = implode('-', array_unique($group_array));
        $item->attribute_options = implode(',', array_unique($attribute_option_keys_array));
        $item->cart_group = preg_replace('/\s*/', '', strtolower($item->group . '-' . $condition));
        // dd($item->cart_group);
    }

    public function makeExtraTitle($items)
    {
        $extra_title = [];
        foreach ($items as $value) {
            if ($value->extra_title) {
                $extra_title[] = $value->extra_title;
            }
        }
        return implode(',', array_unique(explode(',', implode(',', $extra_title))));
    }

    public function makeSubTitle($items)
    {
        $sub_title = [];
        foreach ($items as $value) {
            if ($value->sub_title) {
                $sub_title[] = $value->sub_title;
            }
        }
        return implode(', ', array_unique(explode(', ', implode(', ', $sub_title))));
    }

    public function getAttributeData($items, $item)
    {
        foreach ($items as $value) {
            if ($value->attribute_type_id == 1) {
                $conditions = $value;
            }
            if ($value->attribute_type_id == 2) {
                $networks = $value;
            }
        }

        if (!empty($conditions) && count($conditions->options) > 0) {
            $item->conditions = $conditions;
        }

        if (!empty($networks) && count($networks->options) > 0) {
            $item->networks = $networks;
        }

        return $item;
    }

    public function getAttributes($items, $product)
    {
        foreach ($items as $value) {
            $attributes[] = collect(unserialize($value->item->item_attributes))->toArray();
        }

        $attributes_array = [];
        foreach ($attributes as $attribute) {
            $attribute = $this->getAttribute($attribute);
            $attributes_array = array_merge($attributes_array, $attribute);
        }

        $attributes_options = array();
        $data_checkbox = array();
        $data_option = array();
        foreach ($attributes_array as $attribute_array) {
            if ($attribute_array->id == $attribute_array->id) {
                $attributes_options[$attribute_array->id]['id'] = $attribute_array->id;
                $attributes_options[$attribute_array->id]['title'] = $attribute_array->title;
                $attributes_options[$attribute_array->id]['attribute_type_id'] = $attribute_array->attribute_type_id;
                $attributes_options[$attribute_array->id]['options'][] = $attribute_array->options;

                if ($attribute_array->input_type_id == 1) {
                    $data_option[] = $attribute_array->data;
                }
                if ($attribute_array->input_type_id == 2) {
                    $data_checkbox = $attribute_array->data;
                }
            }
        }

        $option_items = array_merge($data_option, $data_checkbox);

        foreach ($attributes_options as $attributes_option) {
            $options[] = (object)$attributes_option;
        }

        foreach ($options as $option) {
            $unique_options_id = array_unique(explode(',', implode(',', $option->options)));
            $option->options = $this->makeAttribute($unique_options_id, $product, $option_items);
        }

        return $options;
    }

    public function getAttribute($items)
    {
        foreach ($items as $item) {
            if ($item->input_type_id == 1) {
                $item->options = $item->data->id;
            }
            if ($item->input_type_id == 2) {
                $item->options = $this->getOption($item->data);
            }
        }

        return $items;
    }

    public function getOption($items)
    {
        foreach ($items as $item) {
            $options[] = $item->id;
        }

        return implode(',', $options);
    }

    public function makeAttribute($items, $product, $option_items)
    {
        $attributes = unserialize($product->product_attributes);
        $data = [];
        foreach ($attributes as $attribute) {
            $array = $attribute->attribute_options->toArray();
            foreach ($array as $v) {
                $data[] = $v;
            }
        }

        $collection  = collect($data);

        $item_data = array();
        foreach ($items as $item) {
            $attribute_option = $collection->where('id', $item)->first();
            if ($attribute_option) {
                $item_data[] = $attribute_option;
            } else {
                $item = collect($option_items)->where('id', $item)->first();
                if ($item) {
                    $item->file_current = null;
                    $item->file_path = null;
                    $item->extension = null;
                    $item->text_file_current = null;
                    $item->text_file_path = null;
                    $item->text_extension = null;
                    $item_data[] = $item;
                }
            }
        }

        return $item_data;
    }

    public function getCategories($categories)
    {
        $category_titles  = array();
        foreach ($categories as $category) {
            $category = ProductCategory::whereStatus(true)->where('id', $category)->first();
            if ($category) {
                $category_titles[] = $category->title;
            }
        }
        return $categories = implode(',', $category_titles);
    }

    public function getAttributeID($items)
    {
        $options = [];
        foreach ($items as $value) {
            $options[] = preg_replace('/\s*/', '', strtolower($value));
        }

        return implode(',', $options);
    }

    public function makeDetails($items)
    {
        $sku_details_url = array();
        foreach ($items as $value) {
            $value->locations = $this->countries($value->location_id);
            $value->sku_location = '';
            if (!empty($value->location_id)) {
                //$value->sku_location = $this->countries($value->location_id);
                $value->sku_location = $value->location_id;
            }

            $value->estimate_delivery = '';
            if (!empty($location_data->country)) {
                $estimate_delivery_data = EstimatedDeliveryDate::where([
                    'sku_location_id' => $value->location_id,
                    'ship_location_id' => $location_data->country,
                ])->first();

                if (!empty($estimate_delivery_data->sku_location_id)) {
                    $minDurationDays = $estimate_delivery_data->min_duration;
                    $maxDurationDays = $estimate_delivery_data->max_duration;

                    // Get the current date
                    $currentDate = Carbon::now();

                    // Calculate the estimated delivery dates
                    $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                    $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);

                    // Format the dates
                    $minFormatted = $minDeliveryDate->format('M d');
                    $maxFormatted = $maxDeliveryDate->format('M d');

                    $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                }
            }

            $sku = 'SKU: ' . $value->product_item_sku;
            if (!empty($value->sku_location)) {
                $sku =
                    'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong>';
            }
            if (!empty($value->sku_location && $value->estimate_delivery)) {
                $sku =
                    'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong> <img src="' . asset("images/icon/delivery_black.png") . '" alt=""> ' . $value->estimate_delivery;
            }

            $sku_details_url[] = $sku;
        }
        return implode('<br />', $sku_details_url);
    }

    public function requestRefundStatus($items){
        $refund_sku = array();
        $exist_sku = array();
        foreach ($items as $value) {
            $exist_sku[] = $value->sku;
            $refund = OrderRefund::where('sku', $value->sku)->first();
            if($refund){
                $refund_sku[] = $refund->sku;
            }
        }
        return count($exist_sku) - count($refund_sku);
    }

    public function makeDetailsRefund($items,$title,$sub_title=null, $condition=null, $order_return)
    {
        // dd($items);
        $sku_details_url = array();
        foreach ($items as $value) {
            $refund_request = OrderRefund::where('sku', $value->sku)->exists();
            $refund = OrderRefund::where('sku', $value->sku)->first();
            if($refund){
                $refund_status = $this->refund_status($refund->status)->title;
            }
            $refunded_request = OrderRefund::where([
                'sku' => $value->sku,
                'status' => 2,
            ])->exists();

            $sku = $value->sku;
            if($refund_request && $refunded_request){
                $sku = '<span class="text-secondary">' . $value->sku . '(Refunded)</span>';
            }else if($refund_request){
                if($order_return){
                    $route = route('live.products.condition.item', $value->product_item_id);
                    $image_route = route('live.products.item.images', $value->product_item_id);
                    $make_sku = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $value->sku . '</a>';
                    $sku = $make_sku;
                }else{
                    $route = route('live.products.condition.item', $value->product_item_id);
                    $image_route = route('live.products.item.images', $value->product_item_id);
                    $make_sku = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $value->sku . '</a>';
                    $sku = $make_sku.' ('.$refund_status. ') <a href="javascript:void(0)" data-toggle="modal" data-refund_id="'. $refund->id. '" data-target="#editReturnModal" data-product_title="'. $title.'" data-product_sub_title="'.$sub_title. ' - '.$condition.'" data-url="'.route('web_auth.return_order_item', $refund->id).'"><i class="far fa-edit"></i></a>';
                }
            }else{
                $route = route('live.products.condition.item', $value->product_item_id);
                $image_route = route('live.products.item.images', $value->product_item_id);
                $make_sku = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $value->sku . '</a>';
                $sku = $make_sku;
            }

            // $sku = $refunded_request ? '<span class="text-light">'. $value->sku .'</span>' : $value->sku;

            $sku_details_url[] = $sku;
        }
        return implode(', ', $sku_details_url);
    }

    public function makeDetailsUrl($items)
    {
        $location_data = Session::get('shop_logged_data');
        $sku_details_url = array();
        if (count($items) > 1) {
            foreach (collect($items) as $value) {
                $value->locations = $this->countries($value->location_id);
                $value->sku_location = '';
                if (!empty($value->location_id)) {
                    //$value->sku_location = $this->countries($value->location_id);
                    $value->sku_location = $value->location_id;
                }

                $value->estimate_delivery = '';
                if (!empty($location_data->country)) {
                    $estimate_delivery_data = EstimatedDeliveryDate::where([
                        'sku_location_id' => $value->location_id,
                        'ship_location_id' => $location_data->country,
                    ])->first();

                    if (!empty($estimate_delivery_data->sku_location_id)) {
                        $minDurationDays = $estimate_delivery_data->min_duration;
                        $maxDurationDays = $estimate_delivery_data->max_duration;

                        // Get the current date
                        $currentDate = Carbon::now();

                        // Calculate the estimated delivery dates
                        $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                        $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);

                        // Format the dates
                        $minFormatted = $minDeliveryDate->format('M d');
                        $maxFormatted = $maxDeliveryDate->format('M d');

                        $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                    }
                }

                $sku = 'SKU: ' . $value->product_item_sku;
                if (!empty($value->sku_location)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong>';
                }
                if (!empty($value->sku_location && $value->estimate_delivery)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong> <img src="' . asset("images/icon/delivery_black.png") . '" alt=""> ' . $value->estimate_delivery;
                }

                if ($value->sku_partial_paid) {
                    $route = route('live.products.condition.item', $value->product_item_id);
                    $image_route = route('live.products.item.images', $value->product_item_id);
                    $sku_details_url[] = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $sku . '</a>';
                } else {
                    $route = route('live.products.condition.item', $value->product_item_id);
                    $image_route = route('live.products.item.images', $value->product_item_id);
                    $sku_remove_route = route('add_cart', [$value->id]);
                    // $sku_remove_route = route('add_cart', [$value->id, 'checkout' => true]);
                    $sku_details_url[] = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $sku . '</a><a class="remove-cart-sku-item remove_cart_sku_item" data-url="' . $sku_remove_route . '" href="javascript:void(0)""><i class="fa-solid fa-xmark"></i></a>';
                }
            }
        } else {
            foreach ($items as $value) {
                $value->locations = $this->countries($value->location_id);
                $value->sku_location = '';
                if (!empty($value->location_id)) {
                    //$value->sku_location = $this->countries($value->location_id);
                    $value->sku_location = $value->location_id;
                }

                $value->estimate_delivery = '';
                if (!empty($location_data->country)) {
                    $estimate_delivery_data = EstimatedDeliveryDate::where([
                        'sku_location_id' => $value->location_id,
                        'ship_location_id' => $location_data->country,
                    ])->first();

                    if (!empty($estimate_delivery_data->sku_location_id)) {
                        $minDurationDays = $estimate_delivery_data->min_duration;
                        $maxDurationDays = $estimate_delivery_data->max_duration;

                        // Get the current date
                        $currentDate = Carbon::now();

                        // Calculate the estimated delivery dates
                        $minDeliveryDate = $currentDate->copy()->addDays($minDurationDays);
                        $maxDeliveryDate = $currentDate->copy()->addDays($maxDurationDays);

                        // Format the dates
                        $minFormatted = $minDeliveryDate->format('M d');
                        $maxFormatted = $maxDeliveryDate->format('M d');

                        $value->estimate_delivery = "{$minFormatted} - {$maxFormatted}";
                    }
                }

                $sku = 'SKU: ' . $value->product_item_sku;
                if (!empty($value->sku_location)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong>';
                }
                if (!empty($value->sku_location && $value->estimate_delivery)) {
                    $sku =
                        'SKU: <strong>' . $value->product_item_sku . '</strong> <img src="' . asset("images/icon/location_black.png") . '" alt=""><strong>' . $value->sku_location . '</strong> <img src="' . asset("images/icon/delivery_black.png") . '" alt=""> ' . $value->estimate_delivery;
                }

                $route = route('live.products.condition.item', $value->product_item_id);
                $image_route = route('live.products.item.images', $value->product_item_id);
                $sku_details_url[] = '<a class="view-pictures pb-0" data-toggle="modal" data-target="#mobileImagePreviewModal" data-modal_sku_url="' . $route . '" data-modal_images_url="' . $image_route . '" href="javascript:void(0)">' . $sku . '</a>';
            }
        }
        return implode('<br />', $sku_details_url);
    }

    public function makeCondition($items)
    {
        foreach ($items as $value) {
            if ($value->attribute_type_id == 1) {
                $name = $value->data->title;
            }
        }
        return $name;
    }

    public function stockCalculation($items, $cart_group)
    {
        // dd($items);
        if(count($items) > 0){
            foreach ($items as $value) {
                $item_data_array[] = $this->getProductItem($value->id);
            }
            // dd($item_data_array);
            $total_item = count(collect($item_data_array)->where('cart_group', $cart_group));
            $total_cart_item = count(collect($item_data_array)->where('cart_group', $cart_group)->where('stock_status', false));
            // dd($total_item - $total_cart_item);

            return ($total_item - $total_cart_item);
        }
        return 0;
    }

    public function getProductItem($id)
    {
        $item = ProductItem::where('id', $id)->first();
        // $item = ProductItem::find($id);
        $this->makeAttributeData(unserialize($item->item_attributes), $item);
        $product = Product::find($item->product_id);
        $item->product_title = $product->title;
        if ($item->title) {
            $item->title = $item->extra_title . ', ' . $item->title;
        } else {
            $item->title = $item->extra_title;
        }
        if ($item->sub_title) {
            $item->sub_title = $item->extra_sub_title . ', ' . $item->sub_title;
        } else {
            $item->sub_title = $item->extra_sub_title;
        }
        // dd($item);
        $discount_amt = 0;
        if ($item->discount_price && $item->discount_type) {
            if ($item->discount_type == 1) {
                $discount_amt = ($item->sale_price * $item->discount_price) / 100;
                $discount_percent = $item->discount_price;
            } else {
                $discount_amt = $item->discount_price;
                $discount_percent = ($item->discount_price * 100) / $item->sale_price;
            }
            $item->discount_percent = ceil($discount_percent);
            $item->price = $item->sale_price - $discount_amt;
        } else {
            $item->price = $item->sale_price;
        }

        $item->price_off_amt = number_format(ceil($discount_amt), 2) ?? 0;

        $product_item_image = asset('storage/products/' . $product->product_image);

        $attributes = collect(unserialize($item->item_attributes))->sortBy('ordering');
        foreach ($attributes as $value) {
            if ($value->attribute_type_id == 12) {
                if (!empty($value->data->file_path)) {
                    $product_item_image =  asset('storage/products/' . $value->data->file_path);
                }
            }
        }
        if ($item->product_item_image) {
            $product_item_image = asset('storage/product_item_images/' . $item->product_item_image);
        }
        $item->product_item_image = $product_item_image;

        return $item;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Order $order)
    {
        $this->validate($request, [
            'order_status_id' => 'required|integer|string|max:100',
        ]);

        $order->order_items = unserialize($order->order_items);

        if ($request->action == 'send_email_sms') {
            $this->validate($request, [
                'order_status_id' => 'integer|string|max:255',
                'email_template_id*' => 'required|exists:email_templates,id',
                'subject' => 'required|string|max:255',
                'pre_header' => 'required|string|max:255',
                'email_body' => 'required|string',
            ]);

            $user = User::find($order->customer_id);

            $mail_data = (object)[
                'user' => $user,
                'order' => $order,
                'subject' => $request->subject,
                'pre_header' => $request->pre_header,
                'email_body' => $request->email_body,
                'sms_status' => $request->sms_status ? true : false,
                'sms_body' => $request->sms_body,
            ];
            $mail_data->subject = $this->sendEmailData($mail_data)->subject;
            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
            $mail_data->content = $this->sendEmailData($mail_data)->content;
            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

            $lead_source = (object)[
                'admin_id' => auth()->user()->id,
                'admin_email' => auth()->user()->email,
                'admin_phone' => auth()->user()->tel_cell_country_code . auth()->user()->tel_cell,
                'source' => 'admin',
            ];

            $tracking = new Tracking();
            $tracking->order_id = $order->id;
            $tracking->content = serialize($this->sendEmailData($mail_data));
            $tracking->from_email = config('mail.from.address');
            $tracking->to_email = $user->email;
            $tracking->subject = $this->sendEmailData($mail_data)->subject;
            $tracking->lead_source = serialize($lead_source);
            $tracking->ip = $this->getClientIpAddress();
            $tracking->save();

            Mail::to($user->email)->send(new SiteEmail($mail_data));

            if ($request->sms_status && $user->tel_cell) {
                $sms_tracking = Tracking::findOrFail($tracking->id);
                $sms_tracking->sms_content = serialize($mail_data->sms_content);
                $sms_tracking->from_phone = config('app.twilio_number');
                $sms_tracking->to_phone = $user->tel_cell;
                $sms_tracking->update();
            }

            return back()->with('success', 'Mail send Successful.');
        }

        $order->status = $request->order_status_id;
        $order->update();

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.orders.index'))->with('success', 'order update Successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\Response
     */
    public function destroy(Order $order)
    {
        //
    }
}
