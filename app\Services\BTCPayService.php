<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use App\Models\Page;
use Illuminate\Support\Facades\Session;

class BTCPayService
{
    protected $serverUrl;
    protected $apiKey;
    protected $client;

    public function __construct()
    {
        $this->serverUrl = 'https://testnet.demo.btcpayserver.org';
        // $this->apiKey = 'e4dfa3a247906296609004241431541ab67348cc';
        $this->apiKey = '2e27d2bd154c4b72293d9aea2b71b6261368a00d';


        $this->client = new Client([
            'base_uri' => $this->serverUrl,
            'headers' => [
                'Authorization' => 'token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ]
        ]);
    }

    public function createInvoice(array $data, $callBack)
    {
        $thank_page = Page::where('page_key', 'thank_you')->first();
        $cart = Session::get('cart');

        $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('btc');
        $discount_amt = 0;

        $discount_amt = $cart->total * $transactionMethod->discount_price / 100;

        $cart->discount_amt = $discount_amt;

        Session::put('cart', $cart);

        // dd(Session::get('cart'));

        try {
            // Add redirect URL to the invoice data
            $data = array_merge($data, [
                'checkout' => [
                    'redirectURL' =>  $callBack,
                    'redirectAutomatically' => true
                ],
                'metadata' => [
                    'orderNumber' => $data['orderNumber'] ?? uniqid(),
                ]
            ]);


            // $response = $this->client->post('/api/v1/stores/2watyd41Wnp1GpJGMhDLZCrrYLgdwY4oGSvsebALdMbk/invoices', [
            //     'json' => $data
            // ]);
            $response = $this->client->post('/api/v1/stores/AtQAheCbCjsKCN4qqv3pG8DMdGoB7RuhogPcncNmGd8c/invoices', [
                'json' => $data
            ]);
            // Get the response data
            $responseData = json_decode($response->getBody(), true);

            // Log the created invoice
            \Log::info('BTCPay Invoice Created', [
                'invoiceId' => $responseData['id'] ?? null,
                'checkoutLink' => $responseData['checkoutLink'] ?? null
            ]);

            // Return the decoded response body as an associative array
            return $responseData;
        } catch (Exception $e) {
            \Log::error('BTCPay Invoice Creation Error', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw new Exception('BTCPay Error: ' . $e->getMessage());
        }
    }
    public function registerWebhook(string $url, array $events = [])
    {
        try {
            $response = $this->client->post("/api/v1/stores/2watyd41Wnp1GpJGMhDLZCrrYLgdwY4oGSvsebALdMbk/webhooks", [
                'json' => [
                    'enabled' => true,
                    'url' => $url,
                    'secret' => bin2hex(random_bytes(16)), // Generate a secret key
                    'events' => $events
                ]
            ]);

            return json_decode($response->getBody(), true);
        } catch (Exception $e) {
            throw new Exception('BTCPay Webhook Registration Error: ' . $e->getMessage());
        }
    }

    
}
