<?php

namespace App\Listeners;

use App\Events\OrderCancelled;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendOrderCancelledNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(OrderCancelled $event)
    {
        $this->notifier->sendCustomerOrderCancelledNotification($event->order, $event->cart, $event->message_status);
       // $this->notifier->sendAdminOrderCancelledNotifications($event->order, $event->cart);
    }
}
