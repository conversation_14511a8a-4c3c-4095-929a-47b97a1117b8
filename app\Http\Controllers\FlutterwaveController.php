<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\WebPageController;
use Illuminate\Http\Request;
use Redirect;
use KingFlamez\Rave\Facades\Rave as Flutterwave;


class FlutterwaveController extends Controller
{
    /**
     * Initialize Rave payment process
     * @return void
     */
    public function initialize()
    {

        //This generates a payment reference
        $reference = Flutterwave::generateReference();

        // Enter the details of the payment
        $data = [
            'payment_options' => 'card,banktransfer',
            'amount' => 500,
            'email' => request()->email,
            'tx_ref' => $reference,
            'currency' => "NGN",
            'redirect_url' => route('callback'),
            'customer' => [
                'email' => '<EMAIL>',
                "phone_number" => request()->phone,
                "name" => request()->name
            ],

            "customizations" => [
                "title" => 'Movie Ticket',
                "description" => "20th October"
            ]
        ];

        $payment = Flutterwave::initializePayment($data);

        if ($payment['status'] !== 'success') {
            // notify something went wrong
            return;
        }

        return redirect($payment['data']['link']);
    }

    /**
     * Obtain Rave callback information
     * @return void
     */
    public function callback(Request $request)
    {

        $thank_page = Page::where('page_key', 'thank_you')->first();
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        $status = $request->status;

        if ($status === 'successful') {
            // Get Transaction ID and verify
            $transactionID = Flutterwave::getTransactionIDFromCallback();
            $response = Flutterwave::verifyTransaction($transactionID);

            if (
                isset($response['status']) &&
                $response['status'] === 'success' &&
                isset($response['data']['status']) &&
                $response['data']['status'] === 'successful'
            ) {
                $data = $response['data'];
                $flwRef = $data['flw_ref'] ?? $transactionID;
                $chargedAmount = $data['charged_amount'] ?? 0;

                $discount_amt = 0;
                if ($cart->paid_grand_total == 0) {
                    $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('flutterwave');
                    if ($transactionMethod && $transactionMethod->discount_price) {
                        $discount_amt = $transactionMethod->discount_type == 1
                            ? ($cart->total * $transactionMethod->discount_price) / 100
                            : $transactionMethod->discount_price;
                    }
                }
                $controller = app(\App\Http\Controllers\Controller::class);

                $controller->makeCollection(
                    $cart->grand_total - $discount_amt,
                    $transactionMethod->id,
                    $flwRef,
                    2,
                    $discount_amt
                );

                return Redirect::route('page', $thank_page->slug);
            }

            Session::flash('error', 'Transaction verification failed.');
            return Redirect::route('page', $checkout_page->slug);
        }

        if ($status === 'cancelled') {
            Session::flash('error', 'Payment was cancelled.');
        } else {
            Session::flash('error', 'Payment failed.');
        }

        return Redirect::route('page', $checkout_page->slug);
        // Get the transaction from your DB using the transaction reference (txref)
        // Check if you have previously given value for the transaction. If you have, redirect to your successpage else, continue
        // Confirm that the currency on your db transaction is equal to the returned currency
        // Confirm that the db transaction amount is equal to the returned amount
        // Update the db transaction record (including parameters that didn't exist before the transaction is completed. for audit purpose)
        // Give value for the transaction
        // Update the transaction to note that you have given value for the transaction
        // You can also redirect to your success page from here

    }
}
