<?php 
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CarrierOption extends Model
{
    use HasFactory;

    protected $fillable = [
        'attribute_id',
        'title',
        'description',
        'file_path',
        'text_file_path',
        'extension',
        'text_extension',
        'img_url',
        'text_img_url',
        'folder',
        'status',
        'ordering',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // Countries this carrier is available in
    public function countries()
    {
    return $this->hasMany(CarrierOptionCountry::class);
    }

    // Optional: if you're linking to product attributes
    public function attribute()
    {
        return $this->belongsTo(ProductAttribute::class);
    }
}
