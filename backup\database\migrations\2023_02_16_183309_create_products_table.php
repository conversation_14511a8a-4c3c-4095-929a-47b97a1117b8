<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_categories_id')->constrained('product_categories');
            $table->foreignId('product_brand_id')->constrained('product_brands');
            $table->foreignId('product_series_id')->constrained('product_series')->nullable();
            $table->foreignId('product_attribute_id')->nullable()->constrained('product_attributes');
            $table->string("title");
            $table->string("slug")->unique();
            $table->text("short_description")->nullable();
            $table->longText("product_attributes")->nullable();
            $table->longText("description")->nullable();
            $table->longText("specifications")->nullable();
            $table->longText("shipping")->nullable();
            $table->longText("warranty_returns")->nullable();
            $table->string('product_image')->nullable();
            $table->string('meta_title')->nullable();
            $table->string('meta_keyword')->nullable();
            $table->string('meta_description')->nullable();
            $table->text('remark')->nullable();
            $table->integer('ordering')->default(0);
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
}
