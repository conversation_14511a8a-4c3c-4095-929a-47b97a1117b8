<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Page extends Model
{
    use HasFactory;

    public function parent()
    {
        return $this->belongsTo(Page::class, 'parent_menu_id');
    }

    public function children()
    {
        return $this->hasMany(Page::class, 'parent_menu_id', 'id')->orderBy('ordering', 'asc');
    }

    public function menuChildren()
    {
        return $this->hasMany(Page::class, 'parent_menu_id', 'id')->orderBy('ordering', 'asc')->where('status', true);
    }

    public function checkAccessLabel($slug)
    {
        $page = Page::whereSlug($slug)->first();
        if ($page->access_label_id == 1) return true;
        if (!Auth::user()) abort(403);
        $access_label = AccessLabel::findOrFail($page->access_label_id);
        $page_access_labels = explode(',', $access_label->user_groups_id);

        $page_access = false;
        foreach ($page_access_labels as $page_access_label) {
            $page_access = in_array($page_access_label, Auth::user()->userGroup());
        }

        return $page_access;
    }
}
