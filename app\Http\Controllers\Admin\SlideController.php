<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Slide;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;

class SlideController extends Controller
{
    private $folderPath = 'slideshow/';

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($category_id)
    {
        $admin_page_title = 'Slides';

        $items = DB::table('slides')
        ->where('slide_category_id', $category_id)
        ->orderBy('slides.id', 'desc')
        ->paginate($this->itemPerPage);

        $sl = SLGenerator($items);

        return view('admin.slideshow.slide.index', compact('sl', 'items','category_id', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($category_id)
    {
        $admin_page_title = 'Create Slide';
        $slide_image_info = Session::get('slide_image');
        return view('admin.slideshow.slide.create', compact('slide_image_info','category_id', 'admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $category_id)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            'url' => 'nullable|string|url',
            'order' => 'nullable|integer',
            'slide_image' => 'nullable|image|max:' . $this->maxFileSize,
            'heading' => 'nullable|string',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean',
        ]);

        $slide = new Slide;
        $slide->slide_category_id = $category_id;
        $slide->title = $request->title;
        $slide->heading = $request->heading;
        $slide->description = $request->description;
        $slide->url = $request->url;
        $slide_image_info = Session::get('slide_image');
        if ($slide_image_info) {
            $image_name = 'slide_image_' . uniqid() . '.' . $slide_image_info->extension;
            $folderName = $this->folderName();
            Storage::move($slide_image_info->slide_image, $this->folderPath . $folderName.'/' . $image_name);
            $slide->slide_image = $folderName.'/' . $image_name;
            Session::forget('slide_image');
        }
        if ($request->hasFile('slide_image')) {
            $imgName = 'slide_image_' . uniqid() . '.' . $request->slide_image->extension();
            $request->slide_image->storeAs($this->folderPath, $imgName);
            $slide->slide_image = $imgName;
        }
        $slide->ordering = ($request->ordering > 0 ? $request->ordering : '0');
        $slide->status = ($request->status > 0 ? $request->status : '0');
        $slide->save();

        $message = 'Slide successfully added.';
        return ($request->get('btn') == 'save') ? back()->with('success', $message) : redirect()->route('admin.slides.index',$category_id)->with('success', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function show(Slide $slide)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function edit($id, $category_id)
    {
        $admin_page_title = 'Edit Slide';
        $slide = DB::table('slides')
        ->where('slides.slide_category_id', $category_id)
        ->where('slides.id', $id)
        ->first();

        return view('admin.slideshow.slide.edit', compact('slide','category_id', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id, $category_id)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            'url' => 'nullable|string|url',
            'order' => 'nullable|integer',
            'slide_image' => 'nullable|image|max:' . $this->maxFileSize,
            'heading' => 'nullable|string',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean',
        ]);

        $slide = Slide::findOrFail($id);
        $slide->slide_category_id = $category_id;
        $slide->title = $request->title;
        $slide->heading = $request->heading;
        $slide->description = $request->description;
        $slide->url = $request->url;
        $slide->ordering = ($request->ordering > 0 ? $request->ordering : '0');
        $slide->status = ($request->status > 0 ? $request->status : '0');
        $slide->update();

        $message = 'Slider successfully updated.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.slides.index', $category_id)->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, $category_id)
    {
        $slide = Slide::findOrFail($id);
        $this->deleteFile($this->folderPath, $slide->slide_image);
        $slide->delete();

        $items = DB::table('slides')
        ->where('slide_category_id', $category_id)
        ->orderBy('slides.id', 'desc')
        ->paginate($this->itemPerPage);

        $sl = SLGenerator($items);

        return view('admin.jquery_live.slides', compact('items', 'sl', 'category_id'));
    }

    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }

        $slide = Slide::findOrFail($id);

        $this->deleteFile($this->folderPath, $slide->$target);

        $imgName = $target . "_" . $slide->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath . $folderName, $imgName);

        $slide->$target = $folderName. '/'. $imgName;
        $slide->update();

        $url = url('/storage/slideshow/' . $folderName. '/'. $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileRemove($target, $id)
    {
        $slide = Slide::findOrFail($id);
        $this->deleteFile($this->folderPath, $slide->$target);
        $slide->$target = null;
        $slide->update();

        return response()->json('success', 200);
    }
}
