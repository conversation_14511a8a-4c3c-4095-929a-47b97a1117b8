<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailSchedule;
use Carbon\Carbon;
use App\Models\User;
use App\Models\UserGroupMap;

class EmailScheduleCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger-email-schedule';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $today = Carbon::now()->timezone(site_info('site_time_zone'));
        $now_time = Carbon::now()->timezone(site_info('site_time_zone'))->format('H:i');
        // $now_time = Carbon::now()->timezone(site_info('site_time_zone'))->format('H:i:s');

        $this->info('Starting EmailCampaign...');

        $campaigns = DB::table('email_campaigns')
        ->where(function ($query) use ($today) {
            $query->whereNull('expire_date')->orWhereDate('expire_date', '>', $today);
        })
        ->orderByDesc('created_at')
        ->where('status',true)
        ->get();


        foreach ($campaigns as $camp) {
            if( in_array($now_time, explode(',', $camp->frequency_time))){
                $trigger = false;
                switch ($camp->frequency_type) {
                    case 'daily':
                        $trigger = true;
                        break;
                    case 'date':
                        $trigger = ($camp->frequency_date == $today->toDateString());
                        break;
                    case 'days':
                        $trigger = in_array($today->dayOfWeek, explode(',', $camp->frequency_days));
                        break;
                    default:
                        break;
                }

                if ($trigger) {
                    $this->info(">>> Triggering: $camp->title, [" . ($camp->frequency_type) . " @ $camp->frequency_time]");

                    $users_id = UserGroupMap::whereIn('user_group_id', explode(',', $camp->user_groups_id))->get();
                    $this->sendMail($camp->id, $users_id);

                }
            }
        }
        $this->info('Completed.');
    }

    public function sendMail($camp_id, $users_id){
        foreach ($users_id as $value) {
            $user = User::find($value->user_id);
            Mail::to($user->email)->send(new EmailSchedule($camp_id, $user));
        }
    }
}
