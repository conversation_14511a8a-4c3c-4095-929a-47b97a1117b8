@extends('layouts.admin')
@section('title',$admin_page_title)
@section('content')
<section id="sub-nav">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-6">
        <div class="block sub-nav-utility mb-0 pb-0 clearfix">
          <a href="{{ route('admin.stores.index') }}" class="btn btn-primary float-left">Close</a>
        </div>
      </div>
      <div class="col-md-6">
        <div class="block sub-nav-utility mb-0 pb-0 clearfix">
          <div class="float-right">
            <button type="submit" form="seo-info-form" class="btn btn-primary float-right" name="action" value="seo-info">Save</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<section>
    <div class="container-fluid">
        <div class="col-md-12">
            <div class="block px-0">
                <a class="btn btn-primary" href="{{ route('admin.stores.edit', $item->id) }}">Main</a>
                <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'social']) }}">Social</a>
                <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'contact']) }}">Contact</a>
                <a class="btn btn-primary" href="{{ route('admin.stores.edit', [$item->id, 'hours']) }}">Hours</a>
            </div>
        </div>
    </div>
</section>
<section>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-5">
        <div class="block block-content clearfix">
          <div class="block-content-inner clearfix">
            <form action="{{ route('admin.stores.update', [$item->id,'seo']) }}" method="POST" id="seo-info-form">
              @csrf
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label for="meta_title">Meta Title</label>
                    <input type="text" name="meta_title" id="meta_title" class="form-control {{ $errors->has('meta_title') ? 'is-invalid':''}}"
                      value="{{old('meta_title')?old('meta_title'):$item->meta_title }}">
                    @if($errors->has('meta_title'))
                    <small class="form-text text-danger">{{ $errors->first('meta_title') }}</small> @endif
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label for="meta_keywords">Meta Keywords</label>
                    <input type="text" name="meta_keywords" id="meta_keywords" class="form-control {{ $errors->has('meta_keywords') ? 'is-invalid':''}}"
                      value="{{old('meta_keywords')?old('meta_keywords'):$item->meta_keywords }}">
                    @if($errors->has('meta_keywords'))
                    <small class="form-text text-danger">{{ $errors->first('meta_keywords') }}</small> @endif
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label for="meta_description">Meta Description</label>
                    <textarea name="meta_description" id="meta_description" class="form-control {{ $errors->has('meta_description') ? 'is-invalid':''}}"
                      cols="30" rows="4">{{ old('meta_description')?old('meta_description'):$item->meta_description }}</textarea>
                    @if($errors->has('meta_description'))
                    <small class="form-text text-danger">{{ $errors->first('meta_description') }}</small>
                    @endif
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
@endsection
