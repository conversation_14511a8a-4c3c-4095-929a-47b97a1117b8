<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateConditionTypeEnumInCouponsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        
        // Add 'attribute' to the enum options
        DB::statement("ALTER TABLE coupons 
            MODIFY condition_type ENUM('none', 'subtotal', 'category', 'brand', 'product', 'attribute') 
            DEFAULT 'none'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Rollback: remove 'attribute' from the enum options
        DB::statement("ALTER TABLE coupons 
            MODIFY condition_type ENUM('none', 'subtotal', 'category', 'brand', 'product') 
            DEFAULT 'none'");
    }
}
