<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPaymentOptionToOrdersTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('orders') && !Schema::hasColumn('orders', 'payment_option')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->string('payment_option')->nullable()->after('shipping_method');
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('orders') && Schema::hasColumn('orders', 'payment_option')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('payment_option');
            });
        }
    }
}
