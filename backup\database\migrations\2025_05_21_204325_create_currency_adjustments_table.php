<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCurrencyAdjustmentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_adjustments', function (Blueprint $table) {
            $table->id();
            $table->string('from_currency', 3); // Example: USD
            $table->string('to_currency', 3);   // Example: INR
            $table->decimal('adjustment_percent', 6, 2); // Example: -2.50 or 3.75
            $table->timestamps();

            // Prevent duplicate from-to pairs
            $table->unique(['from_currency', 'to_currency']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_adjustments');
    }
}
