<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\AlertPreference;
use App\Models\EmailTemplate;
use App\Models\EmailTemplateGroup;

class MemberWelcome extends Mailable
{
    use Queueable, SerializesModels;

    public $password = null;
    public $user = null;
    public $content = null;
    public $pre_header = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $password)
    {
        $email_template = EmailTemplate::find(4);
        $data = [
            ucfirst($user->first_name),
            ucfirst($user->last_name),
            ucfirst($password),
        ];
        $this->subject = $email_template->subject;
        $this->pre_header = $email_template->pre_header;
        $this->content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->email_body));
        if ($email_template->sms_status) {
            $message = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->sms_body));
            if ($user->tel_cell) {
                app('App\Http\Controllers\Controller')->sendMessage($message, $user->tel_cell_country_code . $user->tel_cell);
            }
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)->view('email_templates.order_confirmed');
    }
}
