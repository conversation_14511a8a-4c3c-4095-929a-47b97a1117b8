<?php

// use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebPageController;
use App\Http\Controllers\NewsletterController;
use App\Http\Controllers\NotifyItemController;
use App\Http\Controllers\FlutterwaveController;
use App\Http\Controllers\Admin\PointSystemController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\CouponController;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\Admin\AlertPreferencesController;
use App\Http\Controllers\CouponCartController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\Admin\AdminPaymentMethodController;

Route::get('login', 'Auth\LoginController@showLoginForm')->name('login');
Route::post('login', 'Auth\LoginController@login');

Route::get('signup', 'Auth\RegisterController@showRegistrationForm')->name('register');
Route::post('signup', 'Auth\RegisterController@register');

Route::post('sign-out', 'Auth\LoginController@logout')->name('logout');

Route::get('forgot-password', 'Auth\ForgotPasswordController@showLinkRequestForm')->name('password.request');
Route::post('email-password', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('password.email');
Route::get('thank-you-reset', 'Auth\ForgotPasswordController@afterSendResetLink')->name('password.thanks');

Route::get('reset-password', 'Auth\ResetPasswordController@showResetForm')->name('password.reset');
Route::post('password-update', 'Auth\ResetPasswordController@reset')->name('password.update');

Route::get('email-verification/{email}/{token}', 'Auth\RegisterController@verifyingMail')->name('verify.mail');
Route::get('resend-email-verification/', 'Auth\RegisterController@showResendVerificationMailForm')->name('verify.mail.resend');
// Resend email verification
Route::post('resend-email-verification', 'Auth\RegisterController@resendVerificationMail');

// Public Checkout Routes
Route::group(['prefix' => 'checkout', 'middleware' => ['web']], function () {
    Route::get('/payment-methods', 'CheckoutController@getAvailablePaymentMethods')
        ->name('checkout.payment-methods');

    Route::get('/payment-methods/{id}/details', 'CheckoutController@getPaymentMethodDetails')
        ->name('checkout.payment-method-details');

    Route::post('/payment-methods/{id}/split-preview', 'PaymentMethodController@calculateSplitPayment')
        ->name('checkout.split-preview');

    Route::post('/process-payment', 'CheckoutController@processPayment')
        ->name('checkout.process-payment');
});

// Public Payment Method Routes
Route::group(['prefix' => 'payment-methods'], function () {
    Route::get('/available', 'PaymentMethodController@getAvailableForCheckout')
        ->name('payment-methods.available');

    Route::get('/{id}', 'PaymentMethodController@show')
        ->name('payment-methods.show');

    Route::get('/{id}/details', 'PaymentMethodController@getPaymentMethodDetails')
        ->name('payment-methods.details');

    Route::post('/{id}/calculate-split', 'PaymentMethodController@calculateSplitPayment')
        ->name('payment-methods.calculate-split');
});

// Payment Callback Routes
Route::group(['prefix' => 'payment'], function () {
    Route::get('/callback', 'CheckoutController@handlePaymentCallback')
        ->name('payment.callback');
    Route::post('/callback', 'CheckoutController@handlePaymentCallback');

    Route::get('/success', 'CheckoutController@paymentSuccess')
        ->name('payment.success');
    Route::get('/failed', 'CheckoutController@paymentFailed')
        ->name('payment.failed');
    Route::get('/cancel', 'CheckoutController@paymentCancelled')
        ->name('payment.cancel');
});

// Admin Routes for Payment Method Management
Route::group(['middleware' => ['auth', 'admin_login'], 'prefix' => 'admin', 'as' => 'admin.'], function () {


});

// Webhook Routes (no authentication needed)
Route::group(['prefix' => 'webhooks'], function () {
    Route::post('/squadpay', 'WebhookController@handleSquadPayWebhook')
        ->name('webhooks.squadpay');

    Route::post('/flutterwave', 'WebhookController@handleFlutterwaveWebhook')
        ->name('webhooks.flutterwave');

    Route::post('/stripe', 'WebhookController@handleStripeWebhook')
        ->name('webhooks.stripe');

    Route::post('/paypal', 'WebhookController@handlePayPalWebhook')
        ->name('webhooks.paypal');

    Route::post('/btcpay', 'WebhookController@handleBTCPayWebhook')
        ->name('webhooks.btcpay');

    Route::post('/klarna', 'WebhookController@handleKlarnaWebhook')
        ->name('webhooks.klarna');

    Route::post('/affirm', 'WebhookController@handleAffirmWebhook')
        ->name('webhooks.affirm');

    Route::post('/afterpay', 'WebhookController@handleAfterpayWebhook')
        ->name('webhooks.afterpay');
});


//Route::get('product-not-available', 'WebPageController@showPage');
Route::post('/apply-coupon', [CouponController::class, 'applyCoupon'])->name('apply-coupon');
Route::post('/sent-promo-code-sms', [NewsletterController::class, 'sentPromoSMS'])->name('sent-promo-code-sms');
Route::post('/discount/mark-closed', function () {
    session()->put('discount_modal_closed', true);
    return response()->json(['status' => 'ok']);
})->name('discount.markClosed');

Route::post('/discount/mark-dismissed', function () {
    session()->put('discount_fully_dismissed', true);
    return response()->json(['status' => 'ok']);
})->name('discount.markDismissed');
Route::get('/unsubscribe-promotional-alerts', [AlertPreferencesController::class, 'unsubscribePromotionalAlerts'])
    ->name('unsubscribe.promotional')
    ->middleware('signed');
Route::get('/check-subscriber', function (Request $request) {

    $field = $request->query('field');
    $value = $request->query('value');

    // Allow only specific fields to prevent SQL injection
    $allowedFields = ['email', 'tel_cell'];

    if (!in_array($field, $allowedFields)) {
        return response()->json(['error' => 'Invalid field'], 400);
    }

    $exists = DB::table('users')->where($field, $value)->exists();

    return response()->json(['exists' => $exists]);
})->name('check-subscriber');

// The route that the button calls to initialize payment
Route::get('/pay', [FlutterwaveController::class, 'initialize'])->name('pay');
// The callback url after a payment
Route::get('/rave/callback', [FlutterwaveController::class, 'callback'])->name('callback');
Route::group(['middleware' => ['auth', 'admin_login'], 'prefix' => 'admin', 'as' => 'admin.'], function () {
    Route::resource('payment-methods', 'Admin\AdminPaymentMethodController');

    Route::post('/payment-methods/{id}/toggle-status', 'Admin\AdminPaymentMethodController@toggleStatus')
        ->name('admin.payment-methods.toggle-status');

    Route::post('/payment-methods/{id}/test-connection', 'Admin\AdminPaymentMethodController@testConnection')
        ->name('admin.payment-methods.test-connection');

    Route::get('/payment-methods/{id}/transactions', 'Admin\AdminPaymentMethodController@getTransactions')
        ->name('admin.payment-methods.transactions');

    Route::post('/payment-methods/bulk-action', 'Admin\AdminPaymentMethodController@bulkAction')
        ->name('admin.payment-methods.bulk-action');

    Route::get('/payment-methods-export', 'Admin\AdminPaymentMethodController@export')
        ->name('admin.payment-methods.export');

    Route::post('/payment-methods-import', 'Admin\AdminPaymentMethodController@import')
        ->name('admin.payment-methods.import');

    Route::get('/payment-methods/{id}/ajax', 'Admin\AdminPaymentMethodController@getAjaxDetails')
        ->name('admin.payment-methods.ajax-details');

    Route::post('/payment-methods/reorder', 'Admin\AdminPaymentMethodController@reorder')
        ->name('admin.payment-methods.reorder');
    Route::get('dashboard', 'Admin\AdminController@index')->name('dashboard');
    Route::get('refresh/data', 'Admin\AdminController@refreshData')->name('refresh.url');
    Route::get('points-system', [PointSystemController::class, 'index'])->name('points-system.index');
    Route::get('points-system/create', [PointSystemController::class, 'create'])->name('points-system.create');
    Route::post('points-system', [PointSystemController::class, 'store'])->name('points-system.store');
    Route::get('points-system/{pointSystem}/edit', [PointSystemController::class, 'edit'])->name('points-system.edit');
    Route::put('points-system/{pointSystem}', [PointSystemController::class, 'update'])->name('points-system.update');
    Route::delete('points-system/{pointSystem}', [PointSystemController::class, 'destroy'])->name('points-system.destroy');
    Route::get('suscriber', [NewsletterController::class, 'index'])->name('user-suscriber');
    Route::get('datatable-data', [NewsletterController::class, 'getData'])->name('datatable.data');

        Route::resource('whatsapp-template-metas', 'Admin\WhatsappTemplateMetaController');

    Route::group(['middleware' => 'auth', 'prefix' => 'live', 'as' => 'live.'], function () {
        Route::get('/series/{brand_id}/{series_id?}', 'Controller@getSeries')->name('series');
        Route::get('/categories/{brand_id}/{option_id?}', 'Controller@getLiveCategories')->name('categories');
    });

    Route::group(['prefix' => 'image/file', 'as' => 'image.file.'], function () {
        Route::post('/upload/temp/{target}/', 'Admin\FileHandleController@fileUploadTemp')->name('upload.temp');
        Route::get('/remove/temp/{target}', 'Admin\FileHandleController@fileRemoveTemp')->name('remove.temp');
    });
    Route::group(['prefix' => 'images/files', 'as' => 'images.files.'], function () {
        Route::post('/upload/{target}', 'Admin\FileHandleController@filesUploadTemp')->name('upload.temp');
        Route::get('/remove/{target}/{id}', 'Admin\FileHandleController@filesRemoveTemp')->name('remove.temp');
        Route::get('/image-info/{target}', 'Admin\FileHandleController@updateImageInfoTemp')->name('upload.temp.image.info');
    });
    Route::group(['prefix' => 'videos/files', 'as' => 'videos.files.'], function () {
        Route::post('/upload/{target}', 'Admin\FileHandleController@filesUploadTemp')->name('upload.temp');
        Route::get('/remove/{target}/{id}', 'Admin\FileHandleController@filesRemoveTemp')->name('remove.temp');
        Route::get('/image-info/{target}', 'Admin\FileHandleController@updateImageInfoTemp')->name('upload.temp.video.info');
    });

    Route::resource('/users', 'Admin\UserController');
    Route::group(['prefix' => 'users/file', 'as' => 'users.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\UserController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\UserController@fileRemove')->name('remove');
    });
    Route::post('/users/selected', 'Admin\UserController@userSelected')->name('users.selected');
    Route::get('/get_users/{user_group_id?}', 'Admin\UserController@getUsersGroup')->name('get_user.user_group');

    Route::get('/pages/search', 'Admin\PageController@search')->name('pages.search');
    Route::resource('/pages', 'Admin\PageController');
    Route::group(['prefix' => 'pages/file', 'as' => 'pages.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\PageController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\PageController@fileRemove')->name('remove');
    });
    Route::group(['middleware' => 'auth', 'prefix' => 'live', 'as' => 'live.'], function () {
        Route::get('/page_types', 'Admin\PageController@pageTypes')->name('page_types');
        Route::get('/page_keys', 'Admin\PageController@pageKeys')->name('page_keys');
    });


    Route::resource('/slideshow', 'Admin\SlideCategoryController')->except(['show']);
    Route::get('/slides/{id}', 'Admin\SlideController@index')->name('slides.index');
    Route::get('/slides/create/{id}', 'Admin\SlideController@create')->name('slides.create');
    Route::post('/slides/{id}', 'Admin\SlideController@store')->name('slides.store');
    Route::get('/slides/edit/{id}/{cat_id}', 'Admin\SlideController@edit')->name('slides.edit');
    Route::put('/slides/{id}/{cat_id}', 'Admin\SlideController@update')->name('slides.update');
    Route::delete('/slides/{id}/{cat_id}', 'Admin\SlideController@destroy')->name('slides.destroy');

    Route::group(['prefix' => 'slide/file', 'as' => 'slide.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\SlideController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\SlideController@fileRemove')->name('remove');
    });

    Route::resource('/posts', 'Admin\PostController');
    Route::group(['prefix' => 'post/file', 'as' => 'post.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\PostController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\PostController@fileRemove')->name('remove');
    });
    Route::resource('/post-categories', 'Admin\PostCategoryController');

    Route::resource('/redeem_cash_rewards', 'Admin\RedeemCashRewardController');

    Route::resource('/reviews', 'Admin\ReviewController');
    // Route::get('/reviews', 'Admin\ReviewController@index')->name('reviews.index');
    // Route::post('/review/store', 'Admin\ReviewController@store')->name('review.store');
    Route::get('/email_sms_details/{id}/{type}', 'Admin\ReviewController@getEmailSmsDetails')->name('get.email_sms_details');
    Route::post('/send_reward/{id}', 'Admin\ReviewController@sendReward')->name('send_reward');

    Route::resource('/products', 'Admin\ProductController')->except(['update']);
    Route::post('/products/{id}', 'Admin\ProductController@update')->name('products.update');
    Route::group(['prefix' => 'products/file', 'as' => 'products.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\ProductController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\ProductController@fileRemove')->name('remove');
    });

    Route::get('/sections/{page_id?}', 'Admin\SectionController@index')->name('sections.index');
    Route::post('/sections/{page_id?}', 'Admin\SectionController@store')->name('sections.store');
    Route::get('/sections/edit/{id}/{page_id?}', 'Admin\SectionController@edit')->name('sections.edit');
    Route::post('/sections/update/{id}/{page_id?}', 'Admin\SectionController@update')->name('sections.update');
    Route::delete('/sections/delete/{id}/{page_id?}', 'Admin\SectionController@destroy')->name('sections.destroy');

    Route::get('/product/items/{product_id?}', 'Admin\ProductItemController@index')->name('product.items.index');
    Route::get('/product/item/create/{product_id?}', 'Admin\ProductItemController@create')->name('product.item.create');
    Route::post('/product/item/store/{product_id?}', 'Admin\ProductItemController@store')->name('product.item.store');
    Route::get('/product/item/edit/{id}/{product_id?}', 'Admin\ProductItemController@edit')->name('product.item.edit');
    Route::post('/product/item/{id}/{product_id?}', 'Admin\ProductItemController@update')->name('product.item.update');
    Route::delete('/product/item/{id}/{product_id?}', 'Admin\ProductItemController@destroy')->name('product.item.destroy');
    Route::get('/product_clone/item/{id}/{product_id?}', 'Admin\ProductItemController@cloneItem')->name('product.item.clone');

    Route::group(['prefix' => 'product/item/file', 'as' => 'product.item.file.'], function () {
        Route::post('/upload/{target}/{id}/{product_id?}', 'Admin\ProductItemController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}/{product_id?}', 'Admin\ProductItemController@fileRemove')->name('remove');
    });

    Route::group(['prefix' => 'product/item/files', 'as' => 'product.item.files.'], function () {
        Route::post('/upload/{target}/{id}/{product_id?}', 'Admin\ProductItemController@filesUpload')->name('upload');
        Route::get('/remove/{target}/{id}/{item}/{product_id?}', 'Admin\ProductItemController@filesRemove')->name('remove');
        Route::get('/image-info/{target}/{item}/{product_id?}', 'Admin\ProductItemController@updateImageInfo')->name('upload.image.info');
    });

    Route::get('/get-product-attributes/item/{product_id}/{item_id?}/', 'Admin\ProductItemController@getProductAttributes')->name('get.product.attribute');
    Route::get('/get-attributes/{attributes_id?}/{product_id?}', 'Admin\ProductItemController@getAttributes')->name('get.attributes');
    Route::post('/update-attribute/{attributes_id?}/{product_id?}', 'Admin\ProductController@updateAttributes')->name('update.attribute');

    Route::get('/attributes/{attributes_id}/{product_id?}/', 'Admin\ProductController@attributeOptions')->name('product.attribute.options');
    Route::post('/add-attributes/{attributes_id}/{product_id?}', 'Admin\ProductController@addAttributeOption')->name('add.product.attribute.option');
    Route::get('/edit-attributes/{option_id}/{attributes_id}/{product_id?}', 'Admin\ProductController@editAttributeOption')->name('edit.product.attribute.option');
    Route::post('/update-attributes/{option_id}/{attributes_id}/{product_id?}', 'Admin\ProductController@updateAttributeOption')->name('update.product.attribute.option');
    Route::delete('/delete-attributes/{option_id}/{attributes_id}/{product_id?}', 'Admin\ProductController@destroyAttributeOption')->name('destroy.product.attribute.option');
    Route::group(['prefix' => '/attribute/item/file', 'as' => 'attribute.item.file.'], function () {
        Route::post('/upload/{target}/{option_id}/{attribute_id}/{product_id?}', 'Admin\ProductController@attributeItemUpload')->name('upload');
        Route::get('/remove/{target}/{option_id}/{attribute_id}/{product_id?}', 'Admin\ProductController@attributeItemRemove')->name('remove');
    });
    Route::delete('/delete-attribute/{attributes_id}/{product_id?}', 'Admin\ProductController@destroyAttribute')->name('destroy.product.attribute');
    Route::get('/product/attributes/{product_id?}', 'Admin\ProductController@getProductAttribute')->name('show.product.attributes');
    Route::get('/add/product/attribute/{product_id?}', 'Admin\ProductController@addAttribute')->name('add.product.attribute');

    Route::get('/product/specifications/{product_id?}', 'Admin\ProductController@getSpecifications')->name('product.specifications');
    Route::post('/add/product/specification/{product_id?}', 'Admin\ProductController@addSpecification')->name('add.product.specification');
    Route::get('/edit/product/specification/{id}/{product_id?}', 'Admin\ProductController@editSpecification')->name('edit.product.specification');
    Route::post('/update/product/specification/{id}/{product_id?}', 'Admin\ProductController@updateSpecification')->name('update.product.specification');
    Route::delete('/delete/product/specification/{id}/{product_id?}', 'Admin\ProductController@deleteSpecification')->name('delete.product.specification');
    Route::group(['prefix' => '/product/specification/file', 'as' => 'product.specification.file.'], function () {
        Route::post('/upload/{target}/{id}/{product_id?}', 'Admin\ProductController@specificationFileUpload')->name('upload');
        Route::get('/remove/{target}/{id}/{product_id?}', 'Admin\ProductController@specificationFileRemove')->name('remove');
    });

    Route::resource('/product-brands', 'Admin\ProductBrandController');
    Route::group(['prefix' => 'product-brands/file', 'as' => 'product-brands.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\ProductBrandController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\ProductBrandController@fileRemove')->name('remove');
    });

    Route::resource('/product-categories', 'Admin\ProductCategoryController');
    Route::resource('/parent-categories', 'Admin\ParentProductCategory');
    Route::group(['prefix' => 'product-categories/file', 'as' => 'product-categories.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\ProductCategoryController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\ProductCategoryController@fileRemove')->name('remove');
    });

    Route::resource('/order-refunds', 'Admin\OrderRefundController');
    Route::resource('/country-message-settings', 'Admin\CountryMessageSettingController');
    Route::resource('coupons', 'Admin\CouponController');

    Route::resource('/shipping-methods', 'Admin\ShippingMethodController');
    Route::resource('/shipping-rules', 'Admin\ShippingRulesController');

    // Route::group(['prefix' => 'shipping-methods/file', 'as' => 'shipping-methods.file.'], function () {
    //     Route::post('/upload/{target}/{id}', 'Admin\ShippingMethodController@fileUpload')->name('upload');
    //     Route::get('/remove/{target}/{id}', 'Admin\ShippingMethodController@fileRemove')->name('remove');
    // });
    Route::get('/shipping_methods/options/{shipping_method_id?}', 'Admin\ShippingMethodController@options')->name('shipping_methods.options');
    Route::post('/shipping_methods/options/add/{shipping_method_id?}', 'Admin\ShippingMethodController@storeOption')->name('shipping_methods.add_option');
    Route::get('/shipping_methods/options/edit/{id}/{shipping_method_id?}', 'Admin\ShippingMethodController@editOption')->name('shipping_methods.edit_option');
    Route::post('/shipping_methods/options/update/{id}/{shipping_method_id?}', 'Admin\ShippingMethodController@updateOption')->name('shipping_methods.update_option');
    Route::delete('/shipping_methods/options/delete/{id}/{shipping_method_id?}', 'Admin\ShippingMethodController@destroyOption')->name('shipping_methods.destroy_option');
    Route::group(['prefix' => '/shipping_methods/items/file', 'as' => 'shipping_methods.items.file.'], function () {
        Route::post('/upload/{target}/{id}/{shipping_method_id?}', 'Admin\ShippingMethodController@fileShippingItemUpload')->name('upload');
        Route::get('/remove/{target}/{id}/{shipping_method_id?}', 'Admin\ShippingMethodController@fileShippingItemRemove')->name('remove');
    });

    // Route::resource('/alert_preferences', 'Admin\AlertPreferencesController');
    Route::get('/alert_preferences', 'Admin\AlertPreferencesController@index')->name('alert_preferences.index');
    Route::post('/alert_preferences', 'Admin\AlertPreferencesController@store')->name('alert_preferences.store');
    Route::get('/alert_preferences/{id}/edit', 'Admin\AlertPreferencesController@edit')->name('alert_preferences.edit');
    Route::post('/alert_preferences/{id}', 'Admin\AlertPreferencesController@update')->name('alert_preferences.update');
    Route::delete('/alert_preferences/{id}', 'Admin\AlertPreferencesController@destroy')->name('alert_preferences.destroy');

    Route::resource('/email_templates', 'Admin\EmailTemplateController');
    Route::resource('/email_template_groups', 'Admin\EmailTemplateGroupController');
    Route::resource('/email_campaigns', 'Admin\EmailCampaignController');
    Route::get('/get/{email_template_group_id}/email_templates', 'Admin\EmailCampaignController@getEmailTemplates')->name('email_campaign.email_templates');

    Route::get('/email_template_group/{order_status_id}/get_email_templates', 'Admin\EmailCampaignController@getEmailTemplatesOrderStatus')->name('order_status.email_templates');

    Route::get('/get/{email_template_id}/email_template', 'Admin\EmailCampaignController@getEmailTemplate')->name('email_campaign.email_template');

    Route::resource('/orders', 'Admin\OrderController');
    Route::post('/orders/create-invoice', [OrderController::class, 'createInvoice'])->name('orders.create-invoice');
    Route::post('/orders/invoice/{order_id}', [OrderController::class, 'updateInvoice'])->name('orders.update-invoice');
    Route::post('/orders/bulkinvoice/{order_id}', [OrderController::class, 'bulkOrder'])->name('orders.bulk-invoice');
    Route::resource('/collects', 'Admin\CollectController');
    // Route::resource('/user_shippings', 'Admin\UserShippingController');
    Route::get('/user_shippings/{user_id?}/{type?}', 'Admin\UserShippingController@index')->name('user_shippings.index');
    Route::post('/store/user_shippings/{user_id?}', 'Admin\UserShippingController@store')->name('user_shippings.store');
    Route::get('/edit/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@edit')->name('user_shippings.edit');
    Route::post('/update/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@update')->name('user_shippings.update');
    Route::delete('/delete/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@destroy')->name('user_shippings.destroy');
    // Route::resource('/user_billings', 'Admin\UserBillingController');
    Route::get('/user_billings/{user_id?}/{type?}', 'Admin\UserBillingController@index')->name('user_billings.index');
    Route::post('/store/user_billings/{user_id?}', 'Admin\UserBillingController@store')->name('user_billings.store');
    Route::get('/edit/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@edit')->name('user_billings.edit');
    Route::post('/update/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@update')->name('user_billings.update');
    Route::delete('/delete/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@destroy')->name('user_billings.destroy');

    Route::resource('/product-series', 'Admin\ProductSeriesController');
    Route::group(['prefix' => 'product-series/file', 'as' => 'product-series.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\ProductSeriesController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\ProductSeriesController@fileRemove')->name('remove');
    });

    Route::resource('/promos', 'Admin\PromoController');
    Route::group(['prefix' => 'promos/file', 'as' => 'promos.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\PromoController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\PromoController@fileRemove')->name('remove');
    });

    Route::resource('/transaction_methods', 'Admin\TransactionMethodController');
    Route::group(['prefix' => 'transaction_methods/file', 'as' => 'transaction_methods.file.'], function () {
        Route::post('/upload/{target}/{id}', 'Admin\TransactionMethodController@fileUpload')->name('upload');
        Route::get('/remove/{target}/{id}', 'Admin\TransactionMethodController@fileRemove')->name('remove');
    });

    Route::get('/product-attributes', 'Admin\ProductAttributeController@index')->name('product_attributes.index');
    Route::post('/product-attributes', 'Admin\ProductAttributeController@store')->name('product_attributes.store');
    Route::get('/product-attributes/{id}/edit', 'Admin\ProductAttributeController@edit')->name('product_attributes.edit');
    Route::post('/product-attributes/{id}', 'Admin\ProductAttributeController@update')->name('product_attributes.update');
    Route::delete('/product-attributes/{id}', 'Admin\ProductAttributeController@destroy')->name('product_attributes.destroy');

    Route::get('/product-attribute/items/{attribute_id}', 'Admin\ProductAttributeItemController@index')->name('product_attribute_items.index');
    Route::get('/product-attribute/items/create/{attribute_id}', 'Admin\ProductAttributeItemController@create')->name('product_attribute_items.create');
    Route::post('/product-attribute/items/{attribute_id}', 'Admin\ProductAttributeItemController@store')->name('product_attribute_items.store');
    Route::get('/product-attribute/items/edit/{id}/{attribute_id}', 'Admin\ProductAttributeItemController@edit')->name('product_attribute_items.edit');
    Route::put('/product-attribute/items/{id}/{attribute_id}', 'Admin\ProductAttributeItemController@update')->name('product_attribute_items.update');
    Route::delete('/product-attribute/items/{id}/{attribute_id}', 'Admin\ProductAttributeItemController@destroy')->name('product_attribute_items.destroy');

    Route::get('/product_attribute/items/options/{attribute_id}/{attribute_item_id?}', 'Admin\ProductAttributeItemController@options')->name('product_attribute_items.options');
    Route::post('/product_attribute/items/add-option/{attribute_id}/{attribute_item_id?}', 'Admin\ProductAttributeItemController@storeOption')->name('product_attribute_items.add_option');
    Route::get('/product_attribute/items/edit/option/{attribute_id}/{option_id}/{attribute_item_id?}', 'Admin\ProductAttributeItemController@editOption')->name('product_attribute_items.option.edit');
    Route::post('/product_attribute/items/update/option/{attribute_id}/{option_id}/{attribute_item_id?}', 'Admin\ProductAttributeItemController@editOptionUpdate')->name('product_attribute_items.option.update');
    Route::delete('/product_attribute/items/destroy/option/{attribute_id}/{option_id}/{attribute_item_id?}', 'Admin\ProductAttributeItemController@optionDestroy')->name('product_attribute_items.option.destroy');
    Route::group(['prefix' => '/product_attribute/items/file', 'as' => 'product_attribute.items.file.'], function () {
        Route::post('/upload/{target}/{attribute_id}/{option_id}/{attribute_item_id?}/{title?}', 'Admin\ProductAttributeItemController@fileAttributeItemUpload')->name('upload');
        Route::get('/remove/{target}/{attribute_id}/{option_id}/{attribute_item_id?}', 'Admin\ProductAttributeItemController@fileAttributeItemRemove')->name('remove');
    });

    Route::resource('/stores', 'Admin\SettingController')->except('edit', 'update');
    Route::get('/stores/edit/{id}/{type?}', 'Admin\SettingController@edit')->name('stores.edit');
    Route::post('/stores/update/{id}/{type?}', 'Admin\SettingController@update')->name('stores.update');
    Route::group(['prefix' => 'file', 'as' => 'file.'], function () {
        Route::post('/upload/{id}/{target}', 'Admin\SettingController@fileUpload')->name('store.upload');
        Route::get('/remove/{id}/{target}', 'Admin\SettingController@fileRemove')->name('store.remove');
    });
    Route::group(['prefix' => 'setting', 'as' => 'setting.'], function () {
        Route::get('/site-information', 'Admin\SettingController@editSite')->name('site');
        Route::post('/site-information', 'Admin\SettingController@updateSite');
        Route::get('/seo-information', 'Admin\SettingController@editSeoInfo')->name('seo_info');
        Route::post('/seo-information', 'Admin\SettingController@updateSeoInfo');
        Route::get('/social-information', 'Admin\SettingController@editSocial')->name('social_info');
        Route::post('/social-information', 'Admin\SettingController@updateSocialInfo');
        Route::get('/contact-information', 'Admin\SettingController@editContactInfo')->name('contact_info');
        Route::post('/contact-information', 'Admin\SettingController@updateContactInfo');
        Route::get('/business_hours', 'Admin\SettingController@editBusinessHoursInfo')->name('business_hours');
        Route::get('/shipping_units_settings', 'Admin\SettingController@editShippingUnitsInfo')->name('shipping_units_settings');
        Route::post('/shipping_units_settings', 'Admin\SettingController@updateShippingUnitsInfo')->name('shipping_units.update');

        Route::post('/business_hours', 'Admin\SettingController@updateBusinessHoursInfo');
        Route::get('/sms-info', 'Admin\SettingController@editSMSInfo')->name('sms_info');
        Route::post('/sms-info', 'Admin\SettingController@updateSMSInfo');
        Route::group(['prefix' => 'file', 'as' => 'file.'], function () {
            Route::post('/upload/{target}', 'Admin\SettingController@fileUpload')->name('upload');
            Route::get('/remove/{target}', 'Admin\SettingController@fileRemove')->name('remove');
        });
    });

    // Route::resource('/estimated-delivery-dates', 'Admin\EstimatedDeliveryDateController');
    Route::get('/estimated-delivery-dates', 'Admin\EstimatedDeliveryDateController@index')->name('estimated_delivery_dates.index');
    Route::post('/estimated-delivery-dates', 'Admin\EstimatedDeliveryDateController@store')->name('estimated_delivery_dates.store');
    Route::get('/estimated-delivery-dates/{id}/edit', 'Admin\EstimatedDeliveryDateController@edit')->name('estimated_delivery_dates.edit');
    Route::post('/estimated-delivery-dates/{id}', 'Admin\EstimatedDeliveryDateController@update')->name('estimated_delivery_dates.update');
    Route::delete('/estimated-delivery-dates/{id}', 'Admin\EstimatedDeliveryDateController@destroy')->name('estimated_delivery_dates.destroy');

    Route::resource('/user-groups', 'Admin\UserGroupController')->except(['show']);
    Route::resource('/access-labels', 'Admin\AccessLabelController')->except(['show']);

    Route::group(['prefix' => 'filemanager', 'middleware' => ['web', 'auth']], function () {
        \UniSharp\LaravelFilemanager\Lfm::routes();
    });
    Route::get('/currency-adjustments', 'Admin\CurrencyAdjustmentController@index')->name('currency.adjustments');
    Route::post('/currency-adjustments', 'Admin\CurrencyAdjustmentController@store')->name('currency.adjustments.store');
    Route::delete('/currency-adjustments/{id}', 'Admin\CurrencyAdjustmentController@destroy')->name('currency.adjustments.destroy');
});

// Route::group(['middleware' => ['auth', 'customer'], 'prefix' => 'customer', 'as' => 'customer.'], function () {
//     Route::post('/store/user_shippings/{user_id?}', 'Admin\UserShippingController@store')->name('user_shippings.store');
//     Route::get('/edit/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@edit')->name('user_shippings.edit');
//     Route::post('/update/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@update')->name('user_shippings.update');

//     Route::post('/store/user_billings/{user_id?}', 'Admin\UserBillingController@store')->name('user_billings.store');
//     Route::get('/edit/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@edit')->name('user_billings.edit');
//     Route::post('/update/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@update')->name('user_billings.update');
// });
Route::group(['middleware' => ['web', 'auth'], 'prefix' => 'web_auth', 'as' => 'web_auth.'], function () {
    Route::get('/user_shippings/{user_id?}/{type?}', 'Admin\UserShippingController@index')->name('user_shippings.index');
    Route::post('/store/user_shippings/{user_id?}', 'Admin\UserShippingController@store')->name('user_shippings.store');
    Route::get('/edit/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@edit')->name('user_shippings.edit');
    Route::post('/update/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@update')->name('user_shippings.update');
    Route::delete('/delete/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@destroy')->name('user_shippings.destroy');

    Route::get('/user_billings/{user_id?}/{type?}', 'Admin\UserBillingController@index')->name('user_billings.index');
    Route::post('/store/user_billings/{user_id?}', 'Admin\UserBillingController@store')->name('user_billings.store');
    Route::get('/edit/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@edit')->name('user_billings.edit');
    Route::post('/update/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@update')->name('user_billings.update');
    Route::delete('/delete/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@destroy')->name('user_billings.destroy');

    Route::group(['prefix' => 'image/file', 'as' => 'image.file.'], function () {
        Route::post('/upload/temp/{target}/', 'Admin\FileHandleController@fileUploadTemp')->name('upload.temp');
        Route::get('/remove/temp/{target}', 'Admin\FileHandleController@fileRemoveTemp')->name('remove.temp');
    });
    Route::group(['prefix' => 'images/files', 'as' => 'images.files.'], function () {
        Route::post('/upload/{target}', 'Admin\FileHandleController@filesUploadTemp')->name('upload.temp');
        Route::get('/remove/{target}/{id}', 'Admin\FileHandleController@filesRemoveTemp')->name('remove.temp');
        Route::get('/image-info/{target}', 'Admin\FileHandleController@updateImageInfoTemp')->name('upload.temp.image.info');
    });
    Route::get('/review_item/{id}', 'WebPageController@getReviewItem')->name('get.review_item');
    // Route::post('/update_review_item/{id}', 'WebPageController@updateProductReview')->name('update.review_review');
    Route::post('/update_review_item/{id}', 'WebPageController@updateProductReview')->name('update.product_review');

    // Route::post('update_product_review/{id}', "WebPageController@updateProductReview")->name('update.product_review');

    Route::group(['prefix' => 'review/item/files', 'as' => 'review.item.files.'], function () {
        Route::post('/upload/{target}/{id}/{product_id?}', 'WebPageController@filesUploadReview')->name('upload');
        Route::get('/remove/{target}/{id}/{item}/{product_id?}', 'WebPageController@filesRemoveReview')->name('remove');
    });
    Route::get('/order_items/{order_id}', 'WebPageController@liveOrderItems')->name('order_items');
    Route::get('/return_order_item/{id}', 'WebPageController@liveOrderReturnItem')->name('return_order_item');
    Route::group(['prefix' => 'refund/item/files', 'as' => 'refund.item.files.'], function () {
        Route::post('/upload/{target}/{id}/{product_id?}', 'WebPageController@filesUploadRefund')->name('upload');
        Route::get('/remove/{target}/{id}/{item}/{product_id?}', 'WebPageController@filesRemoveRefund')->name('remove');
    });

    Route::post('update_product_return/{id}', 'WebPageController@updateProductReturn')->name('update.product_return');
});

Route::get('/live_search/{query}', 'WebPageController@searchLive')->name('search.live');
Route::get('/add-cart/{id}', 'WebPageController@addCart')->name('add_cart');
Route::get('/cart-items', 'WebPageController@cartItems')->name('cart_items');
Route::get('/cart/item/remove/{id}', 'WebPageController@removeProductItem')->name('cart.item.remove');
Route::get('/cart/{product_id}/{attribute_options}/sku_items', 'WebPageController@getCartSkuItem')->name('cart.sku.items');
Route::post('/post-checkout', 'CheckoutController@postCheckout')->name('post.checkout');
Route::post('/get-checkout-totals', 'WebPageController@getTotals')->name('post.totals');
Route::post('/get-payment-summary', 'CheckoutController@getPaymentSummary')->name('post.payment_summary');

Route::post('/get-checkout-cart', 'WebPageController@getCartItems')->name('post.cartitems');

Route::get('/get-is-cart-changed', 'CheckoutController@isCartChanged')->name('is-cart-changed');

Route::post('/cart/apply-coupon', 'CouponCartController@applyCoupon')->name('cart.apply-coupon');
Route::post('/cart/remove-coupon', 'CouponCartController@removeCoupon')->name('cart.remove-coupon');
Route::post('/cart/validate-coupon', 'CouponCartController@validateCoupon')->name('cart.validate-coupon');

Route::get('/coupon/promo-popup', 'CouponCartController@getPromoPopupCoupon')->name('coupon.promo-popup');
Route::post('/checkout/apply-coupon', 'CheckoutController@applyCheckoutCoupon')->name('checkout.apply-coupon');
Route::get('/checkout/cart-totals', 'CheckoutController@getCartTotals')->name('checkout.cart-totals');

Route::post('/site_location_data', 'WebPageController@siteLocationData')->name('post.site_location_data');
Route::get('/site_cart_data', 'WebPageController@siteCartData')->name('site_cart_data');
// Route::get('/site_verify_payment_data', 'WebPageController@verifyPaymentData')->name('site_verify_payment_data');
// Route::get('/pay_cart_update', 'WebPageController@payCartUpdate')->name('pay_cart_update');
Route::get('/paypal', 'WebPageController@getPaymentPaypalStatus')->name('paypal');
Route::get('/stripe', 'WebPageController@getPaymentStripeStatus')->name('stripe');
Route::get('/squadco', 'WebPageController@getPaymentSquadcoStatus')->name('squadco');
// Route::get('get_order_details', 'WebPageController@getOrderDetails')->name('get.order_details');

Route::put('update_profile/{username}', "WebPageController@updateProfile")->name('update.profile');
Route::put('change-password/{username}', 'WebPageController@updateChangePassword')->name('change.password');
Route::put('contact_form/{username}', 'WebPageController@contactForm')->name('contact.form');
Route::post('make_my_account/{email}', 'WebPageController@makeMyAccount')->name('make.my_account');
Route::get('points_to_cash', 'WebPageController@pointToCash')->name('points.convert_cash');
Route::post('redeem_cash', 'WebPageController@postRedeemCash')->name('post.redeem_cash');
Route::get('update_use_reward_points/{status}', 'WebPageController@useRewardPoints')->name('get.use_reward_points');
Route::get('update_use_cash_value/{status}', 'WebPageController@useCashValue')->name('get.use_cash_value');

Route::get('/live/alert_preferences/{user_group_id}/{user_id?}', 'WebPageController@liveAlertPreferences')->name('live.get_alert_preferences');

Route::post('post_product_review', 'WebPageController@postProductReview')->name('post.product_review');
Route::post('post_product_return', 'WebPageController@postProductReturn')->name('post.product_return');

Route::get('/get_sub_menus', 'WebPageController@getMobileSubMenu')->name('get.get_sub_menus');

Route::group(['prefix' => 'live', 'as' => 'live.'], function () {
    Route::get('/submenu/{menu_id}', 'WebPageController@getSubmenu')->name('submenu');

    Route::get('/categories/brands/sort', 'WebPageController@categoriesBrandsSort')->name('categories.brands.search');
    Route::get('/categories/series/sort', 'WebPageController@categoriesSeriesSort')->name('categories.series.search');
    Route::get('/categories/products/sort', 'WebPageController@categoriesProductsSort')->name('categories.products.search');

    Route::get('/brands/categories/sort', 'WebPageController@brandsCategoriesSort')->name('brands.categories.search');
    Route::get('/brands/series/sort', 'WebPageController@brandsSeriesSort')->name('brands.series.search');
    Route::get('/brands/products/sort', 'WebPageController@brandsProductsSort')->name('brands.products.search');

    Route::get('/series/categories/sort', 'WebPageController@seriesCategoriesSort')->name('series.categories.search');
    Route::get('/series/brands/sort', 'WebPageController@seriesBrandsSort')->name('series.brands.search');
    Route::get('/series/products/sort', 'WebPageController@seriesProductsSort')->name('series.products.search');

    Route::get('/product/categories', 'WebPageController@allCategories')->name('categories.all');
    Route::get('/product/brands', 'WebPageController@allBrands')->name('brands.all');
    Route::get('/product/series', 'WebPageController@allSeries')->name('series.all');
    Route::get('/products/all', 'WebPageController@allProducts')->name('products.all');
    Route::get('/products/max_min_price', 'WebPageController@productsMaxMinPrice')->name('products.max_min_price');
    Route::get('/product/images/{id}', 'WebPageController@getProductImages')->name('products.images');

    Route::get('/get/product/attributes', 'WebPageController@getProductAttribute')->name('get.product.attributes');
    Route::get('/products/sort', 'WebPageController@productSort')->name('products.search');
    Route::get('/product/item/{id}', 'WebPageController@getProductItem')->name('products.condition.item');
    Route::get('/product/item/networks/{id}', 'WebPageController@getProductItemNetworks')->name('products.item.networks');
    Route::get('/product/item/images/{id}', 'WebPageController@getProductItemImages')->name('products.item.images');
    Route::get('/product/condition/items/{product_id}/{group_id}/{condition_id}', 'WebPageController@getProductItems')->name('products.condition.items');
    Route::get('/get_location_data/{location_id}', 'WebPageController@getLocationData')->name('get.location_data');
    Route::get('/review_product_items/{review_product_id}', 'WebPageController@getReviewProductItems')->name('get.review_products');
    Route::get('/review_product/{product_item_id}', 'WebPageController@getReviewProduct')->name('get.review_product');
});


//Route::get('/notify-item', [NotifyItemController::class, 'getData'])->name('notify-items-index');
Route::get('/', 'WebPageController@showPage')->name('home');
Route::get('/admin', 'WebPageController@showAdmin')->name('admin_login');
Route::get('/notify-item/{id}', [NotifyItemController::class, 'getData'])->name('notify-items-index');
Route::post('/notify-item/{id}', [NotifyItemController::class, 'notifyByEmail'])->name('notify-items-update');
Route::match(['get', 'post'], '/webhook/meta', [WebhookController::class, 'metaWebhook']);
Route::post('/webhook/twilio', [WebhookController::class, 'twilioWebhook']);
Route::get('/{slug?}/{selector?}', 'WebPageController@showPage')->name('page');
Route::post('/inventory_status/{id}', 'Admin\ProductItemController@inventory_status')->name('inventory_status');
Route::post('/product_inventory_status/{id}', 'Admin\ProductController@product_inventory_status')->name('product_inventory_status');

Route::post('/notify-items', [NotifyItemController::class, 'store'])->name('notify');

Route::post('/subscribe', [NewsletterController::class, 'subscribe'])->name('subscribe');
Route::post('/verify-otp', [NewsletterController::class, 'verifyOtpSession'])->name('verify_otp');
Route::post('/is-user-replied', [NewsletterController::class, 'isUserRepliedtoMessage'])->name('is_user_replied');

Route::get('/user_billings/{user_id?}/{type?}', 'Admin\UserBillingController@index')->name('user_billings.index');
Route::post('/store/user_billings/{user_id?}', 'Admin\UserBillingController@store')->name('user_billings.store');
Route::get('/edit/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@edit')->name('user_billings.edit');
Route::post('/update/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@update')->name('user_billings.update');
Route::delete('/delete/user_billings/{id}/{user_id?}', 'Admin\UserBillingController@destroy')->name('user_billings.destroy');
Route::get('/user_shippings/{user_id?}/{type?}', 'Admin\UserShippingController@index')->name('user_shippings.index');
Route::post('/store/user_shippings/{user_id?}', 'Admin\UserShippingController@store')->name('user_shippings.store');
Route::get('/edit/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@edit')->name('user_shippings.edit');
Route::post('/update/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@update')->name('user_shippings.update');
Route::delete('/delete/user_shippings/{id}/{user_id?}', 'Admin\UserShippingController@destroy')->name('user_shippings.destroy');
Route::post('/promo-apply', 'WebPageController@PromoCode')->name('promo-apply');
Route::post('/webhook/btcpay',  [WebPageController::class, 'handleWebhook']);
Route::post('/cart/apply-promo', 'WebPageController@applyPromo')->name('cart.applyPromo');
Route::post('/promo-register', [WebPageController::class, 'sendDIscount'])->name('promo.register');
Route::post('/reviews/{id}/helpful', [WebPageController::class, 'helpFulCount'])->name('helpful');
