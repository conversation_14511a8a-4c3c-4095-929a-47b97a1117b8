<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSentDiscountsForeignKey extends Migration
{
    public function up()
    {
        Schema::table('sent_discounts', function (Blueprint $table) {
            // First, drop the old foreign key

            // Now add the new foreign key referencing the coupons table
            $table->foreign('promo_id')
                ->references('id')
                ->on('coupons')
                ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('sent_discounts', function (Blueprint $table) {
            // Rollback to the old reference if needed
            $table->dropForeign(['promo_id']);
            $table->foreign('promo_id')
                ->references('id')
                ->on('promos')
                ->onDelete('cascade');
        });
    }
}
