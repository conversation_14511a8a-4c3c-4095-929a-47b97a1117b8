<!-- Creator     : groff version 1.23.0 -->
<!-- CreationDate: Fri Apr 12 13:51:22 2024 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>WEBPINFO</title>

</head>
<body>

<h1 align="center">WEBPINFO</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#INPUT">INPUT</a><br>
<a href="#BUGS">BUGS</a><br>
<a href="#EXAMPLES">EXAMPLES</a><br>
<a href="#AUTHORS">AUTHORS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em">webpinfo - print
out the chunk level structure of WebP files along with basic
integrity checks.</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em"><b>webpinfo</b>
<i>OPTIONS INPUT</i> <b><br>
webpinfo [-h|-help|-H|-longhelp]</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em">This manual page
documents the <b>webpinfo</b> command.</p>

<p style="margin-left:1%; margin-top: 1em"><b>webpinfo</b>
can be used to print out the chunk level structure and
bitstream header information of WebP files. It can also
check if the files are of valid WebP format.</p>

<h2>OPTIONS
<a name="OPTIONS"></a>
</h2>



<p style="margin-left:1%; margin-top: 1em"><b>-version</b></p>

<p style="margin-left:1%;">Print the version number (as
major.minor.revision) and exit.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="1%"></td>


<p><b>-quiet</b></p></td>
<td width="4%">


<p>Do not show chunk parsing information.</p></td>
<td width="95%">
</td></tr>
<tr valign="top" align="left">
<td width="1%"></td>


<p><b>-diag</b></p></td>
<td width="4%">


<p>Show parsing error diagnosis.</p></td>
<td width="95%">
</td></tr>
</table>

<p style="margin-left:1%;"><b>-summary</b></p>

<p style="margin-left:1%;">Show chunk stats summary.</p>

<p style="margin-left:1%;"><b>-bitstream_info</b></p>

<p style="margin-left:1%;">Parse bitstream header.</p>

<p style="margin-left:1%;"><b>-h, -help</b></p>

<p style="margin-left:1%;">A short usage summary.</p>

<p style="margin-left:1%;"><b>-H, -longhelp</b></p>

<p style="margin-left:1%;">Detailed usage instructions.</p>

<h2>INPUT
<a name="INPUT"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em">Input files in
WebP format. Input files must come last, following options
(if any). There can be multiple input files.</p>

<h2>BUGS
<a name="BUGS"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em">Please report
all bugs to the issue tracker:
https://bugs.chromium.org/p/webp <br>
Patches welcome! See this page to get started:
https://www.webmproject.org/code/contribute/submitting-patches/</p>

<h2>EXAMPLES
<a name="EXAMPLES"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em">webpinfo -h <br>
webpinfo -diag -summary input_file.webp <br>
webpinfo -bitstream_info input_file_1.webp input_file_2.webp
<br>
webpinfo *.webp</p>

<h2>AUTHORS
<a name="AUTHORS"></a>
</h2>


<p style="margin-left:1%; margin-top: 1em"><b>webpinfo</b>
is a part of libwebp and was written by the WebP team. <br>
The latest source tree is available at
https://chromium.googlesource.com/webm/libwebp</p>

<p style="margin-left:1%; margin-top: 1em">This manual page
was written by Hui Su &lt;<EMAIL>&gt;, for the
Debian project (and may be used by others).</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:1%; margin-top: 1em"><b>webpmux</b>(1)
<br>
Please refer to https://developers.google.com/speed/webp/
for additional information.</p>
<hr>
</body>
</html>
