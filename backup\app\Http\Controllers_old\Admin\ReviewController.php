<?php

namespace App\Http\Controllers\Admin;

use Validator;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Review;
use App\Models\Reward;
use App\Mail\SiteEmail;
use App\Models\Product;
// use App\Mail\EmailReview;
use App\Models\Tracking;
use Illuminate\Http\Request;
use App\Models\EmailTemplate;
use App\Models\PointSystem;
use App\Models\EmailTemplateGroup;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class ReviewController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Reviews';
        $items = Review::where('parent_review_id', null)->paginate($this->itemPerPage);

        foreach($items as $value){
            $value->review_images = unserialize($value->review_images);
            $user = User::find($value->user_id);
            $product = Product::find($value->product_id);
            $value->details = 'Product: '. $product->title.'<br />Review By: '.$user->first_name.''.$user->last_name;
            $value->status = $this->reviewStatus($value->status);
            $value->date_time = Carbon::parse($value->created_at)->format('M, d Y g:i A');
            // dd($value);
        }

        $sl = SLGenerator($items);
        return view('admin.review.index', compact('sl', 'items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        abort(404);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        abort(404);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Review  $review
     * @return \Illuminate\Http\Response
     */
    public function show(Review $review)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Review  $review
     * @return \Illuminate\Http\Response
     */
    public function edit(Review $review)
    {
        $admin_page_title = 'Update Review';
        $review->reviewStatus = $this->reviewStatus();

        $review->review_images = unserialize($review->review_images);
        $user = User::find($review->user_id);
        $user_country=$user->country;
        $review_points_country_wise=PointSystem::where('country',$user_country)->first();
        if($review_points_country_wise == null){
            $comment_points=0;
            $photo_points=0;
            $video_points=0;
        }
        else{
            $comment_points=0;
            $photo_points=0;
            $video_points=0;
            $reviewImages = $review->review_images ?? '';
            if (is_array($reviewImages)) {
                foreach ($reviewImages as $file) {
                    $fileName = basename($file->review_images); // Extract the file name
                    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION)); // Get the file extension
                    
                    // Check if the file is an image or video
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'])) {
                        // $comment_points=$review_points_country_wise->comment_points;
                        $photo_points=$review_points_country_wise->photo_points;
                        // $video_points=$review_points_country_wise->video_points;
                    } elseif (in_array($extension, ['mp4', 'mov', 'avi', 'mkv', 'webm', 'flv'])) {
                        $video_points=$review_points_country_wise->video_points;

                    } 
                }
            }
            if($review->message != ""){
                $comment_points=$review_points_country_wise->comment_points;
            }
        }
   
        $product = Product::find($review->product_id);
        $review->details = 'Product: ' . $product->title . '<br />Review By: ' . $user->first_name . '' . $user->last_name;
        $review->date_time = Carbon::parse($review->created_at)->format('M, d Y g:i A');

        $reward = Reward::where('review_id', $review->id)->first();

        $review->points = empty($reward) ? $this->review_rewards_points : $reward->points;
        $reply_review = Review::where('parent_review_id', $review->id)->first();
        $review->reply_review = $reply_review->message ?? '';
        $review->email_templates = EmailTemplate::where('status', true)->get();
        $review->email_constants = $this->email_constants();
        $send_sms_emails = Tracking::where('review_id', $review->id)->get();
        foreach($send_sms_emails as $value){
            $value->date_time = Carbon::parse($value->created_at)->format('F j, Y g.iA');
        }
        $review->send_sms_emails = $send_sms_emails;
        $review->email_template_groups = EmailTemplateGroup::where('system_group', 'review')->get();
        $item = $review;

        return view('admin.review.edit', compact('item', 'admin_page_title','comment_points','photo_points','video_points'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Review  $review
     * @return \Illuminate\Http\Response
     */
    // public function update(Request $request, Review $review)
    // {
    //     $this->validate($request, [
    //         'status' => 'required|numeric',
    //         'rating' => 'required|numeric|between:1,5',
    //         'message' => 'required|string|max:255',
    //         'points' => 'required|numeric|between:1,255',
    //         'reply_review' => 'nullable|string',
    //     ]);

    //     if($request->action == 'send_email_sms'){
    //         $this->validate($request, [
    //             'subject' => 'required|string',
    //             'pre_header' => 'required|string',
    //             'email_body' => 'required|string',
    //             'sms_body' => 'nullable|string',
    //         ]);

    //         $user = User::find($review->user_id);

    //         $mail_data = (object)[
    //             'user' => $user,
    //             'review' => $review,
    //             'subject' => $request->subject,
    //             'pre_header' => $request->pre_header,
    //             'email_body' => $request->email_body,
    //             'sms_status' => $request->sms_status ? true : false,
    //             'sms_body' => $request->sms_body,
    //         ];
    //         $mail_data->subject = $this->sendEmailData($mail_data)->subject;
    //         $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
    //         $mail_data->content = $this->sendEmailData($mail_data)->content;
    //         $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

    //         $lead_source = (object)[
    //             'admin_id' => auth()->user()->id,
    //             'admin_email' => auth()->user()->email,
    //             'admin_phone' => auth()->user()->tel_cell_country_code. auth()->user()->tel_cell,
    //             'source' => 'admin',
    //         ];

    //         $tracking = new Tracking();
    //         $tracking->review_id = $review->id;
    //         $tracking->content = serialize($this->sendEmailData($mail_data));
    //         $tracking->from_email = config('mail.from.address');
    //         $tracking->to_email = $user->email;
    //         $tracking->subject = $request->subject;
    //         $tracking->lead_source = serialize($lead_source);
    //         $tracking->ip = $this->getClientIpAddress();
    //         $tracking->save();

    //         Mail::to($user->email)->send(new SiteEmail($mail_data));

    //         if ($request->sms_status && $user->tel_cell) {
    //             $sms_tracking = Tracking::findOrFail($tracking->id);
    //             $sms_tracking->sms_content = serialize($mail_data->sms_content);
    //             $sms_tracking->from_phone = config('app.twilio_number');
    //             $sms_tracking->to_phone = $user->tel_cell;
    //             // $sms_tracking->to_phone = $user->tel_cell;
    //             $sms_tracking->update();
    //         }

    //         return back()->with('success', 'Review update Successful.');
    //     }

    //     $review_images = unserialize($review->review_images);
    //     foreach($review_images as $value){
    //         $value->default = 0;
    //         if($value->id == $request->default_image){
    //             $value->default = 1;
    //         }
    //     }

    //     $review->approve_user_id = auth()->user()->id;
    //     $review->status = $request->status;
    //     $review->rating = $request->rating;
    //     $review->message = $request->message;
    //     $review->review_images = serialize($review_images);
    //     $review->update();

    //     $reply_review = Review::where('parent_review_id', $review->id)->first();
    //     if(empty($reply_review) && $request->reply_review){
    //         $reply_review = new Review;
    //         $reply_review->user_id = $review->user_id;
    //         $reply_review->product_id = $review->product_id;
    //         $reply_review->product_item_id = $review->product_item_id;
    //         $reply_review->parent_review_id = $review->id;
    //         $reply_review->message = $request->reply_review;
    //         $reply_review->ip = $this->getClientIpAddress();
    //         $reply_review->status = 2;
    //         // dd($reply_review);
    //         $reply_review->save();
    //     }
    //     if ($reply_review && $request->reply_review) {
    //         $reply_review = Review::findOrFail($reply_review->id);
    //         $reply_review->user_id = $review->user_id;
    //         $reply_review->product_id = $review->product_id;
    //         $reply_review->product_item_id = $review->product_item_id;
    //         $reply_review->parent_review_id = $review->id;
    //         $reply_review->message = $request->reply_review;
    //         $reply_review->ip = $this->getClientIpAddress();
    //         $reply_review->status = 2;
    //         $reply_review->update();
    //     }

    //     $reward = Reward::where('review_id', $review->id)->first();
    //     if($reward){
    //         $reward = Reward::findOrFail($reward->id);
    //         // if ($review->status == 2) {
    //         //     $reward->review_id = $review->id;
    //         //     $reward->user_id = $review->id;
    //         //     $reward->points = $request->points;
    //         //     $reward->update();
    //         // }else{
    //         //     $reward->delete();
    //         // }
    //         if ($review->status == 1) {
    //             $reward->delete();
    //         }
    //     }else{
    //         // $reward = new Reward;
    //         // if ($review->status == 2) {
    //         //     $reward->review_id = $review->id;
    //         //     $reward->user_id = $review->id;
    //         //     $reward->points = $request->points;
    //         //     $reward->save();
    //         // }
    //     }

    //     return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.reviews.index'))->with('success', 'Review update Successful.');
    // }
    public function update(Request $request, Review $review)
    {
        $this->validate($request, [
            'status' => 'required|numeric',
            'rating' => 'required|numeric|between:1,5',
            'message' => 'required|string|max:255',
            'points' => 'nullable|numeric|between:1,255',
            'reply_review' => 'nullable|string',
        ]);
        // dd($request->all());
        if ($request->action == 'send_email_sms') {
            $this->validate($request, [
                'subject' => 'required|string',
                'pre_header' => 'required|string',
                'email_body' => 'required|string',
                'sms_body' => 'nullable|string',
            ]);
            $user = User::find($review->user_id);
            $mail_data = (object)[
                'user' => $user,
                'review' => $review,
                'subject' => $request->subject,
                'pre_header' => $request->pre_header,
                'email_body' => $request->email_body,
                'sms_status' => $request->sms_status ? true : false,
                'sms_body' => $request->sms_body,
            ];
            $mail_data->subject = $this->sendEmailData($mail_data)->subject;
            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
            $mail_data->content = $this->sendEmailData($mail_data)->content;
            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
            $lead_source = (object)[
                'admin_id' => auth()->user()->id,
                'admin_email' => auth()->user()->email,
                'admin_phone' => auth()->user()->tel_cell_country_code . auth()->user()->tel_cell,
                'source' => 'admin',
            ];
            $tracking = new Tracking();
            $tracking->review_id = $review->id;
            $tracking->content = serialize($this->sendEmailData($mail_data));
            $tracking->from_email = config('mail.from.address');
            $tracking->to_email = $user->email;
            $tracking->subject = $request->subject;
            $tracking->lead_source = serialize($lead_source);
            $tracking->ip = $this->getClientIpAddress();
            $tracking->save();
            Mail::to($user->email)->send(new SiteEmail($mail_data));
            if ($request->sms_status && $user->tel_cell) {
                $sms_tracking = Tracking::findOrFail($tracking->id);
                $sms_tracking->sms_content = serialize($mail_data->sms_content);
                $sms_tracking->from_phone = config('app.twilio_number');
                $sms_tracking->to_phone = $user->tel_cell;
                // $sms_tracking->to_phone = $user->tel_cell;
                $sms_tracking->update();
            }
            return back()->with('success', 'Review update Successful.');
        }
        // $review_images = $review->review_images ? unserialize($review->review_images) : [];
        $review_images = $review->review_images ? @unserialize($review->review_images) : [];
        if (!is_array($review_images)) {
            $review_images = [];
        }
        foreach ($review_images as &$value) {
            if (is_object($value) && property_exists($value, 'id')) {
                $value->default = ($value->id == $request->default_image) ? 1 : 0;
            } else {
                // Handle or log unexpected data format
            }
        }
        // dd('Review Images Updated', $review_images);
        $review->approve_user_id = auth()->user()->id;
        $review->status = $request->status;
        $review->rating = $request->rating;
        $review->message = $request->message;
        $review->review_images = serialize($review_images);
        // dd($review);
        $review->update();
        $reply_review = Review::where('parent_review_id', $review->id)->first();
        if (empty($reply_review) && $request->reply_review) {
            $reply_review = new Review;
            $reply_review->user_id = $review->user_id;
            $reply_review->product_id = $review->product_id;
            $reply_review->product_item_id = $review->product_item_id;
            $reply_review->parent_review_id = $review->id;
            $reply_review->message = $request->reply_review;
            $reply_review->ip = $this->getClientIpAddress();
            $reply_review->status = 2;
            // dd($reply_review);
            $reply_review->save();
        }
        if ($reply_review && $request->reply_review) {
            $reply_review = Review::findOrFail($reply_review->id);
            $reply_review->user_id = $review->user_id;
            $reply_review->product_id = $review->product_id;
            $reply_review->product_item_id = $review->product_item_id;
            $reply_review->parent_review_id = $review->id;
            $reply_review->message = $request->reply_review;
            $reply_review->ip = $this->getClientIpAddress();
            $reply_review->status = 2;
            $reply_review->update();
        }
        $reward = Reward::where('review_id', $review->id)->first();
        if ($reward) {
            $reward = Reward::findOrFail($reward->id);
            // if ($review->status == 2) {
            //     $reward->review_id = $review->id;
            //     $reward->user_id = $review->id;
            //     $reward->points = $request->points;
            //     $reward->update();
            // }else{
            //     $reward->delete();
            // }
            if ($review->status == 1) {
                $reward->delete();
            }
        } else {
            // $reward = new Reward;
            // if ($review->status == 2) {
            //     $reward->review_id = $review->id;
            //     $reward->user_id = $review->id;
            //     $reward->points = $request->points;
            //     $reward->save();
            // }
        }
        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.reviews.index'))->with('success', 'Review update Successful.');
    }










    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Review  $review
     * @return \Illuminate\Http\Response
     */
    public function destroy(Review $review)
    {
        abort(404);
    }
    // public function sendReward($id, Request $request){
    //     $validator = Validator::make($request->all(), [
    //         'reward_subject' => 'required|string',
    //         'reward_pre_header' => 'required|string',
    //         'reward_email_body' => 'required|string',
    //         'reward_sms_body' => 'nullable|string',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json([
    //             'messages' => $validator->messages(),
    //         ], 400);
    //     }
    //     $reward = Reward::where('review_id', $id)->first();
    //     $review = Review::find($id);
    //     // dd($request->all());
    //     if($reward){
    //         $reward = Reward::findOrFail($id);
    //         if ($review->status == 2) {
    //             $reward->review_id = $id;
    //             $reward->user_id = $review->user_id;
    //             $reward->points = $request->reward_points;
    //             $reward->update();
    //         }else{
    //             $reward->delete();
    //         }
    //     }else{
    //         $reward = new Reward;
    //         if ($review->status == 2) {
    //             $reward->review_id = $id;
    //             $reward->user_id = $review->user_id;
    //             $reward->points = $request->reward_points;
    //             $reward->save();
    //         }
    //     }

    //     $user = User::find($review->user_id);

    //     $mail_data = (object)[
    //         'user' => $user,
    //         'review' => $review,
    //         'subject' => $request->reward_subject,
    //         'pre_header' => $request->reward_pre_header,
    //         'email_body' => $request->reward_email_body,
    //         'sms_status' => $request->reward_sms_status ? true : false,
    //         'sms_body' => $request->reward_sms_body,
    //     ];
    //     $mail_data->subject = $this->sendEmailData($mail_data)->subject;
    //     $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
    //     $mail_data->content = $this->sendEmailData($mail_data)->content;
    //     $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

    //     $lead_source = (object)[
    //         'admin_id' => auth()->user()->id,
    //         'admin_email' => auth()->user()->email,
    //         'admin_phone' => auth()->user()->tel_cell_country_code . auth()->user()->tel_cell,
    //         'source' => 'admin',
    //     ];

    //     $tracking = new Tracking();
    //     $tracking->review_id = $review->id;
    //     $tracking->reward_id = $reward->id;
    //     $tracking->content = serialize($this->sendEmailData($mail_data));
    //     $tracking->from_email = config('mail.from.address');
    //     $tracking->to_email = $user->email;
    //     $tracking->subject = $request->reward_subject;
    //     $tracking->lead_source = serialize($lead_source);
    //     $tracking->ip = $this->getClientIpAddress();
    //     $tracking->save();

    //     Mail::to($user->email)->send(new SiteEmail($mail_data));

    //     // if ($request->reward_sms_status && $user->tel_cell)
    //     if ($request->reward_sms_status && $user->tel_cell)
    //     {
    //         $sms_tracking = Tracking::findOrFail($tracking->id);
    //         $sms_tracking->sms_content = serialize($mail_data->sms_content);
    //         $sms_tracking->from_phone = config('app.twilio_number');
    //         $sms_tracking->to_phone = $user->tel_cell;
    //         // $sms_tracking->to_phone = $user->tel_cell;
    //         $sms_tracking->update();
    //     }

    //     // dd($id, $request->all());

    //     return response()->json([
    //         'message' => 'Reward send successfully',
    //     ], 200);
    // }
    public function sendReward($id, Request $request)
    {
        // dd($request->all());
        $validator = Validator::make($request->all(), [
            'reward_subject' => 'required|string',
            'reward_pre_header' => 'required|string',
            'reward_email_body' => 'required|string',
            'reward_sms_body' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $reward = Reward::where('review_id', $id)->first();
        $review = Review::findOrFail($id);
        // Ensure the user is a customer with an account
        $user = User::find($review->user_id);
        if (!$user) {
            return response()->json(['message' => 'Only customers with accounts can earn points.'], 403);
        }
        // Calculate reward points based on review content
        $points = 0;
        if (!empty($review->message)) {
            $points += (int) $request->input('text_points');
        } else {
            Log::info('Review message is empty', ['review_id' => $review->id]);
        }
        if (!empty($review->review_images)) {
            $points += (int) $request->input('image_points');
        }
        if (!empty($review->review_images)) {
            $points += (int) $request->input('video_points');
        }
        // Update or create the reward
        $reward = Reward::updateOrCreate(
            ['review_id' => $id],
            [
                'user_id' => $review->user_id,
                'points' => $points,
            ]
        );
        // if ($reward) {
        //     $reward = Reward::findOrFail($id);
        //     if ($review->status == 2) {
        //         $reward->review_id = $id;
        //         $reward->user_id = $review->user_id;
        //         $reward->points = $request->reward_points;
        //         $reward->update();
        //     } else {
        //         $reward->delete();
        //     }
        // } else {
        //     $reward = new Reward;
        //     if ($review->status == 2) {
        //         $reward->review_id = $id;
        //         $reward->user_id = $review->user_id;
        //         $reward->points = $request->reward_points;
        //         $reward->save();
        //     }
        // }
        $user = User::find($review->user_id);
        $mail_data = (object)[
            'user' => $user,
            'review' => $review,
            'subject' => $request->reward_subject,
            'pre_header' => $request->reward_pre_header,
            'email_body' => $request->reward_email_body,
            'sms_status' => $request->reward_sms_status ? true : false,
            'sms_body' => $request->reward_sms_body,
        ];
        $mail_data->subject = $this->sendEmailData($mail_data)->subject;
        $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
        $mail_data->content = $this->sendEmailData($mail_data)->content;
        $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;
        $lead_source = (object)[
            'admin_id' => auth()->user()->id,
            'admin_email' => auth()->user()->email,
            'admin_phone' => auth()->user()->tel_cell_country_code . auth()->user()->tel_cell,
            'source' => 'admin',
        ];
        $tracking = new Tracking();
        $tracking->review_id = $review->id;
        $tracking->reward_id = $reward->id;
        $tracking->content = serialize($this->sendEmailData($mail_data));
        $tracking->from_email = config('mail.from.address');
        $tracking->to_email = $user->email;
        $tracking->subject = $request->reward_subject;
        $tracking->lead_source = serialize($lead_source);
        $tracking->ip = $this->getClientIpAddress();
        $tracking->save();
        Mail::to($user->email)->send(new SiteEmail($mail_data));
        // if ($request->reward_sms_status && $user->tel_cell)
        if ($request->reward_sms_status && $user->tel_cell) {
            $sms_tracking = Tracking::findOrFail($tracking->id);
            $sms_tracking->sms_content = serialize($mail_data->sms_content);
            $sms_tracking->from_phone = config('app.twilio_number');
            $sms_tracking->to_phone = $user->tel_cell;
            // $sms_tracking->to_phone = $user->tel_cell;
            $sms_tracking->update();
        }
        // dd($id, $request->all());
        return response()->json([
            'message' => 'Reward send successfully',
        ], 200);
    }
    public function getEmailSmsDetails($id, $type){
        if($type == 'email'){
            $item = Tracking::find($id);
            $item->lead_source = unserialize($item->lead_source);
            $item->content = unserialize($item->content);
        }

        if ($type == 'sms') {
            $item = Tracking::find($id);
            $item->lead_source = unserialize($item->lead_source);
            $item->content = unserialize($item->sms_content);
        }

        return response()->json($item, 200);
    }
}
