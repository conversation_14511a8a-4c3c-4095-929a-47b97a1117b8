<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SlideCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Validator;
use App\Models\Slide;

class SlideCategoryController extends Controller
{
    private $folderPath = 'slideshow/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Slideshows';

        $items = DB::table('slide_categories')
        ->orderBy('slide_categories.id', 'desc')
        ->paginate($this->itemPerPage);

        foreach($items as $item){
             $item->have_slide = Slide::where('Slide_category_id', $item->id)->exists();
        }

        $sl = SLGenerator($items);

        return view('admin.slideshow.index', compact('sl','items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'remark' => 'nullable|string|max:150',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' =>'error',
                'messages' =>$validator->messages(),
            ], 200);
        }

        $item = new SlideCategory();
        $item->title = $request->get('title');
        $item->remark = $request->get('remark');
        $item->ordering = $request->get('ordering')?? 0;
        $item->status = $request->get('status') ?? 0;
        $item->save();

        $items = DB::table('slide_categories')
        ->orderBy('slide_categories.id', 'desc')
        ->paginate($this->itemPerPage);

        foreach ($items as $item) {
            $item->have_slide = Slide::where('Slide_category_id', $item->id)->exists();
        }

        $sl = SLGenerator($items);

        return view('admin.jquery_live.slideshow', compact('items', 'sl'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\SlideCategory  $slideCategory
     * @return \Illuminate\Http\Response
     */
    public function show(SlideCategory $slideCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\SlideCategory  $slideCategory
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $item = SlideCategory::findOrFail($request->id);
        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SlideCategory  $slideCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SlideCategory $slideCategory)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:50',
            'remark' => 'nullable|string|max:150',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'messages' => $validator->messages(),
            ], 200);
        }

        $item = SlideCategory::findOrFail($request->id);
        $item->title = $request->get('title');
        $item->remark = $request->get('remark');
        $item->ordering = $request->get('ordering') ?? 0;
        $item->status = $request->get('status') ?? 0;
        $item->update();

        $items = DB::table('slide_categories')
        ->orderBy('slide_categories.id', 'desc')
        ->paginate($this->itemPerPage);

        foreach ($items as $item) {
            $item->have_slide = Slide::where('Slide_category_id', $item->id)->exists();
        }

        $sl = SLGenerator($items);

        return view('admin.jquery_live.slideshow', compact('items', 'sl'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\SlideCategory  $slideCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $slides = DB::table('slides')
        ->where('slide_category_id', $id)
        ->get();

        foreach($slides as $slide){
            $slide = Slide::findOrFail($slide->id);
            $this->deleteFile($this->folderPath, $slide->slide_image);
            $slide->delete();
        }

        $item = SlideCategory::findOrFail($id);
        $item->delete();

        $items = DB::table('slide_categories')
        ->orderBy('slide_categories.id', 'desc')
        ->paginate($this->itemPerPage);

        foreach ($items as $item) {
            $item->have_slide = Slide::where('Slide_category_id', $item->id)->exists();
        }

        $sl = SLGenerator($items);

        return view('admin.jquery_live.slideshow', compact('items', 'sl'));
    }
}
