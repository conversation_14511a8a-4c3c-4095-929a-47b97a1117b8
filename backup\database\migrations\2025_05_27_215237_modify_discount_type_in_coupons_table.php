<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyDiscountTypeInCouponsTable extends Migration
{
    public function up()
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->string('discount_type', 20)->change();
        });
    }

    public function down()
    {
        Schema::table('coupons', function (Blueprint $table) {
            // Change back to original length or type, adjust if needed
            $table->string('discount_type', 10)->change();
        });
    }
}

