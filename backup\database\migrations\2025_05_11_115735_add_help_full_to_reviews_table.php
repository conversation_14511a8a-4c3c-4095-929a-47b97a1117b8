<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddHelpFullToReviewsTable extends Migration
{
    public function up(): void
    {
        if (!Schema::hasColumn('reviews', 'help_full')) {
            Schema::table('reviews', function (Blueprint $table) {
                $table->unsignedInteger('help_full')->default(0)->after('rating');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('reviews', 'help_full')) {
            Schema::table('reviews', function (Blueprint $table) {
                $table->dropColumn('help_full');
            });
        }
    }
}
