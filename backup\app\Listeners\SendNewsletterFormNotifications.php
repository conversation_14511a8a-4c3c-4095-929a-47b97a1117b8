<?php

namespace App\Listeners;

use App\Events\NewsletterFormSubmitted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendNewsletterFormNotifications
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\NewsletterFormSubmitted  $event
     * @return void
     */
    public function handle(NewsletterFormSubmitted $event)
    {
        //
    }
}
