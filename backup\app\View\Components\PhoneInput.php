<?php

namespace App\View\Components;

use Illuminate\View\Component;

class PhoneInput extends Component
{
  
    public  $name;

  
    public  $id;

   
    public  $value;
    public $style;

  
    public function __construct($id = null, $name = 'tel_cell',  $value = '',$style='')
    {
        $this->name = $name;
        $this->id = $id ?? $name;
        $this->value = $value;
        $this->style=$style;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.phone-input');
    }
}
