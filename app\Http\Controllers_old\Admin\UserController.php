<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\RedeemCashReward;
use App\Models\CashReward;
use App\Models\Tracking;
use App\Models\User;
use App\Models\UserBilling;
use App\Models\Reward;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\UserGroup;
use App\Models\UserGroupMap;
use App\Models\UserPermission;
use App\Models\UserShipping;
use Illuminate\Support\Collection;
use Session;
use Illuminate\Support\Facades\Hash;
// use Illuminate\Support\Str;
use Validator;

class UserController extends Controller
{
    private $folderPath = 'users/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $user_group_id = null)
    {
        $admin_page_title = 'Users';
        $user_groups = $this->userGroups();
        if ($request->action == 'search') {
            $user_group_id = $request->input('user_group_id');
            if (empty($user_group_id)) {
                $this->validate($request, [
                    'query' => 'required|min:3',
                ]);
            }
            $keyword = $request->input('query');
            $users = $this->getUsersData($user_group_id, $keyword, true);

            $collection  = new Collection($users);
            if ($user_group_id) {
                $items =  $collection->where('access', '==', true)->where('search_group', '==', true)->paginate($this->itemPerPage)->setPath('');
            } else {
                $items =  $collection->where('access', '==', true)->paginate($this->itemPerPage)->setPath('');
            }
            $pagination =  $items->appends(array(
                'query' => $request->input('query'),
                'user_group_id' => $user_group_id,
            ));
        } else {
            $users = $this->getUsersData();
            $collection  = new Collection($users);
            $items =  $collection->where('access', '==', true)->paginate($this->itemPerPage)->setPath('');
        }

        if ($request->action == 'clear') {
            return redirect()->route('admin.users.index');
        }

        $sl = SLGenerator($items);

        if ($request->view == 'html') {
            return view('admin.jquery_live.users', compact('items', 'sl'));
        }

        return view('admin.user.index', compact('sl', 'items', 'user_groups', 'admin_page_title', 'user_group_id'));
    }

    public function getUsersData($user_group_id = null, $keyword = null, $search = false)
    {
        if ($search) {
            if (auth()->user()->access('master_login')) {
                $users = User::when($keyword, function ($q) use ($keyword) {
                    return $q->where('users.first_name', 'like', '%' . $keyword . '%');
                })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.last_name', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.tel_cell', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.username', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.email', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.pre_address', 'like', '%' . $keyword . '%');
                    })
                    ->orWhereRaw("concat(users.first_name, ' ', users.last_name) like '%$keyword%' ")
                    ->orderBy('users.id', 'desc')
                    ->get();
            } else {
                $users = User::when($keyword, function ($q) use ($keyword) {
                    return $q->where('users.first_name', 'like', '%' . $keyword . '%');
                })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.last_name', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.tel_cell', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.username', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.email', 'like', '%' . $keyword . '%');
                    })
                    ->when($keyword, function ($q) use ($keyword) {
                        return $q->orWhere('users.pre_address', 'like', '%' . $keyword . '%');
                    })
                    ->orWhereRaw("concat(users.first_name, ' ', users.last_name) like '%$keyword%' ")
                    ->where('users.pre_address', 'like', '%' . $keyword . '%')
                    ->orderBy('users.id', 'desc')
                    ->get();
            }
        } else {
            if (auth()->user()->access('master_login')) {
                $users = DB::table('users')
                ->orderBy('users.id', 'desc')
                ->get();
            } else {
                $users = DB::table('users')
                ->orderBy('users.id', 'desc')
                ->where([
                    ['users.id', '!=', Auth()->user()->id]
                ])
                    ->get();
            }
        }

        foreach ($users as $user) {
            $users_group_map = UserGroupMap::where('user_id', $user->id)->get();
            $user->user_group_roles = $this->getCommonUserGroups($users_group_map);
            $user->user_group_title = $this->getUserGroupTitles($users_group_map);
            $user->access =  $this->getUserAccess($user->user_group_roles);
            $user->delete_access =  $this->getDeleteAccess($user->id);
            $user->search_group =  in_array($user_group_id, $user->user_group_roles);
        }

        return $users;
    }

    public function getUsersGroup($user_group_id)
    {
        $user_group = UserGroup::find($user_group_id);
        $admin_page_title = ucfirst($user_group->title);
        $user_groups = $this->userGroups();
        $users = $this->getUsersData();

        foreach ($users as $user) {
            if ($user_group) {
                $user->search_group =  in_array($user_group_id, $user->user_group_roles);
            }
        }

        $collection  = new Collection($users);
        $items =  $collection->where('access', '==', true)
        ->where('search_group', '==', true)
        ->paginate($this->itemPerPage)->setPath('');
        $sl = SLGenerator($items);

        return view('admin.user.index', compact('sl', 'items', 'user_groups', 'admin_page_title', 'user_group_id'));
    }

    public function getDeleteAccess($id)
    {
        $delete_access_status = true;
        return $delete_access_status;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create User';
        $user_groups = $this->userGroups();
        $countries = $this->countries();
        $image_info = Session::get('user_image');
        $user_shippings = collect(Session::get('user_shippings'));
        $user_billings = collect(Session::get('user_billings'));

        // $alert_preference = AlertPreference::where('access_label_id', )->get()
        // $user_permissions = UserPermission::where('user_group_id',3)->get();
        // foreach($user_permissions as $value){
        //     $value->user_permission_component = UserAccessKey::find($value->user_permission_components_id);
        //     // dd($value->user_permission_component);
        // }
        // dd($user_permissions);

        return view('admin.user.create', compact('user_groups', 'image_info', 'countries', 'admin_page_title', 'user_shippings', 'user_billings'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $company_name = $request->company_name_area ? 'required' : 'nullable';
        $username = $request->user_credential_area ? 'required' : 'nullable';
        $password = $request->user_credential_area ? 'required' : 'nullable';
        $country = $request->contact_info_area ? 'required' : 'nullable';
        $postal_code = $request->contact_info_area ? 'required' : 'nullable';
        $city = $request->contact_info_area ? 'required' : 'nullable';
        $state = $request->contact_info_area ? 'required' : 'nullable';
        $address = $request->contact_info_area ? 'required' : 'nullable';
        $this->validate($request, [
            'user_groups' => 'required|exists:user_groups,id',
            // 'user_groups.*' => 'required|exists:user_groups,id',
            'username' => $username.'|string|max:255|unique:users|alpha_dash',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'tel_cell' => 'required|unique:users',
            'tel_cell_country_code' => 'nullable|string',
            'city' => $city.'|string',
            'company_name' => $company_name.'|string',
            'state' => $state.'|string',
            'country' => $country.'|string',
            'postal_code' => $postal_code.'|integer',
            'address' => $address.'|string',
            'address_2' => 'nullable|string',
            'password' => $password.'|string|min:8',
        ]);

        $get_ip = $this->getClientIpAddress();
        // $user_num = abs(crc32(uniqid()));
        $tel_cell_country_code = null;
        if ($request->tel_cell) {
            $tel_cell_country_code = '+' . $request->tel_cell_country_code;
        }

        $user = new User();
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->username = $request->get('username');
        // $user->username = Str::slug($request->get('first_name'), '_') . $user_num;
        $user->email = $request->get('email');
        $user->state = $request->get('state');
        $user->city = $request->get('city');
        $user->postal_code = $request->get('postal_code');
        $user->country = $request->get('country');
        $user->company_name = $request->get('company_name');
        $user->tel_cell = $request->get('tel_cell');
        if ($user->tel_cell) {
            $user->tel_cell_country_code = $tel_cell_country_code;
        }
        $user->address = $request->get('address');
        $user->address_2 = $request->get('address_2');
        // $user->newsletters_offers_promo = $request->get('newsletters_offers_promo') ? 1 : 0;
        // $user->sms_notifications = $request->get('sms_notifications') ? 1 : 0;
        $user->alert_preferences = $request->get('alert_preferences') ? serialize($request->get('alert_preferences')) : null;
        $user->password = Hash::make($request->get('password'));
        $user->verified = $request->get('verification_status') ?? 0;
        $user->ip = $get_ip;
        $image_info = Session::get('user_image');
        if ($image_info) {
            $image_name = $user->username . uniqid() . '.' . $image_info->extension;
            $folderName = $this->folderName();
            Storage::move($image_info->user_image, $this->folderPath . $folderName.'/' . $image_name);
            $user->user_image = $folderName.'/' . $image_name;
            Session::forget('user_image');
        }
        if ($request->hasFile('user_image')) {
            $user->user_image = $user->username . time() . '.' . $request->user_image->extension();
            $storeStatus = $request->user_image->storeAs($this->folderPath, $user->user_image);
            $storeStatus ? $user->update() : null;
        }
        $user->save();

        foreach ($request->user_groups as $user_group_id) {
            $group_map = new UserGroupMap();
            $group_map->user_id = $user->id;
            $group_map->user_group_id = $user_group_id;
            $group_map->save();
        }

        if (Session::has('user_shippings')) {
            $user_shippings_items = collect(Session::get('user_shippings'));
            foreach($user_shippings_items as $value){
                $user_shipping = new UserShipping();
                $user_shipping->user_id = $item->id;
                $user_shipping->first_name = $value->first_name;
                $user_shipping->last_name = $value->last_name;
                $user_shipping->email = $value->email;
                $user_shipping->tel_cell = $value->tel_cell;
                $user_shipping->tel_cell_country_code = $value->tel_cell_country_code;
                $user_shipping->company_name = $value->company_name;
                $user_shipping->country = $value->country;
                $user_shipping->postal_code = $value->postal_code;
                $user_shipping->city = $value->city;
                $user_shipping->state = $value->state;
                $user_shipping->address = $value->address;
                $user_shipping->address_2 = $value->address_2;
                $user_shipping->primary = $value->primary ?? 0;
                $user_shipping->save();
            }
        }

        if (Session::has('user_billings')) {
            $user_billings_items = collect(Session::get('user_billings'));
            foreach ($user_billings_items as $value) {
                $user_billing = new UserBilling();
                $user_billing->user_id = $item->id;
                $user_billing->first_name = $value->first_name;
                $user_billing->last_name = $value->last_name;
                $user_billing->email = $value->email;
                $user_billing->tel_cell = $value->tel_cell;
                $user_billing->tel_cell_country_code = $value->tel_cell_country_code;
                $user_billing->company_name = $value->company_name;
                $user_billing->country = $value->country;
                $user_billing->postal_code = $value->postal_code;
                $user_billing->city = $value->city;
                $user_billing->state = $value->state;
                $user_billing->address = $value->address;
                $user_billing->address_2 = $value->address_2;
                $user_billing->primary = $value->primary ?? 0;
                $user_billing->save();
            }
        }

        Session::forget('user_shippings');
        Session::forget('user_billings');

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.users.index'))->with('success', 'User Added Successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $admin_page_title = 'Edit User';
        $item = User::findOrFail($id);
        $item->countries = $this->countries();
        $item->user_groups = $this->userGroups();

        $exist_user_groups = UserGroupMap::where('user_id', $item->id)->get(['user_group_id']);
        $exist_user_groups_array = array();
        foreach ($exist_user_groups as $exist_user_group) {
            $exist_user_groups_array[] = $exist_user_group->user_group_id;
        }
        $item->exist_user_groups_array = $exist_user_groups_array;

        if($item->alert_preferences){
            $item->alert_preferences = unserialize($item->alert_preferences);
        }

        if (Session::has('user_shippings')) {
            $user_shippings = DB::table('user_shippings')->where('user_id', $item->id)->get();
            $session_user_shippings = collect(Session::get('user_shippings'))->where('user_id', $item->id);
            $user_shippings = collect($user_shippings->merge($session_user_shippings));
        }else{
            $user_shippings = UserShipping::where('user_id', $item->id)->get();
        }

        if (Session::has('user_billings')) {
            $user_billings = DB::table('user_billings')->where('user_id', $item->id)->get();
            $session_user_billings = collect(Session::get('user_billings'))->where('user_id', $item->id);
            $user_billings = collect($user_billings->merge($session_user_billings));
        }else{
            $user_billings = UserBilling::where('user_id', $item->id)->get();
        }

        // dd($item->alert_preferences);

        return view('admin.user.edit', compact('item', 'admin_page_title', 'user_shippings', 'user_billings'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($request->all());
        $company_name = $request->company_name_area ? 'required' : 'nullable';
        $username = $request->user_credential_area ? 'required' : 'nullable';
        // $password = $request->user_credential_area ? 'required' : 'nullable';
        $country = $request->contact_info_area ? 'required' : 'nullable';
        $postal_code = $request->contact_info_area ? 'required' : 'nullable';
        $city = $request->contact_info_area ? 'required' : 'nullable';
        $state = $request->contact_info_area ? 'required' : 'nullable';
        $address = $request->contact_info_area ? 'required' : 'nullable';
        $this->validate($request, [
            'user_groups' => 'required|exists:user_groups,id',
            // 'user_groups.*' => 'required|exists:user_groups,id',
            'username' => $username."|string|max:255|alpha_dash|unique:users,username," . $id,
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            "email" => "nullable|email|unique:users,email," . $id,
            "tel_cell" => "nullable|unique:users,tel_cell," . $id,
            'tel_cell_country_code' => 'nullable|string',
            'city' => $city . '|string',
            'company_name' => $company_name . '|string',
            'state' => $state . '|string',
            'country' => $country . '|string',
            'postal_code' => $postal_code . '|integer',
            'address' => $address . '|string',
            'address_2' => 'nullable|string',
            'password' => 'nullable|string|min:8',
        ]);

        $get_ip = $this->getClientIpAddress();
        $user_num = abs(crc32(uniqid()));
        $tel_cell_country_code = null;
        if ($request->tel_cell) {
            $tel_cell_country_code = '+' . $request->tel_cell_country_code;
        }
        $user = User::findOrFail($id);
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->username = $request->get('username');
        // $user->username = $request->get('username') ?? Str::slug($request->get('first_name'), '_') . $user_num;
        $user->email = $request->get('email');
        $user->state = $request->get('state');
        $user->city = $request->get('city');
        $user->postal_code = $request->get('postal_code');
        $user->country = $request->get('country');
        $user->company_name = $request->get('company_name');
        $user->tel_cell = $request->get('tel_cell');
        if ($user->tel_cell) {
            $user->tel_cell_country_code = $tel_cell_country_code;
        }
        $user->address = $request->get('address');
        $user->address_2 = $request->get('address_2');
        // $user->newsletters_offers_promo = $request->get('newsletters_offers_promo') ? 1 : 0;
        // $user->sms_notifications = $request->get('sms_notifications') ? 1 : 0;
        // dd($request->get('alert_preferences'));
        $user->alert_preferences = $request->get('alert_preferences') ? serialize($request->get('alert_preferences')) : null;
        // dd($request->get('alert_preferences'));
        if($request->user_credential_area){
            $user->password = $request->get('password') ? Hash::make($request->get('password')) : $user->password;
        }else{
            $user->password = null;
        }

        $user->verified = $request->get('verification_status') ?? 0;
        $user->ip = $get_ip;
        $image_info = Session::get('user_image');
        if ($image_info) {
            $image_name = $user->username . uniqid() . '.' . $image_info->extension;
            $folderName = $this->folderName();
            Storage::move($image_info->user_image, $this->folderPath . $folderName.'/' . $image_name);
            $user->user_image = $folderName.'/' . $image_name;
            Session::forget('user_image');
        }
        if ($request->hasFile('user_image')) {
            $user->user_image = $user->username . time() . '.' . $request->user_image->extension();
            $storeStatus = $request->user_image->storeAs($this->folderPath, $user->user_image);
            $storeStatus ? $user->update() : null;
        }
        $user->update();

        $exist_user_groups = UserGroupMap::where('user_id', $user->id)->get(['user_group_id']);
        if (count($exist_user_groups) > 0) {
            $exist_user_groups_array = array();
            foreach ($exist_user_groups as $exist_user_group) {
                $exist_user_groups_array[] = $exist_user_group->user_group_id;
            }
            //old delete
            $get_account_groups = array_diff($exist_user_groups_array, $request->user_groups);
            foreach ($get_account_groups as $get_account_group) {
                $user_group_map = UserGroupMap::where([
                    ['user_id', $user->id],
                    ['user_group_id', $get_account_group]
                ])->first();
                if ($user_group_map) {
                    $user_group_map =  UserGroupMap::findOrFail($user_group_map->id);
                    $user_group_map->delete();
                }
            }
            $new_get_account_groups = array_diff($request->user_groups, $exist_user_groups_array);
            foreach ($new_get_account_groups as $new_get_account_group) {
                $group_map = new UserGroupMap();
                $group_map->user_id = $user->id;
                $group_map->user_group_id = $new_get_account_group;
                $group_map->save();
            }
        } else {
            foreach ($request->user_groups as $user_group_id) {
                $group_map = new UserGroupMap();
                $group_map->user_id = $user->id;
                $group_map->user_group_id = $user_group_id;
                $group_map->save();
            }
        }

        if (Session::has('user_shippings')) {
            $user_shippings_items = collect(Session::get('user_shippings'))->where('user_id', $user->id);
            foreach ($user_shippings_items as $value) {
                $user_shipping = new UserShipping();
                $user_shipping->user_id = $user->id;
                $user_shipping->first_name = $value->first_name;
                $user_shipping->last_name = $value->last_name;
                $user_shipping->email = $value->email;
                $user_shipping->tel_cell = $value->tel_cell;
                $user_shipping->tel_cell_country_code = $value->tel_cell_country_code;
                $user_shipping->company_name = $value->company_name;
                $user_shipping->country = $value->country;
                $user_shipping->postal_code = $value->postal_code;
                $user_shipping->city = $value->city;
                $user_shipping->state = $value->state;
                $user_shipping->address = $value->address;
                $user_shipping->address_2 = $value->address_2;
                $user_shipping->primary = $value->primary ?? 0;
                $user_shipping->save();
            }
        }

        if (Session::has('user_billings')) {
            $user_billings_items = collect(Session::get('user_billings'))->where('user_id', $user->id);
            foreach ($user_billings_items as $value) {
                $user_billing = new UserBilling();
                $user_billing->user_id = $user->id;
                $user_billing->first_name = $value->first_name;
                $user_billing->last_name = $value->last_name;
                $user_billing->email = $value->email;
                $user_billing->tel_cell = $value->tel_cell;
                $user_billing->tel_cell_country_code = $value->tel_cell_country_code;
                $user_billing->company_name = $value->company_name;
                $user_billing->country = $value->country;
                $user_billing->postal_code = $value->postal_code;
                $user_billing->city = $value->city;
                $user_billing->state = $value->state;
                $user_billing->address = $value->address;
                $user_billing->address_2 = $value->address_2;
                $user_billing->primary = $value->primary ?? 0;
                $user_billing->save();
            }
        }

        Session::forget('user_shippings');
        Session::forget('user_billings');

        $message = 'User Information Edit Successful.';
        return ($request->get('btn') == 'update') ? back()->with('success', $message) : redirect()->route('admin.users.index')->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user)
    {
        $user_group_map = UserGroupMap::where('user_id', $user->id)->first();
        if ($user_group_map) {
            $user_group_map_data = UserGroupMap::findOrFail($user_group_map->id);
            $user_group_map_data->delete();
        }

        $trackings = Tracking::where('user_id', $user->id)->get();
        if(count($trackings) > 0){
            foreach($trackings as $value){
                $tracking = Tracking::findOrFail($value->id);
                $tracking->delete();
            }
        }

        $rewards = Reward::where('user_id', $user->id)->get();
        if (count($rewards) > 0) {
            foreach ($rewards as $value) {
                $reward = Reward::findOrFail($value->id);
                $reward->delete();
            }
        }

        $cash_rewards = CashReward::where('user_id', $user->id)->get();
        if (count($cash_rewards) > 0) {
            foreach ($cash_rewards as $value) {
                $cash_reward = CashReward::findOrFail($value->id);
                $cash_reward->delete();
            }
        }

        $redeem_cash_rewards = RedeemCashReward::where('user_id', $user->id)->get();
        if (count($redeem_cash_rewards) > 0) {
            foreach ($redeem_cash_rewards as $value) {
                $redeem_cash_reward = RedeemCashReward::findOrFail($value->id);
                $redeem_cash_reward->delete();
            }
        }

        $user_shippings = UserShipping::where('user_id', $user->id)->get();
        if (count($user_shippings) > 0) {
            foreach ($user_shippings as $value) {
                $user_shipping = UserShipping::findOrFail($value->id);
                $user_shipping->delete();
            }
        }

        $user_billings = UserBilling::where('user_id', $user->id)->get();
        if (count($user_billings) > 0) {
            foreach ($user_billings as $value) {
                $user_billing = UserBilling::findOrFail($value->id);
                $user_billing->delete();
            }
        }

        $user->delete();

        $message = '<strong>' . $user->first_name . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }

    /**
     * Upload File Function For This Component
     */
    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }

        $user = User::findOrFail($id);

        $this->deleteFile($this->folderPath, $user->$target); // deleting old File

        $imgName = $user->username . "_" . $user->id . "_" . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath . $folderName, $imgName);

        $user->$target = $folderName. '/'. $imgName;
        $user->update();

        $url = url('/storage/users/' . $folderName. '/' . $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    /**
     * Remove File Function For This Component
     *
     * @param string target target key is table key
     * @return boolean
     */
    public function fileRemove($target, $id)
    {
        $user = User::findOrFail($id);
        $this->deleteFile($this->folderPath, $user->$target);
        $user->$target = null;
        $user->update();

        return response()->json('success', 200);
    }
}
