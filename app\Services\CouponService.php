<?php

namespace App\Services;

use App\Models\Coupon;
use App\Models\User;
use App\Models\CouponUsage;
use App\Models\ProductCategory;
use App\Models\ProductBrand;
use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Exception;

class CouponService
{
    /**
     * Validate coupon by code and user
     */
    public function validateCoupon(string $code, ?User $user = null): Coupon
    {
        $coupon = Coupon::where('code', $code)
            ->where('status', 'active')
            ->first();

        if (!$coupon) {
            throw new Exception("Invalid coupon code.");
        }

        $now = now();

        // Check date validity
        if (!$coupon->never_expire) {
            if ($coupon->from_date && $now->lt($coupon->from_date)) {
                throw new Exception("Coupon is not yet valid.");
            }
            if ($coupon->to_date && $now->gt($coupon->to_date)) {
                throw new Exception("Coupon has expired.");
            }
        }

        // Check guest checkout
        if (!$user && !$coupon->guest_checkout) {
            throw new Exception("This coupon requires user registration.");
        }

        // Check usage limits
        if ($user) {
            if ($coupon->uses_per_customer) {
                $userUsageCount = CouponUsage::where('coupon_id', $coupon->id)
                    ->where('user_id', $user->id)
                    ->count();
                if ($userUsageCount >= $coupon->uses_per_customer) {
                    throw new Exception("You have reached the usage limit for this coupon.");
                }
            }
        }

        if ($coupon->uses_per_coupon) {
            $totalUsageCount = CouponUsage::where('coupon_id', $coupon->id)->count();
            if ($totalUsageCount >= $coupon->uses_per_coupon) {
                throw new Exception("This coupon has reached its usage limit.");
            }
        }

        return $coupon;
    }

    /**
     * Apply coupon to cart
     */
    public function applyCouponToCart(string $code, ?User $user = null): array
    {
        $coupon = $this->validateCoupon($code, $user);
        $cart = Session::get('cart');

        if (!$cart || empty($cart->items)) {
            throw new Exception("Your cart is empty.");
        }

        Log::info('Applying coupon to cart', [
            'coupon_code' => $code,
            'coupon_type' => $coupon->coupon_type,
            'discount_type' => $coupon->discount_type,
            'discount_amount' => $coupon->discount_amount,
            'condition_type' => $coupon->condition_type,
            'cart_total' => $cart->total ?? 0
        ]);

        // Get eligible items based on coupon conditions
        $eligibleItems = $this->getEligibleCartItems($coupon, $cart->items);
        
        if ($eligibleItems->isEmpty()) {
            throw new Exception("No items in your cart are eligible for this coupon.");
        }

        // Calculate eligible subtotal
        $eligibleSubtotal = $this->calculateEligibleSubtotal($eligibleItems);

        Log::info('Eligible items calculated', [
            'eligible_items_count' => $eligibleItems->count(),
            'eligible_subtotal' => $eligibleSubtotal
        ]);

        // Check minimum subtotal requirement
        if ($coupon->condition_type === 'subtotal' && $coupon->min_subtotal) {
            if ($eligibleSubtotal < $coupon->min_subtotal) {
                throw new Exception("Minimum cart value of " . getDefaultCurrencySymbol() . number_format($coupon->min_subtotal, 2) . " required for this coupon.");
            }
        }

        // Calculate discount
        $discountAmount = $this->calculateDiscount($coupon, $eligibleSubtotal);

        Log::info('Discount calculated', [
            'discount_amount' => $discountAmount,
            'eligible_subtotal' => $eligibleSubtotal
        ]);

        if ($discountAmount <= 0) {
            throw new Exception("This coupon does not provide any discount for your cart.");
        }

        // Remove any existing coupon first
        $this->removeExistingCoupon($cart);

        // Apply discount to cart
        $this->applyDiscountToCart($cart, $coupon, $discountAmount, $eligibleItems);

        return [
            'success' => true,
            'message' => 'Coupon applied successfully!',
            'coupon_code' => $code,
            'discount_amount' => $discountAmount,
            'eligible_items_count' => $eligibleItems->count(),
            'new_total' => $cart->grand_total
        ];
    }

    /**
     * Get eligible cart items based on coupon conditions
     */
    protected function getEligibleCartItems(Coupon $coupon, $cartItems): Collection
    {
        $items = collect($cartItems);

        Log::info('Filtering eligible items', [
            'condition_type' => $coupon->condition_type,
            'total_items' => $items->count()
        ]);

        switch ($coupon->condition_type) {
            case 'category':
                return $this->filterByCategory($items, $coupon);
            
            case 'brand':
                return $this->filterByBrand($items, $coupon);
            
            case 'product':
                return $this->filterByProduct($items, $coupon);
            
            case 'attribute':
                return $this->filterByAttribute($items, $coupon);
            
            case 'subtotal':
            case 'none':
            default:
                return $items;
        }
    }

    /**
     * Filter items by category
     */
    protected function filterByCategory($items, Coupon $coupon): Collection
    {
        $allowedCategoryIds = $coupon->productCategories->pluck('id')->toArray();
        
        return $items->filter(function ($item) use ($allowedCategoryIds) {
            // Check if item has cart_items property or is a direct cart item
            if (isset($item->cart_items)) {
                foreach ($item->cart_items as $cartItem) {
                    $product = Product::find($cartItem->product_id);
                    if ($product && in_array($product->product_category_id, $allowedCategoryIds)) {
                        return true;
                    }
                }
            } else {
                // Direct cart item
                $product = Product::find($item->product_id);
                if ($product && in_array($product->product_category_id, $allowedCategoryIds)) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * Filter items by brand
     */
    protected function filterByBrand($items, Coupon $coupon): Collection
    {
        $allowedBrandIds = $coupon->productBrands->pluck('id')->toArray();
        
        return $items->filter(function ($item) use ($allowedBrandIds) {
            if (isset($item->cart_items)) {
                foreach ($item->cart_items as $cartItem) {
                    $product = Product::find($cartItem->product_id);
                    if ($product && in_array($product->product_brand_id, $allowedBrandIds)) {
                        return true;
                    }
                }
            } else {
                $product = Product::find($item->product_id);
                if ($product && in_array($product->product_brand_id, $allowedBrandIds)) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * Filter items by specific products
     */
    protected function filterByProduct($items, Coupon $coupon): Collection
    {
        $allowedProductIds = $coupon->products->pluck('id')->toArray();
        
        return $items->filter(function ($item) use ($allowedProductIds) {
            if (isset($item->cart_items)) {
                foreach ($item->cart_items as $cartItem) {
                    if (in_array($cartItem->product_id, $allowedProductIds)) {
                        return true;
                    }
                }
            } else {
                if (in_array($item->product_id, $allowedProductIds)) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * Filter items by attributes
     */
    protected function filterByAttribute($items, Coupon $coupon): Collection
    {
        $allowedAttributeIds = $coupon->productAttributes->pluck('id')->toArray();
        
        return $items->filter(function ($item) use ($allowedAttributeIds) {
            if (isset($item->cart_items)) {
                foreach ($item->cart_items as $cartItem) {
                    $product = Product::with('productAttributes')->find($cartItem->product_id);
                    if ($product) {
                        $productAttributeIds = $product->productAttributes->pluck('id')->toArray();
                        if (array_intersect($productAttributeIds, $allowedAttributeIds)) {
                            return true;
                        }
                    }
                }
            } else {
                $product = Product::with('productAttributes')->find($item->product_id);
                if ($product) {
                    $productAttributeIds = $product->productAttributes->pluck('id')->toArray();
                    if (array_intersect($productAttributeIds, $allowedAttributeIds)) {
                        return true;
                    }
                }
            }
            return false;
        });
    }

    /**
     * Calculate eligible subtotal
     */
    protected function calculateEligibleSubtotal($eligibleItems): float
    {
        $subtotal = 0;
        
        foreach ($eligibleItems as $item) {
            if (isset($item->cart_items)) {
                foreach ($item->cart_items as $cartItem) {
                    $price = isset($cartItem->discounted_price) && $cartItem->discounted_price > 0 
                        ? $cartItem->discounted_price 
                        : $cartItem->price;
                    $quantity = $cartItem->quantity ?? 1;
                    $subtotal += $price ;
                }
            } else {
                // Direct cart item
                $price = isset($item->discounted_price) && $item->discounted_price > 0 
                    ? $item->discounted_price 
                    : $item->price;
                $quantity = $item->quantity ?? 1;
                $subtotal += $price ;
            }
        }

        return round($subtotal, 2);
    }

    /**
     * Calculate discount amount
     */
    protected function calculateDiscount(Coupon $coupon, float $subtotal): float
    {
        if ($coupon->value_type === 'free_shipping') {
            // Free shipping - we'll handle this differently
            return 0; // For now, return 0 and handle shipping separately
        }

        // Fix: Check for both 'percentage' and 'percent' to handle different naming conventions
        if (in_array($coupon->discount_type, ['percentage', 'percent'])) {
            $discountAmount = round($subtotal * ($coupon->discount_amount / 100), 2);
            Log::info('Percentage discount calculated', [
                'subtotal' => $subtotal,
                'percentage' => $coupon->discount_amount,
                'discount_amount' => $discountAmount
            ]);
            return $discountAmount;
        }

        // Fixed amount - don't exceed the subtotal
        $discountAmount = min($coupon->discount_amount, $subtotal);
        Log::info('Fixed discount calculated', [
            'subtotal' => $subtotal,
            'fixed_amount' => $coupon->discount_amount,
            'discount_amount' => $discountAmount
        ]);
        
        return round($discountAmount, 2);
    }

    /**
     * Remove existing coupon from cart
     */
    protected function removeExistingCoupon($cart): void
    {
        if (isset($cart->applied_coupon)) {
            // Remove previous discount
            if (isset($cart->applied_coupon['discount_amount'])) {
                $cart->totalDiscount = max(0, ($cart->totalDiscount ?? 0) - $cart->applied_coupon['discount_amount']);
                
                // Remove local currency discount
                $location_data = Session::get('shop_logged_data');
                if ($location_data && $location_data->currency_rate > 0) {
                    $cart->totalDiscount_local = max(0, ($cart->totalDiscount_local ?? 0) - ($cart->applied_coupon['discount_amount'] * $location_data->currency_rate));
                }
            }
            
            // Remove free shipping
            if (isset($cart->applied_coupon['free_shipping'])) {
                $cart->freeShipping = false;
            }
            
            unset($cart->applied_coupon);
        }
    }

    /**
     * Apply discount to cart
     */
    protected function applyDiscountToCart($cart, Coupon $coupon, float $discountAmount, $eligibleItems): void
    {
        // Store coupon information in cart
        $cart->applied_coupon = [
            'code' => $coupon->code,
            'name' => $coupon->name,
            'discount_amount' => $discountAmount,
            'type' => $coupon->discount_type,
            'value_type' => $coupon->value_type,
            'eligible_items_count' => $eligibleItems->count()
        ];

        // Apply discount
        if ($coupon->value_type === 'discount') {
            $cart->totalDiscount = ($cart->totalDiscount ?? 0) + $discountAmount;
            
            // Recalculate grand total
            $cart->grand_total = ($cart->total ?? 0) - $cart->totalDiscount;
            
            // Apply local currency discount if applicable
            $location_data = Session::get('shop_logged_data');
            if ($location_data && $location_data->currency_rate > 0) {
                $cart->totalDiscount_local = ($cart->totalDiscount_local ?? 0) + ($discountAmount * $location_data->currency_rate);
                $cart->grand_total_local = ($cart->total_local ?? 0) - $cart->totalDiscount_local;
            }
            
            Log::info('Discount applied to cart', [
                'total_discount' => $cart->totalDiscount,
                'grand_total' => $cart->grand_total,
                'local_discount' => $cart->totalDiscount_local ?? 0,
                'local_total' => $cart->grand_total_local ?? 0
            ]);
            
        } elseif ($coupon->value_type === 'free_shipping') {
            $cart->freeShipping = true;
            $cart->applied_coupon['free_shipping'] = true;
        }

        // Update session
        Session::put('cart', $cart);
    }

    /**
     * Remove coupon from cart
     */
    public function removeCouponFromCart(): array
    {
        $cart = Session::get('cart');
        
        if (!$cart || !isset($cart->applied_coupon)) {
            throw new Exception("No coupon applied to remove.");
        }

        $removedCoupon = $cart->applied_coupon;

        // Remove discount
        if (isset($cart->applied_coupon['discount_amount'])) {
            $cart->totalDiscount = max(0, ($cart->totalDiscount ?? 0) - $cart->applied_coupon['discount_amount']);
            $cart->grand_total = ($cart->total ?? 0) - $cart->totalDiscount;
            
            // Remove local currency discount
            $location_data = Session::get('shop_logged_data');
            if ($location_data && $location_data->currency_rate > 0) {
                $cart->totalDiscount_local = max(0, ($cart->totalDiscount_local ?? 0) - ($cart->applied_coupon['discount_amount'] * $location_data->currency_rate));
                $cart->grand_total_local = ($cart->total_local ?? 0) - $cart->totalDiscount_local;
            }
        }

        // Remove free shipping
        if (isset($cart->applied_coupon['free_shipping'])) {
            $cart->freeShipping = false;
        }

        // Clear coupon data
        unset($cart->applied_coupon);
        Session::put('cart', $cart);

        return [
            'success' => true,
            'message' => 'Coupon removed successfully!',
            'removed_coupon' => $removedCoupon['code'],
            'new_total' => $cart->grand_total
        ];
    }

    /**
     * Record coupon usage after order completion
     */
    public function recordCouponUsage(string $couponCode, ?int $userId, int $orderId): void
    {
        $coupon = Coupon::where('code', $couponCode)->first();
        
        if ($coupon) {
            CouponUsage::create([
                'coupon_id' => $coupon->id,
                'user_id' => $userId,
                'order_id' => $orderId,
            ]);
        }
    }

    /**
     * Get coupon for promo popup
     */
    public function getPromoPopupCoupon(): ?Coupon
    {
        return Coupon::where('show_in_promo_popup', true)
            ->where('status', 'active')
            ->where(function ($query) {
                $query->where('never_expire', true)
                    ->orWhere(function ($q) {
                        $q->where('from_date', '<=', now())
                          ->where('to_date', '>=', now());
                    });
            })
            ->first();
    }
}