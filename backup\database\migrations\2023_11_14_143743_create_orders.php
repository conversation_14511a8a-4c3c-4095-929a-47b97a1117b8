<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('users')->cascadeOnUpdate();
            $table->foreignId('created_by_id')->nullable()->constrained('users')->cascadeOnUpdate();
            $table->foreignId('user_shipping_id')->nullable()->constrained('user_shippings')->cascadeOnUpdate();
            $table->foreignId('user_billing_id')->nullable()->constrained('user_billings')->cascadeOnUpdate();
            $table->string('order_no')->unique();
            $table->date('order_date');
            $table->decimal('sub_total_amount', 20, 2);
            $table->decimal('total_amount', 20, 2);
            $table->decimal('sales_tax_amount', 20, 2)->nullable();
            $table->decimal('shipping_amount', 20, 2);
            $table->decimal('transaction_discount', 20, 2)->nullable();
            $table->longText('remarks')->nullable();
            $table->longText('order')->nullable();
            $table->longText('order_items')->nullable();
            $table->ipAddress('ip')->nullable();
            $table->integer('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
