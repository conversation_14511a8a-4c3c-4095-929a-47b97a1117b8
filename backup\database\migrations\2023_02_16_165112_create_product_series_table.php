<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductSeriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_series', function (Blueprint $table) {
            $table->id();
            $table->string("title");
            $table->foreignId('product_brand_id')->constrained('product_brands');
            $table->foreignId('product_category_id')->constrained('product_categories')->nullable();
            $table->string("slug")->unique();
            $table->string("series_link")->nullable();
            $table->string("description")->nullable();
            $table->string("series_image")->nullable();
            $table->integer('ordering')->default(0);
            $table->boolean("status")->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_series');
    }
}
