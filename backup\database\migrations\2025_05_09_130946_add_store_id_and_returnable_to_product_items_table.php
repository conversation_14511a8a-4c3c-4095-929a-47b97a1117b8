<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStoreIdAndReturnableToProductItemsTable extends Migration
{
    public function up()
    {
        if (Schema::hasTable('product_items')) {
            Schema::table('product_items', function (Blueprint $table) {
                if (!Schema::hasColumn('product_items', 'store_id')) {
                    $table->unsignedBigInteger('store_id')->nullable()->after('id');
                }

                if (!Schema::hasColumn('product_items', 'returnable')) {
                    $table->boolean('returnable')->default(1)->after('store_id');
                }
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('product_items')) {
            Schema::table('product_items', function (Blueprint $table) {
                if (Schema::hasColumn('product_items', 'store_id')) {
                    $table->dropColumn('store_id');
                }

                if (Schema::hasColumn('product_items', 'returnable')) {
                    $table->dropColumn('returnable');
                }
            });
        }
    }
}
