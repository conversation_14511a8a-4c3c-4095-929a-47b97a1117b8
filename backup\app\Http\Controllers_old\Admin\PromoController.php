<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Promo;
use Illuminate\Http\Request;
use Session;

class PromoController extends Controller
{
    private $folderPath = 'promos/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Promos';
        $items = Promo::paginate($this->itemPerPage);

        $sl = SLGenerator($items);
        return view('admin.promo.index', compact('sl','items', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Promo';
        $image_info = Session::get('promo_image');
        return view('admin.promo.create', compact('admin_page_title', 'image_info'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            'promo_code' => 'required|string|max:255|unique:promos',
            'from_date' => 'nullable|date_format:"m/d/Y"',
            'to_date' => 'nullable|date_format:"m/d/Y"',
            'description' => 'nullable|string',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
            'never_expire' => 'nullable|boolean',
        ]);

        $item = new Promo;
        $item->title = $request->get('title');
        $item->promo_code = $request->get('promo_code');
        $item->from_date = $this->formatDate($request->get('from_date'));
        $item->to_date = $this->formatDate($request->get('to_date'));
        $item->description = $request->get('description');
        $item->discount_type = $request->get('discount_type');
        $item->discount_price = $request->get('discount_price');
        $item->ordering = $request->get('ordering') ?? 0;
        $item->status = $request->get('status') ?? 0;
        $item->never_expire = $request->get('never_expire') ?? 0;
        $item->save();

        $item = Promo::find($item->id);
        $image_info = Session::get('promo_image');
        if ($image_info) {
            $image_name = $item->id . uniqid() . '.' . $image_info->extension;
            $folderName = $this->folderName();
            Storage::move($image_info->promo_image, $this->folderPath . $folderName.'/' . $image_name);
            $item->promo_image = $folderName.'/' . $image_name;
            Session::forget('promo_image');
        }
        if ($request->hasFile('promo_image')) {
            $item->promo_image = $item->id . time() . '.' . $request->promo_image->extension();
            $storeStatus = $request->promo_image->storeAs($this->folderPath, $item->promo_image);
            $storeStatus ? $item->update() : null;
        }

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.promos.index'))->with('success', 'Promo Added Successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function show(Promo $promo)
    {
        abort('404');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function edit(Promo $promo)
    {
        $admin_page_title = 'Edit Promo';
        $promo->from_date =$this->undoFormatDate($promo->from_date);
        $promo->to_date = $this->undoFormatDate($promo->to_date);
        return view('admin.promo.edit', compact('admin_page_title', 'promo'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Promo $promo)
    {
        $this->validate($request, [
            'title' => 'required|string|max:255',
            "promo_code" => "nullable|unique:promos,promo_code," . $promo->id,
            'from_date' => 'nullable|date_format:"m/d/Y"',
            'to_date' => 'nullable|date_format:"m/d/Y"',
            'description' => 'nullable|string',
            'discount_type' => 'nullable|numeric',
            'discount_price' => 'nullable|numeric|between:0,9999999999.99',
            'ordering' => 'nullable|numeric',
            'status' => 'nullable|boolean',
            'never_expire' => 'nullable|boolean',
        ]);

        $promo->title = $request->get('title');
        $promo->promo_code = $request->get('promo_code');
        $promo->from_date = $this->formatDate($request->get('from_date'));
        $promo->to_date = $this->formatDate($request->get('to_date'));
        $promo->description = $request->get('description');
        $promo->discount_type = $request->get('discount_type');
        $promo->discount_price = $request->get('discount_price');
        $promo->ordering = $request->get('ordering') ?? 0;
        $promo->status = $request->get('status') ?? 0;
        $promo->never_expire = $request->get('never_expire') ?? 0;
        $promo->update();

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.promos.index'))->with('success', 'Promo update Successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Promo  $promo
     * @return \Illuminate\Http\Response
     */
    public function destroy(Promo $promo)
    {
        $promo = Promo::findOrFail($promo->id);
        $promo->delete();

        $message = '<strong>' . $promo->title . '</strong> removed successful';
        return response()->json([
            'message' => $message,
        ], 200);
    }

    /**
     * Upload File Function For This Component
     */
    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json(
                    [
                        'status' => 'failed',
                        'message' =>  $message
                    ],
                    200
                );
                break;
            }
        }

        $item = Promo::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target); // deleting old File

        $imgName = $item->id . "_" . $item->id . "_" . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath . $folderName, $imgName);

        $item->$target = $folderName. '/'. $imgName;
        $item->update();

        $url = url('/storage/promos/' . $folderName. '/'. $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    /**
     * Remove File Function For This Component
     *
     * @param string target target key is table key
     * @return boolean
     */
    public function fileRemove($target, $id)
    {
        $item = Promo::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();

        return response()->json('success', 200);
    }
}
