<?php

namespace App\Listeners;

use App\Events\PromotionalAlerts;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendPromotionalAlertsNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(PromotionalAlerts $event)
    {
        $this->notifier->sendPromotionalAlertsNotifications($event->user, $event->message_status,  $event->template);
        //$this->notifier->sendAdminOrderDeliveredNotifications($event->order, $event->cart);
    }
}
