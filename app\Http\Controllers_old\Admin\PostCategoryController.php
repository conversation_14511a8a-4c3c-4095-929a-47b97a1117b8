<?php

namespace App\Http\Controllers\Admin;

use Validator;
use Illuminate\Support\Str;
use App\Models\PostCategory;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PostCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Post Categories';
        $items = PostCategory::paginate($this->itemPerPage);
        $sl = SLGenerator($items);
        return view('admin.post.categories.index', compact('items','sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'New Category';
        return view('admin.post.categories.create',compact('admin_page_title'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string',
            // 'slug' => 'required|string|alpha_dash|unique:post_categories,slug',
            'ordering' => 'nullable|string',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|unique:post_categories,slug'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        PostCategory::create([
            'title' => $request->get('title'),
            'slug' => $item_slug,
            'ordering' => $request->get('ordering') ?? 0,
            'description' => $request->get('description'),
            'status' => $request->get('status') ?? 0
        ]);

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.post-categories.index'))->with('success', 'Post category created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\PostCategory  $postCategory
     * @return \Illuminate\Http\Response
     */
    public function show(PostCategory $postCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\PostCategory  $postCategory
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $admin_page_title = 'Edit Category';
        $category = PostCategory::findOrFail($id);
        return view('admin.post.categories.edit', compact('category', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\PostCategory  $postCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'title' => 'required|string',
            'slug' => 'required|string|alpha_dash|unique:post_categories,slug,' . $id,
            'description' => 'nullable|string',
            'ordering' => 'required|string',
            'status' => 'nullable|boolean'
        ]);

        PostCategory::findOrFail($id)->update([
            'title' => $request->get('title'),
            'slug' => $request->get('slug'),
            'ordering' => $request->get('ordering'),
            'status' => $request->get('status') ?? 0,
            'description' => $request->get('description')
        ]);

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.post-categories.index'))->with('success', 'Post category updated successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\PostCategory  $postCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = PostCategory::findOrFail($id);
        $item->delete();

        $items = PostCategory::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        return view('admin.jquery_live.post-categories', compact('items', 'sl'));
    }
}
