<?php

namespace App\Http\Controllers;

use URL;
use DateTime;
use Redirect;
use Validator;
use Carbon\Carbon;
use App\Models\Cart;
use App\Models\Page;
use App\Models\User;
use EasyPost\Parcel;
use PayPal\Api\Item;
use App\Models\Order;
use App\Models\Promo;
use App\Models\Slide;
use App\Services\Box;
use EasyPost\Address;
use PayPal\Api\Payer;
use App\Models\Review;
use App\Models\Reward;
use EasyPost\EasyPost;
use PayPal\Api\Amount;
use App\Mail\SiteEmail;
use App\Models\Collect;
use App\Models\Product;
use App\Models\Section;
use App\Models\Setting;
use PayPal\Api\Payment;
use App\Models\Tracking;
use PayPal\Api\ItemList;
use App\Models\OrderItem;
use App\Models\UserGroup;
use App\Models\CashReward;
use App\Models\AccessLabel;
use App\Models\DHLTracking;
use App\Models\OrderRefund;
use App\Models\PointSystem;
use App\Models\ProductItem;
use App\Models\ShippingBox;
use App\Models\UserBilling;
use Illuminate\Support\Str;
use PayPal\Api\Transaction;
use PayPal\Rest\ApiContext;
use App\Models\ProductBrand;
use App\Models\UserGroupMap;
use App\Models\UserShipping;
use App\Services\DHLService;
use Illuminate\Http\Request;
use PayPal\Api\RedirectUrls;
use App\Models\CustomerPromo;
use App\Models\EmailTemplate;
use App\Models\ProductSeries;
use App\Models\SentDiscount;
use App\Models\AlertPreference;
use App\Models\ProductCategory;
use App\Services\BTCPayService;
use App\Models\RedeemCashReward;
use PayPal\Api\PaymentExecution;
use App\Models\TransactionMethod;
use App\Models\EmailTemplateGroup;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ShippingMethodOption;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use App\Models\EstimatedDeliveryDate;
use PayPal\Auth\OAuthTokenCredential;
use App\Models\ProdcutCategoriesParent;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use App\Models\GeneralSetting;
use App\Models\ShippingRule;
use KingFlamez\Rave\Facades\Rave as Flutterwave;
use App\Events\OrderPlaced;
use App\Http\Controllers\CheckoutController;
use Illuminate\Validation\ValidationException;
use App\Services\CouponService;
use App\Models\PaymentMethod;
use App\Services\PaymentProcessingService;

class CheckoutController extends Controller
{
    protected $couponService;
    private $_api_context;
    protected $btcPayService;
    public function __construct()
    {
        $paypal_conf = \Config::get('paypal');
        $this->btcPayService = new BTCPayService();
        $this->couponService = new CouponService();
        $this->_api_context = new \PayPal\Rest\ApiContext(
            new \PayPal\Auth\OAuthTokenCredential(
                $paypal_conf['client_id'],
                $paypal_conf['secret']
            )
        );
        $this->_api_context->setConfig($paypal_conf['settings']);
    }

    public function handleUser($checkout_order, $request)
    {
        $customer = null;
        $customer_tell = $request->get('customer_tel_cell');

        if (Auth::user()) {
            $validator = Validator::make($request->all(), [
                'customer_first_name' => 'required|string|max:255',
                'customer_last_name' => 'required|string|max:255',
                'customer_email' => 'nullable|email',
                "customer_tel_cell" => "required|string|max:20",
                'customer_company_name' => 'nullable|string',
                'customer_city' => 'nullable|string',
                'customer_state' => 'nullable|string',
                'customer_country' => 'nullable|string',
                'customer_postal_code' => 'nullable|integer',
                'customer_address_line_1' => 'nullable|string',
                'customer_address_line_2' => 'nullable|string',
                'email_notifications' => 'nullable|boolean',
                'sms_notifications' => 'nullable|boolean',
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'customer_first_name' => 'required|string|max:255',
                'customer_last_name' => 'required|string|max:255',
                'customer_email' => 'nullable|email',
                "customer_tel_cell" => "required|string|max:20",
                'customer_company_name' => 'nullable|string',
                'customer_city' => 'nullable|string',
                'customer_state' => 'nullable|string',
                'customer_country' => 'nullable|string',
                'customer_postal_code' => 'nullable|integer',
                'customer_address_line_1' => 'nullable|string',
                'customer_address_line_2' => 'nullable|string',
                'email_notifications' => 'nullable|boolean',
                'sms_notifications' => 'nullable|boolean',
            ]);
        }

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $customer = (object)[
            'first_name' => $request->get('customer_first_name'),
            'last_name' => $request->get('customer_last_name'),
            'email' => $request->get('customer_email'),
            'tel_cell' => preg_replace('/[^\d+]/', '', $customer_tell),
            'company_name' => $request->get('customer_company_name'),
            'city' => $request->get('customer_city'),
            'state' => $request->get('customer_state'),
            'country' => $request->get('customer_country'),
            'country_name' => $request->get('customer_country'),
            'postal_code' => $request->get('customer_postal_code'),
            'address' => $request->get('customer_address_line_1'),
            'address_2' => $request->get('customer_address_line_2'),
            'email_notifications' => $request->get('email_notifications') ?? 0,
            'sms_notifications' => $request->get('sms_notifications') ?? 0,
        ];

        return $customer;
    }

    public function getShippingMethods($checkout_order)
    {
        return [
            'id' => 'FREE',
            'carrier' => 'Free Shipping',
            'service' => 'Standard',
            'rate' => 0.00,
            'retail_rate' => 0.00,
            'currency' => 'USD',
            'currency_symbol' => getDefaultCurrencySymbol(),
            'carrier_account_id' => 'FREE',
            'shipment_id' => 'FREE',
        ];
    }

    /**
     * Process shipping form submission
     */
    public function processShippingSelection($request)
    {

        $shippingConfig = $request->input('shipping_config', []);
        $shippingMethod = $request->input('shipping_method', []);

        Log::info('Processing shipping selection', [
            'config' => $shippingConfig,
            'method' => $shippingMethod
        ]);

        $processedShipping = [];
        $totalShippingCost = 0;

        foreach ($shippingConfig as $countryCode => $config) {
            $countryShipping = $this->processCountryShipping(
                $countryCode,
                $config,
                $shippingMethod[$countryCode] ?? []
            );

            if ($countryShipping) {
                $processedShipping[$countryCode] = $countryShipping;
                $totalShippingCost += $countryShipping['total_cost'];
            }
        }

        return [
            'shipping_details' => $processedShipping,
            'total_shipping_cost' => $totalShippingCost,
            'cart_signature' => $request->input('cart_signature'), // Save cart signature
            'shipping_summary' => $this->generateShippingSummary($processedShipping)
        ];
    }

    private function processCountryShipping($countryCode, $config, $methodData)
    {
        $countryShipping = [
            'country_code' => $countryCode,
            'country_name' => $config['country_name'] ?? $countryCode,
            'shipping_mode' => $config['mode'] ?? 'single',
            'total_items' => $config['total_items'] ?? 0,
            'shipments' => [],
            'total_cost' => 0
        ];

        Log::info('Processing country shipping', [
            'country' => $countryCode,
            'mode' => $config['mode'] ?? 'single',
            'method_data' => $methodData
        ]);

        // Process based on shipping mode
        switch ($config['mode']) {
            case 'single':
            case 'together':
                $shipment = $this->processCombinedShipping($countryCode, $config, $methodData);
                if ($shipment) {
                    $countryShipping['shipments'][] = $shipment;
                }
                break;

            case 'individually':
                if (isset($config['stores']) && is_array($config['stores'])) {
                    foreach ($config['stores'] as $storeId => $storeConfig) {
                        $storeMethodData = $methodData['stores'][$storeId] ?? [];
                        if (!empty($storeMethodData) && isset($storeMethodData['carrier'])) {
                            $shipment = $this->processIndividualStoreShipping(
                                $countryCode,
                                $storeId,
                                $storeConfig,
                                $storeMethodData
                            );
                            if ($shipment) {
                                $countryShipping['shipments'][] = $shipment;
                            }
                        }
                    }
                }
                break;
        }

        // Calculate total cost for this country
        $countryShipping['total_cost'] = array_sum(
            array_column($countryShipping['shipments'], 'shipping_cost')
        );

        Log::info('Country shipping processed', [
            'country' => $countryCode,
            'shipments_count' => count($countryShipping['shipments']),
            'total_cost' => $countryShipping['total_cost']
        ]);

        return $countryShipping;
    }

    private function processCombinedShipping($countryCode, $config, $methodData)
    {
        // Log for debugging
        Log::info('Processing combined shipping', [
            'country' => $countryCode,
            'mode' => $config['mode'] ?? 'unknown',
            'carrier' => $methodData['carrier'] ?? 'missing',
            'rate_details' => $methodData['rate_details'] ?? 'missing'
        ]);

        // Check if we have the required data
        if (empty($methodData['carrier'])) {
            Log::warning('Missing carrier for combined shipping', [
                'country' => $countryCode,
                'methodData' => $methodData
            ]);
            return null;
        }

        // Check if rate_details exists
        if (empty($methodData['rate_details'])) {
            Log::warning('Missing rate_details for combined shipping', [
                'country' => $countryCode,
                'methodData' => $methodData
            ]);
            return null;
        }

        $itemIds = !empty($config['item_ids']) ? explode(',', $config['item_ids']) : [];
        $storeIds = !empty($config['store_ids']) ? explode(',', $config['store_ids']) : [];

        // Ensure shipping_cost is properly converted to float
        $shippingCost = isset($methodData['rate_details']['retail_rate']) ?
            (float) $methodData['rate_details']['retail_rate'] : 0.0;

        // Handle estimated delivery date properly
        $estimatedDelivery = isset($methodData['rate_details']['estimated_days']) ?
            $methodData['rate_details']['estimated_days'] : null;

        // If estimated_days is a number of days from now, convert it to a date
        if (is_numeric($estimatedDelivery)) {
            $estimatedDelivery = date('Y-m-d', strtotime('+' . $estimatedDelivery . ' days'));
        } elseif (empty($estimatedDelivery)) {
            $estimatedDelivery = date('Y-m-d', strtotime('+7 days'));
        }

        $shipment = [
            'shipment_type' => ($config['mode'] === 'single') ? 'single' : 'combined',
            'shipment_id' => $this->generateShipmentId($countryCode, 'combined'),
            'carrier' => $methodData['carrier'],
            'shipping_cost' => $shippingCost,
            'currency_symbol' => $methodData['rate_details']['currency_symbol'] ?? '$',
            'estimated_delivery' => $estimatedDelivery,
            'service_code' => $methodData['rate_details']['service_code'] ?? null,
            'item_ids' => $itemIds,
            'store_ids' => $storeIds,
            'tracking_info' => [
                'status' => 'pending',
                'tracking_number' => null,
                'created_at' => now()
            ]
        ];

        Log::info('Combined shipment created', [
            'shipment_id' => $shipment['shipment_id'],
            'shipping_cost' => $shipment['shipping_cost'],
            'carrier' => $shipment['carrier']
        ]);

        return $shipment;
    }

    private function processIndividualStoreShipping($countryCode, $storeId, $storeConfig, $methodData)
    {
        // Log for debugging
        Log::info('Processing individual store shipping', [
            'country' => $countryCode,
            'store' => $storeId,
            'carrier' => $methodData['carrier'] ?? 'missing',
            'rate_details' => $methodData['rate_details'] ?? 'missing'
        ]);

        // Check if we have the required data
        if (empty($methodData['carrier'])) {
            Log::warning('Missing carrier for individual shipping', [
                'country' => $countryCode,
                'store' => $storeId,
                'methodData' => $methodData
            ]);
            return null;
        }

        // Check if rate_details exists
        if (empty($methodData['rate_details'])) {
            Log::warning('Missing rate_details for individual shipping', [
                'country' => $countryCode,
                'store' => $storeId,
                'methodData' => $methodData
            ]);
            return null;
        }

        $itemIds = !empty($storeConfig['item_ids']) ? explode(',', $storeConfig['item_ids']) : [];

        // Ensure shipping_cost is properly converted to float
        $shippingCost = isset($methodData['rate_details']['retail_rate']) ?
            (float) $methodData['rate_details']['retail_rate'] : 0.0;

        // Handle estimated delivery date properly
        $estimatedDelivery = isset($methodData['rate_details']['estimated_days']) ?
            $methodData['rate_details']['estimated_days'] : null;

        // If estimated_days is a number of days from now, convert it to a date
        if (is_numeric($estimatedDelivery)) {
            $estimatedDelivery = date('Y-m-d', strtotime('+' . $estimatedDelivery . ' days'));
        } elseif (empty($estimatedDelivery)) {
            $estimatedDelivery = date('Y-m-d', strtotime('+7 days'));
        }

        $shipment = [
            'shipment_type' => 'individual',
            'shipment_id' => $this->generateShipmentId($countryCode, $storeId),
            'store_id' => $storeId,
            'store_name' => $storeConfig['store_name'] ?? 'Store ' . $storeId,
            'carrier' => $methodData['carrier'],
            'shipping_cost' => $shippingCost,
            'currency_symbol' => $methodData['rate_details']['currency_symbol'] ?? '$',
            'estimated_delivery' => $estimatedDelivery,
            'service_code' => $methodData['rate_details']['service_code'] ?? null,
            'item_ids' => $itemIds,
            'tracking_info' => [
                'status' => 'pending',
                'tracking_number' => null,
                'created_at' => now()
            ]
        ];

        Log::info('Individual shipment created', [
            'shipment_id' => $shipment['shipment_id'],
            'store_id' => $storeId,
            'shipping_cost' => $shipment['shipping_cost'],
            'carrier' => $shipment['carrier']
        ]);

        return $shipment;
    }

    /**
     * Save shipping details to database
     */
    public function saveShippingToOrder($orderId, $shippingDetails)
    {
        try {
            // First, delete any existing shipments for this order
            DB::table('order_shipments')->where('order_id', $orderId)->delete();

            foreach ($shippingDetails['shipping_details'] as $countryCode => $countryData) {
                foreach ($countryData['shipments'] as $shipment) {
                    // Save to order_shipments table
                    DB::table('order_shipments')->insert([
                        'order_id' => $orderId,
                        'shipment_id' => $shipment['shipment_id'],
                        'country_code' => $countryCode,
                        'shipment_type' => $shipment['shipment_type'],
                        'store_id' => $shipment['store_id'] ?? null,
                        'store_name' => $shipment['store_name'] ?? null,
                        'carrier' => $shipment['carrier'],
                        'shipping_cost' => $shipment['shipping_cost'],
                        'currency_symbol' => $shipment['currency_symbol'],
                        'estimated_delivery' => $shipment['estimated_delivery'],
                        'service_code' => $shipment['service_code'],
                        'status' => 'pending',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            // Update order total with shipping cost
            DB::table('orders')
                ->where('id', $orderId)
                ->update([
                    'shipping_cost' => $shippingDetails['total_shipping_cost'],
                    'updated_at' => now()
                ]);

            Log::info('Shipping saved to order successfully', [
                'order_id' => $orderId,
                'total_shipping_cost' => $shippingDetails['total_shipping_cost']
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving shipping to order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'shipping_details' => $shippingDetails
            ]);
            throw $e;
        }
    }

    /**
     * Generate shipping summary for emails/notifications
     */
    private function generateShippingSummary($processedShipping)
    {
        $summary = [];

        foreach ($processedShipping as $countryCode => $countryData) {
            $countrySummary = [
                'country' => $countryData['country_name'],
                'mode' => $countryData['shipping_mode'],
                'total_cost' => $countryData['total_cost'],
                'shipments_count' => count($countryData['shipments']),
                'carriers' => array_unique(array_column($countryData['shipments'], 'carrier'))
            ];

            $summary[$countryCode] = $countrySummary;
        }

        return $summary;
    }

    // Helper methods
    private function generateShipmentId($countryCode, $identifier)
    {
        return strtoupper($countryCode) . '_' . $identifier . '_' . time() . rand(100, 999);
    }

    /**
     * Process shipping form submission - Main endpoint
     */
    public function processShipping(Request $request)
    {
        try {
            // Log the incoming request
            Log::info('Shipping processing started', [
                'shipping_config' => $request->input('shipping_config'),
                'shipping_method' => $request->input('shipping_method')
            ]);

            // Validate the shipping data
            $this->validateShippingData($request);

            // Process shipping selection
            $shippingResult = $this->processShippingSelection($request);

            // Save to order if order_id is provided
            if ($request->has('order_id')) {
                $orderId = $request->input('order_id');
                $this->saveShippingToOrder($orderId, $shippingResult);
            }

            Log::info('Shipping processing completed successfully', [
                'total_cost' => $shippingResult['total_shipping_cost'],
                'countries' => array_keys($shippingResult['shipping_details'])
            ]);

            // Return response
            return response()->json([
                'success' => true,
                'shipping_details' => $shippingResult['shipping_details'],
                'total_shipping_cost' => $shippingResult['total_shipping_cost'],
                'summary' => $shippingResult['shipping_summary']
            ]);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage() . ' in ' . __METHOD__ . ' at line ' . __LINE__);


            return response()->json([
                'success' => false,
                'error' => 'Failed to process shipping selection',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function validateShippingData($request)
    {
        $rules = [
            'shipping_config' => 'required|array',
            'shipping_config.*.country_code' => 'required|string',
            'shipping_config.*.mode' => 'required|in:single,together,individually',
            'shipping_method' => 'required|array',
            'shipping_method.*.carrier' => 'required_without:shipping_method.*.stores',
            'shipping_method.*.stores.*.carrier' => 'required_if:shipping_config.*.mode,individually',
        ];

        $messages = [
            'shipping_config.required' => 'Shipping configuration is required',
            'shipping_method.required' => 'Shipping method selection is required',
            'shipping_method.*.carrier.required_without' => 'Carrier selection is required',
            'shipping_method.*.stores.*.carrier.required_if' => 'Carrier selection is required for each store when shipping individually',
        ];

        $request->validate($rules, $messages);
    }

    /**
     * Debug method to see the exact structure of submitted data
     */
    public function debugShippingData(Request $request)
    {
        return response()->json([
            'shipping_config' => $request->input('shipping_config'),
            'shipping_method' => $request->input('shipping_method'),
            'all_data' => $request->all()
        ]);
    }

    /**
     * Get shipping summary for display in checkout review/confirmation
     */
    public function getShippingSummary($shippingDetails)
    {
        if (empty($shippingDetails) || !isset($shippingDetails['shipping_details'])) {
            return null;
        }

        $summary = [
            'total_cost' => $shippingDetails['total_shipping_cost'] ?? 0,
            'countries' => [],
            'total_shipments' => 0
        ];

        foreach ($shippingDetails['shipping_details'] as $countryCode => $countryData) {
            $countryInfo = [
                'country_name' => $countryData['country_name'],
                'mode' => $countryData['shipping_mode'],
                'shipments' => [],
                'country_total' => $countryData['total_cost']
            ];

            foreach ($countryData['shipments'] as $shipment) {
                $shipmentInfo = [
                    'carrier' => $shipment['carrier'],
                    'service_code' => $shipment['service_code'] ?? 'Standard',
                    'cost' => $shipment['shipping_cost'],
                    'currency_symbol' => $shipment['currency_symbol'],
                    'estimated_delivery' => $shipment['estimated_delivery'],
                    'shipment_type' => $shipment['shipment_type']
                ];

                if (isset($shipment['store_name'])) {
                    $shipmentInfo['store_name'] = $shipment['store_name'];
                }

                $countryInfo['shipments'][] = $shipmentInfo;
                $summary['total_shipments']++;
            }

            $summary['countries'][$countryCode] = $countryInfo;
        }

        return $summary;
    }
    public function isCartChanged(Request $request)
    {
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        $current_cart_signature = count($cart->items);
        $stored_cart_signature = $checkout_order->shipping->shipping_method['cart_signature'] ?? '';
        $cart_has_changed = $current_cart_signature != $stored_cart_signature;
        if ($checkout_order->shipping) {
            $checkout_order->shipping->shipping_method['cart_signature'] = $current_cart_signature;
        }
        // Save back to session
        Session::put('checkout_order', $checkout_order);
        return response()->json([
            'status' => $cart_has_changed,
        ], 200);
    }

    public function postCheckout(Request $request)
    {

        try {
            $checkoutController = new CheckoutController();
            $webPageController = new WebPageController();
            // Initialize base data
            $location_data = Session::get('shop_logged_data', []);
            $cart = Session::get('cart');
            $checkout_order = Session::get('checkout_order');
            $countries = $this->countries();

            // Validate cart exists
            if (!$cart) {
                return response()->json(['error' => 'Cart not found'], 400);
            }

            // Check for split payment continuation - ADD THIS
            $splitContinuation = Session::get('split_payment_continuation');
            if ($splitContinuation && $request->has('place_order')) {
                // Restore the payment method and split option from continuation
                $checkout_order->payment->selected_method_id = $splitContinuation['previous_payment_method_id'];
                $checkout_order->payment->split_option = $splitContinuation['split_option'];
                $checkout_order->payment->split_payment = true;

                // Update the amount to remaining amount
                // $cart->grand_total = $splitContinuation['remaining_amount'];

                Session::put('checkout_order', $checkout_order);
                Session::put('cart', $cart);
                // Update split payment state for second transaction
                $splitState = Session::get('split_payment_state');
                if ($splitState) {
                    $splitState['current_transaction'] = 2;
                    Session::put('split_payment_state', $splitState);
                }
            }

            // Generate cart signature the same way as original code
            $current_cart_items = collect($cart->items)->map(function ($item) {
                return $item['id'] . ':' . ($item['quantity'] ?? 1);
            })->sort()->implode('|');
            $current_cart_signature = count($cart->items);

            $selectedPaymentMethod = Session::get('selected_payment_method');
            if ($checkout_order) {
                $new_properties = $webPageController->getPaymentMethodCalculations($cart, $location_data);

                foreach ($new_properties as $key => $value) {
                    $checkout_order->$key = $value;
                }
            }

            Session::put('checkout_order', $checkout_order);
            $checkout_order = Session::get('checkout_order');
            // Initialize response data
            $response_data = [
                'checkout_order' => $checkout_order,
                'countries' => $countries,
                'location_data' => $location_data,
                'cart' => $cart,
                'user_shippings' => [],
                'user_billings' => [],
                'current_cart_signature' => $current_cart_signature,
                'current_cart_items' => $current_cart_items
            ];
            $response_data['split_continuation'] = $splitContinuation ?? false;
            $response_data['show_split_continuation'] = $splitContinuation ? true : false;
            if (auth()->check()) {
                $response_data['user_shippings'] = UserShipping::where('user_id', auth()->id())
                    ->orderByDesc('primary') // true (1) will come first
                    ->get();
                $response_data['user_billings'] = UserBilling::where('user_id', auth()->id())
                    ->orderByDesc('primary')
                    ->get();
            }
            // Handle different checkout steps
            if ($request->has('continue_shipping') && $request->get('next_step') === 'shipping') {
                return $this->handleCustomerStep($request, $checkoutController, $response_data);
            }

            if ($request->has('continue_payment')) {
                return $this->handleShippingStep($request, $checkoutController, $response_data);
            }

            if ($request->has('place_order')) {
                return $this->handlePaymentStepUpdated($request, $checkoutController, $response_data);
            }

            // Default return for page load - ADD split continuation data


            return $this->buildCheckoutView($response_data);
        } catch (\Exception $e) {
            Log::error('Checkout error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'error' => 'An error occurred during checkout. Please try again.',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getPaymentSummary(Request $request)
    {
        $paymentMethod = $request->payment_method_id;
        $methodOptions = $request->method_options; // Split payment option like "60-40"
        $useRewardPoints = $request->use_reward_points;
        $useCashValue = $request->use_cash_value;
        $location_data = array();
        $webPageController = new WebPageController();
        if (Session::has('shop_logged_data')) {
            $location_data = Session::get('shop_logged_data');
        }

        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        // Update cart with checkbox selections
        if ($useRewardPoints !== null) {
            $cart->use_reward_point_status = (bool) $useRewardPoints;
            Session::put('cart', $cart);
        }

        if ($useCashValue !== null) {
            $cart->use_cash_value_status = (bool) $useCashValue;
            Session::put('cart', $cart);
        }

        // Store selected payment method and options in session for page refresh
        if ($paymentMethod) {
            Session::put('selected_payment_method', $paymentMethod);
            Session::put('selected_method_options', $methodOptions); // Store split option
        }

        $total_reward_points = 0;
        $cash_value = 0;
        $cash_total_reward = 0;
        $split_continuation = Session::get('split_payment_continuation');
        if ($split_continuation) {
            return view('web.jquery_live.payment_summary', compact(
                'checkout_order',
                'location_data',
                'cart',
                'total_reward_points',
                'cash_value',
                'cash_total_reward',
                'split_continuation'
            ));
        }
        $selectedPaymentMethod = Session::get('selected_payment_method');
        if ($checkout_order) {
            $new_properties = $webPageController->getPaymentMethodCalculations($cart, $location_data);

            foreach ($new_properties as $key => $value) {
                $checkout_order->$key = $value;
            }
        }

        Session::put('checkout_order', $checkout_order);

        return view('web.jquery_live.payment_summary', compact(
            'checkout_order',
            'location_data',
            'cart',
            'total_reward_points',
            'cash_value',
            'cash_total_reward',
        ));
    }
    /**
     * Handle customer information step
     */
    private function handleCustomerStep($request, $checkoutController, $response_data)
    {
        try {
            // Validate and process customer data
            $customer = $this->handleUser($response_data['checkout_order'], $request);
            $webPageController = new WebPageController();
            $cart = Session::get('cart');
            $location_data = Session::get('shop_logged_data');

            // Get shipping methods for the next step
            $shipping_methods = $webPageController->getEasyPostShipping($request);
            if ($shipping_methods && isset($shipping_methods['status']) && $shipping_methods['status'] == 400) {
                $response_data['shipping_methods'] = $shipping_methods;
                return $this->buildCheckoutView($response_data);
            }

            // Update checkout order with customer info
            $checkout_order = $this->buildCheckoutOrder([
                'customer' => $customer,
                'customer_status' => true,
                'shipping_tab_status' => true,
                'shipping_methods' => $shipping_methods,
                'active_tab' => 'shipping'
            ], $response_data);
            $new_properties = $webPageController->getPaymentMethodCalculations($cart, $location_data);

            foreach ($new_properties as $key => $value) {
                $checkout_order->$key = $value;
            }
            Session::put('checkout_order', $checkout_order);
            $response_data['checkout_order'] = $checkout_order;
            $response_data['shipping_methods'] = $shipping_methods;

            return $this->buildCheckoutView($response_data);
        } catch (ValidationException $e) {
            return response()->json(['messages' => $e->validator->messages()], 400);
        }
    }

    /**
     * Handle shipping information step
     */
    private function handleShippingStep($request, $checkoutController, $response_data)
    {
        try {
            $checkout_order = $response_data['checkout_order'];
            $webPageController = new WebPageController();
            // Check if cart has changed using original logic
            $stored_cart_signature = $checkout_order->shipping->shipping_method['cart_signature'] ?? '';
            $cart_has_changed = $response_data['current_cart_signature'] != $stored_cart_signature;

            if ($cart_has_changed && !empty($stored_cart_signature)) {
                Log::info('Cart has changed during checkout', [
                    'current' => $response_data['current_cart_signature'],
                    'stored' => $stored_cart_signature
                ]);

                return response()->json([
                    'cart_changed' => true,
                    'message' => 'Cart has changed. Please reselect shipping options.'
                ], 400);
            }

            // Process shipping selection
            $shipping_result = $this->processShippingSelection($request);

            if (!$shipping_result || empty($shipping_result['shipping_details'])) {
                return response()->json([
                    'error' => 'Please select shipping options for all items',
                    'shipping_selection_error' => true
                ], 400);
            }

            // Build shipping object
            $shipping = $this->buildShippingObject($request, $checkout_order, $shipping_result, $response_data['location_data']);

            // Build payment object
            $payment = $this->buildPaymentObject($request, $checkout_order);
            $shipping_methods = $webPageController->getEasyPostShipping($request);

            // Update checkout order

            $updated_checkout_order = $this->buildCheckoutOrder(
                array_merge(
                    (array) $checkout_order, // Convert object to array to preserve all existing properties
                    [
                        'customer' => $checkout_order->customer,
                        'customer_status' => true,
                        'shipping' => $shipping,
                        'shipping_tab_status' => true,
                        'shipping_status' => true,
                        'payment' => $payment,
                        'payment_tab_status' => true,
                        'active_tab' => 'payment',
                        'shipping_methods' => $shipping_methods
                    ]
                ),
                $response_data
            );
            $response_data['shipping_methods'] = $shipping_methods;


            Session::put('checkout_order', $updated_checkout_order);

            // Update cart with split payment calculations
            $this->updateCartCalculations($response_data['cart'], $shipping_result['total_shipping_cost'], $response_data['location_data']);

            $response_data['checkout_order'] = $updated_checkout_order;
            $response_data['cart'] = $response_data['cart'];

            // Get user addresses if authenticated
            if (auth()->check()) {
                $response_data['user_shippings'] = UserShipping::where('user_id', auth()->id())
                    ->orderByDesc('primary') // true (1) will come first
                    ->get();
                $response_data['user_billings'] = UserBilling::where('user_id', auth()->id())
                    ->orderByDesc('primary')
                    ->get();
            }

            return $this->buildCheckoutView($response_data);
        } catch (\Exception $e) {
            Log::error('Shipping step error: ' . $e->getMessage() . ' in ' . __METHOD__ . ' at line ' . __LINE__);

            return response()->json(['error' => 'Error processing shipping information'], 500);
        }
    }

    /**
     * Handle payment processing step
     */
    private function handlePaymentStep($request, $checkoutController, $response_data)
    {
        try {
            $checkout_order = $response_data['checkout_order'];

            // Validate payment method
            $validator = Validator::make($request->all(), [
                'payment_method' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json(['messages' => $validator->messages()], 400);
            }

            // UPDATE: Save the payment method to checkout order before processing
            $payment_method = $request->get('payment_method');
            $pay_by_squadco = $request->get('pay_by_squadco');

            // Update the payment object with the selected method
            if (!$checkout_order->payment) {
                $checkout_order->payment = new \stdClass();
            }

            $checkout_order->payment->payment_method = $payment_method;
            $checkout_order->payment->pay_by_squadco = $pay_by_squadco;

            // Save updated checkout order to session
            Session::put('checkout_order', $checkout_order);

            Log::info('Payment method updated in checkout order', [
                'payment_method' => $payment_method,
                'pay_by_squadco' => $pay_by_squadco
            ]);

            // Handle pay later option
            if ($request->get('payment_option') === 'pay_later') {
                $this->makeOrder();
                $contact_page = Page::where('page_key', 'contactTeam')->first();
                return ['cart_amount' => $response_data['cart']->grand_total, 'maximum_amount' => 0, 'page_slug' => $contact_page->slug ?? 'contact'];
            }

            // Check maximum amount
            $site_info = Setting::first();
            $maximum_amount = $site_info->price_range ?? PHP_INT_MAX;
            if ($response_data['cart'] && $response_data['cart']->grand_total > $maximum_amount) {
                $this->makeOrder();
                $contacTeam = Page::where('page_key', 'contactTeam')->first();
                return ['cart_amount' => $response_data['cart']->grand_total, 'maximum_amount' => $maximum_amount, 'page_slug' => $contacTeam->slug ?? 'contact'];
            }

            // Process payment based on method
            switch ($payment_method) {
                case 'pay_by_stripe_card':
                    $redirect_url = $this->stripeCard($response_data['cart']);
                    break;

                case 'pay_by_paypal':
                    $redirect_url = $this->payWithPaypal();
                    break;

                case 'pay_by_flutter':
                    $redirect_url = $this->flutterWave($response_data['cart']);
                    break;

                case 'pay_by_squadco':
                    return $this->handleSquadcoPayment($request, $checkout_order);

                case 'btc':
                    $this->makeOrder();
                    $redirect_url = $this->initiateBTCPay($request);
                    break;

                case 'flutterwave':
                    $this->makeOrder();
                    $redirect_url = $this->initiateFlutterPay(
                        $request,
                        $checkout_order->payment,
                        $checkout_order->customer,
                        $response_data['location_data']
                    );
                    break;

                default:
                    return response()->json(['error' => 'Invalid payment method'], 400);
            }

            return response()->json(['redirect_url' => $redirect_url]);
        } catch (\Exception $e) {
            Log::error('Payment step error: ' . $e->getMessage());
            return response()->json(['error' => 'Error processing payment'], 500);
        }
    }

    /**
     * Handle Squadco payment processing
     */
    private function handleSquadcoPayment($request, $checkout_order)
    {
        $validator = Validator::make($request->all(), [
            'pay_by_squadco' => 'required|string|in:pay_by_squadco_usd,pay_by_squadco_local',
        ]);

        if ($validator->fails()) {
            return response()->json(['messages' => $validator->messages()], 400);
        }

        // Validate split payment if selected
        if ($request->get('split_payment') === 'split_payment') {
            $split_validator = Validator::make($request->all(), [
                'split_payment_amount' => 'required|string|in:50_50,60_40,80_20',
            ], [
                'split_payment_amount.required' => 'You have to choose split payment option',
            ]);

            if ($split_validator->fails()) {
                return response()->json(['messages' => $split_validator->messages()], 400);
            }
        }

        $data = $this->squadcoPay($request);
        return response()->json($data);
    }
    public function squadcoPay($request)
    {
        try {
            // Get necessary session data
            $cart = Session::get('cart');
            $location_data = Session::get('shop_logged_data');
            $checkout_order = Session::get('checkout_order');
            $shippingAmount_local = session('shippingAmount_local', 0);
            $shippingAmount = Session::get('shippingAmount') ?? 0;
            $cart_totalDiscount = $cart->totalDiscount ?? 0;
            if (!$cart || !$checkout_order) {
                throw new \Exception('Invalid cart or checkout data');
            }
            // Get product descriptions
            $description = [];
            foreach ($cart->items as $value) {
                $description[] = $value->product_item_sku . '-' . $value->product_title . ' ' . $value->title . ' ' . $value->sub_title;
            }
            // dd($checkout_order);
            $currency = ($checkout_order->payment->pay_by_squadco == 'pay_by_squadco_local') ? 'NGN' : 'USD';
            $shippingAmount = Session::get('shippingAmount', 0);
            $remainingCartAmount = max(0, ($cart->grand_total ?? 0) - ($cart->paid_grand_total ?? 0));
            $amount = intval($remainingCartAmount + $shippingAmount);  // Add shipping for payment calculation only

            if ($request->pay_by_squadco != 'pay_by_squadco_local' &&  $cart_totalDiscount > 0) {
                if ($cart_totalDiscount > 0) {
                    $amount_totalDiscount = intval(($cart_totalDiscount) * 100);
                }
                $amount = $amount - $amount_totalDiscount;
            }
            $discount_amt = 0;
            $amount_due = false;
            $transaction_ref = $this->getRandomStringUniqid();
            // Initialize payment variables
            //  dd($transaction_ref);
            // Handle discount calculation for non-local payment
            if (
                Session::has('shop_logged_data') &&
                $request->pay_by_squadco != 'pay_by_squadco_local' &&
                $cart->paid_grand_total == 0
            ) {
                $payment_method = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_squadco');
                if ($payment_method->discount_price) {
                    if ($payment_method->discount_type == 1) {
                        $discount_amt = ($cart->total * $payment_method->discount_price) / 100;
                    } else {
                        $discount_amt = $payment_method->discount_price;
                    }
                    $amount = $amount - $discount_amt;
                    if ($shippingAmount > 0) {
                        $amount = $amount + $shippingAmount;
                    }
                    $amount = intval($amount * 100);
                }
            }
            $shippingAmount_local = session('shippingAmount_local', 0);
            if (
                isset($cart->grand_total_local, $cart->totalDiscount_local) &&
                $cart->grand_total_local > 0 &&
                $shippingAmount_local > 0 &&
                $cart->totalDiscount_local > 0
            ) {
                $cart->grand_total_local_cal = ($cart->grand_total_local + $shippingAmount_local) - $cart->totalDiscount_local;
            } elseif (
                isset($cart->grand_total_local, $cart->totalDiscount_local) &&
                $cart->grand_total_local > 0 &&
                $cart->totalDiscount_local > 0
            ) {
                $cart->grand_total_local_cal = $cart->grand_total_local  - $cart->totalDiscount_local;
            } elseif ((empty($cart->totalDiscount_local) || !isset($cart->totalDiscount_local))  && $shippingAmount_local > 0) {
                $cart->grand_total_local_cal = $cart->grand_total_local + $shippingAmount_local;
            }
            $grandTotalLocal = isset($cart->grand_total_local_cal) ? $cart->grand_total_local_cal : $cart->grand_total_local;
            // Handle local payment calculation
            if (Session::has('shop_logged_data') && $request->pay_by_squadco == 'pay_by_squadco_local') {
                $currency = $this->getLocalCurrencyCode($location_data->country);
                $amount = round($grandTotalLocal - $cart->paid_grand_total_local, 2);
                // Handle maximum amount limit
                if ($amount >= 5000000) {
                    $amount = 5000000;
                    $amount_due = true;
                } else if ($request->split_payment_amount) {
                    $amount_due = true;
                    switch ($request->split_payment_amount) {
                        case '50_50':
                            $amount = round($grandTotalLocal * 0.5, 2);
                            break;
                        case '60_40':
                            $amount = round($grandTotalLocal * 0.6, 2);
                            break;
                        case '80_20':
                            $amount = round($grandTotalLocal * 0.8, 2);
                            break;
                    }
                }
                $amount = (int) round($amount * 100); // Convert NGN to kobo
            }
            // Get customer payment details
            $pay_email = $checkout_order->customer->email;
            $customer_name = $checkout_order->customer->first_name . ' ' . $checkout_order->customer->last_name;
            $phone_number = $checkout_order->customer->tel_cell;
            // Handle different billing address
            if ($checkout_order->payment->payment_same_as_contact == 0) {
                $user_billing = UserBilling::where('id', $request->user_billing_id)->first();

                if ($user_billing) {
                    $pay_email = $user_billing->email;
                    $customer_name = $user_billing->first_name . ' ' . $user_billing->last_name;
                    $phone_number = $user_billing->tel_cell;
                }
            }
            //dd($pay_email);
            // Update cart data
            $cart->intent_pay = $amount;
            $cart->discount_amt = $discount_amt;
            $cart->transaction_refs = array($transaction_ref);
            $cart->amount_due = $amount_due;
            // Handle split payment cart updates
            if ($amount_due) {
                $cart->cart_total_local = $cart->grand_total_local;
                if (!in_array($transaction_ref, $cart->transaction_refs)) {
                    array_push($cart->transaction_refs, $transaction_ref);
                }
            }
            // Convert amount to cents for Squadco
            $amount = intval(round($amount));
            // Prepare payment payload
            $payment_payload = [
                "authorization_key" => app('App\Http\Controllers\Controller')->transactionMethod('pay_by_squadco')->private_key,
                "customer_name" => $customer_name,
                "phone_number" => $phone_number,
                "amount" => $amount,
                "email" => $pay_email,
                "currency" => $currency,
                "initiate_type" => "inline",
                "transaction_ref" => $transaction_ref,
                "callback_url" => URL::route('squadco'),
                "pass_charge" => false,
                "payment_channels" => ['card', 'bank', 'ussd', 'transfer'],
                "metadata" => (object)$description,
            ];
            // Update session data
            Session::put('cart', $cart);
            Session::put('squadco_payment_ref', $transaction_ref);
            // Create order if doesn't exist
            if (empty($cart->order)) {
                $this->makeOrder();
            }
            return $payment_payload;
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Payment initialization failed',
                'message' => $e->getMessage()
            ], 422);
        }
    }
    /**
     * Build shipping object from request data
     */
    private function buildShippingObject($request, $checkout_order, $shipping_result, $location_data)
    {
        $customer = $checkout_order->customer;
        $same_address = $request->get('shipping_same_address', 0);

        return (object)[
            'first_name' => $same_address &&  $customer ? $customer->first_name : $request->get('shipping_first_name'),
            'last_name' => $same_address &&  $customer ? $customer->last_name : $request->get('shipping_last_name'),
            'email' => $same_address &&  $customer ? $customer->email : $request->get('shipping_email'),
            'tel_cell' => $same_address &&  $customer ? $customer->tel_cell : preg_replace('/[^\d+]/', '', $request->get('shipping_tel_cell')),
            'company_name' => $same_address &&  $customer ? $customer->company_name : $request->get('shipping_company_name'),
            'city' => $same_address &&  $customer ? $customer->city : $request->get('shipping_city'),
            'state' => $same_address &&  $customer ? $customer->state : $request->get('shipping_state'),
            'country' => $same_address &&  $customer ? $customer->country : $request->get('shipping_country'),
            'country_name' => $same_address &&  $customer ? $customer->country_name : $this->countries($request->get('shipping_country')),
            'postal_code' => $same_address &&  $customer ? $customer->postal_code : $request->get('shipping_postal_code'),
            'address' => $same_address &&  $customer ? $customer->address : $request->get('shipping_address_line_1'),
            'address_2' => $same_address &&  $customer ? $customer->address_2 : $request->get('shipping_address_line_2'),
            'shipping_method' => $shipping_result,
            'shipping_same_address' => $same_address,
            'shipping_amount' => $shipping_result['total_shipping_cost'] ?? 0,
            'shipping_amount_local' => $location_data ? ($shipping_result['total_shipping_cost'] ?? 0) * $location_data->currency_rate : 0,
            'user_shipping_id' => $request->get('user_shipping_id'),
            'user_shipping' => $request->get('user_shipping_id') ? UserShipping::find($request->get('user_shipping_id')) : null,
        ];
    }

    /**
     * Build payment object from request data
     */
    private function buildPaymentObject($request, $checkout_order)
    {
        $customer = $checkout_order->customer;
        $same_as_contact = $request->get('payment_same_as_contact', 0);

        return (object)[
            'first_name' => $same_as_contact &&  $customer ? $customer->first_name : $request->get('payment_first_name'),
            'last_name' => $same_as_contact &&  $customer ? $customer->last_name : $request->get('payment_last_name'),
            'email' => $same_as_contact &&  $customer ? $customer->email : $request->get('payment_email'),
            'tel_cell' => $same_as_contact &&  $customer ? $customer->tel_cell : preg_replace('/[^\d+]/', '', $request->get('payment_tel_cell')),
            'company_name' => $same_as_contact &&  $customer ? $customer->company_name : $request->get('payment_company_name'),
            'city' => $same_as_contact &&  $customer ? $customer->city : $request->get('payment_city'),
            'state' => $same_as_contact &&  $customer ? $customer->state : $request->get('payment_state'),
            'country' => $same_as_contact &&  $customer ? $customer->country : $request->get('payment_country'),
            'country_name' => $same_as_contact &&  $customer ? $customer->country_name : $this->countries($request->get('payment_country')),
            'postal_code' => $same_as_contact &&  $customer ? $customer->postal_code : $request->get('payment_postal_code'),
            'address' => $same_as_contact &&  $customer ? $customer->address : $request->get('payment_address_line_1'),
            'address_2' => $same_as_contact &&  $customer ? $customer->address_2 : $request->get('payment_address_line_2'),
            'payment_method' => $request->get('payment_method'), // Make sure this is always captured
            'pay_by_squadco' => $request->get('pay_by_squadco'),
            'payment_same_as_contact' => $same_as_contact,
            'user_billing_id' => $request->get('user_billing_id'),
            'user_billing' => $request->get('user_billing_id') ? UserBilling::find($request->get('user_billing_id')) : null,
            'payment_option' => $request->get('payment_option'),
        ];
    }

    /**
     * Build checkout order object
     */
    private function buildCheckoutOrder($data, $response_data)
    {

        $defaults = [
            'customer' => null,
            'customer_tab_status' => false,
            'customer_status' => false,
            'shipping' => null,
            'shipping_methods' => null,
            'shipping_tab_status' => false,
            'shipping_status' => false,
            'payment' => null,
            'payment_tab_status' => false,
            'payment_status' => false,
            'location_data' => $response_data['location_data'],
            'cart' => $response_data['cart'],
            'active_tab' => 'customer',
        ];

        return (object)array_merge($defaults, $data);
    }

    /**
     * Update cart calculations for split payments
     */
    private function updateCartCalculations($cart, $shipping_cost, $location_data)
    {
        $currency_rate = $location_data->currency_rate ?? 1;
        $local_currency_symbol = $location_data->currency_symbol ?? '$';

        $shipping_local = $shipping_cost * $currency_rate;
        $grand_total = $cart->total + $shipping_cost;
        $grand_total_local = $cart->total_local + $shipping_local;

        // Apply discounts if they exist
        if (isset($cart->totalDiscount) && $cart->totalDiscount > 0) {
            $grand_total -= $cart->totalDiscount;
        }

        if (isset($cart->totalDiscount_local) && $cart->totalDiscount_local > 0) {
            $grand_total_local -= $cart->totalDiscount_local;
        }

        // Calculate split payment options
        $cart->amount_50_50 = getDefaultCurrencySymbol() . number_format($grand_total * 0.5, 2) . ' - ' . getDefaultCurrencySymbol() . number_format($grand_total * 0.5, 2);
        $cart->amount_50_50_local = $local_currency_symbol . number_format($grand_total_local * 0.5, 2) . ' - ' . $local_currency_symbol . number_format($grand_total_local * 0.5, 2);

        $cart->amount_60_40 = getDefaultCurrencySymbol() . number_format($grand_total * 0.6, 2) . ' - ' . getDefaultCurrencySymbol() . number_format($grand_total * 0.4, 2);
        $cart->amount_60_40_local = $local_currency_symbol . number_format($grand_total_local * 0.6, 2) . ' - ' . $local_currency_symbol . number_format($grand_total_local * 0.4, 2);

        $cart->amount_80_20 = getDefaultCurrencySymbol() . number_format($grand_total * 0.8, 2) . ' - ' . getDefaultCurrencySymbol() . number_format($grand_total * 0.2, 2);
        $cart->amount_80_20_local = $local_currency_symbol . number_format($grand_total_local * 0.8, 2) . ' - ' . $local_currency_symbol . number_format($grand_total_local * 0.2, 2);

        Session::put('cart', $cart);
    }

    /**
     * Build checkout view response
     */
    private function buildCheckoutView($response_data)
    {
        extract($response_data);
        return view('web.jquery_live.checkout_order', compact(
            'checkout_order',
            'countries',
            'location_data',
            'cart',
            'user_shippings',
            'user_billings',
            'shipping_methods',
            'current_cart_signature',
            'split_continuation',
            'show_split_continuation'
        ));
    }

    public function stripeCard($cart)
    {
        // Extensive null and type checking
        if (!$cart || !Session::has('cart')) {
            Log::error('Cart is null or not in session');
            return null;
        }
        $checkoutOrder = Session::get('checkout_order');
        if (!$checkoutOrder) {
            Log::error('Checkout order is null');
            return null;
        }
        // Null-safe shipping method retrieval
        $shippingMethod = optional($checkoutOrder->shipping)->shipping_method ?? 'shipping_standard';
        $shippingAmount = $checkoutOrder->shipping->shipping_amount ?? 0;
        // Determine shipping text with null-safe approach
        $shippingText = $checkoutOrder->shipping->shipping_method_data['carrier'] ?? 'shipping_standard';
        // Initialize Stripe Client with error checking
        $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_stripe_card');
        if (!$transactionMethod) {
            Log::error('Transaction method is null');
            return null;
        }
        $stripe = new \Stripe\StripeClient($transactionMethod->private_key ?? '');
        // Prepare line items with null-safe checks
        $description = [];
        $priceData = [];
        // Null-safe cart items iteration
        $cartItems = $cart->items ?? [];
        foreach ($cartItems as $item) {
            // Null-safe item property access
            $itemDescription = ($item->product_item_sku ?? 'N/A') . '-' .
                ($item->product_title ?? '') . ' ' .
                ($item->title ?? '') . ' ' .
                ($item->sub_title ?? '');
            $description[] = trim($itemDescription);
            // Prepare shipping options
            $shipping = [
                [
                    'shipping_rate_data' => [
                        'type' => 'fixed_amount',
                        'fixed_amount' => [
                            'amount' => max(0, intval($shippingAmount * 100)),
                            'currency' => 'usd',
                        ],
                        'display_name' => $shippingText,
                    ],
                ],
            ];
            // Null-safe image handling
            $images = [];
            if ($item->product_item_images) {
                try {
                    $unserializedImages = unserialize($item->product_item_images);
                    $images = array_filter([
                        $item->product_item_image,
                        ...(is_array($unserializedImages) ? $this->getItemImages($unserializedImages) : [])
                    ]);
                } catch (\Exception $e) {
                    Log::error('Image unserialization error: ' . $e->getMessage());
                    $images = [$item->product_item_image];
                }
            }
            $unitPrice = isset($item->discounted_price) && $item->discounted_price > 0
                ? $item->discounted_price
                : ($item->price ?? 0);
            $priceData[] = [
                'price_data' => [
                    'product_data' => [
                        'name' => trim($itemDescription),
                        'images' => $images ?: [],
                    ],
                    'unit_amount' => max(0, intval(($unitPrice ?? 0) * 100)),
                    'currency' => 'USD',
                ],
                'quantity' => max(1, intval($item->quantity ?? 1)),
            ];
        }
        // Null-safe coupon creation
        $coupon = null;
        if (($cart->paid_grand_total ?? 0) == 0 &&
            ($transactionMethod->discount_price ?? 0) > 0
        ) {
            try {
                $coupon = $transactionMethod->discount_type == 1
                    ? $stripe->coupons->create([
                        'percent_off' => $transactionMethod->discount_price,
                        'duration' => 'once',
                        'name' => 'Transaction Discount',
                    ])
                    : $stripe->coupons->create([
                        'amount_off' => intval($transactionMethod->discount_price) * 100,
                        'duration' => 'once',
                        'name' => 'Transaction Discount',
                        'currency' => 'USD',
                    ]);
            } catch (\Exception $e) {
                Log::error('Coupon creation error: ' . $e->getMessage());
            }
        }
        // Determine customer email with null-safe checks
        $payEmail = optional($checkoutOrder->payment)->email ?? '';
        if (optional($checkoutOrder->payment)->payment_same_as_contact == 0) {
            $userBilling = UserBilling::find(optional($checkoutOrder->payment)->user_billing_id);
            $payEmail = $userBilling->email ?? $payEmail;
        }
        // Prepare Stripe Checkout Session parameters
        $checkoutSessionParams = [
            'ui_mode' => 'hosted',
            'mode' => 'payment',
            'customer_email' => $payEmail,
            'payment_method_types' => ['card', 'alipay'],
            'line_items' => $priceData,
            'success_url' => URL::route('stripe'),
            'cancel_url' => URL::route('stripe'),
            'shipping_options' => $shipping,
            'payment_intent_data' => [
                'description' => implode('|', array_filter($description))
            ]
        ];
        // Add coupon if exists
        if ($coupon) {
            $checkoutSessionParams['discounts'] = [
                ['coupon' => $coupon->id]
            ];
        }
        //    dd($checkoutSessionParams);
        $checkoutSession = $stripe->checkout->sessions->create($checkoutSessionParams);
        // Store checkout session in session
        Session::put('stripe_checkout_session', $checkoutSession);
        // Create order if not exists
        if (empty($cart->order)) {
            $this->makeOrder();
        }
        if (!empty($cart->promo_code)) { // Check if promo_code is not empty
            CustomerPromo::create([
                'promo_id' => $cart->promo_code,
                'customer_id' => $cart->coupan_user_id
            ]);
        }
        return $checkoutSession->url;
    }
    public function makeOrder()
    {
        \Log::info('order function started');

        $auth_user = Auth()->user();
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        if (Session::has('checkout_order')) {
            // Handle user information
            if (Auth::user()) {
                $user = User::findOrFail(Auth::user()->id);
                $user->first_name = $checkout_order->customer->first_name;
                $user->last_name = $checkout_order->customer->last_name;
                $user->tel_cell = $checkout_order->customer->tel_cell;
                $user->company_name = $checkout_order->customer->company_name;
                $user->city = $checkout_order->customer->city;
                $user->state = $checkout_order->customer->state;
                $user->country = $checkout_order->customer->country;
                $user->postal_code = $checkout_order->customer->postal_code;
                $user->address = $checkout_order->customer->address;
                $user->address_2 = $checkout_order->customer->address_2;
                $user->email_notifications = $checkout_order->customer->email_notifications ?? 0;
                $user->sms_notifications = $checkout_order->customer->sms_notifications ?? 0;
                $user->update();
            } else {
                $user = User::where('email', $checkout_order->customer->email)->first();
                if (empty($user)) {
                    $user = new User;
                    $user->first_name = $checkout_order->customer->first_name;
                    $user->last_name = $checkout_order->customer->last_name;
                    $user->email = $checkout_order->customer->email;
                    $user->tel_cell = $checkout_order->customer->tel_cell;
                    $user->company_name = $checkout_order->customer->company_name;
                    $user->city = $checkout_order->customer->city;
                    $user->state = $checkout_order->customer->state;
                    $user->country = $checkout_order->customer->country;
                    $user->postal_code = $checkout_order->customer->postal_code;
                    $user->address = $checkout_order->customer->address;
                    $user->address_2 = $checkout_order->customer->address_2;
                    $user->email_notifications = $checkout_order->customer->email_notifications;
                    $user->sms_notifications = $checkout_order->customer->sms_notifications;
                    $user->save();

                    $group_map = new UserGroupMap();
                    $group_map->user_id = $user->id;
                    $group_map->user_group_id = 2;
                    $group_map->save();

                    $user_group_map = UserGroupMap::where('user_id', $user->id)->first();
                    $alert_preferences = AlertPreference::where('user_group_id', 'LIKE', '%' . $user_group_map->user_group_id . '%')->get();
                    $value_key_array = array();
                    foreach ($alert_preferences as $value) {
                        $value_key_array[$value->key] = [
                            'email' => $user->email_notifications ? 1 : 0,
                            'sms' => $user->sms_notifications ? 1 : 0,
                        ];
                    }
                    $user = User::findOrFail($user->id);
                    $user->alert_preferences = serialize($value_key_array);
                    $user->update();
                }
            }

            // Handle shipping address
            $user_shipping_id = null;

            // Check if user selected an existing shipping address
            if (!empty($checkout_order->shipping->user_shipping_id)) {
                // Use the selected shipping address
                $user_shipping_id = $checkout_order->shipping->user_shipping_id;
                \Log::info('Using selected shipping address ID: ' . $user_shipping_id);
            } else {
                // Create new shipping address
                $user_shipping = new UserShipping();
                $user_shipping->user_id = $auth_user ? Auth()->user()->id : $user->id;

                if ($checkout_order->shipping->shipping_same_address != 0) {
                    // Use customer address as shipping address
                    $user_shipping->first_name = $checkout_order->shipping->first_name;
                    $user_shipping->last_name = $checkout_order->shipping->last_name;
                    $user_shipping->email = $checkout_order->shipping->email;
                    $user_shipping->tel_cell = $checkout_order->shipping->tel_cell;
                    $user_shipping->company_name = $checkout_order->shipping->company_name;
                    $user_shipping->country = $checkout_order->shipping->country;
                    $user_shipping->postal_code = $checkout_order->shipping->postal_code;
                    $user_shipping->city = $checkout_order->shipping->city;
                    $user_shipping->state = $checkout_order->shipping->state;
                    $user_shipping->address = $checkout_order->shipping->address;
                    $user_shipping->address_2 = $checkout_order->shipping->address_2;
                } else {
                    // Use separate shipping address
                    $user_shipping->first_name = $checkout_order->customer->first_name;
                    $user_shipping->last_name = $checkout_order->customer->last_name;
                    $user_shipping->email = $checkout_order->customer->email;
                    $user_shipping->tel_cell = $checkout_order->customer->tel_cell;
                    $user_shipping->company_name = $checkout_order->customer->company_name;
                    $user_shipping->country = $checkout_order->customer->country;
                    $user_shipping->postal_code = $checkout_order->customer->postal_code;
                    $user_shipping->city = $checkout_order->customer->city;
                    $user_shipping->state = $checkout_order->customer->state;
                    $user_shipping->address = $checkout_order->customer->address;
                    $user_shipping->address_2 = $checkout_order->customer->address_2;
                }

                $user_shipping->shipping_method = serialize($checkout_order->shipping->shipping_method);
                $user_shipping->save();
                $user_shipping_id = $user_shipping->id;
                \Log::info('Created new shipping address ID: ' . $user_shipping_id);
            }

            // Handle billing address
            $user_billing_id = null;

            // Check if user selected an existing billing address
            if (!empty($checkout_order->payment->user_billing_id)) {
                // Use the selected billing address
                $user_billing_id = $checkout_order->payment->user_billing_id;
                \Log::info('Using selected billing address ID: ' . $user_billing_id);
            } else {
                // Create new billing address
                $user_billing = new UserBilling();
                $user_billing->user_id = $auth_user ? Auth()->user()->id : $user->id;

                if ($checkout_order->payment->payment_same_as_contact != 0) {
                    // Use payment address
                    $user_billing->first_name = $checkout_order->payment->first_name;
                    $user_billing->last_name = $checkout_order->payment->last_name;
                    $user_billing->email = $checkout_order->payment->email;
                    $user_billing->tel_cell = $checkout_order->payment->tel_cell;
                    $user_billing->company_name = $checkout_order->payment->company_name;
                    $user_billing->country = $checkout_order->payment->country;
                    $user_billing->postal_code = $checkout_order->payment->postal_code;
                    $user_billing->city = $checkout_order->payment->city;
                    $user_billing->state = $checkout_order->payment->state;
                    $user_billing->address = $checkout_order->payment->address;
                    $user_billing->address_2 = $checkout_order->payment->address_2;
                } else {
                    // Use customer address as billing address
                    $user_billing->first_name = $checkout_order->customer->first_name;
                    $user_billing->last_name = $checkout_order->customer->last_name;
                    $user_billing->email = $checkout_order->customer->email;
                    $user_billing->tel_cell = $checkout_order->customer->tel_cell;
                    $user_billing->company_name = $checkout_order->customer->company_name;
                    $user_billing->country = $checkout_order->customer->country;
                    $user_billing->postal_code = $checkout_order->customer->postal_code;
                    $user_billing->city = $checkout_order->customer->city;
                    $user_billing->state = $checkout_order->customer->state;
                    $user_billing->address = $checkout_order->customer->address;
                    $user_billing->address_2 = $checkout_order->customer->address_2;
                }

                $user_billing->payment_option = $checkout_order->payment->payment_option;

                $user_billing->save();
                $user_billing_id = $user_billing->id;
                \Log::info('Created new billing address ID: ' . $user_billing_id);
            }

            // Create the order
            $order = new Order;
            $order->customer_id = $auth_user ? Auth()->user()->id : $user->id;
            $order->created_by_id = $auth_user ? Auth()->user()->id : $user->id;
            $order->user_shipping_id = $user_shipping_id; // Use the correct shipping ID
            $order->user_billing_id = $user_billing_id;   // Use the correct billing ID
            $order->payment_proof = $checkout_order->payment->payment_option;
            $order->order_no = $this->getRandomStringUniqid();
            $order->order_date = Carbon::now();
            $order->ip = $this->getClientIpAddress();
            $order->sub_total_amount = $cart->total;
            $order->total_amount = $cart->grand_total;
            $order->sales_tax_amount = 0;
            $order->shipping_amount = $checkout_order->shipping->shipping_amount;
            $order->shipping_method = serialize($checkout_order->shipping->shipping_method);
            $selectedPaymentMethod = Session::get('selected_payment_method');

            $PaymentMethod = \App\Models\PaymentMethod::find($selectedPaymentMethod);
            $order->payment_option = $PaymentMethod->title;

            $order->payment_method =  $selectedPaymentMethod;
            $order->status = 1;
            $order->save();

            // Create order items
            foreach ($cart->items as $cart_value) {
                foreach ($cart_value->cart_items as $sku_value) {
                    $order_item = new OrderItem;
                    $order_item->order_id = $order->id;
                    $order_item->product_id = $sku_value->product_id;
                    $order_item->product_item_id = $sku_value->id;
                    $order_item->price = $sku_value->price;
                    $order_item->sku = $sku_value->product_item_sku;
                    $order_item->save();

                    $product_item = ProductItem::findOrFail($sku_value->id);
                    if ($cart->amount_due == false) {
                        $product_item->stock_status = 'sold';
                        $product_item->partial_sale = 0;
                        $product_item->inventory_status = 'incoming_sold';
                        $product_item->update();
                    } else {
                        $product_item->stock_status = 'sold';
                        $product_item->partial_sale = 1;
                        $product_item->inventory_status = 'incoming_sold';
                        $product_item->update();
                    }
                }
            }

            $order->shipping_method_option = serialize($checkout_order->shipping->shipping_method);
            $cart->order = $order;
            $cart->order_create = true;
            $cart->user = $user;

            Session::put(['cart' => $cart]);

            \Log::info('Order created successfully with ID: ' . $order->id);
            \Log::info('Using shipping address ID: ' . $user_shipping_id);
            \Log::info('Using billing address ID: ' . $user_billing_id);
        }
        $this->recordCouponUsageForOrder($order, $user->id);
        return $order;
    }
    public function getItemImages($items)
    {
        foreach ($items as $value) {
            $images[] = asset('storage/products/' . $value->file_path);
        }
        return $images;
    }
    function getRandomStringUniqid($length = 16)
    {
        $string = uniqid(rand());
        $randomString = substr($string, 0, $length);
        return $randomString;
    }
    protected function recordCouponUsageForOrder($order, $user)
    {
        $cart = Session::get('cart');

        if ($cart && isset($cart->applied_coupon)) {
            $this->couponService->recordCouponUsage(
                $cart->applied_coupon['code'],
                $user ? $user : null,
                $order->id
            );
        }
    }
    public function getCartTotals()
    {
        $cart = Session::get('cart');
        $location_data = Session::get('shop_logged_data');

        if (!$cart) {
            return response()->json(['error' => 'No cart found'], 404);
        }

        $totals = [
            'subtotal' => number_format($cart->total ?? 0, 2),
            'discount' => number_format($cart->totalDiscount ?? 0, 2),
            'grand_total' => number_format($cart->grand_total ?? 0, 2),
            'subtotal_local' => number_format($cart->total_local ?? 0, 2),
            'discount_local' => number_format($cart->totalDiscount_local ?? 0, 2),
            'grand_total_local' => number_format($cart->grand_total_local ?? 0, 2),
            'currency_symbol' => getDefaultCurrencySymbol(),
            'local_currency_symbol' => $location_data->currency_symbol ?? '',
            'applied_coupon' => $cart->applied_coupon ?? null,
            'free_shipping' => $cart->freeShipping ?? false,
            'shipping_amount' => $cart->shipping_amount ?? 0
        ];

        return response()->json($totals);
    }
    protected function validateCartForCheckout()
    {
        $cart = Session::get('cart');

        if (!$cart || empty($cart->items)) {
            throw new \Exception('Cart is empty');
        }

        // Re-validate applied coupon if exists
        if (isset($cart->applied_coupon)) {
            try {
                $user = Auth::user();
                $this->couponService->validateCoupon($cart->applied_coupon['code'], $user);
            } catch (\Exception $e) {
                // Remove invalid coupon
                $this->couponService->removeCouponFromCart();
                throw new \Exception('Your applied coupon is no longer valid: ' . $e->getMessage());
            }
        }

        return true;
    }
    protected function validateCartTotals($expectedTotal = null)
    {
        $cart = Session::get('cart');

        if (!$cart) {
            throw new Exception('Cart not found');
        }

        // Recalculate totals to ensure accuracy
        $calculatedTotal = $cart->total - ($cart->totalDiscount ?? 0);

        if ($expectedTotal && abs($calculatedTotal - $expectedTotal) > 0.01) {
            throw new Exception('Cart total mismatch. Please refresh and try again.');
        }

        return $calculatedTotal;
    }
    public function applyCheckoutCoupon(Request $request)
    {
        try {
            $request->validate([
                'coupon_code' => 'required|string|max:255'
            ]);

            $user = Auth::user();
            $couponCode = strtoupper(trim($request->coupon_code));

            $result = $this->couponService->applyCouponToCart($couponCode, $user);

            // Update checkout order totals
            $this->updateCheckoutOrderTotals();

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result,
                'redirect' => false
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
    protected function updateCheckoutOrderTotals()
    {
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        if ($cart && $checkout_order) {
            // Update any necessary checkout order calculations
            Session::put('checkout_order', $checkout_order);
        }
    }
    public function payWithPaypal()
    {
        // Retrieve essential data with null checks
        $checkout_page = Page::where('page_key', 'checkout')->firstOrFail();
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        // Validate critical data
        if (!$cart || !$checkout_order) {
            throw new \Exception('Cart or checkout order is missing.');
        }
        $this->makeOrder();
        // Calculate cart amount with null-safe operations
        $cart_amount = max(0, $cart->grand_total - ($cart->paid_grand_total ?? 0));
        $item_array = [];
        $description = [];
        // Validate cart items
        if (empty($cart->items)) {
            throw new \Exception('No items in the cart.');
        }
        // Ensure cart->items is iterable
        if (!is_array($cart->items) && !$cart->items instanceof \Traversable) {
            throw new \Exception('Cart items are not iterable.');
        }
        // Create the item list with comprehensive error handling
        foreach ($cart->items as $value) {
            // Ensure all required fields exist
            $itemName = trim(
                ($value->product_item_sku ?? 'N/A') . '-' .
                    ($value->product_title ?? '') . ' ' .
                    ($value->title ?? '') . ' ' .
                    ($value->sub_title ?? '')
            );
            $description[] = $itemName;
            // Validate item price and quantity
            $itemPrice = max(0, floatval($value->price ?? 0));
            $itemQuantity = max(1, intval($value->quantity ?? 1));
            $item = new Item();
            $item->setName($itemName)
                ->setCurrency('USD')
                ->setQuantity(max(1, intval($value->quantity ?? 1)))
                ->setPrice(number_format(max(0, floatval($value->price ?? 0)), 2, '.', ''));
            $item_array[] = $item;
        }
        // Handle discounts with comprehensive checks
        $discount_amt = 0;
        $transactionMethod = app('App\Http\Controllers\Controller')->transactionMethod('pay_by_paypal');
        if (($cart->paid_grand_total ?? 0) == 0 && $transactionMethod) {
            $discountPrice = $transactionMethod->discount_price ?? 0;
            if ($discountPrice > 0) {
                $discount_amt = $transactionMethod->discount_type == 1
                    ? ($cart->total * $discountPrice) / 100
                    : $discountPrice;
            }
            $cart_amount = max(0, $cart_amount - $discount_amt);
        }
        // Validate shipping information
        $shippingAmount = max(0, floatval(optional($checkout_order->shipping)->shipping_amount ?? 0));
        // Prepare the payer
        $payer = new Payer();
        $payer->setPaymentMethod('paypal');
        // Prepare the amount details with strict validation
        $amountDetails = [
            'subtotal' => number_format(max(0, $cart->total ?? 0), 2, '.', ''),
            'tax' => '0.00',
            'shipping' => number_format($shippingAmount, 2, '.', ''),
            'discount' => number_format($discount_amt, 2, '.', ''),
        ];
        $totalAmount = max(0.01, round(
            ($cart->total ?? 0) + $shippingAmount - $discount_amt,
            2
        ));
        $amount = new Amount();
        $amount->setCurrency('USD')
            ->setDetails($amountDetails)
            ->setTotal(number_format($totalAmount, 2, '.', ''));
        // Prepare the item list
        $item_list = new ItemList();
        $item_list->setItems($item_array);
        // Create the transaction
        $transaction = new Transaction();
        $transaction->setAmount($amount)
            ->setItemList($item_list)
            ->setDescription(implode('|', array_filter($description)));
        // Set the redirect URLs
        $redirect_urls = new RedirectUrls();
        $redirect_urls->setReturnUrl(URL::route('paypal'))
            ->setCancelUrl(URL::route('paypal'));
        // Create the payment
        $payment = new Payment();
        $payment->setIntent('sale')
            ->setPayer($payer)
            ->setRedirectUrls($redirect_urls)
            ->setTransactions([$transaction]);
        // Extensive logging for debugging
        \Log::info('PayPal Payment Request Details', [
            'cart_amount' => $cart_amount,
            'total_items' => count($item_array),
            'discount_amount' => $discount_amt,
            'shipping_amount' => $shippingAmount,
            'payment_details' => $payment->toArray()
        ]);
        // $payment->create($this->_api_context);
        // dd($payment);
        try {
            // Create payment with error handling
            $payment->create($this->_api_context);
            // Store PayPal payment ID in session
            Session::put('paypal_payment_id', $payment->getId());
            // Find approval URL
            // $redirect_url = null;
            foreach ($payment->getLinks() as $link) {
                if ($link->getRel() == 'approval_url') {
                    $redirect_url = $link->getHref();
                    break;
                }
            }
            //dd($redirect_url);
            // Redirect or handle errors
            // Ensure it's a string and redirecty
            return $redirect_url;
        } catch (PPConnectionException $ex) {
            // Detailed connection error logging
            \Log::error('PayPal Connection Error', [
                'error_message' => $ex->getMessage(),
                'error_data' => $ex->getData(),
                'debug_mode' => \Config::get('app.debug')
            ]);
        } catch (\Exception $ex) {
            // Catch-all for any other unexpected errors
            \Log::error('Unexpected PayPal Payment Error', [
                'error_message' => $ex->getMessage(),
                'error_trace' => $ex->getTraceAsString()
            ]);
        }
    }
    public function initiateBTCPay(Request $request)
    {
        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');
        foreach ($cart->items as $item) {
            $data = [
                'amount' => $cart->grand_total,
                'currency' => 'USD',
            ];
        }
        try {
            $invoice = $this->btcPayService->createInvoice($data);
            Session::put('btc_invoice_id', $invoice['id']);
            return $invoice['checkoutLink'];
        } catch (\Exception $e) {
            Log::error('BTCPay Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function initiateFlutterPay(Request $request, $payment, $customer, $location_data)
    {

        $cart = Session::get('cart');
        $checkout_order = Session::get('checkout_order');

        $reference = Flutterwave::generateReference();
        $payment_method = app('App\Http\Controllers\Controller')->transactionMethod('flutterwave');
        $allowedCurrencies = explode(',', $payment_method->allowed_currency);

        // Trim any whitespace
        $allowedCurrencies = array_map('trim', $allowedCurrencies);
        $finalAmount = $cart->grand_total;

        // $paymentData = $this->getLocalCostAndCurrency($payment_method, $location_data);
        $pay_email = $checkout_order->payment->email;
        if ($checkout_order->payment->payment_same_as_contact == 0) {

            $user_billing = UserBilling::where('id', $request->user_billing_id)->first();

            if ($user_billing) {
                $pay_email = $user_billing->email;
                $customer_name = $user_billing->first_name . ' ' . $user_billing->last_name;
                $phone_number = $user_billing->tel_cell;
            }
        }
        $data = [
            'payment_options' => 'card,banktransfer',
            'amount' => $finalAmount,
            'email' => $pay_email,
            'tx_ref' => $reference,
            'currency' => getDefaultCurrencyCode(),
            'redirect_url' => route('callback'),
            'customer' => [
                'email' => $pay_email,
                "phone_number" => $payment->tel_cell,
                "name" => $payment->first_name
            ],
        ];

        $payment = Flutterwave::initializePayment($data);
        if ($payment['status'] !== 'success') {
            // notify something went wrong
            return;
        }


        return $payment['data']['link'];
    }
    /**
     * Get available payment methods for current checkout session
     */
    public function getAvailablePaymentMethods(Request $request)
    {
        try {
            $checkout_order = Session::get('checkout_order');

            if (!$checkout_order || !isset($checkout_order->shipping)) {
                return response()->json(['error' => 'Shipping information required'], 400);
            }

            $country = Auth::user() ? $checkout_order->shipping->user_shipping->country : $checkout_order->shipping->country;
            $currency = $checkout_order->cart->currency ?? 'USD';
            $amount = $checkout_order->cart->grand_total ?? 0;

            $paymentMethods = PaymentMethod::getAvailableForCheckout($country, $currency)
                ->filter(function ($method) use ($country, $currency, $amount) {
                    return $method->isAvailable($country, $currency, $amount);
                })
                ->map(function ($method) use ($amount, $currency) {
                    return $this->formatPaymentMethodForCheckout($method, $amount, $currency);
                });

            return response()->json([
                'success' => true,
                'payment_methods' => $paymentMethods->values(),
                'checkout_summary' => [
                    'country' => $country,
                    'currency' => $currency,
                    'amount' => $amount
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching available payment methods: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to load payment methods'], 500);
        }
    }

    /**
     * Updated payment step handler
     */
    private function handlePaymentStepUpdated($request, $checkoutController, $response_data)
    {
        try {
            $checkout_order = $response_data['checkout_order'];

            // Validate payment method selection
            $validator = Validator::make($request->all(), [
                'payment_method_id' => 'required|integer|exists:payment_methods,id',
                'split_payment' => 'nullable|string',
                'split_option' => 'required_if:split_payment,true|string',
                'payment_option' => 'nullable|string',
            ], [
                'payment_method_id.required' => 'Please select a payment method.',
                'payment_method_id.integer' => 'Payment method ID must be a valid number.',
                'payment_method_id.exists' => 'The selected payment method is not valid or has been disabled.',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->messages()->first()], 400);
            }

            $paymentMethodId = $request->get('payment_method_id');
            $paymentMethod = PaymentMethod::findOrFail($paymentMethodId);

            // Validate payment method is available for this order
            $country = Auth::user() ? $checkout_order->shipping->user_shipping->country : $checkout_order->shipping->country;
            $currency = $checkout_order->cart->currency ?? 'USD';
            $amount = $checkout_order->cart->grand_total;

            if (!$paymentMethod->isAvailable($country, $currency, $amount)) {
                return response()->json(['error' => 'Selected payment method is not available'], 400);
            }

            // Handle pay later option
            if ($request->get('payment_option') === 'pay_later') {
                return $this->handlePayLaterOption($checkout_order);
            }

            // Check maximum amount restrictions
            $site_info = Setting::first();
            $maximum_amount = $site_info->price_range ?? PHP_INT_MAX;
            if ($amount > $maximum_amount) {
                return $this->handleMaximumAmountExceeded($checkout_order, $amount, $maximum_amount);
            }

            // Validate split payment if requested
            if ($request->get('split_payment')) {
                $this->validateSplitPayment($request, $paymentMethod, $amount);
            }

            // Update checkout order with payment method
            $this->updateCheckoutOrderWithPaymentMethod($checkout_order, $paymentMethod, $request);

            // Process the payment
            return $this->processPaymentWithNewSystem($checkout_order, $paymentMethod, $request);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage() . ' in ' . __METHOD__ . ' at line ' . __LINE__);

            return response()->json(['error' =>  $e->getMessage()], 500);
        }
    }

    public function processPayment(Request $request)
    {
        try {
            Log::info('Processing payment request', [
                'request_data' => $request->all(),
                'session_split_continuation' => Session::get('split_payment_continuation')
            ]);

            // Get checkout order and cart
            $checkout_order = Session::get('checkout_order');
            $cart = Session::get('cart');

            if (!$checkout_order || !$cart) {
                return response()->json(['error' => 'Checkout session not found'], 400);
            }

            // Get payment method
            $paymentMethodId = $request->get('payment_method_id');
            $paymentMethod = PaymentMethod::find($paymentMethodId);

            if (!$paymentMethod || !$paymentMethod->is_active) {
                return response()->json(['error' => 'Invalid payment method'], 400);
            }

            // Handle pay later option
            if ($request->get('payment_option') === 'pay_later') {
                return $this->handlePayLaterOption($checkout_order);
            }

            // CRITICAL FIX: Handle split payment continuation
            $splitContinuation = Session::get('split_payment_continuation');

            // Determine split payment parameters
            $splitPayment = false;
            $splitOption = null;

            if ($splitContinuation) {
                // For continuation, always use split payment
                $splitPayment = true;
                $splitOption = $splitContinuation['split_option'] ?? $request->get('split_option');

                Log::info('Using split payment from continuation', [
                    'split_option' => $splitOption,
                    'continuation_data' => $splitContinuation
                ]);
            } else {
                // For new payments, check request
                $splitPayment = $request->get('split_payment') === 'true' || $request->get('split_payment') === true;
                $splitOption = $request->get('split_option');

                Log::info('Checking new split payment request', [
                    'split_payment' => $splitPayment,
                    'split_option' => $splitOption,
                    'raw_split_payment' => $request->get('split_payment')
                ]);
            }

            // Validate split payment if requested
            if ($splitPayment) {
                try {
                    $this->validateSplitPayment($request, $paymentMethod, $cart->grand_total);
                } catch (\Exception $e) {
                    Log::error('Split payment validation failed: ' . $e->getMessage());
                    return response()->json(['error' => $e->getMessage()], 400);
                }
            }

            // Calculate payment amount
            $amount = $cart->grand_total;
            if ($splitContinuation && isset($splitContinuation['remaining_amount'])) {
                $amount = $splitContinuation['remaining_amount'];
                Log::info('Using remaining amount for split payment continuation', [
                    'remaining_amount' => $amount,
                    'original_total' => $cart->grand_total
                ]);
            }

            // Update checkout order with payment method and split payment info
            $this->updateCheckoutOrderWithPaymentMethod($checkout_order, $paymentMethod, $request, $splitContinuation);

            // Prepare payment data
            $paymentData = $this->preparePaymentData(
                $checkout_order,
                $paymentMethod,
                $amount,
                $request,
                $splitContinuation
            );

            // CRITICAL: Ensure split payment data is included in payment data
            if ($splitPayment && $splitOption) {
                $paymentData['split_payment'] = true;
                $paymentData['split_option'] = $splitOption;

                Log::info('Split payment data added to payment request', [
                    'split_payment' => true,
                    'split_option' => $splitOption
                ]);
            }

            // Process payment with the payment method
            $result = $paymentMethod->processPayment($paymentData);

            if (!$result || !is_array($result)) {
                Log::error('Invalid payment result', ['result' => $result]);
                return response()->json(['error' => 'Payment processing failed'], 500);
            }

            // Save payment result
            $this->savePaymentResult($checkout_order, $result, $paymentMethod);

            // Handle different payment result types
            return $this->handlePaymentResult($result, $paymentMethod);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'error' => 'Payment processing faileds: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process payment using the new payment system
     */
    protected function processPaymentWithNewSystem($checkout_order, PaymentMethod $paymentMethod, Request $request)
    {
        try {
            $paymentService = new PaymentProcessingService($paymentMethod);
            // Prepare payment data
            $paymentData = $this->preparePaymentData($checkout_order, $paymentMethod, $request);

            // Process payment
            $result = $paymentService->processPayment($paymentData);
            if ($result['success']) {
                // Save payment information to checkout order
                $cart = Session::get('cart');
                //dd(Session::get('split_payment_continuation') );
                if ($cart) {
                    // Calculate the amount that was intended to be paid
                    $splitContinuation = Session::get('split_payment_continuation');
                    if ($splitContinuation && isset($splitContinuation['remaining_amount'])) {
                        $cart->intent_pay = $splitContinuation['remaining_amount'];
                        $cart->transaction_ref = array($paymentData['reference']);
                        $cart->amount_due = $splitContinuation['remaining_amount'];
                    } else {
                        // Calculate remaining amount
                        $totalAmount = $cart->grand_total ?? 0;
                        $paidAmount = ($result['amount'] / 100) ?? 0;
                        $cart->intent_pay = max(0, $totalAmount - $paidAmount);
                        $cart->amount_due = 0;
                        $cart->transaction_ref = array($paymentData['reference']);
                    }

                    // Also set discount_amt if not set
                    if (!isset($cart->discount_amt)) {
                        $cart->discount_amt = 0;
                    }

                    // Save updated cart
                    Session::put('cart', $cart);

                    Log::info('Set intent_pay on cart', [
                        'intent_pay' => $cart->intent_pay,
                        'payment_method' => $paymentMethod->name
                    ]);
                }
                $this->savePaymentResult($checkout_order, $result, $paymentMethod);

                // Return appropriate response based on payment type
                return $this->handlePaymentResult($result, $paymentMethod);
            } else {
                Log::error('Payment processing failed', [
                    'payment_method' => $paymentMethod->name,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);

                return response()->json([
                    'error' => $result['error'] ?? 'Payment processing failed'
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage() . ' in ' . __METHOD__ . ' at line ' . __LINE__);

            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    /**
     * Helper method to get correct checkout page route
     */
    private function getCheckoutPageRoute()
    {
        $checkout_page = Page::where('page_key', 'checkout')->first();
        return $checkout_page ? route('page', $checkout_page->slug) : route('home');
    }

    /**
     * Helper method to get correct cart page route
     */
    private function getCartPageRoute()
    {
        $cart_page = Page::where('page_key', 'cart')->first();
        return $cart_page ? route('page', $cart_page->slug) : route('home');
    }
    /**
     * Prepare payment data for processing
     */
    protected function preparePaymentData($checkout_order, PaymentMethod $paymentMethod, Request $request)
    {
        $cart = Session::get('cart');
        $shippingAmount = Session::get('shippingAmount', 0);
        $currency_rate = Session::get('shop_logged_data')->currency_rate ?? 1;

        // ** CRITICAL FIX: Calculate correct amount for payment gateway **
        $splitContinuation = Session::get('split_payment_continuation');

        if ($splitContinuation) {
            // For split payment continuation, use remaining amount
            $amount = $splitContinuation['remaining_amount'];

            Log::info('Using split continuation amount', ['amount' => $amount]);
        } else {
            // For new payments, calculate base amount
            $cartTotal = $cart->grand_total ?? 0;

            $paidAmount = $cart->paid_grand_total ?? 0;
            $remainingCartAmount = $cartTotal - $paidAmount;

            // Add shipping to get total amount
            $totalAmount = $remainingCartAmount + $shippingAmount;

            // Check if split payment is requested
            if ($request->get('split_payment') === 'true' || $request->get('split_payment') === true) {
                // dd('ss');
                $splitOption = $request->get('split_option');

                if (empty($splitOption)) {
                    throw new \Exception('Split option is required when split payment is enabled');
                }

                // Validate split option format and extract percentages
                if (!preg_match('/^(\d+)-(\d+)$/', $splitOption, $matches)) {
                    throw new \Exception('Invalid split option format');
                }

                $firstPercentage = (int)$matches[1];
                $secondPercentage = (int)$matches[2];

                // Validate percentages add up to 100
                if ($firstPercentage + $secondPercentage !== 100) {
                    throw new \Exception('Split percentages must add up to 100');
                }

                // Validate this split option is supported by the payment method
                if (!in_array($splitOption, $paymentMethod->split_payment_options ?? [])) {
                    throw new \Exception('Split option not supported by selected payment method');
                }

                // For first payment, use the first percentage of total amount
                // $amount = round($totalAmount * $firstPercentage / 100, 2);
                $amount = $totalAmount;

                Log::info('Using split payment first amount', [
                    'total_amount' => $totalAmount,
                    'split_option' => $splitOption,
                    'first_percentage' => $firstPercentage,
                    'first_amount' => $amount
                ]);
                $feeBreakdown = $paymentMethod->getFeeBreakdown($amount);
                $amount = $totalAmount + $feeBreakdown['customer_fees'];
                //dd($amount);
            } else {
                // Regular payment - use full remaining amount

                $amount = $totalAmount;
            }
        }

        // Validate amount is positive
        if ($amount <= 0) {
            throw new \Exception('Payment amount must be greater than zero');
        }

        // Calculate fees and discounts based on the payment amount

        $feeBreakdown = $paymentMethod->getFeeBreakdown($amount);
        $discountAmount = $paymentMethod->getDiscountAmount($amount, $cart->currency ?? 'USD');
        // Final amount including fees and discounts
        $finalAmount = $amount - $discountAmount;
        // $finalAmount = $amount;


        // Define cancel URL properly
        $checkout_page = Page::where('page_key', 'checkout')->first();
        $cancelUrl = $checkout_page ? route('page', $checkout_page->slug) : route('home');

        // Prepare complete payment data

        $paymentData = [
            'amount' =>  $splitContinuation ? $amount : $finalAmount,
            'currency' => $cart->currency ?? 'USD',
            'reference' => $this->generatePaymentReference(),
            'description' => 'Order payment - ' . ($cart->cart_id ?? 'Unknown'),
            'customer' => [
                'id' => $checkout_order->customer->id ?? null,
                'email' => $checkout_order->customer->email,
                'name' => ($checkout_order->customer->first_name ?? '') . ' ' . ($checkout_order->customer->last_name ?? ''),
                'phone' => $checkout_order->customer->tel_cell ?? null,
            ],
            'shipping' => $checkout_order->shipping ?? null,
            'billing' => $checkout_order->customer ?? null,
            'callback_url' => route('payment.callback'),
            'success_url' => route('payment.success'),
            'cancel_url' => $cancelUrl,
            'metadata' => [
                'checkout_session_id' => Session::getId(),
                'payment_method_id' => $paymentMethod->id,
                'fee_breakdown' => $feeBreakdown,
                'discount_amount' => $discountAmount,
            ]
        ];

        // Add split payment information if requested
        if ($splitContinuation || $request->get('split_payment')) {
            $paymentData['split_payment'] = true;
            $paymentData['split_option'] = $splitContinuation['split_option'] ?? $request->get('split_option');

            Log::info('Split payment data included in payment preparation', [
                'split_payment' => true,
                'split_option' => $paymentData['split_option']
            ]);
        }

        return $paymentData;
    }

    /**
     * Generate unique payment reference
     */
    protected function generatePaymentReference(): string
    {
        return 'PAY_' . time() . '_' . Str::random(8);
    }

    /**
     * Save payment result to checkout order
     */
    protected function savePaymentResult($checkout_order, array $result, PaymentMethod $paymentMethod)
    {
        if (!$checkout_order->payment) {
            $checkout_order->payment = new \stdClass();
        }

        $checkout_order->payment->payment_method_id = $paymentMethod->id;
        $checkout_order->payment->payment_method_name = $paymentMethod->name;
        $checkout_order->payment->payment_method_title = $paymentMethod->title;
        $checkout_order->payment->transaction_reference = $result['reference'] ?? null;
        $checkout_order->payment->transaction_id = $result['transaction_id'] ?? null;
        $checkout_order->payment->authorization_url = $result['authorization_url'] ?? null;
        $checkout_order->payment->payment_status = 'pending';
        $checkout_order->payment->processed_at = now();
        // Save split payment information if applicable
        if (isset($result['split_calculation'])) {
            $checkout_order->payment->split_payment = true;
            $checkout_order->payment->split_calculation = $result['split_calculation'];
        }

        // Save partial transaction information if applicable
        if (isset($result['partial_transaction_info'])) {
            $checkout_order->payment->partial_transactions = true;
            $checkout_order->payment->partial_transaction_info = $result['partial_transaction_info'];
        }

        Session::put('checkout_order', $checkout_order);
    }

    /**
     * Handle payment result and return appropriate response
     */
    protected function handlePaymentResult(array $result, PaymentMethod $paymentMethod)
    {
        // ** CRITICAL FIX: Calculate remaining amount for split payments **
        $cart = Session::get('cart');
        $splitContinuation = Session::get('split_payment_continuation');

        // Determine the correct amount to use
        $amount = $result['amount'] ?? null;
        if ($splitContinuation && isset($splitContinuation['remaining_amount'])) {
            // Use remaining amount for split payment continuation
            $amount = $splitContinuation['remaining_amount'];
            Log::info('Using remaining amount for split payment', [
                'original_amount' => $result['amount'] ?? 'not_set',
                'remaining_amount' => $amount,
                'payment_method' => $paymentMethod->name
            ]);
            $paidAmount = $result['amount'];
        } elseif ($cart) {
            // Calculate remaining amount if not in continuation
            $totalAmount = $cart->grand_total ?? 0;
            $paidAmount = $result['amount'];
            $remainingAmount = max(0, $totalAmount - $paidAmount);

            if ($remainingAmount > 0 && $remainingAmount < $totalAmount) {
                $amount = $remainingAmount;
                Log::info('Calculated remaining amount for payment', [
                    'total_amount' => $totalAmount,
                    'paid_amount' => $paidAmount,
                    'remaining_amount' => $remainingAmount,
                    'payment_method' => $paymentMethod->name
                ]);
            }
        }

        // ** SPECIAL HANDLING FOR SQUAD INLINE CHECKOUT **
        if ($paymentMethod->name === 'squadpay' || $paymentMethod->type === 'squadco') {
            //dd($amount);
            //$amount = intval($paidAmount * 100);
            return response()->json([
                'success' => true,
                'payment_method' => 'squadpay',
                'transaction_reference' => $result['reference'] ?? null,
                'authorization_key' => $result['authorization_key'] ?? null,
                'amount' => $paidAmount, // ** Use calculated remaining amount **
                'email' => $result['email'] ?? null,
                'currency' => $result['currency'] ?? 'NGN',
                'customer_name' => $result['customer_name'] ?? null,
                'phone_number' => $result['phone_number'] ?? null,
                'callback_url' => $result['callback_url'] ?? null,
                'initiate_type' => 'inline',
                'payment_channels' => $result['payment_channels'] ?? ['card', 'bank', 'ussd', 'transfer'],
                'metadata' => $result['metadata'] ?? [],
                'is_split_payment' => $splitContinuation ? true : false,
                'remaining_amount' => $amount
            ]);
        }

        // For methods that require redirect (most external payment gateways)
        if (isset($result['authorization_url'])) {
            return response()->json([
                'success' => true,
                'redirect_url' => $result['authorization_url'],
                'payment_method' => $paymentMethod->name,
                'transaction_reference' => $result['reference'] ?? null,
                'authorization_key' => $result['public_key'] ?? null,
                'amount' => $amount, // ** Use calculated remaining amount **
                'email' => $result['email'] ?? null,
                'callback_url' => $result['callback_url'] ?? null,
                'metadata' => [
                    'order_id' => $result['order_id'] ?? null,
                    'customer_id' => $result['customer_id'] ?? null,
                ],
                'currency' => $result['currency'] ?? 'USD',
                'is_split_payment' => $splitContinuation ? true : false,
                'remaining_amount' => $amount
            ]);
        }

        // Handle immediate success (like some digital wallets)
        if (isset($result['payment_completed']) && $result['payment_completed']) {
            return response()->json([
                'success' => true,
                'order_complete' => true,
                'payment_method' => $paymentMethod->name,
                'transaction_reference' => $result['reference'] ?? null,
                'amount' => $amount, // ** Use calculated remaining amount **
                'is_split_payment' => $splitContinuation ? true : false
            ]);
        }

        // Handle 3DS or additional verification required
        if (isset($result['requires_3ds'])) {
            return response()->json([
                'success' => true,
                'requires_3ds' => true,
                'redirect_url' => $result['three_ds_url'] ?? null,
                'payment_method' => $paymentMethod->name,
                'transaction_reference' => $result['reference'] ?? null,
                'amount' => $amount, // ** Use calculated remaining amount **
                'is_split_payment' => $splitContinuation ? true : false
            ]);
        }

        // Default case - return error
        Log::warning('Unknown payment result type', [
            'result' => $result,
            'payment_method' => $paymentMethod->name
        ]);

        return response()->json([
            'error' => 'Unknown payment result type',
            'payment_method' => $paymentMethod->name
        ], 500);
    }

    /**
     * Validate split payment request
     */
    protected function validateSplitPayment(Request $request, PaymentMethod $paymentMethod, float $amount)
    {
        if (!$paymentMethod->supports_split_payment) {
            throw new \Exception('Split payment not supported by selected method');
        }

        $splitOption = $request->get('split_option');
        if (!in_array($splitOption, $paymentMethod->split_payment_options)) {
            throw new \Exception('Invalid split payment option');
        }

        if ($paymentMethod->split_payment_threshold && $amount < $paymentMethod->split_payment_threshold) {
            throw new \Exception('Amount is below minimum threshold for split payment');
        }
    }

    /**
     * Update checkout order with selected payment method
     */
    protected function updateCheckoutOrderWithPaymentMethod($checkout_order, PaymentMethod $paymentMethod, Request $request, $splitContinuation = null)
    {
        if (!$checkout_order->payment) {
            $checkout_order->payment = new \stdClass();
        }

        $checkout_order->payment->selected_method_id = $paymentMethod->id;
        $checkout_order->payment->selected_method_name = $paymentMethod->name;

        // Handle split payment parameters
        if ($splitContinuation) {
            // For continuation, preserve existing split payment settings
            $checkout_order->payment->split_payment_requested = true;
            $checkout_order->payment->split_option = $splitContinuation['split_option'] ?? $request->get('split_option');
        } else {
            // For new payments
            $checkout_order->payment->split_payment_requested = $request->get('split_payment') === 'true' || $request->get('split_payment') === true;
            $checkout_order->payment->split_option = $request->get('split_option');
        }

        Log::info('Updated checkout order payment info', [
            'payment_method_id' => $paymentMethod->id,
            'split_payment_requested' => $checkout_order->payment->split_payment_requested,
            'split_option' => $checkout_order->payment->split_option
        ]);

        Session::put('checkout_order', $checkout_order);
    }

    /**
     * Handle pay later option
     */
    protected function handlePayLaterOption($checkout_order)
    {
        $this->makeOrder();
        $contact_page = Page::where('page_key', 'contactTeam')->first();

        return response()->json([
            'success' => true,
            'pay_later' => true,
            'cart_amount' => $checkout_order->cart->grand_total,
            'page_slug' => $contact_page->slug ?? 'contact'
        ]);
    }

    /**
     * Handle maximum amount exceeded
     */
    protected function handleMaximumAmountExceeded($checkout_order, $amount, $maximum_amount)
    {
        $this->makeOrder();
        $contact_page = Page::where('page_key', 'contactTeam')->first();

        return response()->json([
            'success' => true,
            'amount_exceeded' => true,
            'cart_amount' => $amount,
            'maximum_amount' => $maximum_amount,
            'page_slug' => $contact_page->slug ?? 'contact'
        ]);
    }

    /**
     * Format payment method for checkout display
     */
    protected function formatPaymentMethodForCheckout(PaymentMethod $method, float $amount, string $currency): array
    {
        $feeBreakdown = $method->getFeeBreakdown($amount);
        $discountAmount = $method->getDiscountAmount($amount, $currency);
        $discountValue = $method->discount_value;

        $formatted = [
            'id' => $method->id,
            'name' => $method->name,
            'title' => $method->title,
            'subtitle' => $method->subtitle,
            'logo_url' => $method->logo_url,
            'type' => $method->type,
            'discount_type' => $method->discount_type,
            'fee_percentage' => $method->fee_percentage,
            'fee_fixed' => $method->fee_fixed,
            'payment_options' => $method->payment_options ?? [],
            'supports_split_payment' => $method->supports_split_payment,
            'fee_info' => [
                'customer_fees' => $feeBreakdown['customer_fees'],
                'fee_bearer' => $feeBreakdown['fee_bearer'],
                'transaction_count' => $feeBreakdown['transaction_count'],
                'fee_display' => $this->formatFeeDisplay($feeBreakdown)
            ],
            'discount_amount' => $discountAmount,
            'discount_value' => $discountValue,
            'final_amount' => $amount + $feeBreakdown['customer_fees'] - $discountAmount,
            'requires_partial_transactions' => $method->exceedsSingleTransactionLimit($amount),
        ];

        // Add split payment options if supported
        if ($method->supports_split_payment) {
            $formatted['split_payment_options'] = $method->getSplitPaymentConfigurations();
            $formatted['split_payment_threshold'] = $method->split_payment_threshold;
        }

        // Add partial transaction info if needed
        if ($method->exceedsSingleTransactionLimit($amount)) {
            $partialTransactions = $method->calculatePartialTransactions($amount);
            $formatted['partial_transaction_info'] = [
                'max_single_amount' => $method->max_single_transaction,
                'transaction_count' => count($partialTransactions),
                'transactions' => $partialTransactions,
                'warning_message' => "This payment will be split into " . count($partialTransactions) . " transactions due to provider limits."
            ];
        }

        return $formatted;
    }

    /**
     * Format fee display for frontend
     */
    protected function formatFeeDisplay(array $feeBreakdown): string
    {
        if ($feeBreakdown['customer_fees'] <= 0) {
            return $feeBreakdown['fee_bearer'] === 'system' ? 'No fees (covered by us)' : 'No fees';
        }

        $feeText = 'Fee: $' . number_format($feeBreakdown['customer_fees'], 2);

        if ($feeBreakdown['transaction_count'] > 1) {
            $feeText .= " ({$feeBreakdown['transaction_count']} transactions)";
        }

        if ($feeBreakdown['fee_bearer'] === 'split') {
            $feeText .= ' (shared)';
        }

        return $feeText;
    }



    /**
     * Get payment method details for frontend
     */
    public function getPaymentMethodDetails(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        try {
            $paymentMethod = PaymentMethod::findOrFail($id);
            $amount = (float) $request->get('amount');
            $currency = $request->get('currency');

            $details = [
                'id' => $paymentMethod->id,
                'name' => $paymentMethod->name,
                'title' => $paymentMethod->title,
                'subtitle' => $paymentMethod->subtitle,
                'type' => $paymentMethod->type,
                'payment_options' => $paymentMethod->payment_options,
                'supports_split_payment' => $paymentMethod->supports_split_payment,
                'fee_breakdown' => $paymentMethod->getFeeBreakdown($amount),
                'discount_amount' => $paymentMethod->getDiscountAmount($amount, $currency),
                'requires_partial_transactions' => $paymentMethod->exceedsSingleTransactionLimit($amount),
            ];

            // Add split payment calculations if supported
            if ($paymentMethod->supports_split_payment && $amount >= ($paymentMethod->split_payment_threshold ?? 0)) {
                $details['split_payment_options'] = $paymentMethod->getSplitPaymentConfigurations();
                $details['split_calculations'] = [];

                foreach ($paymentMethod->split_payment_options as $option) {
                    try {
                        $details['split_calculations'][$option] = $paymentMethod->calculateSplitPayment($amount, $option);
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }

            return response()->json([
                'success' => true,
                'payment_method' => $details
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching payment method details: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to load payment method details'], 500);
        }
    }


    public function paymentSuccess(Request $request)
    {

        try {
            // Get order details from session or request
            $orderId = $request->get('order_id') ?? Session::get('completed_order_id');
            $reference = $request->get('reference') ?? Session::get('payment_reference');

            // Find the thank you page
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if (!$thank_page) {
                Log::error('Thank you page not found');
                return redirect()->route('home')->with('success', 'Payment completed successfully!');
            }

            // Get checkout order from session
            $checkout_order = Session::get('checkout_order');
            $cart = Session::get('cart');

            // Try to find the order
            $order = null;
            if ($orderId) {
                $order = Order::find($orderId);
            } elseif ($reference) {
                $order = Order::where('payment_reference', $reference)->first();
            }

            // Log success for debugging
            Log::info('Payment success - redirecting to thank you page', [
                'order_id' => $orderId,
                'reference' => $reference,
                'has_order' => $order ? true : false,
                'has_checkout_order' => $checkout_order ? true : false,
                'has_cart' => $cart ? true : false
            ]);

            // Store success data in session for the thank you page
            Session::put('thank_you_data', [
                'payment_status' => 'success',
                'payment_reference' => $reference,
                'order' => $order,
                'checkout_order' => $checkout_order,
                'cart' => $cart,
                'success_message' => Session::get('success', 'Your payment has been processed successfully!'),
                'guest_user' => !auth()->check(),
                'shipping_cost' => $this->extractShippingCost($checkout_order, $cart),
                'payment_fees' => $this->extractPaymentFees($checkout_order),
                'discount_amount' => $this->extractDiscountAmount($checkout_order, $cart)
            ]);

            // Clear sensitive session data
            //Session::forget(['checkout_order', 'payment_reference', 'completed_order_id']);
            Session::forget(['split_payment_state', 'split_payment_continuation']);

            return redirect()->route('page', $thank_page->slug);
        } catch (\Exception $e) {
            Log::error('Payment success page error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            // Fallback - still redirect to thank you page with minimal data
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if ($thank_page) {
                Session::put('thank_you_data', [
                    'payment_status' => 'success',
                    'success_message' => 'Payment completed successfully!'
                ]);
                return redirect()->route('page', $thank_page->slug);
            }

            return redirect()->route('home')->with('success', 'Payment completed successfully!');
        }
    }


    /**
     * Handle failed payment - redirect to thank you page with error
     */
    public function paymentFailed(Request $request)
    {
        try {
            $reference = $request->get('reference') ?? $request->get('tx_ref') ?? Session::get('payment_reference');
            $errorMessage = $request->get('error') ?? Session::get('error', 'Payment was not successful. Please try again.');

            // Find the thank you page
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if (!$thank_page) {
                Log::error('Thank you page not found for payment failure');
                return redirect()->route('home')->with('error', $errorMessage);
            }

            // Find the failed transaction/order if possible
            $order = null;
            $checkout_order = Session::get('checkout_order');
            $cart = Session::get('cart');

            if ($reference) {
                // Try to find order by payment reference
                $order = Order::where('payment_reference', $reference)->first();
            }

            // Log failure for analysis
            Log::warning('Payment failure - redirecting to thank you page', [
                'reference' => $reference,
                'error_message' => $errorMessage,
                'has_checkout_order' => $checkout_order ? true : false,
                'has_order' => $order ? true : false
            ]);

            // Get available payment methods for retry
            $availablePaymentMethods = [];
            if ($checkout_order) {
                try {
                    $country = $checkout_order->shipping->country ?? 'US';
                    $currency = $checkout_order->cart->currency ?? 'USD';
                    $amount = $checkout_order->cart->grand_total ?? 0;

                    $availablePaymentMethods = PaymentMethod::getAvailableForCheckout($country, $currency, $amount);
                } catch (\Exception $e) {
                    Log::error('Error loading payment methods for retry: ' . $e->getMessage());
                }
            }

            // Store failure data in session for the thank you page
            Session::put('thank_you_data', [
                'payment_status' => 'failed',
                'error_message' => $errorMessage,
                'payment_reference' => $reference,
                'order' => $order,
                'checkout_order' => $checkout_order,
                'cart' => $cart,
                'available_payment_methods' => $availablePaymentMethods,
                'retry_url' => $this->getCheckoutPageRoute(),
                'support_url' => route('contact'),
                'home_url' => route('home')
            ]);

            return redirect()->route('page', $thank_page->slug);
        } catch (\Exception $e) {
            Log::error('Payment failed page error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            // Fallback
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if ($thank_page) {
                Session::put('thank_you_data', [
                    'payment_status' => 'failed',
                    'error_message' => 'Payment was not successful. Please try again.',
                    'retry_url' => $this->getCheckoutPageRoute(),
                    'support_url' => route('contact'),
                    'home_url' => route('home')
                ]);
                return redirect()->route('page', $thank_page->slug);
            }

            return redirect()->route('home')->with('error', 'Payment was not successful. Please try again.');
        }
    }

    /**
     * Handle cancelled payment - redirect to thank you page with cancellation info
     */
    public function paymentCancelled(Request $request)
    {
        try {
            $reference = $request->get('reference') ?? Session::get('payment_reference');
            $checkout_order = Session::get('checkout_order');
            $cart = Session::get('cart');

            // Find the thank you page
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if (!$thank_page) {
                Log::error('Thank you page not found for payment cancellation');
                return redirect()->route('home')->with('info', 'Payment was cancelled.');
            }

            // Log cancellation
            Log::info('Payment cancellation - redirecting to thank you page', [
                'reference' => $reference,
                'has_checkout_order' => $checkout_order ? true : false,
                'has_cart' => $cart ? true : false
            ]);

            // Store cancellation data in session for the thank you page
            Session::put('thank_you_data', [
                'payment_status' => 'cancelled',
                'cancel_message' => 'Payment was cancelled. Your order has not been processed.',
                'payment_reference' => $reference,
                'checkout_order' => $checkout_order,
                'cart' => $cart,
                'cart_total' => $checkout_order && isset($checkout_order->cart) ? $checkout_order->cart->grand_total ?? 0 : 0,
                'retry_url' => $this->getCheckoutPageRoute(),
                'cart_url' => route('cart'),
                'home_url' => route('home')
            ]);

            return redirect()->route('page', $thank_page->slug);
        } catch (\Exception $e) {
            Log::error('Payment cancelled page error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            // Fallback
            $thank_page = Page::where('page_key', 'thank_you')->first();
            if ($thank_page) {
                Session::put('thank_you_data', [
                    'payment_status' => 'cancelled',
                    'cancel_message' => 'Payment was cancelled.',
                    'retry_url' => route('home'),
                    'cart_url' => route('cart'),
                    'home_url' => route('home')
                ]);
                return redirect()->route('page', $thank_page->slug);
            }

            return redirect()->route('home')->with('info', 'Payment was cancelled.');
        }
    }
    public function handlePaymentCallback(Request $request)
    {
      
        try {
            $reference = $request->get('reference') ?? $request->get('tx_ref') ??
                $request->get('transaction_reference') ?? Session::get('payment_reference');
                
            if (!$reference) {
                $reference = '';
            }
            if (empty($reference)) {
                Log::error('Payment callback: No reference provided', $request->all());
                return $this->redirectToPaymentFailed('Invalid payment reference');
            }

            Log::info('Payment callback received', [
                'reference' => $reference,
                'request_data' => $request->all()
            ]);

            // Get checkout order and cart
            $checkout_order = Session::get('checkout_order');
            $cart = Session::get('cart');

            if (!$checkout_order || !$cart) {
                Log::error('Payment callback: Missing checkout order or cart', [
                    'has_checkout_order' => $checkout_order ? true : false,
                    'has_cart' => $cart ? true : false
                ]);
                return $this->redirectToPaymentFailed('Session data not found');
            }

            // Get payment method
            $paymentMethod = PaymentMethod::find($checkout_order->payment->selected_method_id ?? null);
            if (!$paymentMethod) {
                Log::error('Payment callback: Payment method not found');
                return $this->redirectToPaymentFailed('Payment method not found');
            }

            // Initialize payment service - CORRECTED: Use PaymentProcessingService instead of paymentMethod
            $paymentService = new PaymentProcessingService($paymentMethod);

            // Check if this is a split payment callback
            $splitState = Session::get('split_payment_state');
            // CORRECTED: Use paymentService instead of paymentMethod for verification
            if ($splitState) {
                // Handle split payment verification
                $verificationResult = $paymentService->handleSplitPaymentCallback($reference);
            } else {
                // Handle regular payment verification
                $verificationResult = $paymentService->verifyPayment($reference);
            }

            if (!$verificationResult || !$verificationResult['success']) {
                Log::error('Payment verification failed', [
                    'reference' => $reference,
                    'result' => $verificationResult
                ]);
                return $this->redirectToPaymentFailed($verificationResult['message'] ?? 'Payment verification failed');
            }
            // CORRECTED: Enhanced logic for split payment handling
            if ($splitState) {
                // Handle split payment scenarios
                // Handle split payment scenarios
                if (isset($verificationResult['split_payment_continue']) && $verificationResult['split_payment_continue']) {
                    // This is the first transaction of a split payment - continue to second payment
                    $firstTransactionAmount = $verificationResult['amount_paid'] ?? $splitState['split_calculation']['first_transaction']['amount'];
                    $remainingAmount = $verificationResult['remaining_amount'] ?? $splitState['split_calculation']['second_transaction']['amount'];
                    $currency_rate = Session::get('shop_logged_data')->currency_rate ?? 1;

                    // ** CRITICAL FIX: Properly update cart with first payment **
                    $shippingAmount = Session::get('shippingAmount', 0);

                    // Calculate how much of the cart amount was paid (excluding shipping portion)
                    $firstPercentage = $splitState['split_calculation']['first_transaction']['percentage'];
                    $totalAmountWithShipping = $splitState['total_amount'];
                    $cartPortion = ($totalAmountWithShipping - $shippingAmount) * $firstPercentage / 100;

                    // Update cart with the cart portion only
                    $cart->paid_grand_total = ($cart->paid_grand_total ?? 0) + $cartPortion;

                    // Update local currency amounts if applicable
                    if (isset($cart->grand_total_local)) {
                        $cart->paid_grand_total_local = ($cart->paid_grand_total_local ?? 0) + ($cartPortion * $currency_rate);
                    }

                    Session::put('cart', $cart);

                    Log::info('First split payment processed', [
                        'first_transaction_amount' => $firstTransactionAmount,
                        'cart_portion_paid' => $cartPortion,
                        'total_cart_paid' => $cart->paid_grand_total,
                        'remaining_amount' => $remainingAmount
                    ]);

                    // Store continuation data with correct remaining amount
                    Session::put('split_payment_continuation', [
                        'split_payment' => true,
                        'split_option' => $splitState['split_option'] ?? null,
                        'remaining_amount' => $remainingAmount,
                        'first_payment_reference' => $reference,
                        'previous_payment_method_id' => $checkout_order->payment->selected_method_id ?? null,
                        'total_transactions' => 2,
                        'current_transaction' => 2,
                        'original_total' => $splitState['total_amount'],
                        'first_payment_amount' => $firstTransactionAmount,
                        'cart_portion_paid' => $cartPortion
                    ]);

                    // Update checkout order payment status
                    $checkout_order->payment->payment_status = 'partial';
                    $checkout_order->payment->first_payment_completed = true;
                    $checkout_order->payment->first_payment_amount = $firstTransactionAmount;
                    $checkout_order->payment->remaining_amount = $remainingAmount;
                    $checkout_order->payment->first_payment_reference = $reference;

                    Session::put('checkout_order', $checkout_order);

                    // Redirect back to checkout with scroll parameter
                    $checkout_page = Page::where('page_key', 'checkout')->first();
                    if ($checkout_page) {
                        return redirect()->route('page', $checkout_page->slug)
                            ->with([
                                'scroll_to_banner' => true,
                                'info' => 'First payment completed successfully. Please complete your second payment.'
                            ]);
                    }
                } elseif (isset($verificationResult['split_payment_completed']) && $verificationResult['split_payment_completed']) {
                    // Split payment fully completed
                    $totalAmountPaid = $verificationResult['total_amount_paid'] ?? $splitState['total_amount'];

                    // Update cart with final payment
                    $cart->paid_grand_total = $totalAmountPaid;
                    if (isset($cart->grand_total_local)) {
                        $currency_rate = Session::get('shop_logged_data')->currency_rate ?? 1;
                        $cart->paid_grand_total_local = $totalAmountPaid / $currency_rate;
                    }

                    Session::put('cart', $cart);

                    Log::info('Split payment fully completed', [
                        'total_amount_paid' => $totalAmountPaid,
                        'reference' => $reference
                    ]);

                    // Process order completion
                    return $this->processOrderCompletion($checkout_order, $cart, $verificationResult);
                }
            } else {
                // Regular single payment - CORRECTED: Set proper verification result structure
                if ($verificationResult['verified'] ?? false) {
                    // CORRECTED: Handle the case where verification result doesn't have split payment fields
                    $amountPaid = $verificationResult['amount_paid'] ?? $cart->grand_total;
                    $currency_rate = Session::get('shop_logged_data')->currency_rate ?? 1;

                    // Update cart with payment
                    $cart->paid_grand_total = $amountPaid;
                    if (isset($cart->grand_total_local)) {
                        $cart->paid_grand_total_local = $amountPaid / $currency_rate;
                    }

                    Session::put('cart', $cart);

                    Log::info('Regular payment completed', [
                        'amount_paid' => $amountPaid,
                        'reference' => $reference
                    ]);

                    // Process order completion
                    return $this->processOrderCompletion($checkout_order, $cart, $verificationResult);
                }
            }

            // If we reach here, verification failed
            Log::error('Payment verification returned success but not verified', [
                'reference' => $reference,
                'result' => $verificationResult
            ]);
            return $this->redirectToPaymentFailed('Payment verification failed');
        } catch (\Exception $e) {
            Log::error('Payment callback error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return $this->redirectToPaymentFailed('An error occurred processing your payment');
        }
    }

    private function initiateSecondSplitPayment($checkout_order, $paymentMethod, $remainingAmount)
    {
        try {
            // Get split state to determine continuation parameters
            $splitState = Session::get('split_payment_state');

            if (!$splitState) {
                throw new \Exception('Split payment state not found');
            }

            // Update split state for second transaction
            $splitState['current_transaction'] = 2;
            Session::put('split_payment_state', $splitState);

            // Prepare continuation request
            $continuationRequest = new Request([
                'payment_method_id' => $paymentMethod->id,
                'split_payment' => 'true',
                'split_option' => $splitState['split_option'],
                'continuation' => true,
                'remaining_amount' => $remainingAmount
            ]);

            // Process second payment
            return $this->processPaymentWithNewSystem($checkout_order, $paymentMethod, $continuationRequest, true);
        } catch (\Exception $e) {
            Log::error('Error initiating second split payment: ' . $e->getMessage());
            Session::forget('split_payment_state');
            return $this->redirectToPaymentFailed('Error processing second payment');
        }
    }

    /**
     * Helper method to process order completion
     */
    private function processOrderCompletion($checkout_order, $cart, $verificationResult)
    {
        try {
            // Create order if not exists
            if (empty($cart->order)) {
                $this->makeOrder();
            }

            // Handle promo code if exists
            if (!empty($cart->promo_code)) {
                CustomerPromo::create([
                    'promo_id' => $cart->promo_code,
                    'customer_id' => $cart->coupan_user_id
                ]);
            }
            $paymentMethod = PaymentMethod::find($checkout_order->payment->selected_method_id ?? null);

            // Create collection record
            if ($paymentMethod) {
                $discount_amt = 0;
                if ($cart->paid_grand_total == 0) {
                    if ($paymentMethod->discount_price) {
                        if ($paymentMethod->discount_type == 1) {
                            $discount_amt = ($cart->total * $paymentMethod->discount_price) / 100;
                        } else {
                            $discount_amt = $paymentMethod->discount_price;
                        }
                    }
                }


                // $this->makeCollection(
                //     $cart->grand_total - $discount_amt,
                //     $paymentMethod->id,
                //     $verificationResult['reference'] ?? $verificationResult['provider_response']['data']['transaction_reference'] ?? 'unknown',
                //     2, // Success status
                //     $discount_amt
                // );
            }
            // Clear split payment state if exists
            Session::forget('split_payment_state');

            // Get thank you page
            $thank_page = Page::where('page_key', 'thank_you')->first();

            if (!$thank_page) {
                Log::warning('Thank you page not found, redirecting to home');
                return redirect()->route('home')->with('success', 'Payment completed successfully');
            }

            // Store completion data for thank you page
            Session::put('thank_you_data', [
                'payment_status' => 'completed',
                'payment_reference' => $verificationResult['reference'] ?? 'unknown',
                'amount_paid' => $cart->paid_grand_total,
                'checkout_order' => $checkout_order,
                'cart' => $cart
            ]);

            return redirect()->route('page', $thank_page->slug);
        } catch (\Exception $e) {
            Log::error('Error processing order completion: ' . $e->getMessage());
            return $this->redirectToPaymentFailed('Error completing your order');
        }
    }
    /**
     * Enhanced handlePaymentCallback method to redirect to thank you page
     */

    public function handlePaymentCallbackOld(Request $request)
    {

        try {
            $reference = $request->get('reference') ?? $request->get('tx_ref') ??
                $request->get('transaction_reference') ?? Session::get('payment_reference');

            if (empty($reference)) {
                Log::error('Payment callback: No reference provided', $request->all());
                return $this->redirectToPaymentFailed('Invalid payment reference');
            }

            Log::info('Payment callback received', [
                'reference' => $reference,
                'request_data' => $request->all()
            ]);

            // Get checkout order and cart
            $checkout_order = Session::get('checkout_order');
            $cart = Session::get('cart');

            if (!$checkout_order || !$cart) {
                Log::error('Payment callback: Missing checkout order or cart', [
                    'has_checkout_order' => $checkout_order ? true : false,
                    'has_cart' => $cart ? true : false
                ]);
                return $this->redirectToPaymentFailed('Session data not found');
            }

            // Get payment method
            $paymentMethod = PaymentMethod::find($checkout_order->payment->selected_method_id ?? null);
            if (!$paymentMethod) {
                Log::error('Payment callback: Payment method not found');
                return $this->redirectToPaymentFailed('Payment method not found');
            }

            // Verify payment with the payment processor
            $verificationResult = $paymentMethod->verifyPayment($reference);

            if (!$verificationResult || !$verificationResult['success']) {
                Log::error('Payment verification failed', [
                    'reference' => $reference,
                    'result' => $verificationResult
                ]);
                return $this->redirectToPaymentFailed($verificationResult['message'] ?? 'Payment verification failed');
            }

            // Check if this is the first payment of a split payment
            if (isset($verificationResult['is_first_split_payment']) && $verificationResult['is_first_split_payment']) {
                $firstTransactionAmount = $verificationResult['amount_paid'] ?? 0;
                $remainingAmount = $verificationResult['remaining_amount'] ?? 0;
                $currency_rate = Session::get('shop_logged_data')->currency_rate ?? 1;

                // Update cart with partial payment
                $cart->paid_grand_total = ($cart->paid_grand_total ?? 0) + $firstTransactionAmount;

                // Update local currency amounts if applicable
                if (isset($cart->grand_total_local)) {
                    $cart->paid_grand_total_local = ($cart->paid_grand_total_local ?? 0) + ($firstTransactionAmount * $currency_rate);
                }

                // Mark items as partially paid
                if (isset($cart->items)) {
                    foreach ($cart->items as $item) {
                        $item->sku_partial_paid = true;
                    }
                }

                // Update cart in session
                Session::put('cart', $cart);

                // Store split payment continuation data
                Session::put('split_payment_continuation', [
                    'status' => 'first_payment_completed',
                    'message' => 'First payment completed successfully! Please complete the second payment.',
                    'amount_paid' => $firstTransactionAmount,
                    'remaining_amount' => $remainingAmount,
                    'remaining_amount_local' => $remainingAmount * $currency_rate,
                    'transaction_progress' => '1 of 2',
                    'first_transaction_reference' => $reference,
                    'split_info' => $verificationResult['split_info'] ?? null,
                    // Store payment method information for restoration
                    'payment_method_id' => $checkout_order->payment->selected_method_id ?? null,
                    'payment_method_name' => $checkout_order->payment->selected_method_name ?? null,
                    'previous_payment_method_id' => $checkout_order->payment->selected_method_id ?? null,
                    // Store split payment options for second payment
                    'split_payment' => true,
                    'split_option' => $checkout_order->payment->split_option ?? null
                ]);

                // Update checkout order payment status
                $checkout_order->payment->payment_status = 'partial';
                $checkout_order->payment->first_payment_completed = true;
                $checkout_order->payment->first_payment_amount = $firstTransactionAmount;
                $checkout_order->payment->remaining_amount = $remainingAmount;
                $checkout_order->payment->first_payment_reference = $reference;

                Session::put('checkout_order', $checkout_order);

                // Log successful first payment
                Log::info('First split payment completed', [
                    'reference' => $reference,
                    'amount_paid' => $firstTransactionAmount,
                    'remaining_amount' => $remainingAmount
                ]);

                // FIX: Redirect back to checkout with scroll parameter
                $checkout_page = Page::where('page_key', 'checkout')->first();
                if ($checkout_page) {
                    return redirect()->route('page', $checkout_page->slug)
                        ->with(['scroll_to_banner' => true, 'info' => 'First payment completed. Please complete your second payment.']);
                } else {
                    return redirect()->route('home')
                        ->with('info', 'First payment completed. Please complete your second payment.');
                }
            }

            // Handle second split payment or regular payment completion
            if (isset($verificationResult['split_payment_completed'])) {
                // Second payment of split payment completed
                $cart = Session::get('cart');

                // Update cart with final payment
                $totalAmountPaid = $verificationResult['total_amount_paid'] ?? 0;
                $cart->paid_grand_total = $totalAmountPaid;

                if (isset($cart->grand_total_local)) {
                    $location_data = Session::get('shop_logged_data');
                    $currency_rate = $location_data->currency_rate ?? 1;
                    $cart->paid_grand_total_local = $totalAmountPaid * $currency_rate;
                }

                Session::put('cart', $cart);
            }

            // Payment completed (either single payment or second split payment)
            $checkout_order->payment->payment_status = 'completed';
            $checkout_order->payment->verified_at = now();
            $checkout_order->payment->verification_result = $verificationResult;

            if (isset($verificationResult['split_payment_completed'])) {
                $checkout_order->payment->split_payment_completed = true;
                $checkout_order->payment->total_amount_paid = $verificationResult['total_amount_paid'];
            }

            Session::put('checkout_order', $checkout_order);

            // Create the order
            $order = $this->makeOrder();

            // Store order info and redirect to success
            Session::put('completed_order_id', $order->id);
            Session::put('payment_reference', $reference);

            // Clear split payment continuation
            Session::forget('split_payment_continuation');

            return $this->redirectToPaymentSuccess('Payment completed successfully');
        } catch (\Exception $e) {
            Log::error('Payment callback error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return $this->redirectToPaymentFailed('Payment processing error occurred');
        }
    }

    private function redirectToPaymentSuccess($message = 'Payment completed successfully')
    {
        $thank_page = Page::where('page_key', 'thank_you')->first();
        if ($thank_page) {
            return redirect()->route('page', $thank_page->slug)->with('success', $message);
        }
        return redirect()->route('home')->with('success', $message);
    }

    /**
     * Helper method to redirect to payment failed
     */
    private function redirectToPaymentFailed($error = 'Payment failed')
    {
        $thank_page = Page::where('page_key', 'thank_you')->first();
        if ($thank_page) {
            return redirect()->route('page', $thank_page->slug)->with('error', $error);
        }
        return redirect()->route('home')->with('error', $error);
    }

    /**
     * Extract shipping cost from checkout order or cart
     */
    private function extractShippingCost($checkout_order, $cart)
    {
        if (isset($checkout_order->shipping_cost)) {
            return $checkout_order->shipping_cost;
        } elseif (isset($checkout_order->shipping->cost)) {
            return $checkout_order->shipping->cost;
        } elseif (isset($cart->shipping_cost)) {
            return $cart->shipping_cost;
        }
        return 0;
    }

    /**
     * Extract payment fees from checkout order
     */
    private function extractPaymentFees($checkout_order)
    {
        if (isset($checkout_order->payment->fees)) {
            return $checkout_order->payment->fees;
        } elseif (isset($checkout_order->payment_fees)) {
            return $checkout_order->payment_fees;
        }
        return 0;
    }

    /**
     * Extract discount amount from checkout order or cart
     */
    private function extractDiscountAmount($checkout_order, $cart)
    {
        if (isset($checkout_order->discount_amount)) {
            return $checkout_order->discount_amount;
        } elseif (isset($cart->discount_amt)) {
            return $cart->discount_amt;
        } elseif (isset($cart->totalDiscount)) {
            return $cart->totalDiscount;
        }
        return 0;
    }


    /**
     * Enhanced preparePaymentData method with better error handling and validation
     */

    protected function preparePaymentData2($checkout_order, PaymentMethod $paymentMethod, Request $request): array
    {
        try {
            // ** CRITICAL FIX: Calculate remaining amount for split payments **
            $cart = Session::get('cart');
            $splitContinuation = Session::get('split_payment_continuation');
            $location_data = Session::get('shop_logged_data');

            // Determine the correct amount to process
            $amount = 0;
            if ($splitContinuation && isset($splitContinuation['remaining_amount'])) {
                // Use remaining amount for split payment continuation
                $amount = $splitContinuation['remaining_amount'];
                Log::info('Using remaining amount from split continuation', [
                    'remaining_amount' => $amount,
                    'payment_method' => $paymentMethod->name
                ]);
            } elseif ($cart) {
                // Calculate remaining amount
                $totalAmount = $cart->grand_total ?? 0;
                $paidAmount = $cart->paid_grand_total ?? 0;
                $amount = max(0, $totalAmount - $paidAmount);

                Log::info('Calculated payment amount', [
                    'total_amount' => $totalAmount,
                    'paid_amount' => $paidAmount,
                    'remaining_amount' => $amount,
                    'is_split_continuation' => $splitContinuation ? true : false
                ]);
            }

            if ($amount <= 0) {
                throw new \InvalidArgumentException('Invalid payment amount: ' . $amount);
            }

            // Determine currency
            $currency = 'USD';
            if ($location_data && isset($location_data->country)) {
                $currency = $location_data->country === 'NG' ? 'NGN' : 'USD';
            }

            // Calculate fees and discounts
            try {
                $feeBreakdown = $paymentMethod->getFeeBreakdown($amount);
            } catch (\Exception $e) {
                Log::warning('Fee calculation failed, using zero fees', [
                    'payment_method' => $paymentMethod->name,
                    'error' => $e->getMessage()
                ]);
                $feeBreakdown = ['customer_fees' => 0, 'transaction_count' => 1, 'fee_bearer' => 'customer'];
            }

            try {
                $discountAmount = $paymentMethod->getDiscountAmount($amount, $currency);
            } catch (\Exception $e) {
                Log::warning('Discount calculation failed, using zero discount', [
                    'payment_method' => $paymentMethod->name,
                    'error' => $e->getMessage()
                ]);
                $discountAmount = 0;
            }

            // Calculate final amount
            $finalAmount = $amount + ($feeBreakdown['customer_fees'] ?? 0) - $discountAmount;

            if ($finalAmount <= 0) {
                throw new \InvalidArgumentException('Final payment amount must be greater than zero');
            }

            // Generate unique payment reference
            $paymentReference = $this->generatePaymentReference();

            // Prepare customer data
            $customerName = '';
            $customerEmail = '';
            $customerId = null;
            $customerPhone = null;

            if (isset($checkout_order->customer)) {
                $customerName = trim(($checkout_order->customer->first_name ?? '') . ' ' . ($checkout_order->customer->last_name ?? ''));
                $customerEmail = $checkout_order->customer->email ?? '';
                $customerId = $checkout_order->customer->id ?? null;
                $customerPhone = $checkout_order->customer->tel_cell ?? $checkout_order->customer->phone ?? null;
            }

            if (empty($customerEmail)) {
                throw new \InvalidArgumentException('Customer email is required for payment processing');
            }

            // Get callback URLs
            $thank_page = Page::where('page_key', 'thank_you')->first();
            $baseCallbackUrl = $thank_page ? route('page', $thank_page->slug) : route('home');

            // ** SPECIAL HANDLING FOR SQUAD PAYMENTS **
            if ($paymentMethod->name === 'squadpay' || $paymentMethod->type === 'squadco') {
                // Convert to kobo/cents for Squad (multiply by 100)
                //$squadAmount = intval(round($finalAmount * 100));

                return [
                    'amount' => $finalAmount, // Amount in kobo/cents
                    'original_amount' => $amount, // Original amount in currency units
                    'final_amount' => $finalAmount, // Final amount in currency units
                    'currency' => $currency,
                    'reference' => $paymentReference,
                    'customer_name' => $customerName,
                    'email' => $customerEmail,
                    'phone_number' => $customerPhone,
                    'callback_url' => route('payment.callback'), // Squad specific callback
                    'authorization_key' => $paymentMethod->getApiConfig()['private_key'] ?? null,
                    'initiate_type' => 'inline',
                    'payment_channels' => ['card', 'bank', 'ussd', 'transfer'],
                    'pass_charge' => false,
                    'metadata' => [
                        'order_id' => $checkout_order->id ?? null,
                        'customer_id' => $customerId,
                        'is_split_payment' => $splitContinuation ? true : false,
                        'remaining_amount' => $amount
                    ]
                ];
            }

            // For other payment methods
            $paymentData = [
                'amount' => $finalAmount,
                'original_amount' => $amount,
                'currency' => strtoupper($currency),
                'reference' => $paymentReference,
                'order_id' => $checkout_order->id ?? null,
                'customer_name' => $customerName,
                'customer_id' => $customerId,
                'email' => $customerEmail,
                'phone' => $customerPhone,
                'callback_url' => $baseCallbackUrl,
                'return_url' => $baseCallbackUrl,
                'cancel_url' => route('checkout.index'),
                'metadata' => [
                    'order_id' => $checkout_order->id ?? null,
                    'customer_id' => $customerId,
                    'payment_method' => $paymentMethod->name,
                    'is_split_payment' => $splitContinuation ? true : false,
                    'remaining_amount' => $amount
                ],

            ];
            if ($request->get('split_payment')) {
                $paymentData['split_payment'] = true;
                $paymentData['split_option'] = $request->get('split_option');
            }
            Log::info('Payment data prepared', [
                'payment_method' => $paymentMethod->name,
                'amount' => $finalAmount,
                'currency' => $currency,
                'reference' => $paymentReference,
                'is_split_payment' => $splitContinuation ? true : false
            ]);

            return $paymentData;
        } catch (\Exception $e) {
            Log::error('Failed to prepare payment data: ' . $e->getMessage(), [
                'payment_method' => $paymentMethod->name,
                'checkout_order' => $checkout_order,
                'request' => $request->all()
            ]);
            throw $e;
        }
    }

    public function index(Request $request)
    {
        try {
            // Check for split payment continuation
            $splitContinuation = Session::get('split_payment_continuation');

            // Your existing checkout order logic (keep as is)
            $checkout_order = Session::get('checkout_order');
            if (!$checkout_order) {
                $this->makeCheckoutOrder();
                $checkout_order = Session::get('checkout_order');
            }

            if (!$checkout_order) {
                return redirect()->route('cart')->with('error', 'Unable to initialize checkout');
            }

            // Your existing code for loading other data (keep as is)
            // Just make sure to add these two lines to your return statement:

            return view('checkout', [
                // Your existing variables...
                'page' => $page,
                'checkout_order' => $checkout_order,
                'cart' => $cart,
                'countries' => $countries,
                'shipping_methods' => $shipping_methods,
                'total_reward_points' => $total_reward_points,
                'location_data' => $location_data,

                // ADD these two lines:
                'split_continuation' => $splitContinuation,
                'show_split_continuation' => $splitContinuation ? true : false
            ]);
        } catch (\Exception $e) {
            Log::error('Checkout page error: ' . $e->getMessage());
            return redirect()->route('cart')->with('error', 'Unable to load checkout page');
        }
    }
}
