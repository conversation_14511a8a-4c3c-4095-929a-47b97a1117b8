<section id="main_nav">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-4 col-sm-4">

                <div class="block component-title my-0 clearfix">
                    <h3>{{ $admin_page_title }}</h3>
                </div>



            </div>
            <div class="col-md-8 col-sm-8">
                <div class="d-flex align-items-center justify-content-md-end justify-content-center">
                    <div class="block site-area my-0 clearfix">
                        <ul class="nav float-right justify-content-center">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/') }}">View Site</a>
                            </li>
                            @if (permissions('setting'))
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Setting</a>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item {{ (request()->is('admin/user-groups*')) ? 'active ' : '' }}" href="{{ route('admin.user-groups.index') }}">User Groups</a>
                                    <a class="dropdown-item {{ (request()->is('admin/access-labels*')) ? 'active ' : '' }}" href="{{ route('admin.access-labels.index') }}">Access</a>
                                    <a class="dropdown-item {{ (request()->is('admin/alert_preferences*')) ? 'active ' : '' }}" href="{{ route('admin.alert_preferences.index') }}">Subscription Types and Preferences</a>
                                    <a class="dropdown-item {{ (request()->is('admin/transaction_methods*')) ? 'active ' : '' }}" href="{{ route('admin.transaction_methods.index') }}">Transaction Methods</a>
                                    <a class="dropdown-item {{ (request()->is('admin/shipping-methods*')) ? 'active ' : '' }}" href="{{ route('admin.shipping-methods.index') }}">Shipping Methods</a>
                                    <a class="dropdown-item {{ (request()->is('admin/shipping-rules*')) ? 'active ' : '' }}" href="{{ route('admin.shipping-rules.index') }}">Shipping Rules</a>
                                                                <a class="dropdown-item" href="{{ route('admin.setting.shipping_units_settings') }}">Shipping Units configuration</a>
                                                                                                                                <a class="dropdown-item" href="{{ route('admin.currency.adjustments') }}">Currency Rate Adjustments</a>
                                                                                                                                 <a class="dropdown-item" href="{{ route('admin.country-message-settings.index') }}">Country Based SMS Rules</a>


                                    <a class="dropdown-item {{ (request()->is('admin/estimated-delivery-dates*')) ? 'active ' : '' }}" href="{{ route('admin.estimated_delivery_dates.index') }}">Estimated Delivery Date</a>
                                    <a class="dropdown-item {{ (request()->is('admin/storess*')) ? 'active ' : '' }}" href="{{ route('admin.stores.index') }}">Stores</a>
                                    <a class="dropdown-item" href="{{ route('admin.setting.site') }}">General</a>
                                    {{-- <a class="dropdown-item {{ (request()->is('admin/terms*')) ? 'active ' : '' }}" href="{{ route('admin.terms.index') }}">Terms</a> --}}
                                    {{-- <div class="dropdown-divider"></div> --}}
                                    {{-- <a class="dropdown-item" href="{{ route('admin.setting.site') }}">General</a>
                                    <a class="dropdown-item" href="{{ route('admin.setting.seo_info') }}">SEO</a>
                                    <a class="dropdown-item" href="{{ route('admin.setting.social_info') }}">Social</a>
                                    <a class="dropdown-item" href="{{ route('admin.setting.contact_info') }}">Contact</a>
                                    <a class="dropdown-item" href="{{ route('admin.setting.business_hours') }}">Hours</a> --}}
                                </div>
                            </li>
                            @endif
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Account</a>
                                <div class="dropdown-menu dropdown-menu-right">
                                    {{-- <a class="dropdown-item" href="{{ route('page', auth()->user()->username) }}">My Profile</a> --}}
                                    <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Sign out</a>
                                </div>
                            </li>
                        </ul>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                            {{ csrf_field() }}
                        </form>

                    </div>
                    <div class=" d-block d-lg-none">
                        <button class="btn btn-primary admin-menu" type="button">
                            <i class="fa fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>