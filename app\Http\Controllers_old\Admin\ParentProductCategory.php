<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\ProductCategory;
use App\Http\Controllers\Controller;
use App\Models\ProdcutCategoriesParent;

class ParentProductCategory extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = ' Parent Categories';
        $items = ProdcutCategoriesParent::paginate($this->itemPerPage);
        $sl = SLGenerator($items);
        return view('admin.product.parentCategory.index',compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'New Parent Category';
        $productCategories = ProductCategory::all();
        return view('admin.product.parentCategory.create',compact('admin_page_title','productCategories'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|unique:product_categories,slug|max:255',
            'description' => 'nullable|string',
        ]);
        ProdcutCategoriesParent::create($validatedData);

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.parent-categories.index'))->with('success', 'Product category created successful.');


    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProdcutCategoriesParent  $prodcutCategoriesParent
     * @return \Illuminate\Http\Response
     */
    public function show(ProdcutCategoriesParent $prodcutCategoriesParent)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProdcutCategoriesParent  $prodcutCategoriesParent
     * @return \Illuminate\Http\Response
     */
    public function edit(ProdcutCategoriesParent $prodcutCategoriesParent)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProdcutCategoriesParent  $prodcutCategoriesParent
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProdcutCategoriesParent $prodcutCategoriesParent)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProdcutCategoriesParent  $prodcutCategoriesParent
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProdcutCategoriesParent $prodcutCategoriesParent)
    {
        //
    }
}
