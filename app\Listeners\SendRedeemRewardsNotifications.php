<?php

namespace App\Listeners;

use App\Events\RedeemRewards;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendRedeemRewardsNotifications
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\RedeemRewards  $event
     * @return void
     */
    public function handle(RedeemRewards $event)
    {
        //
    }
}
