<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductBrand;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Session;
use Illuminate\Support\Facades\Storage;
use Validator;
use Response;
use Illuminate\Support\Str;

class ProductBrandController extends Controller
{
    private $folderPath = 'products/brands/';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Brands';
        $items = ProductBrand::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        return view('admin.product.brand.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $admin_page_title = 'Create Brand';
        $brand_image_info = Session::get('brand_image');
        $categories = ProductCategory::whereStatus(true)->get();
        $categoriesID = [];
        foreach ($categories as $category) {
            $categoriesID[] = explode(',', $category->id);
        }
        return view('admin.product.brand.create', compact('brand_image_info', 'admin_page_title', 'categories', 'categoriesID'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|string',
            // 'slug' => 'required|string|alpha_dash|unique:product_brands,slug',
            'product_categories_id' => 'required|exists:product_categories,id',
            'ordering' => 'nullable|string',
            'brand_link' => 'nullable|url',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|unique:product_brands,slug'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        $item = new ProductBrand;
        $item->title = $request->title;
        $item->product_categories_id = implode(',', $request->product_categories_id);
        $item->slug = $item_slug;
        $item->brand_link = $request->brand_link;
        $item->description = $request->description;
        $item->ordering = $request->ordering ?? 0;
        $item->status = $request->status ?? 0;
        $brand_image_info = Session::get('brand_image');
        if ($brand_image_info) {
            $image_name = $item->slug . '_brand_' . uniqid() . '.' . $brand_image_info->extension;
            $folderName = $this->folderName();
            Storage::move($brand_image_info->brand_image, $this->folderPath . $folderName . '/' . $image_name);
            $item->brand_image = $folderName . '/' . $image_name;
            Session::forget('brand_image');
        }
        if ($request->hasFile('brand_image')) {
            $folderName = $this->folderName();
            $imgName = $item->slug . '_brand_' . uniqid() . '.' . $request->brand_image->extension();
            $request->brand_image->storeAs($this->folderPath . $folderName, $imgName);
            $item->brand_image = $folderName. '/'. $imgName;
        }
        $item->save();

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.product-brands.index'))->with('success', 'Gallery category created successful.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProductBrand  $productBrand
     * @return \Illuminate\Http\Response
     */
    public function show(ProductBrand $productBrand)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductBrand  $productBrand
     * @return \Illuminate\Http\Response
     */
    public function edit(ProductBrand $productBrand)
    {
        $admin_page_title = 'Edit Brand';
        $item = $productBrand;
        $categories = ProductCategory::whereStatus(true)->get();
        $categoriesID = explode(',', $item->product_categories_id);
        // dd($categoriesID);
        return view('admin.product.brand.edit', compact('item', 'admin_page_title', 'categories', 'categoriesID'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductBrand  $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductBrand $productBrand)
    {
        $this->validate($request, [
            'title' => 'required|string',
            'product_categories_id' => 'required|exists:product_categories,id',
            'ordering' => 'nullable|string',
            'brand_link' => 'nullable|url',
            'description' => 'nullable|string',
            'status' => 'nullable|boolean'
        ]);

        if ($request->slug) {
            $item_slug = Str::slug($request->slug, '-');
        } else {
            $item_slug = Str::slug($request->title, '-');
        }

        $validator = Validator::make(['slug' => $item_slug], [
            'slug' => 'required|string|alpha_dash|unique:product_brands,slug,' . $productBrand->id,
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput($request->input())->withErrors($validator)->with('warning', 'slug already exist.');
        }

        $item = ProductBrand::findOrFail($productBrand->id);
        $item->title = $request->title;
        $item->product_categories_id = implode(',', $request->product_categories_id);
        $item->slug = $item_slug;
        $item->brand_link = $request->brand_link;
        $item->description = $request->description;
        $item->ordering = $request->ordering ?? 0;
        $item->status = $request->status ?? 0;
        $item->save();

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.product-brands.index'))->with('success', 'Post category updated successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductBrand  $productBrand
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProductBrand $productBrand)
    {
        $item = ProductBrand::findOrFail($productBrand->id);
        $this->deleteFile($this->folderPath, $item->brand_image);
        $item->delete();

        $items = ProductBrand::paginate($this->itemPerPage);
        $sl = SLGenerator($items);

        return view('admin.jquery_live.brands', compact('items', 'sl'));
    }

    public function fileUpload(Request $request, $target, $id)
    {
        $validator = Validator::make($request->all(), [
            $target => 'image|mimes:jpg,jpeg,gif,png,svg,webp|max:' . $this->maxFileSize,
        ]);

        if ($validator->fails()) {
            foreach ($validator->messages()->all() as $key => $message) {
                return response()->json([
                    'status' => 'failed',
                    'message' =>  $message
                ], 200);
                break;
            }
        }

        $item = ProductBrand::findOrFail($id);

        $this->deleteFile($this->folderPath, $item->$target);

        $imgName = $item->slug . '_' . $target . "_" . $item->id . uniqid() . '.' . $request->$target->extension();
        $folderName = $this->folderName();
        $request->$target->storeAs($this->folderPath. '/' . $folderName, $imgName);

        $item->$target = $folderName . '/' . $imgName;
        $item->update();

        $url = url('/storage/products/brands/' . $folderName. '/' . $imgName);

        return response()->json(
            [
                'status' => 'success',
                'file_url' => $url
            ],
            200
        );
    }

    public function fileRemove($target, $id)
    {
        $item = ProductBrand::findOrFail($id);
        $this->deleteFile($this->folderPath, $item->$target);
        $item->$target = null;
        $item->update();

        return response()->json('success', 200);
    }
}
