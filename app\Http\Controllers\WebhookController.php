<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Models\Promo;
use App\Models\Coupon;
use App\Models\SentDiscount;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Setting;
use App\Models\Subscriber;
use Illuminate\Support\Facades\Mail;
use App\Mail\PromoCodeEmail;
use App\Mail\SiteEmail;
use Illuminate\Support\Facades\Session;
use App\Models\PaymentMethod;
use App\Models\Order;
use App\Models\Transaction;
class WebhookController extends Controller
{
    public function metaWebhook(Request $request)
    {
        Log::error('Incoming Meta: ' . json_encode($request->all()));

        // Verification
        if ($request->isMethod('get')) {
            if ($request->hub_verify_token === env('WHATSAPP_VERIFY_TOKEN')) {

                return response($request->hub_challenge, 200);
            }
            return response('Invalid token', 403);
        }
        $entries = $request->input('entry', []);
        foreach ($entries as $entry) {
            foreach ($entry['changes'] as $change) {
                $value = $change['value'];
                Log::error('Incoming Status: ');

                if (isset($value['statuses'])) {
                    $this->handleStatusUpdate($value['statuses']);
                }
                if (isset($value['messages'])) {
                    $this->handleIncomingMessage($request);
                }
            }
        }



        return response()->json(['status' => 'ok']);
    }




    public function handleIncomingMessage($request)
    {
        Log::error('Incoming Message: ' . json_encode($request->all()));

        // Message received
        $message = $request->input('entry.0.changes.0.value.messages.0');

        $phone = '+' . $message['from'] ?? null;
        $text = strtolower(trim($message['text']['body'] ?? ''));
        $discount = SentDiscount::where('phone', $phone)->first();

        $promo = Coupon::find($discount->promo_id);
        $promoCode = $promo->code;
        if ($discount && $discount->status !== 'sent' && $text == 'start') {
            $discount->status = 'sent';
            $discount->user_replied = true;
            $discount->save();

            $promoCode = Coupon::where('id', $discount->promo_id)->first();

            try {

                // $whatsappSent = $this->sendViaMetaWhatsApp($phone, [$promoCode->code], 'offer_code');
                // $sentDiscount = SentDiscount::where('phone', $phone)->first();

                // if ($sentDiscount) {
                //     Subscriber::create([
                //         'email' => $sentDiscount->email,
                //         'phone' => $phone,
                //     ]);
                // }
                $details = (object)[
                    'pre_header' => 'Here is your exclusive promo code!',
                    'subject' => 'Here is your promo code!',
                    'content' => '<p>Use promo code <strong>' . $promoCode->code . '</strong> at checkout to get your offer.</p>',
                ];

                Mail::to($discount->email)->send(new SiteEmail($details));
                $sentDiscount = SentDiscount::where('phone', $phone)->first();

                // if ($sentDiscount) {
                //     Subscriber::create([
                //         'email' => $sentDiscount->email,
                //         'phone' => $phone,
                //     ]);
                // }
                $this->addGuestUser($discount->email, $phone);
            } catch (\Exception $e) {
                Log::error('Meta WhatsApp message error: ' . $e->getMessage());
            }
        }

        if ($text == 'stop') {
            try {
                $subscriber = Subscriber::where('phone', $phone)->first();

                if ($subscriber) {
                    $subscriber->email_notification_status = false;
                    $subscriber->sms_notification_status = false;
                    $subscriber->whatsapp_notification_status = false;
                    $subscriber->save();
                }
                $user = User::where('tel_cell', $phone);

                // Deserialize
                $preferences = @unserialize($user->alert_preferences);

                if ($preferences && is_array($preferences)) {
                    if (isset($preferences['promotional_alerts'])) {
                        $preferences['promotional_alerts']['sms'] = '0';
                    }
                }
                $user->alert_preferences = serialize($preferences);
                $user->save();
                $whatsappSent = $this->sendViaMetaWhatsApp($phone, ['there', 'all'], 'unsubscribe_notification');
            } catch (\Exception $e) {
                Log::error('Meta WhatsApp message error: ' . $e->getMessage());
            }
        }
    }

    public function twilioWebhook(Request $request)
    {
        $phone = $request->input('From'); // Format: whatsapp:+123456789 or +123456789
        $message = strtolower($request->input('Body'));
        Log::error('twilioWebhook message: ' . $message);
        $phone = str_replace('whatsapp:', '', $phone);

        Log::error('twilioWebhook phone: ' . $phone);
        Log::error('twilioWebhook all: ' . json_encode($request->all()));

        $discount = SentDiscount::where('phone', $phone)->first();
        $site_data = Session::get('shop_logged_data');
        $site_info = Setting::first();
        $selectedCountry = $site_data->country ?? $site_info->country;
        $twilioSenderId = \App\Models\CountryMessageSetting::where('country_code', $selectedCountry)->value('twilio_sms_from') ?? env('TWILIO_SID');
        $promo = Coupon::find($discount->promo_id);
        $promoCode = $promo->code;
        if ($discount && $discount->status !== 'sent' && $message == 'start') {
            Log::error('twilioWebhook inside: ' . json_encode($promoCode));
            $discount->user_replied = true;
            $discount->status = 'sent';
            $discount->save();

            $promoCode = Coupon::where('id', $discount->promo_id)->first();

            try {
                $details = (object)[
                    'pre_header' => 'Here is your exclusive promo code!',
                    'subject' => 'Here is your promo code!',
                    'content' => '<p>Use promo code <strong>' . $promoCode->code . '</strong> at checkout to get your offer.</p>',
                ];

                Mail::to($discount->email)->send(new SiteEmail($details));
                // $twSent = $this->sendViaTwilioSms($phone, 'Here is your promo code: ' . $promoCode->promo_code, 'reply', $twilioSenderId);
                $sentDiscount = SentDiscount::where('phone', $phone)->first();

                // if ($sentDiscount) {
                //     Subscriber::create([
                //         'email' => $sentDiscount->email,
                //         'phone' => $phone,
                //     ]);
                // }
                $this->addGuestUser($discount->email, $phone);
            } catch (\Exception $e) {
                Log::error('twil error: ' . $e->getMessage());
            }
        }
        if ($message == 'stop') {
            try {
                $subscriber = Subscriber::where('phone', $phone)->first();

                if ($subscriber) {
                    $subscriber->email_notification_status = false;
                    $subscriber->sms_notification_status = false;
                    $subscriber->whatsapp_notification_status = false;
                    $subscriber->save();
                }
                $user = User::where('tel_cell', $phone);

                // Deserialize
                $preferences = @unserialize($user->alert_preferences);

                if ($preferences && is_array($preferences)) {
                    if (isset($preferences['promotional_alerts'])) {
                        $preferences['promotional_alerts']['sms'] = '0';
                    }
                }
                $user->alert_preferences = serialize($preferences);
                $user->save();
                $twSent = $this->sendViaTwilioSms($phone, "Hi, we're sorry to see you go. You've successfully unsubscribed from all notifications.", 'reply', $twilioSenderId);
            } catch (\Exception $e) {
                Log::error('Meta WhatsApp message error: ' . $e->getMessage());
            }
        }

        return response("OK", 200);
    }
    protected function handleStatusUpdate(array $statuses)
    {
        foreach ($statuses as $status) {
            $phone = $status['recipient_id'];
            $state = $status['status'];
            DB::table('message_statuses')
                ->where('phone', $phone)
                ->update(['status' => $state]);
            Log::info("Delivery status for $phone: $state");
        }
    }
    /**
     * Handle SquadPay webhook
     */
    public function handleSquadPayWebhook(Request $request)
    {
        try {
            $paymentMethod = PaymentMethod::where('name', 'squadpay')->first();
            if (!$paymentMethod) {
                Log::error('SquadPay payment method not found');
                return response('Payment method not configured', 404);
            }

            // Verify webhook signature
            if (!$this->verifySquadPaySignature($request, $paymentMethod)) {
                Log::error('SquadPay webhook signature verification failed');
                return response('Unauthorized', 401);
            }

            $payload = $request->all();
            $eventType = $payload['event'] ?? null;
            $data = $payload['data'] ?? [];

            Log::info('SquadPay webhook received', ['event' => $eventType, 'data' => $data]);

            switch ($eventType) {
                case 'charge_successful':
                    return $this->handleSquadPaySuccess($data, $paymentMethod);
                case 'charge_failed':
                    return $this->handleSquadPayFailure($data, $paymentMethod);
                case 'refund_successful':
                    return $this->handleSquadPayRefund($data, $paymentMethod);
                default:
                    Log::info('Unhandled SquadPay webhook event', ['event' => $eventType]);
                    return response('Event not handled', 200);
            }

        } catch (\Exception $e) {
            Log::error('SquadPay webhook error: ' . $e->getMessage(), ['request' => $request->all()]);
            return response('Server error', 500);
        }
    }

    /**
     * Handle Flutterwave webhook
     */
    public function handleFlutterwaveWebhook(Request $request)
    {
        try {
            $paymentMethod = PaymentMethod::where('name', 'flutterwave')->first();
            if (!$paymentMethod) {
                Log::error('Flutterwave payment method not found');
                return response('Payment method not configured', 404);
            }

            // Verify webhook signature
            if (!$this->verifyFlutterwaveSignature($request, $paymentMethod)) {
                Log::error('Flutterwave webhook signature verification failed');
                return response('Unauthorized', 401);
            }

            $payload = $request->all();
            $eventType = $payload['event'] ?? null;
            $data = $payload['data'] ?? [];

            Log::info('Flutterwave webhook received', ['event' => $eventType, 'data' => $data]);

            switch ($eventType) {
                case 'charge.completed':
                    return $this->handleFlutterwaveSuccess($data, $paymentMethod);
                case 'charge.failed':
                    return $this->handleFlutterwaveFailure($data, $paymentMethod);
                case 'transfer.completed':
                    return $this->handleFlutterwaveRefund($data, $paymentMethod);
                default:
                    Log::info('Unhandled Flutterwave webhook event', ['event' => $eventType]);
                    return response('Event not handled', 200);
            }

        } catch (\Exception $e) {
            Log::error('Flutterwave webhook error: ' . $e->getMessage(), ['request' => $request->all()]);
            return response('Server error', 500);
        }
    }

    /**
     * Handle Stripe webhook
     */
    public function handleStripeWebhook(Request $request)
    {
        try {
            $paymentMethod = PaymentMethod::where('name', 'stripe')->first();
            if (!$paymentMethod) {
                Log::error('Stripe payment method not found');
                return response('Payment method not configured', 404);
            }

            // Verify webhook signature
            if (!$this->verifyStripeSignature($request, $paymentMethod)) {
                Log::error('Stripe webhook signature verification failed');
                return response('Unauthorized', 401);
            }

            $payload = $request->all();
            $eventType = $payload['type'] ?? null;
            $data = $payload['data']['object'] ?? [];

            Log::info('Stripe webhook received', ['event' => $eventType, 'data' => $data]);

            switch ($eventType) {
                case 'checkout.session.completed':
                    return $this->handleStripeSuccess($data, $paymentMethod);
                case 'payment_intent.payment_failed':
                    return $this->handleStripeFailure($data, $paymentMethod);
                case 'charge.dispute.created':
                    return $this->handleStripeDispute($data, $paymentMethod);
                case 'invoice.payment_succeeded':
                    return $this->handleStripeRecurringSuccess($data, $paymentMethod);
                default:
                    Log::info('Unhandled Stripe webhook event', ['event' => $eventType]);
                    return response('Event not handled', 200);
            }

        } catch (\Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage(), ['request' => $request->all()]);
            return response('Server error', 500);
        }
    }

    /**
     * Handle PayPal webhook
     */
    public function handlePayPalWebhook(Request $request)
    {
        try {
            $paymentMethod = PaymentMethod::where('name', 'paypal')->first();
            if (!$paymentMethod) {
                Log::error('PayPal payment method not found');
                return response('Payment method not configured', 404);
            }

            // Verify webhook signature
            if (!$this->verifyPayPalSignature($request, $paymentMethod)) {
                Log::error('PayPal webhook signature verification failed');
                return response('Unauthorized', 401);
            }

            $payload = $request->all();
            $eventType = $payload['event_type'] ?? null;
            $resource = $payload['resource'] ?? [];

            Log::info('PayPal webhook received', ['event' => $eventType, 'resource' => $resource]);

            switch ($eventType) {
                case 'CHECKOUT.ORDER.APPROVED':
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePayPalSuccess($resource, $paymentMethod);
                case 'PAYMENT.CAPTURE.DENIED':
                case 'CHECKOUT.ORDER.VOIDED':
                    return $this->handlePayPalFailure($resource, $paymentMethod);
                case 'PAYMENT.CAPTURE.REFUNDED':
                    return $this->handlePayPalRefund($resource, $paymentMethod);
                default:
                    Log::info('Unhandled PayPal webhook event', ['event' => $eventType]);
                    return response('Event not handled', 200);
            }

        } catch (\Exception $e) {
            Log::error('PayPal webhook error: ' . $e->getMessage(), ['request' => $request->all()]);
            return response('Server error', 500);
        }
    }

    /**
     * Handle BTCPay webhook
     */
    public function handleBTCPayWebhook(Request $request)
    {
        try {
            $paymentMethod = PaymentMethod::where('name', 'btcpay')->first();
            if (!$paymentMethod) {
                Log::error('BTCPay payment method not found');
                return response('Payment method not configured', 404);
            }

            $payload = $request->all();
            $eventType = $payload['type'] ?? null;
            $data = $payload['data'] ?? [];

            Log::info('BTCPay webhook received', ['event' => $eventType, 'data' => $data]);

            switch ($eventType) {
                case 'InvoiceSettled':
                case 'InvoiceProcessing':
                    return $this->handleBTCPaySuccess($data, $paymentMethod);
                case 'InvoiceExpired':
                case 'InvoiceInvalid':
                    return $this->handleBTCPayFailure($data, $paymentMethod);
                default:
                    Log::info('Unhandled BTCPay webhook event', ['event' => $eventType]);
                    return response('Event not handled', 200);
            }

        } catch (\Exception $e) {
            Log::error('BTCPay webhook error: ' . $e->getMessage(), ['request' => $request->all()]);
            return response('Server error', 500);
        }
    }

    /**
     * Handle BNPL webhooks (Klarna, Affirm, Afterpay)
     */
    public function handleKlarnaWebhook(Request $request)
    {
        return $this->handleBNPLWebhook($request, 'klarna');
    }

    public function handleAffirmWebhook(Request $request)
    {
        return $this->handleBNPLWebhook($request, 'affirm');
    }

    public function handleAfterpayWebhook(Request $request)
    {
        return $this->handleBNPLWebhook($request, 'afterpay');
    }

    /**
     * Generic BNPL webhook handler
     */
    protected function handleBNPLWebhook(Request $request, string $providerName)
    {
        try {
            $paymentMethod = PaymentMethod::where('name', $providerName)->first();
            if (!$paymentMethod) {
                Log::error("{$providerName} payment method not found");
                return response('Payment method not configured', 404);
            }

            $payload = $request->all();
            Log::info("{$providerName} webhook received", ['payload' => $payload]);

            // Each BNPL provider has different webhook formats
            // This is a generic implementation that should be customized per provider
            $eventType = $payload['event_type'] ?? $payload['type'] ?? $payload['event'] ?? null;
            
            switch ($eventType) {
                case 'order_authorized':
                case 'payment_authorized':
                case 'checkout_success':
                    return $this->handleBNPLSuccess($payload, $paymentMethod);
                case 'order_cancelled':
                case 'payment_failed':
                case 'checkout_failed':
                    return $this->handleBNPLFailure($payload, $paymentMethod);
                case 'refund_issued':
                case 'order_refunded':
                    return $this->handleBNPLRefund($payload, $paymentMethod);
                default:
                    Log::info("Unhandled {$providerName} webhook event", ['event' => $eventType]);
                    return response('Event not handled', 200);
            }

        } catch (\Exception $e) {
            Log::error("{$providerName} webhook error: " . $e->getMessage(), ['request' => $request->all()]);
            return response('Server error', 500);
        }
    }

    // Signature verification methods
    protected function verifySquadPaySignature(Request $request, PaymentMethod $paymentMethod): bool
    {
        $signature = $request->header('X-Squad-Signature');
        $webhookSecret = $paymentMethod->getApiConfig()['webhook_secret'] ?? '';
        
        if (!$signature || !$webhookSecret) {
            return false;
        }

        $computedSignature = hash_hmac('sha512', $request->getContent(), $webhookSecret);
        return hash_equals($computedSignature, $signature);
    }

    protected function verifyFlutterwaveSignature(Request $request, PaymentMethod $paymentMethod): bool
    {
        $signature = $request->header('verif-hash');
        $webhookSecret = $paymentMethod->getApiConfig()['webhook_secret'] ?? '';
        
        if (!$signature || !$webhookSecret) {
            return false;
        }

        return hash_equals($webhookSecret, $signature);
    }

    protected function verifyStripeSignature(Request $request, PaymentMethod $paymentMethod): bool
    {
        $signature = $request->header('Stripe-Signature');
        $webhookSecret = $paymentMethod->getApiConfig()['webhook_secret'] ?? '';
        
        if (!$signature || !$webhookSecret) {
            return false;
        }

        // This is a simplified verification - use Stripe SDK for production
        return true; // Implement proper Stripe signature verification
    }

    protected function verifyPayPalSignature(Request $request, PaymentMethod $paymentMethod): bool
    {
        // PayPal signature verification implementation
        // This is provider-specific and should use PayPal SDK
        return true; // Implement proper PayPal signature verification
    }

    // Success handlers
    protected function handleSquadPaySuccess(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentSuccess($data, $paymentMethod, [
            'transaction_reference' => $data['transaction_ref'] ?? null,
            'transaction_id' => $data['gateway_transaction_ref'] ?? null,
            'amount' => ($data['amount'] ?? 0) / 100, // Convert from kobo
            'currency' => $data['currency'] ?? 'NGN',
            'customer_email' => $data['email'] ?? null,
        ]);
    }

    protected function handleFlutterwaveSuccess(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentSuccess($data, $paymentMethod, [
            'transaction_reference' => $data['tx_ref'] ?? null,
            'transaction_id' => $data['id'] ?? null,
            'amount' => $data['amount'] ?? 0,
            'currency' => $data['currency'] ?? 'NGN',
            'customer_email' => $data['customer']['email'] ?? null,
        ]);
    }

    protected function handleStripeSuccess(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentSuccess($data, $paymentMethod, [
            'transaction_reference' => $data['metadata']['reference'] ?? null,
            'transaction_id' => $data['id'] ?? null,
            'amount' => ($data['amount_total'] ?? 0) / 100, // Convert from cents
            'currency' => strtoupper($data['currency'] ?? 'USD'),
            'customer_email' => $data['customer_details']['email'] ?? null,
        ]);
    }

    protected function handlePayPalSuccess(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentSuccess($data, $paymentMethod, [
            'transaction_reference' => $data['custom_id'] ?? null,
            'transaction_id' => $data['id'] ?? null,
            'amount' => $data['amount']['value'] ?? 0,
            'currency' => $data['amount']['currency_code'] ?? 'USD',
            'customer_email' => $data['payer']['email_address'] ?? null,
        ]);
    }

    protected function handleBTCPaySuccess(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentSuccess($data, $paymentMethod, [
            'transaction_reference' => $data['orderId'] ?? null,
            'transaction_id' => $data['id'] ?? null,
            'amount' => $data['price'] ?? 0,
            'currency' => $data['currency'] ?? 'BTC',
            'customer_email' => $data['buyer']['email'] ?? null,
        ]);
    }

    protected function handleBNPLSuccess(array $data, PaymentMethod $paymentMethod)
    {
        // Generic BNPL success handler - customize per provider
        return $this->processPaymentSuccess($data, $paymentMethod, [
            'transaction_reference' => $data['merchant_reference'] ?? $data['reference'] ?? null,
            'transaction_id' => $data['order_id'] ?? $data['payment_id'] ?? null,
            'amount' => $data['amount'] ?? 0,
            'currency' => $data['currency'] ?? 'USD',
            'customer_email' => $data['consumer']['email'] ?? $data['customer']['email'] ?? null,
        ]);
    }

    // Failure handlers
    protected function handleSquadPayFailure(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentFailure($data, $paymentMethod, [
            'transaction_reference' => $data['transaction_ref'] ?? null,
            'reason' => $data['message'] ?? 'Payment failed',
        ]);
    }

    protected function handleFlutterwaveFailure(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentFailure($data, $paymentMethod, [
            'transaction_reference' => $data['tx_ref'] ?? null,
            'reason' => $data['processor_response'] ?? 'Payment failed',
        ]);
    }

    protected function handleStripeFailure(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentFailure($data, $paymentMethod, [
            'transaction_reference' => $data['metadata']['reference'] ?? null,
            'reason' => $data['last_payment_error']['message'] ?? 'Payment failed',
        ]);
    }

    protected function handlePayPalFailure(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentFailure($data, $paymentMethod, [
            'transaction_reference' => $data['custom_id'] ?? null,
            'reason' => $data['reason_code'] ?? 'Payment failed',
        ]);
    }

    protected function handleBTCPayFailure(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentFailure($data, $paymentMethod, [
            'transaction_reference' => $data['orderId'] ?? null,
            'reason' => 'Payment expired or invalid',
        ]);
    }

    protected function handleBNPLFailure(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processPaymentFailure($data, $paymentMethod, [
            'transaction_reference' => $data['merchant_reference'] ?? $data['reference'] ?? null,
            'reason' => $data['decline_reason'] ?? $data['error_message'] ?? 'Payment failed',
        ]);
    }

    // Refund handlers
    protected function handleSquadPayRefund(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processRefund($data, $paymentMethod, [
            'transaction_reference' => $data['transaction_ref'] ?? null,
            'refund_id' => $data['refund_id'] ?? null,
            'amount' => ($data['refund_amount'] ?? 0) / 100,
        ]);
    }

    protected function handleFlutterwaveRefund(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processRefund($data, $paymentMethod, [
            'transaction_reference' => $data['reference'] ?? null,
            'refund_id' => $data['id'] ?? null,
            'amount' => $data['amount'] ?? 0,
        ]);
    }

    protected function handleStripeRefund(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processRefund($data, $paymentMethod, [
            'transaction_reference' => $data['metadata']['reference'] ?? null,
            'refund_id' => $data['id'] ?? null,
            'amount' => ($data['amount'] ?? 0) / 100,
        ]);
    }

    protected function handlePayPalRefund(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processRefund($data, $paymentMethod, [
            'transaction_reference' => $data['custom_id'] ?? null,
            'refund_id' => $data['id'] ?? null,
            'amount' => $data['amount']['value'] ?? 0,
        ]);
    }

    protected function handleBNPLRefund(array $data, PaymentMethod $paymentMethod)
    {
        return $this->processRefund($data, $paymentMethod, [
            'transaction_reference' => $data['merchant_reference'] ?? null,
            'refund_id' => $data['refund_id'] ?? null,
            'amount' => $data['refund_amount'] ?? 0,
        ]);
    }

    // Core processing methods
    protected function processPaymentSuccess(array $webhookData, PaymentMethod $paymentMethod, array $paymentInfo)
    {
        try {
            DB::beginTransaction();

            $reference = $paymentInfo['transaction_reference'];
            if (!$reference) {
                throw new \Exception('No transaction reference found in webhook');
            }

            // Find the order by reference
            $order = Order::where('payment_reference', $reference)->first();
            if (!$order) {
                Log::warning('Order not found for payment success', ['reference' => $reference]);
                DB::rollBack();
                return response('Order not found', 404);
            }

            // Update order status
            $order->update([
                'payment_status' => 'completed',
                'payment_confirmed_at' => now(),
                'transaction_id' => $paymentInfo['transaction_id'],
                'payment_provider_response' => $webhookData,
            ]);

            // Create transaction record
            $this->createTransactionRecord($order, $paymentMethod, $paymentInfo, 'success');

            // Send confirmation email
            $this->sendPaymentConfirmationEmail($order);

            // Update inventory
            $this->updateInventory($order);

            DB::commit();

            Log::info('Payment success processed', [
                'order_id' => $order->id,
                'reference' => $reference,
                'amount' => $paymentInfo['amount'],
                'payment_method' => $paymentMethod->name
            ]);

            return response('Payment success processed', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error processing payment success: ' . $e->getMessage(), [
                'webhook_data' => $webhookData,
                'payment_info' => $paymentInfo
            ]);
            return response('Error processing payment', 500);
        }
    }

    protected function processPaymentFailure(array $webhookData, PaymentMethod $paymentMethod, array $failureInfo)
    {
        try {
            $reference = $failureInfo['transaction_reference'];
            if (!$reference) {
                return response('No transaction reference', 400);
            }

            $order = Order::where('payment_reference', $reference)->first();
            if (!$order) {
                Log::warning('Order not found for payment failure', ['reference' => $reference]);
                return response('Order not found', 404);
            }

            $order->update([
                'payment_status' => 'failed',
                'payment_failed_at' => now(),
                'payment_failure_reason' => $failureInfo['reason'],
                'payment_provider_response' => $webhookData,
            ]);

            // Create transaction record
            $this->createTransactionRecord($order, $paymentMethod, $failureInfo, 'failed');

            // Send failure notification
            $this->sendPaymentFailureEmail($order, $failureInfo['reason']);

            Log::info('Payment failure processed', [
                'order_id' => $order->id,
                'reference' => $reference,
                'reason' => $failureInfo['reason'],
                'payment_method' => $paymentMethod->name
            ]);

            return response('Payment failure processed', 200);

        } catch (\Exception $e) {
            Log::error('Error processing payment failure: ' . $e->getMessage());
            return response('Error processing failure', 500);
        }
    }

    protected function processRefund(array $webhookData, PaymentMethod $paymentMethod, array $refundInfo)
    {
        try {
            DB::beginTransaction();

            $reference = $refundInfo['transaction_reference'];
            $order = Order::where('payment_reference', $reference)->first();
            
            if (!$order) {
                Log::warning('Order not found for refund', ['reference' => $reference]);
                DB::rollBack();
                return response('Order not found', 404);
            }

            // Create refund record
            $refund = $order->refunds()->create([
                'refund_id' => $refundInfo['refund_id'],
                'amount' => $refundInfo['amount'],
                'status' => 'completed',
                'payment_method_id' => $paymentMethod->id,
                'provider_response' => $webhookData,
                'processed_at' => now(),
            ]);

            // Update order refund status
            $totalRefunded = $order->refunds()->sum('amount');
            if ($totalRefunded >= $order->total_amount) {
                $order->update(['refund_status' => 'fully_refunded']);
            } else {
                $order->update(['refund_status' => 'partially_refunded']);
            }

            // Create transaction record
            $this->createTransactionRecord($order, $paymentMethod, $refundInfo, 'refund');

            // Send refund confirmation
            $this->sendRefundConfirmationEmail($order, $refund);

            DB::commit();

            Log::info('Refund processed', [
                'order_id' => $order->id,
                'refund_amount' => $refundInfo['amount'],
                'refund_id' => $refundInfo['refund_id']
            ]);

            return response('Refund processed', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error processing refund: ' . $e->getMessage());
            return response('Error processing refund', 500);
        }
    }

    protected function createTransactionRecord($order, PaymentMethod $paymentMethod, array $info, string $type)
    {
        return Transaction::create([
            'order_id' => $order->id,
            'payment_method_id' => $paymentMethod->id,
            'transaction_id' => $info['transaction_id'] ?? null,
            'reference' => $info['transaction_reference'] ?? null,
            'type' => $type,
            'amount' => $info['amount'] ?? 0,
            'currency' => $info['currency'] ?? 'USD',
            'status' => $type === 'failed' ? 'failed' : 'completed',
            'provider_response' => $info,
            'processed_at' => now(),
        ]);
    }

    protected function sendPaymentConfirmationEmail($order)
    {
        // Implementation for sending payment confirmation email
        // Use your existing email service
    }

    protected function sendPaymentFailureEmail($order, $reason)
    {
        // Implementation for sending payment failure email
    }

    protected function sendRefundConfirmationEmail($order, $refund)
    {
        // Implementation for sending refund confirmation email
    }

    protected function updateInventory($order)
    {
        // Implementation for updating inventory after successful payment
        // This should reduce stock quantities for ordered items
    }
}
