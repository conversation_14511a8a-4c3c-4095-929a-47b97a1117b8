<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddGlobalToPromosTable extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('promos') && !Schema::hasColumn('promos', 'global')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->boolean('global')->default(0)->after('id');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('promos') && Schema::hasColumn('promos', 'global')) {
            Schema::table('promos', function (Blueprint $table) {
                $table->dropColumn('global');
            });
        }
    }
}
