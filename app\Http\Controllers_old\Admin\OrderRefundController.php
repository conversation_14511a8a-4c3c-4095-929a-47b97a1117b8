<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OrderItem;
use App\Models\OrderRefund;
use App\Models\Product;
use App\Models\ProductItem;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\EmailTemplate;
use App\Mail\SiteEmail;
use App\Models\EmailTemplateGroup;
use App\Models\Order;
use App\Models\Tracking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class OrderRefundController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admin_page_title = 'Order Refunds';
        $items = OrderRefund::paginate($this->itemPerPage);
        foreach ($items as $value) {
            $value->refund_images = unserialize($value->refund_images);
            $user = User::find($value->user_id);
            $refund_by_user = User::find($value->refund_by_user_id);
            $order_item = OrderItem::where('sku', $value->sku)->first();
            $product = Product::find($order_item->product_id);
            // $product_item = ProductItem::find($order_item->product_item_id);
            // dd($product,$product_item);
            $value->details = 'Customer: '. $user->first_name .' '. $user->last_name.'<br />Product: ' . $product->title. '<br />SKU: ' . $value->sku;
            // $value->details = 'Product: ' . $product->title . '<br />Refund By: ' . $refund_by_user->first_name . '' . $refund_by_user->last_name;
            $value->status = $this->refund_status($value->status)->title;
            $value->date_time = Carbon::parse($value->created_at)->format('M, d Y g:i A');
            // dd($value);
        }
        // dd($items);
        $sl = SLGenerator($items);
        return view('admin.refund.index', compact('items', 'sl', 'admin_page_title'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        abort(404);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        abort(404);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\OrderRefund  $orderRefund
     * @return \Illuminate\Http\Response
     */
    public function show(OrderRefund $orderRefund)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\OrderRefund  $orderRefund
     * @return \Illuminate\Http\Response
     */
    public function edit(OrderRefund $orderRefund)
    {
        $admin_page_title = 'Update Refund';
        $orderRefund->refundStatus = $this->refund_status();
        $orderRefund->refund_images = unserialize($orderRefund->refund_images);
        $orderRefund->user = User::find($orderRefund->user_id);

        $orderRefund->refund_by_user = User::find($orderRefund->refund_by_user_id);
        $order = Order::find($orderRefund->order_id);
        // dd($order);
        $order_item = OrderItem::where('sku', $orderRefund->sku)->first();
        $orderRefund->product = Product::find($order_item->product_id);
        $orderRefund->product_item = ProductItem::find($order_item->product_item_id);
        // $orderRefund->status = $this->refund_status($orderRefund->status)->title;
        $orderRefund->date_time = Carbon::parse($orderRefund->created_at)->format('M, d Y g:i A');
        $orderRefund->details = 'Customer: ' . $orderRefund->user->first_name . ' ' . $orderRefund->user->last_name . '<br />Product: ' . $orderRefund->product->title . '<br />Order: #' .$order->order_no.'<br />SKU: ' . $orderRefund->sku .'<br />Order Price: $'. $order_item->price;

        $orderRefund->email_templates = EmailTemplate::where('status', true)->get();
        $orderRefund->email_constants = $this->email_constants();
        $send_sms_emails = Tracking::where('order_refund_id', $orderRefund->id)->get();
        foreach ($send_sms_emails as $value) {
            $value->date_time = Carbon::parse($value->created_at)->format('F j, Y g.iA');
        }
        $orderRefund->send_sms_emails = $send_sms_emails;
        $orderRefund->email_template_groups = EmailTemplateGroup::where('system_group', 'refund')->get();

        $item = $orderRefund;
        // dd($item);

        return view('admin.refund.edit', compact('item', 'admin_page_title'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\OrderRefund  $orderRefund
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, OrderRefund $orderRefund)
    {
        $this->validate($request, [
            'status' => 'required|numeric',
            'amount' => 'required|numeric|between:0,9999999999.99',
        ]);

        if ($request->action == 'send_email_sms') {
            $this->validate($request, [
                'subject' => 'required|string',
                'pre_header' => 'required|string',
                'email_body' => 'required|string',
                'sms_body' => 'nullable|string',
            ]);

            $user = User::find($orderRefund->user_id);

            $mail_data = (object)[
                'user' => $user,
                'refund' => $orderRefund,
                'subject' => $request->subject,
                'pre_header' => $request->pre_header,
                'email_body' => $request->email_body,
                'sms_status' => $request->sms_status ? true : false,
                'sms_body' => $request->sms_body,
            ];
            $mail_data->subject = $this->sendEmailData($mail_data)->subject;
            $mail_data->pre_header = $this->sendEmailData($mail_data)->pre_header;
            $mail_data->content = $this->sendEmailData($mail_data)->content;
            $mail_data->sms_content = $this->sendEmailData($mail_data)->sms_body;

            $lead_source = (object)[
                'admin_id' => auth()->user()->id,
                'admin_email' => auth()->user()->email,
                'admin_phone' => auth()->user()->tel_cell_country_code . auth()->user()->tel_cell,
                'source' => 'admin',
            ];

            $tracking = new Tracking();
            $tracking->order_refund_id = $orderRefund->id;
            $tracking->content = serialize($this->sendEmailData($mail_data));
            $tracking->from_email = config('mail.from.address');
            $tracking->to_email = $user->email;
            $tracking->subject = $request->subject;
            $tracking->lead_source = serialize($lead_source);
            $tracking->ip = $this->getClientIpAddress();
            $tracking->save();

            Mail::to($user->email)->send(new SiteEmail($mail_data));

            if ($request->sms_status && $user->tel_cell) {
                $sms_tracking = Tracking::findOrFail($tracking->id);
                $sms_tracking->sms_content = serialize($mail_data->sms_content);
                $sms_tracking->from_phone = config('app.twilio_number');
                $sms_tracking->to_phone = $user->tel_cell;
                $sms_tracking->update();
            }

            return back()->with('success', 'Review update Successful.');
        }

        $orderRefund->refund_by_user_id  = auth()->user()->id;
        $orderRefund->status = $request->status;
        $orderRefund->amount = $request->amount;
        $orderRefund->update();

        return (($request->get('btn') == 'update') ? back() : redirect()->route('admin.order-refunds.index'))->with('success', 'Refund update Successful.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\OrderRefund  $orderRefund
     * @return \Illuminate\Http\Response
     */
    public function destroy(OrderRefund $orderRefund)
    {
        abort(404);
    }
}
