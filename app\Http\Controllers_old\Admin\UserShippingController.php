<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserShipping;
use Illuminate\Http\Request;
use Validator;
use Session;
use Illuminate\Support\Facades\DB;

class UserShippingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($user_id = null,$type=null)
    {
        $checkout_order = Session::get('checkout_order');
        if(empty($user_id)){
            $user_shippings = collect(Session::get('user_shippings'));
        }else{
            $user_shippings = DB::table('user_shippings')->where('user_id', $user_id)->get();
            $session_user_shippings = collect(Session::get('user_shippings'))->where('user_id', $user_id);
            $user_shippings = collect($user_shippings->merge($session_user_shippings));
        }

        if (count($user_shippings) > 0) {
            return view('admin.jquery_live.user_shippings', compact('user_shippings', 'user_id', 'type', 'checkout_order'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $user_id = null)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:user_shippings',
            'tel_cell' => 'required|string|max:20|regex:/^\+?[0-9\s\-()]*$/',
           
            'city' => 'required|string',
            'state' => 'required|string',
            'country' => 'required|string',
            'postal_code' => 'required|integer',
            'address' => 'required|string',
            'address_2' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }
        $tel_cell = $request->get('tel_cell');
        $item = (object)([
            'id' => uniqid(),
            'user_id' => $user_id,
            'first_name' => $request->get('first_name'),
            'last_name' => $request->get('last_name'),
            'email' =>  $request->get('email'),
            'tel_cell' =>  preg_replace('/[^\d+]/', '', $tel_cell),
           
            'company_name' =>  $request->get('company_name'),
            'country' =>  $request->get('country'),
            'postal_code' =>  $request->get('postal_code'),
            'city' =>  $request->get('city'),
            'state' =>  $request->get('state'),
            'address' =>  $request->get('address'),
            'address_2' =>  $request->get('address_2'),
            'primary' => $request->get('primary') ?? 0,
        ]);

        
            $item = new UserShipping;
            $item->user_id = $user_id;
            $item->first_name = $request->get('first_name');
            $item->last_name = $request->get('last_name');
            $item->email = $request->get('email');
            $item->tel_cell =  preg_replace('/[^\d+]/', '', $tel_cell);
          
            $item->company_name = $request->get('company_name');
            $item->country = $request->get('country');
            $item->postal_code = $request->get('postal_code');
            $item->city = $request->get('city');
            $item->state = $request->get('state');
            $item->address = $request->get('address');
            $item->address_2 = $request->get('address_2');
            $item->primary = $request->get('primary') ?? 0;
            $item->save();

            $message = '<strong>' . $item->first_name . '</strong> added successful';

            return response()->json([
                'message' => $message,
            ], 200);
        

        if (Session::has('user_shippings')) {
            $user_shippings = Session::get('user_shippings');
            $user_shippings_collect = collect($user_shippings);
            if (!empty($user_shippings_collect)) {
                $items = $user_shippings_collect->toArray();
                if (count($items) > 10) {
                    return response()->json(
                        [
                            'message' =>  'limit end'
                        ],
                        400
                    );
                }
                array_push($items, $item);
            }
        } else {
            $items = array($item);
        }

        $user_shippings_items = collect($items);

        Session::put([
            'user_shippings' => $user_shippings_items,
        ]);

        $message = '<strong>' . $item->first_name . '</strong> added successful';

        return response()->json([
            'message' => $message,
        ], 200);

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserShipping  $userShipping
     * @return \Illuminate\Http\Response
     */
    public function show(UserShipping $userShipping)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\UserShipping  $userShipping
     * @return \Illuminate\Http\Response
     */
    public function edit($id, $user_id = null)
    {
        if($user_id){
            $user_shippings = DB::table('user_shippings')->where('user_id', $user_id)->get();
            $session_user_shippings = collect(Session::get('user_shippings'))->where('user_id', $user_id);
            $item = collect($user_shippings->merge($session_user_shippings))->where('id', $id)->first();
        }else{
            $user_shippings = collect(Session::get('user_shippings'));
            $item = collect($user_shippings)->where('id', $id)->first();
        }
        return response()->json($item, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserShipping  $userShipping
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id, $user_id = null)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            "email" => "required|email|unique:user_shippings,email," . $id,
            "tel_cell" => "required|string|max:20|regex:/^\+?[0-9\s\-()]*$/" ,
          
            'city' => 'required|string',
            'state' => 'required|string',
            'country' => 'required|string',
            'postal_code' => 'required|integer',
            'address' => 'required|string',
            'address_2' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'messages' => $validator->messages(),
            ], 400);
        }

        $user_shippings_items = collect(Session::get('user_shippings'));
        if($user_id){
            $item = UserShipping::findOrFail($id);
        }else{
            $item = collect($user_shippings_items)->where('id', $id)->first();
        }
        // dd($request->all());
        $item->user_id = $user_id;
        $item->first_name = $request->get('first_name');
        $item->last_name = $request->get( 'last_name');
        $item->email = $request->get( 'email');
        $item->tel_cell =preg_replace('/[^\d+]/', '', $request->get( 'tel_cell')) ;
       
        $item->company_name = $request->get( 'company_name');
        $item->country = $request->get( 'country');
        $item->postal_code = $request->get( 'postal_code');
        $item->city = $request->get( 'city');
        $item->state = $request->get( 'state');
        $item->address = $request->get( 'address');
        $item->address_2 = $request->get( 'address_2');
        $item->primary = $request->get('primary') ?? 0;
        // dd($item);
        if ($user_id) {
            $item->save();
        }

        $user_shippings = DB::table('user_shippings')->where('user_id', $user_id)->get();
        foreach($user_shippings as $user_shipping){
            $user_shipping = UserShipping::findOrFail($user_shipping->id);
            $user_shipping->primary = 0;
            $user_shipping->update();
            if($user_shipping->id == $item->id){
                $user_shipping->primary = $request->get('primary') ?? 0;
                $user_shipping->update();
            }
        }

        $message = '<strong>' . $item->first_name . '</strong> update successful';

        Session::put([
            'user_shippings' => $user_shippings_items,
        ]);

        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UserShipping  $userShipping
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, $user_id = null)
    {
        if ($user_id && !Session::has('user_shippings')) {
            $item = UserShipping::findOrFail($id);
            $message = '<strong>' . $item->first_name . '</strong> Delete successful';
            $item->delete();

            return response()->json([
                'message' => $message,
                'status' => 'success',
            ], 200);
        }

        if (Session::has('user_shippings')) {
            $user_shippings_items = collect(Session::get('user_shippings'));
            $item = collect($user_shippings_items)->where('id', $id)->first();
            $message = '<strong>' . $item->first_name . '</strong> Delete successful';

            foreach ($user_shippings_items as $key => $value) {
                if ($value->id == $id) {
                    unset($user_shippings_items[$key]);
                    break;
                }
            }

            Session::put([
                'user_shippings' => $user_shippings_items,
            ]);
        }

        return response()->json([
            'message' => $message,
            'status' => 'success',
        ], 200);
    }
}
