<?php

namespace App\Http\Controllers\Admin;

use App\Models\Coupon;
use App\Models\ProductCategory;
use App\Models\ProductBrand;
use App\Models\Product;
use App\Models\User;
use App\Models\ProductAttribute;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use App\Services\CouponService;

class CouponController extends Controller
{
    public function index(Request $request)
    {
        $admin_page_title = 'Manage Coupons';
        $categories = ProductCategory::all();
        $brands = ProductBrand::all();
        $products = Product::all();
        $attributes = ProductAttribute::all();
        //  $allCoupons = Coupon::latest()->paginate(10);
        $allCoupons = Coupon::paginate(10);

        $sl = SLGenerator($allCoupons);
        if ($request->view == 'html') {
            return view('admin.jquery_live.coupons', compact('sl', 'allCoupons', 'categories', 'brands', 'products', 'attributes', 'admin_page_title'));
        }
        return view('admin.coupons.index', compact('sl', 'allCoupons', 'categories', 'brands', 'products', 'attributes', 'admin_page_title'));
    }

    public function create()
    {
        $admin_page_title = 'Create Coupon';
        $categories = ProductCategory::all();
        $brands = ProductBrand::all();
        $products = Product::all();
        $attributes = ProductAttribute::all();

        return view('admin.coupons.create', compact('categories', 'brands', 'products', 'attributes', 'admin_page_title'));
    }

    public function store(Request $request)
    {

            $data = $request->validate([
                'image' => 'nullable|image|max:2048',
                'name' => 'required|string|max:255',
                'coupon_type' => 'required|in:general,conditional,shipping',
                'code' => 'required|string|max:255',
                'value_type' => 'required|in:discount,free_shipping',
                'discount_type' => 'nullable|in:percent,fixed',
                'discount_amount' => 'nullable|numeric|min:0',
                'description' => 'nullable|string',
                'status' => 'required|in:active,inactive',
                'customer_group' => 'nullable|string',
                'guest_checkout' => 'nullable|boolean',
                'condition_type' => 'nullable|string',
                'uses_per_coupon' => 'nullable|integer|min:0',
                'uses_per_customer' => 'nullable|integer|min:0',
                'from_date' => 'nullable|date',
                'to_date' => 'nullable|date|after_or_equal:from_date',
                'never_expire' => 'nullable|boolean',
            ], [
            'code.required' => 'The coupon code field is required.',
        ]);
            
            if ($request->hasFile('image')) {
                $data['image'] = $request->file('image')->store('coupons', 'public');
            }

            $data['guest_checkout'] = $request->has('guest_checkout');
            $data['never_expire'] = $request->has('never_expire');
            $data['show_in_promo_popup'] = $request->has('show_in_promo_popup');
            if ($request->show_in_promo_popup) {
                // Set all other coupons to false
                Coupon::where('show_in_promo_popup', true)->update(['show_in_promo_popup' => false]);
            }
            $coupon = Coupon::create($data);

            $this->attachConditions($coupon, $request);
        

        return (($request->get('btn') == 'save') ? back() : redirect()->route('admin.coupons.index'))->with('success', 'Coupon Added Successful.');

        //return redirect()->route('admin.coupons.index')->with('success', 'Coupon created successfully.');
    }

    public function edit(Coupon $coupon)
    {
        $admin_page_title = 'Edit Coupon';
        $categories = ProductCategory::all();
        $brands = ProductBrand::all();
        $products = Product::all();
        $attributes = ProductAttribute::all();


        return view('admin.coupons.edit', compact('coupon', 'categories', 'brands', 'products', 'attributes', 'admin_page_title'));
    }

    public function update(Request $request, Coupon $coupon)
    {
       // dd($request->all());
        //try {
        $data = $request->validate([
            'image' => 'nullable|image|max:2048',
            'name' => 'required|string|max:255',
            'coupon_type' => 'required|in:general,conditional,shipping',
            'code' => 'required|string|max:255',
            'value_type' => 'required|in:discount,free_shipping',
            'discount_type' => 'nullable|in:percent,fixed',
            'discount_amount' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'customer_groups' => 'nullable|string',
            'guest_checkout' => 'nullable|boolean',
            'condition_type' => 'nullable|string',
            'uses_per_coupon' => 'nullable|integer|min:0',
            'uses_per_customer' => 'nullable|integer|min:0',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
            'never_expire' => 'nullable|boolean',
            'show_in_promo_popup' => 'nullable|boolean'

        ], [
            'code.required' => 'The coupon code field is required.',
        ]);
        //dd($data);
        // if ($data->fails()) {
        //     return redirect()->back()->withErrors($data)->withInput();
        // }
        if ($request->hasFile('image')) {
            if ($coupon->image) {
                Storage::disk('public')->delete($coupon->image);
            }
            $data['image'] = $request->file('image')->store('coupons', 'public');
        }

        //$data['guest_checkout'] = $request->has('guest_checkout');
        //$data['never_expire'] = $request->has('never_expire');
        //$data['show_in_promo_popup'] = $request->has('show_in_promo_popup');
        if ($request->has('show_in_promo_popup')) {
            // Set all other coupons to false
            Coupon::where('show_in_promo_popup', true)->update(['show_in_promo_popup' => false]);
        }
        $coupon->update($data);

        $this->attachConditions($coupon, $request);
        // } catch (\Illuminate\Validation\ValidationException $e) {
        //   return redirect()->back()->withErrors($data)->withInput();

        // dd($e->errors()); // This will show which fields failed validation
        // }
        return (($request->get('btn') == 'save') ? redirect()->route('admin.coupons.create') : redirect()->route('admin.coupons.index'))->with('success', 'Coupon Updated Successful.');

        // return redirect()->route('admin.coupons.index')->with('success', 'Coupon updated successfully.');
    }

    public function destroy(Coupon $coupon)
    {
        if ($coupon->image) {
            Storage::disk('public')->delete($coupon->image);
        }
        $coupon->delete();

        // if (request()->wantsJson()) {
        //     return response()->json(['status' => 'success', 'message' => 'Coupon deleted successfully.']);
        // }

        // return redirect()->route('admin.coupons.index')->with('success', 'Coupon deleted successfully.');
        $message = '<strong>' . $coupon->name . '</strong> removed successful';

        return response()->json([
            'message' => $message,
        ], 200);
    }

    private function attachConditions(Coupon $coupon, Request $request)
    {
        // This method assumes the coupon has relationships like categories(), brands(), etc.
        if ($request->condition_type === 'category' && $request->has('categories')) {
            $coupon->productCategories()->sync($request->input('categories'));
        } elseif ($request->condition_type === 'brand' && $request->has('brands')) {
            $coupon->ProductBrands()->sync($request->input('brands'));
        } elseif ($request->condition_type === 'product' && $request->has('products')) {
            $coupon->products()->sync($request->input('products'));
        } elseif ($request->condition_type === 'attribute' && $request->has('attributes')) {
            $coupon->productAttributes()->sync($request->input('attributes'));
        } elseif ($request->condition_type === 'subtotal' && $request->has('subtotal')) {
            $coupon->update(['min_subtotal' => $request->input('subtotal')]);
        } else {
            // Clear all condition relationships if none selected
            $coupon->productCategories()->detach();
            $coupon->ProductBrands()->detach();
            $coupon->products()->detach();
            $coupon->productAttributes()->detach();
        }
    }


    public function applyCoupon(Request $request, User $user)
    {
        try {
            $request->validate(['code' => 'required|string']);


            // Assuming cartItems is a Collection of items with product relation
            $cartItems = []; // $user->cartItems;
            $couponService = new CouponService();


            // Validate coupon with user context
            $coupon = $couponService->validateCoupon($request->code, $user);

            // Apply coupon with coupon code, user, and cart items
            $result = $couponService->applyCoupon($request->code, $user, $cartItems);

            return response()->json($result);
        } catch (Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
