<?php

namespace App\Listeners;

use App\Events\PaymentSuccess;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendPaymentSuccessNotifications implements ShouldQueue
{
    protected $notifier;

    public function __construct(NotificationService $notifier)
    {
        $this->notifier = $notifier;
    }

    public function handle(PaymentFailed $event)
    {
        $this->notifier->sendCustomerPaymentSuccessNotification($event->user, $event->order);
    }
}
