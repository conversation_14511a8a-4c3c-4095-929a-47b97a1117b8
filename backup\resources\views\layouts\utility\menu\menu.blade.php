<div id="accordionMainMenu" class="accordion custom-scrollbar scrollbar-macosx">
    @foreach ($page->menu as $menu)
    <div class="card">
        <div class="card-header" id="heading{{ $menu->id }}">
            <h5 class="mb-0">
                @if(count($menu->children) > 0)
                <a id="menu_id_{{ $menu->id }}" class="menu_id_{{ $menu->id }} {{ $menu->menu_active ?'active':'' }}{{ count($menu->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}>
                    <span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span>
                </a>

                <button class="btn btn-link collapsed accordion-btn" data-toggle="collapse" data-target="#collapse{{ $menu->id }}" aria-expanded="false" aria-controls="collapse{{ $menu->id }}">
                    <i class="fas fa-plus"></i><i class="fas fa-minus"></i>
                </button>
                @else
                <a class="{{ $menu->menu_active ?'active ':'' }}{{ count($menu->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}>
                    <span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span>
                </a>
                @endif
            </h5>
        </div>
        @if(count($menu->children) > 0)
        <div id="collapse{{ $menu->id }}" class="collapse" aria-labelledby="heading{{ $menu->id }}" data-parent="#accordionMainMenu" data-height="{{ (count($menu->menuChildren) * 54) + 20 }}px" data-id={{ $menu->id }}>
            <div class="card-body custom-scrollbar scrollbar-macosx">
                @include('layouts.utility.menu.sub_menu',[
                    'items' => $menu->menuChildren,
                    'level' => 2,
                    'menu' => $menu,
                ])
            </div>
        </div>
        {{-- <script type="text/javascript">
        $(document).ready(function() {
            setTimeout(function() {
                let id = "{{ $menu->id }}";
                let accordion_body_height = $('#collapse'+id).attr('data-height');
                $('.block.menu-accordion .card #collapse'+id +' .card-body').css('height',accordion_body_height+'px');
            }, 700);
        });
        </script> --}}
        @endif
    </div>
    @endforeach
</div>



{{-- <ul class="custom-scrollbar scrollbar-macosx">
    @foreach ($page->menu as $menu)
        @if($menu->access)
            <li class="{{ count($menu->menuChildren) > 0 ? 'parent' : '' }}">
                <a class="{{ $menu->menu_active ?'active ':'' }} have-child-sub-items" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}>
                    @if($menu->menu_icon)<i class="{{ $menu->menu_icon }}"></i>@endif
                    <span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span>
                    @if(count($menu->menuChildren) > 0)<i class="fas fa-plus"></i><i class="fas fa-minus"></i>@endif
                </a>
                @if(count($menu->menuChildren) > 0)
                    @include('layouts.utility.menu.sub_menu',[
                        'items' => $menu->menuChildren,
                    ])
                @endif
            </li>
        @endif
    @endforeach
</ul> --}}

{{-- <div id="accordionMainMenu" class="accordion custom-scrollbar scrollbar-macosx">
    @foreach ($page->menu as $menu)
    <div class="card">
        <div class="card-header" id="heading{{ $menu->id }}">
        <h5 class="mb-0">
            @if(count($menu->children)> 0)
            <a id="menu_id_{{ $menu->id }}" class="menu_id_{{ $menu->id }} {{ $menu->menu_active ?'active':'' }}{{ count($menu->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}><span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span></a>

            <button class="btn btn-link collapsed accordion-btn" data-toggle="collapse" data-target="#collapse{{ $menu->id }}" aria-expanded="false" aria-controls="collapse{{ $menu->id }}">
                <i class="fas fa-plus"></i><i class="fas fa-minus"></i>
            </button>
            @else
            <a class="{{ $menu->menu_active ?'active ':'' }}{{ count($menu->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}><span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span></a>
            @endif
        </h5>
        </div>
        @if(count($menu->children) > 0)
        <div id="collapse{{ $menu->id }}" class="collapse" aria-labelledby="heading{{ $menu->id }}" data-parent="#accordionMainMenu" data-height="{{ (count($menu->menuChildren) * 54) + 20 }}">
            <div class="card-body custom-scrollbar scrollbar-macosx">
                @include('layouts.utility.menu.sub_menu',[
                    'items' => $menu->menuChildren,
                    'menu_id' => $menu->id,
                ])
            </div>
        </div>
        <script type="text/javascript">
        setTimeout(function() {
            let id = "{{ $menu->id }}";
            let accordion_body_height = $('#collapse'+id).attr('data-height');
            $('.block.menu-accordion .card #collapse'+id +' .card-body').css('height',accordion_body_height+'px');
        }, 300);
        </script>
        @endif
    </div>
    @endforeach --}}
  {{-- <div class="card">
    <div class="card-header" id="headingTwo">
      <h5 class="mb-0">
        <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
          Collapsible Group Item #2
        </button>
      </h5>
    </div>
    <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
      <div class="card-body">
        Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-header" id="headingThree">
      <h5 class="mb-0">
        <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
          Collapsible Group Item #3
        </button>
      </h5>
    </div>
    <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-parent="#accordion">
      <div class="card-body">
        Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.
      </div>
    </div>
  </div> --}}
{{-- </div> --}}

{{-- <div id="accordionMainMenu" class="accordion custom-scrollbar scrollbar-macosx">
    @foreach ($page->menu as $menu)
        @if($menu->access)
        <div class="card">
            <div class="card-header" id="heading{{ $menu->id }}">
                @if(count($menu->children)> 0)
                <a id="menu_id_{{ $menu->id }}" class="menu_id_{{ $menu->id }} {{ $menu->menu_active ?'active':'' }}{{ count($menu->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}><span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span></a>

                <button class="btn collapsed accordion-btn" type="button" data-toggle="collapse" data-target="#collapse{{ $menu->id }}" aria-expanded="false" aria-controls="collapse{{ $menu->id }}">
                    <i class="fas fa-plus"></i><i class="fas fa-minus"></i>
                </button>
                @else
                <a class="{{ $menu->menu_active ?'active ':'' }}{{ count($menu->children) > 0 ? ' have-child-sub-items' : '' }}" href="{{ $menu->url_type=='custom_url'? $menu->slug : route('page', [$menu->slug]) }}" {{$menu->url_type=='custom_url'?'target="_blank"':''}}><span>{{$menu->menu_name?$menu->menu_name:$menu->title}}</span></a>
                @endif
            </div>
            @if(count($menu->children)> 0)
            <div id="collapse{{ $menu->id }}" class="collapse card-body-wrapper" aria-labelledby="heading{{ $menu->id }}" data-parent="#accordionMainMenu">
                <div class="card-body custom-scrollbar scrollbar-macosx">
                    @include('layouts.utility.menu.sub_menu',[
                        'items' => $menu->menuChildren,
                        'menu_id' => $menu->id,
                    ])
                </div>
            </div>
            @endif
        </div>
        @endif
    @endforeach
</div> --}}
{{-- data-parent="#accordionMainMenu" --}}
