<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Roboto,Arial;font-size:12pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Roboto,Arial;font-size:12pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:docs-Roboto,Arial;font-size:12pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:docs-Roboto,Arial;font-size:12pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1142694392C0" style="width:173px;" class="column-headers-background">A</th><th id="1142694392C1" style="width:2210px;" class="column-headers-background">B</th></tr></thead><tbody><tr style="height: 20px"><th id="1142694392R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0">Field</td><td class="s1">Description</td></tr><tr style="height: 20px"><th id="1142694392R1" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">2</div></th><td class="s2">Coupon Image</td><td class="s3">Graphics of coupon</td></tr><tr style="height: 20px"><th id="1142694392R2" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">3</div></th><td class="s2">Coupon Name</td><td class="s3">Give the Coupon a name for Internal Reference.</td></tr><tr style="height: 210px"><th id="1142694392R3" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">4</div></th><td class="s2">Coupon Type</td><td class="s3"><span style="font-size:12pt;font-family:Roboto,Arial;font-weight:bold;color:#000000;">General Coupon: </span><span style="font-size:12pt;font-family:Roboto,Arial;color:#000000;">(No condition needed)<br><br></span><span style="font-size:12pt;font-family:Roboto,Arial;font-weight:bold;color:#000000;">Conditional Coupon:</span><span style="font-size:12pt;font-family:Roboto,Arial;color:#000000;"><br>- Automatic Couply Apply - No coupon code needed. Discount will be applied automatically in the cart, if the conditions are met.<br>- Specific Coupon - Customers will need to enter a predefined code before they can receive the discount. Once you select this option another field will open called Coupon Code (required if you have selected this option) where you can set the code. Use numbers or letter only. Coupon condition also need to be met<br><br></span><span style="font-size:12pt;font-family:Roboto,Arial;font-weight:bold;color:#000000;">Shipping Coupon:</span><span style="font-size:12pt;font-family:Roboto,Arial;color:#000000;"> (applies only for shipping)<br> </span></td></tr><tr style="height: 20px"><th id="1142694392R4" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">5</div></th><td class="s2">Coupon Code</td><td class="s3">The Code that customer can use to get discounts</td></tr><tr style="height: 20px"><th id="1142694392R5" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">6</div></th><td class="s2">Coupon value</td><td class="s3">Two Values:<br>    - Discount (fixed or percent)(This value if selected will always apply to Cart Subtotal or individual cart item)<br>    - Free Shipping ( Automatically no shipping fee collected)</td></tr><tr style="height: 20px"><th id="1142694392R6" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">7</div></th><td class="s2">Description</td><td class="s3">Also for Internal Reference, you can leave a more detailed note about the use or function of this coupon.</td></tr><tr style="height: 20px"><th id="1142694392R7" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">8</div></th><td class="s2">Status</td><td class="s3">Use Active for the coupon to be in affect as soon as it is saved, or as soon as the From Date begins<br>Use Inactive if this discount has expired or is a work in progress<br></td></tr><tr style="height: 20px"><th id="1142694392R8" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">9</div></th><td class="s2">Customer Groups</td><td class="s3">Two Values:<br>    - New<br>    - Active</td></tr><tr style="height: 20px"><th id="1142694392R9" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">10</div></th><td class="s2">Guest Checkout</td><td class="s3">Checkbox if checked coupon can be used during guest checkout</td></tr><tr style="height: 160px"><th id="1142694392R10" style="height: 160px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 160px">11</div></th><td class="s2">Coupon Type Condition</td><td class="s3" dir="ltr"><span style="font-weight:bold;">for Conditional Coupon</span>:<br>- no condition<br>- Cart Subtotal greater than or equal a particular value<br>-  Based on Category. for example, 20% off when you buy 5 phone and 5 tablets<br>-  Based of Brand.  for example, 20% off when 2 Apple products<br>-  Based on Products. For example, free shipping on all iPhone 16 Pro Max<br>-  Based SKU Attributes. For example, buy one get one FREE when for all 64 GB iPhones</td></tr><tr style="height: 20px"><th id="1142694392R11" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">12</div></th><td class="s2">Uses per Coupon</td><td class="s3">- limit the number of times the coupon can be used storewide. For example if you have a flash sale where the first 100 customers get a discount, but the 101st person to use the discount will not be able to use the coupon and the promotion will be over.</td></tr><tr style="height: 20px"><th id="1142694392R12" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">13</div></th><td class="s2">Uses per Customer</td><td class="s3">- Enter a numerical value to enforce how many times a customer can use the discount. This will only work for logged in customers and not guest purchases - you may want to deselect &quot;NOT LOGGED IN&quot; from the Customer Groups.</td></tr><tr style="height: 20px"><th id="1142694392R13" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">14</div></th><td class="s2">From Date &amp; To Date</td><td class="s3">Set a date range for the rule to take effect. If you leave the date range empty the rule starts upon saving and will not expire.</td></tr><tr style="height: 20px"><th id="1142694392R14" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">15</div></th><td class="s2">Never Expire</td><td class="s3">Checkbox if checked coupon will never expire</td></tr><tr style="height: 20px"><th id="1142694392R15" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">16</div></th><td class="s2">Coupon Discount Type</td><td class="s3">Percentage or fixed cash value</td></tr><tr style="height: 20px"><th id="1142694392R16" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">17</div></th><td class="s2">Discount Amount</td><td class="s3">Numeric Value for the amount or percentage to be discounted from cart items or subtotal</td></tr></tbody></table></div>