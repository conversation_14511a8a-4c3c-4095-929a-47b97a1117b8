@extends('layouts.admin')
@section('title','New Page')
@section('content')
<section id="sub-nav">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <div class="block sub-nav-utility mb-0 pb-0 clearfix">
                    <a href="{{ route('admin.stores.index') }}" class="btn btn-primary float-left">Close</a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="block sub-nav-utility mb-0 pb-0 clearfix">
                    <div class="float-right">
                        <button type="submit" value="saveAndClose" name="btn" class="btn btn-primary float-left mr-1" form="create_form">Save & Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section>
    <div class="container-fluid">
        <form id="create_form" method="POST" action="{{ route('admin.stores.store') }}" enctype="multipart/form-data">
            @csrf
            <div class="row">
                <div class="col-md-4">
                    <div class="block br-0 pr-0 clearfix">
                        <div class="form-group">
                            <label for="site_name">Store Name *</label>
                            <input type="text" name="site_name" id="site_name" class="form-control {{ $errors->has('site_name') ? 'is-invalid':''}}" value="{{old('site_name')}}">
                            @if($errors->has('site_name'))
                            <small class="form-text text-danger">{{ $errors->first('site_name') }}</small>
                            @endif
                        </div>
                        <!--<div class="form-group">-->
                        <!--    <label for="site_slogan">Site Slogan *</label>-->
                        <!--    <input type="text" name="site_slogan" id="site_slogan" class="form-control {{ $errors->has('site_slogan') ? 'is-invalid':''}}" value="{{old('site_slogan') }}">-->
                        <!--    @if($errors->has('site_slogan'))-->
                        <!--    <small class="form-text text-danger">{{ $errors->first('site_slogan') }}</small>-->
                        <!--    @endif-->
                        <!--</div>-->
                        <!--<div class="form-group">-->
                        <!--    <label for="site_slogan">Site Link *</label>-->
                        <!--    <input type="text" name="site_link" id="site_link" class="form-control {{ $errors->has('site_link') ? 'is-invalid':''}}" value="{{old('site_link') }}">-->
                        <!--    @if($errors->has('site_link'))-->
                        <!--    <small class="form-text text-danger">{{ $errors->first('site_slogan') }}</small>-->
                        <!--    @endif-->
                        <!--</div>-->


                    </div>
                </div>
                <div class="col-md-8">
                    <div class="block">
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                @php
                                  use App\Http\Controllers\Controller;
                                  $countries = (new Controller)->countries();
                                @endphp

                                <label for="country">Location</label>

                                <select class="selectpicker form-control" name="country" id="country" data-live-search="true">
                                    @foreach ($countries as $key=>$value)
                                    <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                                @if ($errors->has('country'))
                                <span class="text-danger">
                                    <small>{{ $errors->first('country') }}</small>
                                </span>
                                @endif
                            </div>
                            <div class="form-group col-md-4">
                                <label for="site_name">Time Zone *</label>
                                <select name="site_time_zone" class="selectpicker" id="site_time_zone" data-live-search="true" data-width="100%">
                                    @foreach ($times_zones as $key =>$value)
                                    <option value="{{$key}}" {{ $key == old('site_time_zone') ? 'selected' : ''}}>{{$value}}</option>
                                    @endforeach
                                </select>
                            </div>


                             <div class="form-group col-md-4">
                                <label for="status">Store Pickup</label>
                                <div class="switch switch-checkbox m-0">
                                    <input id="store_pickup" name="store_pickup" type="checkbox" value="1" >
                                    <span class="toggle-outside">
                                        <span class="status-text">
                                            <span class="status-off">OFF</span>
                                            <span class="status-on">ON</span>
                                        </span>
                                        <span class="toggle-inside"></span>
                                    </span>
                                </div>
                                @if($errors->has('store_pickup'))
                                <small class="text-danger">{{ $errors->first('store_pickup') }}</small>
                                @endif
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="order_return_days">Order Return Days *</label>
                                <input type="text" name="order_return_days" id="order_return_days" class="form-control {{ $errors->has('order_return_days') ? 'is-invalid':''}}" value="{{old('order_return_days') }}">
                                @if($errors->has('order_return_days'))
                                <small class="form-text text-danger">{{ $errors->first('order_return_days') }}</small>
                                @endif
                            </div>
                            <div class="form-group col-md-6">
                                <label for="order_warranty_days">Order Warranty Days *</label>
                                <input type="text" name="order_warranty_days" id="order_warranty_days" class="form-control {{ $errors->has('order_warranty_days') ? 'is-invalid':''}}" value="{{old('order_warranty_days') }}">
                                @if($errors->has('order_warranty_days'))
                                <small class="form-text text-danger">{{ $errors->first('order_warranty_days') }}</small>
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="block mt-0 pt-0">
                        <div class="form-group" id="terms_condition">
                            <label class="clearfix" for="terms_condition">
                                <div class="float-left">Terms and Condition *</div>
                            </label>
                            <textarea id='terms_condition' name="terms_condition" id="terms_condition" class="form-control ckeditor {{ $errors->has('terms_condition') ? 'is-invalid':''}}" cols="30" rows="4">{{ old('terms_condition') }}</textarea>
                            @if($errors->has('terms_condition'))
                            <small class="form-text text-danger">{{ $errors->first('terms_condition') }}</small>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>
@endsection
