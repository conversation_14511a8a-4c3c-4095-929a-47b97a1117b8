<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    protected $fillable = [
        'name', 'coupon_type', 'code', 'value_type', 'discount_type', 'discount_amount',
        'description', 'status', 'customer_groups', 'guest_checkout', 'condition_type',
        'min_subtotal', 'uses_per_coupon', 'uses_per_customer', 'from_date', 'to_date', 'never_expire', 'image', 'show_in_promo_popup'
    ];

    protected $casts = [
        'customer_groups' => 'array',
        'guest_checkout' => 'boolean',
        'never_expire' => 'boolean',
        'from_date' => 'date',
        'to_date' => 'date',
    ];

    // Categories (for category condition)
    public function productCategories()
    {
        return $this->belongsToMany(ProductCategory::class, 'coupon_product_categories');
    }

    // Brands (for brand condition)
    public function productBrands()
    {
        return $this->belongsToMany(ProductBrand::class, 'coupon_product_brands');
    }

    // Products (for product condition)
    public function products()
    {
        return $this->belongsToMany(Product::class, 'coupon_products');
    }

    // Product Items (SKUs) (for SKU condition)
    public function productItems()
    {
        return $this->belongsToMany(ProductItem::class, 'coupon_product_items');
    }

    // Product Attributes (for attribute condition)
    public function productAttributes()
    {
        return $this->belongsToMany(ProductAttribute::class, 'coupon_product_attributes');
    }
    public function usages()
{
    return $this->hasMany(CouponUsage::class);
}

}
