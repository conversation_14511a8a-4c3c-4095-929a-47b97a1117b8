<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingMethodOptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_method_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_method_id')->nullable()->constrained('shipping_methods');
            $table->string('title');
            $table->decimal('amount', 20, 2);
            $table->integer('ordering')->default(0);
            $table->text('shipping_method_option_image')->nullable();
            $table->boolean('status')->default(0);
            $table->timestamps();
        });

        Schema::table('shipping_methods', function (Blueprint $table) {
            $table->dropColumn('sub_title');
            $table->dropColumn('amount');
            $table->dropColumn('ordering');
            $table->dropColumn('shipping_method_image');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_method_options');
    }
}
