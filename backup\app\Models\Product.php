<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;
    const INVENTORY_STATUS = [
        'in_stock',
        'in_stock_otm',
        'out_of_stock',
        'discontinued',
    ];
    public function getInventoryStatusLabelAttribute()
    {
        return match ($this->inventory_status) {
            'in_stock' => 'In Stock',
            'in_stock_otm' => 'In Stock OTM',
            'out_of_stock' => 'Out Of Stock',
            'discontinued' => 'Discontinued',
            default => 'N/A',
        };
    }
    public static function inventoryStatusOptions(): array
    {
        return [
            'in_stock' => 'In Stock',
            'in_stock_otm' => 'In Stock OTM',
            'out_of_stock' => 'Out Of Stock',
            'discontinued' => 'Discontinued',
        ];
    }
}
