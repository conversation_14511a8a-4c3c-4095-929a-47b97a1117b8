<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;
use App\Models\User;
use App\Models\UserAccessKey;
use App\Models\Page;
use App\Models\UserGroup;
use App\Models\UserGroupMap;
use App\Models\Component;
use App\Models\AccessLabel;
use App\Models\AlertPreference;
use Session;
use Illuminate\Support\Facades\Storage;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        Session::flush();

        Setting::updateOrCreate([
            'site_name' => 'Buy 1guygadget',
            'site_slogan' => 'Lorem Ipsum is simply dummy text',
            // 'tel_cell_country_code' => '88',
            'tel_cell' => '+885125180922',
            'email' => '<EMAIL>',
            'country' => 'USA',
            'address' => 'USA',
        ]);

        $master_login = User::updateOrCreate([
            'first_name' => 'Super',
            'last_name' => 'User',
            // 'tel_cell_country_code' => '1',
            'tel_cell' => '+15125180922',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'verified' => true,
            'address' => 'Unknown',
            'address_2' => 'Unknown',
            'password' => bcrypt('Superadmin1819'),
        ]);

        $manager_user = User::updateOrCreate([
            'first_name' => 'Manager',
            'last_name' => 'User',
            'username' => 'manager',
            'email' => '<EMAIL>',
            'verified' => true,
            'address' => 'Unknown',
            'address_2' => 'Unknown',
            'password' => bcrypt('Superadmin1819'),
        ]);

        $admin_user = User::updateOrCreate([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'username' => 'admin_login',
            'email' => '<EMAIL>',
            'verified' => true,
            'address' => 'Unknown',
            'address_2' => 'Unknown',
            'password' => bcrypt('Superadmin1819'),
        ]);

        $customer_user = User::updateOrCreate([
            'first_name' => 'Customer',
            'last_name' => 'User',
            'username' => 'customer',
            'email' => '<EMAIL>',
            'verified' => true,
            'address' => 'Unknown',
            'address_2' => 'Unknown',
            'password' => bcrypt('123456#'),
        ]);

        $master_login_access = UserAccessKey::updateOrCreate(['title' => 'Master Login Access', 'key' => 'master_login', 'type' => 'access']);
        $admin_login_view_access = UserAccessKey::updateOrCreate(['title' => 'Admin Login Access', 'key' => 'admin_login', 'type' => 'access']);
        $site_login_view_access = UserAccessKey::updateOrCreate(['title' => 'Site Login Access', 'key' => 'site_login', 'type' => 'access']);

        UserAccessKey::updateOrCreate(['title' => 'Access', 'key' => 'access', 'type' => 'component']);
        UserAccessKey::updateOrCreate(['title' => 'Add', 'key' => 'add', 'type' => 'component']);
        UserAccessKey::updateOrCreate(['title' => 'Edit', 'key' => 'edit', 'type' => 'component']);
        UserAccessKey::updateOrCreate(['title' => 'Delete', 'key' => 'delete', 'type' => 'component']);

        Component::updateOrCreate(['title' => 'User', 'key' => 'user']);
        Component::updateOrCreate(['title' => 'Page', 'key' => 'page']);
        Component::updateOrCreate(['title' => 'Setting', 'key' => 'setting']);
        Component::updateOrCreate(['title' => 'Product', 'key' => 'product']);
        Component::updateOrCreate(['title' => 'Product Category', 'key' => 'product_category']);
        Component::updateOrCreate(['title' => 'Product Brand', 'key' => 'product_brand']);
        Component::updateOrCreate(['title' => 'Product Series', 'key' => 'product_series']);
        Component::updateOrCreate(['title' => 'Product Attribute', 'key' => 'product_attribute']);
        Component::updateOrCreate(['title' => 'Promo', 'key' => 'promo']);
        Component::updateOrCreate(['title' => 'Email Template', 'key' => 'email_template']);
        Component::updateOrCreate(['title' => 'Email Template Group', 'key' => 'email_template_group']);
        Component::updateOrCreate(['title' => 'Email Campaign', 'key' => 'email_campaign']);
        Component::updateOrCreate(['title' => 'Review', 'key' => 'review']);
        Component::updateOrCreate(['title' => 'Order', 'key' => 'order']);

        // 𝗢𝗿𝗱𝗲𝗿 𝗘𝘃𝗲𝗻𝘁𝘀
        AlertPreference::create(['title' => 'New Order Alert (Website)', 'key' => 'new_order', 'type' => 'default']);
        AlertPreference::create(['title' => 'New Order Alert (Store Sale)', 'key' => 'new_order_store', 'type' => 'default']);
        // AlertPreference::create(['title' => 'Order Shipped Alert', 'key' => 'order_shipped', 'type' => 'default']);
        // AlertPreference::create(['title' => 'Order Delivered Alert', 'key' => 'order_delivered', 'type' => 'default']);
        // AlertPreference::create(['title' => 'Order Cancelled Alert', 'key' => 'order_cancelled', 'type' => 'default']);
        // AlertPreference::create(['title' => 'Order Refunded Alert', 'key' => 'order_refunded', 'type' => 'default']);

        AlertPreference::create(['title' => 'Order Status', 'key' => 'order_status', 'type' => 'default']);

        // 𝗣𝗮𝘆𝗺𝗲𝗻𝘁 𝗘𝘃𝗲𝗻𝘁𝘀
        AlertPreference::create(['title' => 'Payment Success Alert', 'key' => 'payment_success', 'type' => 'default']);
        AlertPreference::create(['title' => 'Payment Failed Alert', 'key' => 'payment_failed', 'type' => 'default']);

        // 𝗨𝘀𝗲𝗿 / 𝗔𝗰𝗰𝗼𝘂𝗻𝘁
        AlertPreference::create(['title' => 'New Account Registered Alert', 'key' => 'account_registered', 'type' => 'default']);
        AlertPreference::create(['title' => 'Password Reset Alert', 'key' => 'password_reset', 'type' => 'default']);

        // 𝗖𝗼𝗻𝘁𝗮𝗰𝘁 & 𝗠𝗮𝗿𝗸𝗲𝘁𝗶𝗻𝗴
        AlertPreference::create(['title' => 'Review Form Alert', 'key' => 'review_form', 'type' => 'default']);

        // 𝗣𝗿𝗼𝗺𝗼𝘁𝗶𝗼𝗻𝘀 & 𝗥𝗲𝘄𝗮𝗿𝗱𝘀
        AlertPreference::create(['title' => 'Coupon Used Alert', 'key' => 'coupon_used', 'type' => 'default']);
        AlertPreference::create(['title' => 'Redeem Rewards Alert', 'key' => 'redeem_rewards', 'type' => 'default']);

        // 𝗜𝗻𝘃𝗲𝗻𝘁𝗼𝗿𝘆 & 𝗪𝗶𝘀𝗵𝗹𝗶𝘀𝘁
        AlertPreference::create(['title' => 'Low Inventory Alert', 'key' => 'low_inventory', 'type' => 'default']);
        AlertPreference::create(['title' => 'Notify Me Order Alert', 'key' => 'notify_me_order', 'type' => 'default']);


        $public_user_group = UserGroup::updateOrCreate(['title' => 'Public', 'status' => true]);
        $guest_user_group = UserGroup::updateOrCreate(['title' => 'Guest', 'user_group_parent_id' => $public_user_group->id, 'status' => true]);
        $customer_user_group = UserGroup::updateOrCreate(['title' => 'Customer', 'user_group_parent_id' => $public_user_group->id, 'user_permission_accesses_id' => $site_login_view_access->id, 'status' => true]);
        $manager_user_group = UserGroup::updateOrCreate(['title' => 'Manager', 'user_group_parent_id' => $public_user_group->id, 'user_permission_accesses_id' => $admin_login_view_access->id]);
        $admin_user_group = UserGroup::updateOrCreate(['title' => 'Admin', 'user_group_parent_id' => $manager_user_group->id, 'user_permission_accesses_id' => $admin_login_view_access->id, 'status' => true]);
        $super_user_group = UserGroup::updateOrCreate(['title' => 'Super User', 'user_group_parent_id' => $public_user_group->id, 'user_permission_accesses_id' => $master_login_access->id, 'status' => true]);

        UserGroupMap::updateOrCreate(['user_id' => $master_login->id, 'user_group_id' => $super_user_group->id]);
        UserGroupMap::updateOrCreate(['user_id' => $manager_user->id, 'user_group_id' => $manager_user_group->id]);
        UserGroupMap::updateOrCreate(['user_id' => $admin_user->id, 'user_group_id' => $admin_user_group->id]);
        UserGroupMap::updateOrCreate(['user_id' => $customer_user->id, 'user_group_id' => $customer_user_group->id]);

        $public_access_label = AccessLabel::updateOrCreate(['title' => 'Public', 'user_groups_id' => $public_user_group->id, 'status' => true]);
        AccessLabel::updateOrCreate(['title' => 'Customer', 'user_groups_id' => $customer_user_group->id, 'status' => true]);
        AccessLabel::updateOrCreate(['title' => 'Guest', 'user_groups_id' => $guest_user_group->id, 'status' => true]);
        $special_access_label = AccessLabel::updateOrCreate(['title' => 'Special', 'user_groups_id' => $customer_user_group->id . ',' . $manager_user_group->id, 'status' => true]);
        AccessLabel::updateOrCreate(['title' => 'Super User', 'user_groups_id' => $super_user_group->id, 'status' => true]);

        Page::updateOrCreate(['title' => 'Home', 'slug' => 'home', 'is_menu' => true, 'menu_name' => 'Home', 'ordering' => 1, 'status' => 1, 'page_type' => 'build_page', 'page_key' => 'home', 'access_label_id' => $public_access_label->id, 'menu_location' => 'main_menu']);
        Page::updateOrCreate(['title' => 'My Profile', 'slug' => 'my-profile', 'is_menu' => true, 'menu_name' => 'Profile', 'ordering' => 1, 'status' => 1, 'page_type' => 'build_page', 'page_key' => 'my_profile', 'access_label_id' => $special_access_label->id, 'menu_location' => 'user_menu']);
        Page::updateOrCreate(['title' => 'My Orders', 'slug' => 'my-orders', 'is_menu' => true, 'menu_name' => 'Orders', 'ordering' => 1, 'status' => 1, 'page_type' => 'build_page', 'page_key' => 'my_orders', 'access_label_id' => $special_access_label->id, 'menu_location' => 'user_menu']);
        Page::updateOrCreate(['title' => 'Change Password', 'slug' => 'change-password', 'is_menu' => true, 'menu_name' => 'Change Password', 'ordering' => 1, 'status' => 1, 'page_type' => 'build_page', 'page_key' => 'change_password', 'access_label_id' => $special_access_label->id, 'menu_location' => 'user_menu']);
        Page::updateOrCreate(['title' => 'Cart', 'slug' => 'cart', 'ordering' => 1, 'status' => 1, 'page_type' => 'build_page', 'page_key' => 'cart', 'access_label_id' => $public_access_label->id]);
    }
}
