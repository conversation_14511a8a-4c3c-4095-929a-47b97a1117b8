<?php

namespace App\Services;

use DateTime;
use Exception;
use GuzzleHttp\Client;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;

class DHLService
{
    protected $client;
    protected $apiUrl;
    protected $headers;

    public function __construct() {}

    public function getRates($data)
    {
        if (
            isset($data['originCountryCode']) && isset($data['destinationCountryCode'])
            && $data['originCountryCode'] == $data['destinationCountryCode']
        ) {
            $data['isCustomsDeclarable'] = 'false';
        } else {
            $data['isCustomsDeclarable'] = 'true';
        }
        $url = 'https://express.api.dhl.com/mydhlapi/test/rates?' . http_build_query($data);


        $headers = [
            'Message-Reference' => 'ref_' . substr(md5(uniqid()), 0, 28), // Generate unique reference
            'Message-Reference-Date' => now()->toIso8601ZuluString(),
            'Plugin-Name' => 'MyPlugin',
            'Plugin-Version' => '1.0',
            'Shipping-System-Platform-Name' => 'MyPlatform',
            'Shipping-System-Platform-Version' => '1.0',
            'Webstore-Platform-Name' => 'MyWebstore',
            'Webstore-Platform-Version' => '1.0',
            'Authorization' => 'Basic ' . base64_encode('apB5lH4lW3qB4j:M@7aW$0iD^6yJ!1d')
        ];


        try {
            $response = Http::timeout(30)->withHeaders($headers)->get($url);

            if ($response->successful()) {
                $originalResponse = $response->json();
                $shippingMethods = $this->extractShippingMethods($originalResponse);
                return (object) [
                    'response' => $originalResponse,
                    'shipping_methods' => $shippingMethods,
                    'status' => $response->status()
                ];
            } else {
                // Log the error for debugging
                \Log::error('DHL API Error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'request_data' => $data
                ]);

                return (object)[
                    'error' => 'Failed to fetch shipping methods',
                    'status' => $response->status(),
                    'message' => $response->body()
                ];
            }
        } catch (\Exception $e) {
            \Log::error('DHL API Exception', ['message' => $e->getMessage()]);

            return (object)[
                'error' => 'Exception occurred while fetching shipping methods',
                'status' => 500,
                'message' => $e->getMessage()
            ];
        }
    }
    private function extractShippingMethods(array $response): array
    {
        $shippingMethods = [];


        if (isset($response['products'])) {
            foreach ($response['products'] as $product) {
                $shippingMethods[] = [
                    'productCode' => $product['productCode'] ?? null,
                    'productName' => $product['productName'] ?? null,
                    'totalPrice' => $product['totalPrice'] ?? null,
                    'currency' => $product['currency'] ?? null,
                    'deliveryCapabilities' => $product['deliveryCapabilities'] ?? null,
                    'weight' => $product['weight'] ?? null,
                    'isCustomerAgreement' => $product['isCustomerAgreement'] ?? null
                ];
            }
        }

        return $shippingMethods;
    }
    public function createShippment($data)
    {
        $shop_data = Session::get('shop_logged_data');
        $site_data = Setting::where('country', $shop_data->country)->where('status', 1)->first();
        $url = 'https://express.api.dhl.com/mydhlapi/test/shipments';
        $controller = new Controller();


        $username = 'apB5lH4lW3qB4j';
        $password = 'M@7aW$0iD^6yJ!1d';;
        $lineItems = collect($data->cart->items)->map(function ($item, $index) {
            return [
                "number" => $index + 1,
                "description" =>  "",
                "price" =>  intval($item->price),
                "quantity" => [
                    "value" => $index + 1,
                    "unitOfMeasurement" => "PCS"
                ],
                "commodityCodes" => [
                    ["typeCode" => "inbound", "value" => "8517.13.0000"],
                    ["typeCode" => "outbound", "value" => "8517.13.0001"]
                ],
                "exportReasonType" => "commercial_purpose_or_sale",
                "manufacturerCountry" => "PT",
                "weight" => [
                    "netValue" => 0.87,
                    "grossValue" => 1.05
                ]
            ];
        })->toArray();
        $packages = collect($data->cart->items)->map(function ($item, $index) {
            return [
                "typeCode" => "2BP",
                "weight" => floatval($item->box_with_weight ?? 4), // Convert to float
                "dimensions" => [
                    "length" => floatval($item->box_with_length ?? 10), // Convert to float
                    "width" => floatval($item->box_with_width ?? 15), // Convert to float
                    "height" => floatval($item->box_with_height ?? 10), // Convert to float
                ],
            ];
        })->toArray();

        $body = [
            "plannedShippingDateAndTime" => (new DateTime('+5 days'))->format('Y-m-d\TH:i:s\G\M\TP'),
            "pickup" => [
                "isRequested" => true,
                "specialInstructions" => [
                    [
                        "value" => "please ring door bell",
                        "typeCode" => "TBD"
                    ]
                ],
                "pickupDetails" => [
                    "postalAddress" => [
                        "postalCode"    => $site_data->zip_code ?? '',
                        "cityName"      => $site_data->city ?? '',
                        "countryCode"   => $site_data->country ?? '',
                        "provinceCode"  => $site_data->country ?? '',
                        "addressLine1"  => $site_data->address ?? '',
                        "addressLine2"  => 'address2',
                        "addressLine3"  => 'address3',
                        "countyName"    => $site_data->site_name ?? '',
                        "provinceName"  => isset($site_data->country) ? $controller->countries($site_data->country) : '',
                        "countryName"   => isset($site_data->country) ? $controller->countries($site_data->country) : '',
                    ],
                    "contactInformation" => [
                        "email" => $site_data->email ?? '',
                        "phone" => $site_data->tel_cell ?? '',
                        "mobilePhone" => $site_data->tel_cell ?? '',
                        "companyName" => $site_data->site_name ?? '',
                        "fullName" => $site_data->site_name ?? ''
                    ],
                    "typeCode" => "business"
                ],

            ],
            "productCodes" => collect($data->shipping->shipping_method_data)->map(function ($productCode) {

                return [
                    "productCode" => $productCode['shipping_rates']['id'],
                    "localProductCode" => $productCode['shipping_rates']['id'],
                ];
            })->toArray(),
            "getRateEstimates" => false,
            "accounts" => [
                [
                    "typeCode" => "shipper",
                    "number" => "*********"
                ]
            ],

            "customerDetails" => [
                "receiverDetails" => [
                    "postalAddress" => [
                        "postalCode" => $data->customer->postal_code,
                        "cityName" => $data->customer->city,
                        "countryCode" => $data->customer->country,
                        "addressLine1" => $data->customer->address,
                        "addressLine2" => "nothing",
                        "addressLine3" => "nothing",
                        "countryName" => $data->customer->country_name
                    ],
                    "contactInformation" => [
                        "email" => $data->customer->email,
                        "phone" => $data->customer->tel_cell,
                        "mobilePhone" => $data->customer->tel_cell,
                        "companyName" => $data->customer->company_name ?? 'nothing',
                        "fullName" => $data->customer->first_name . ' ' . $data->customer->last_name
                    ],
                    "typeCode" => "business"
                ],
                "shipperDetails" => [
                    "postalAddress" => [
                        "postalCode" => $site_data->zip_code ?? '',
                        "cityName" => $site_data->city ?? '',
                        "countryCode" => $site_data->country ?? '',
                        "addressLine1" => $site_data->address ?? '',
                        "addressLine2" => "nothing",
                        "addressLine3" => "nothing",
                        "countryName" => $controller->countries($site_data->country ?? '')
                    ],
                    "contactInformation" => [
                        "email" => $site_data->email ?? '',
                        "phone" => $site_data->tel_cell ?? '',
                        "mobilePhone" => $site_data->tel_cell ?? '',
                        "companyName" => $site_data->site_name ?? '',
                        "fullName" => $site_data->site_name ?? ''
                    ]
                ]
            ],
            "content" => [
                "exportDeclaration" => [
                    "exportReasonType" => "commercial_purpose_or_sale",
                    "invoice" => [
                        "number" => "W417679401",
                        "date" => date('Y-m-d'),
                    ],
                    "remarks" => [["value" => "RemarkDescription1"]],
                    "lineItems" => $lineItems
                ],
                "packages" => $packages,
                "isCustomsDeclarable" => true,
                "declaredValue" => 100,
                "declaredValueCurrency" => getDefaultCurrencyCode(),
                "description" => "testing",
                "incoterm" => "DAP",
                "unitOfMeasurement" => "metric"
            ],
            "documentImages" => [
                [
                    "typeCode" => "CIN",
                    "imageFormat" => "PDF",
                    "content" => "JVBERi0xLjcNCiW1tbW1DQoxIDAgb2JqDQo8PC9UeXBlL0NhdGFsb2cvUGFnZXMgL1hSZWZTdG0gMjgwMDg+Pg0Kc3RhcnR4cmVmDQoyODcyMw0KJSVFT0Y="
                ]
            ],
            "estimatedDeliveryDate" => [
                "isRequested" => true,
                "typeCode" => "QDDC"
            ],
        ];


        try {

            $response = Http::withBasicAuth($username, $password)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'x-version' => '2.0'
                ])
                ->post($url, $body);


            if ($response->successful()) {

                return (object) [
                    'response' => $response->json(),
                    'status_code' => $response->status()

                ];
            } else {
                // Handle error response
                return (object) [
                    'response' => $response->json(),
                    'error' => 'DHL API Error: ' . ($response->json('message') ?? 'Unknown error'),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            // Handle exceptions
            return (object) [
                'response' => null,
                'error' => 'Exception: ' . $e->getMessage(),
                'shipping_methods' => []
            ];
        }
    }
    public function bulkcreateShippment($data)
    {
        $shop_data = Session::get('shop_logged_data');
        $site_data = Setting::where('country', $shop_data->country)->where('status', 1)->first();
        $url = 'https://express.api.dhl.com/mydhlapi/test/shipments';
        $controller = new Controller();


        $username = 'apB5lH4lW3qB4j';
        $password = 'M@7aW$0iD^6yJ!1d';;
        $lineItems = collect($data->productItems)->map(function ($item, $index) {
            return [
                "number" => $index + 1,
                "description" =>  "",
                "price" =>  intval($item->sale_price),
                "quantity" => [
                    "value" => $index + 1,
                    "unitOfMeasurement" => "PCS"
                ],
                "commodityCodes" => [
                    ["typeCode" => "inbound", "value" => "8517.13.0000"],
                    ["typeCode" => "outbound", "value" => "8517.13.0001"]
                ],
                "exportReasonType" => "commercial_purpose_or_sale",
                "manufacturerCountry" => "PT",
                "weight" => [
                    "netValue" => 0.87,
                    "grossValue" => 1.05
                ]
            ];
        })->toArray();
        $packages = collect($data->productItems)->map(function ($item, $index) {
            return [
                "typeCode" => "2BP",
                "weight" => floatval($item->product->box_with_weight ?? 1), // Convert to float
                "dimensions" => [
                    "length" => floatval($item->product->box_with_length ?? 1), // Convert to float
                    "width" => floatval($item->product->box_with_width ?? 1), // Convert to float
                    "height" => floatval($item->product->box_with_height ?? 1), // Convert to float
                ],
            ];
        })->toArray();

        $body = [
            "plannedShippingDateAndTime" => (new DateTime($data->date))->modify('+5 days')->format('Y-m-d\TH:i:s\G\M\TP'),
            "pickup" => [
                "isRequested" => true,
                "specialInstructions" => [
                    [
                        "value" => "please ring door bell",
                        "typeCode" => "TBD"
                    ]
                ],
                "pickupDetails" => [
                    "postalAddress" => [
                        "postalCode" => $site_data->zip_code,
                        "cityName" => $site_data->city,
                        "countryCode" => $site_data->country,
                        "provinceCode" => $site_data->country,
                        "addressLine1" => $site_data->address,
                        "addressLine2" => "addres2",
                        "addressLine3" => "addres3",
                        "countyName" => $site_data->site_name,
                        "provinceName" => $controller->countries($site_data->country),
                        "countryName" => $controller->countries($site_data->country)
                    ],
                    "contactInformation" => [
                        "email" => $site_data->email,
                        "phone" => $site_data->tel_cell,
                        "mobilePhone" => $site_data->tel_cell,
                        "companyName" => $site_data->site_name,
                        "fullName" => $site_data->site_name
                    ],
                    "typeCode" => "business"
                ],

            ],
            "productCode" => 'P',
            "localProductCode" => 'P',
            "getRateEstimates" => false,
            "accounts" => [
                [
                    "typeCode" => "shipper",
                    "number" => "*********"
                ]
            ],

            "customerDetails" => [
                "receiverDetails" => [
                    "postalAddress" => [
                        "postalCode" => strval($data->customer->postal_code),
                        "cityName" => $data->customer->city,
                        "countryCode" => $data->customer->country,
                        "addressLine1" => $data->customer->address,
                        "addressLine2" => "nothing",
                        "addressLine3" => "nothing",
                        "countryName" => $controller->countries($data->customer->country)
                    ],
                    "contactInformation" => [
                        "email" => $data->customer->email ?? '<EMAIL>',
                        "phone" => $data->customer->tel_cell,
                        "mobilePhone" => $data->customer->tel_cell,
                        "companyName" => $data->customer->company_name ?? 'nothing',
                        "fullName" => $data->customer->first_name . ' ' . $data->customer->last_name
                    ],
                    "typeCode" => "business"
                ],
                "shipperDetails" => [
                    "postalAddress" => [
                        "postalCode" => $site_data->zip_code,
                        "cityName" => $site_data->city,
                        "countryCode" => $site_data->country,
                        "addressLine1" => $site_data->address,
                        "addressLine2" => "nothing",
                        "addressLine3" => "nothing",
                        "countryName" => $controller->countries($site_data->country)
                    ],
                    "contactInformation" => [
                        "email" => $site_data->email,
                        "phone" => $site_data->tel_cell,
                        "mobilePhone" => $site_data->tel_cell,
                        "companyName" => $site_data->site_name,
                        "fullName" => $site_data->site_name
                    ]
                ]
            ],
            "content" => [
                "exportDeclaration" => [
                    "exportReasonType" => "commercial_purpose_or_sale",
                    "invoice" => [
                        "number" => "W417679401",
                        "date" => date('Y-m-d'),
                    ],
                    "remarks" => [["value" => "RemarkDescription1"]],
                    "lineItems" => $lineItems
                ],
                "packages" => $packages,
                "isCustomsDeclarable" => true,
                "declaredValue" => 100,
                "declaredValueCurrency" => getDefaultCurrencyCode(),
                "description" => 'P',
                "incoterm" => "DAP",
                "unitOfMeasurement" => "metric"
            ],
            "documentImages" => [
                [
                    "typeCode" => "CIN",
                    "imageFormat" => "PDF",
                    "content" => "JVBERi0xLjcNCiW1tbW1DQoxIDAgb2JqDQo8PC9UeXBlL0NhdGFsb2cvUGFnZXMgL1hSZWZTdG0gMjgwMDg+Pg0Kc3RhcnR4cmVmDQoyODcyMw0KJSVFT0Y="
                ]
            ],
            "estimatedDeliveryDate" => [
                "isRequested" => true,
                "typeCode" => "QDDC"
            ],
        ];


        try {

            $response = Http::withBasicAuth($username, $password)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'x-version' => '2.0'
                ])
                ->post($url, $body);


            if ($response->successful()) {

                return (object) [
                    'response' => $response->json(),
                    'status_code' => $response->status()

                ];
            } else {
                // Handle error response
                return (object) [
                    'response' => $response->json(),
                    'error' => 'DHL API Error: ' . ($response->json('message') ?? 'Unknown error'),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            // Handle exceptions
            return (object) [
                'response' => null,
                'error' => 'Exception: ' . $e->getMessage(),
                'shipping_methods' => []
            ];
        }
    }
    public function trackingParcel($trackingNumber)
    {
        $username = 'apB5lH4lW3qB4j';
        $password = 'M@7aW$0iD^6yJ!1d';
        $dhlTrackingUrl = "https://express.api.dhl.com/mydhlapi/test/shipments/$trackingNumber/tracking";

        $response = Http::withBasicAuth($username, $password)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'x-version' => '2.0'
            ])
            ->get($dhlTrackingUrl);

        if ($response->successful()) {
            return $response->json(); // Returns latest tracking info
        } else {
            return response()->json(['error' => 'Unable to fetch tracking details'], 400);
        }
    }

    public function landedCost($data)
    {
        // dd($data);
        $username = 'apB5lH4lW3qB4j';
        $password = 'M@7aW$0iD^6yJ!1d';;
        $url = 'https://express.api.dhl.com/mydhlapi/test/landed-cost';
        $shipmentDetails = $this->getSamplePayload($data);



        try {
            // Generate a unique message reference



            $response = Http::withHeaders([
                'content-type' => 'application/json',
                'Message-Reference' => 'ref_' . substr(md5(uniqid()), 0, 28),
                'Message-Reference-Date' => now()->toIso8601ZuluString(),
                'Plugin-Name' => 'MyPlugin',
                'Plugin-Version' => '1.0',
                'Shipping-System-Platform-Name' => 'MyPlatform',
                'Shipping-System-Platform-Version' => '1.0',
                'Webstore-Platform-Name' => 'MyWebstore',
                'Webstore-Platform-Version' => '1.0',
                'Authorization' => 'Basic ' . base64_encode('apB5lH4lW3qB4j:M@7aW$0iD^6yJ!1d')
            ])->post($url, $shipmentDetails);





            if ($response->successful()) {
                return $response->json();
            }

            Log::error('DHL Landed Cost API error', [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('DHL Landed Cost API exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    public function getSamplePayload($data)
    {


        $lineItems = collect($data->items)->map(function ($item, $index) {
            return [
                "number" => $index + 1,
                "name" => $item->title,
                "description" => $item->description ?? 'test',
                "manufacturerCountry" => "US",
                "partNumber" => "12345555",
                "quantity" => $index + 1,
                "commodityCode" => "640391",

                "quantityType" => "prt",
                "unitPrice" => (int)$item->sale_price * 0.01,
                "unitPriceCurrencyCode" => getDefaultCurrencyCode(),

                "customsValueCurrencyCode" => getDefaultCurrencyCode(),

                "weight" => 5,
                "weightUnitOfMeasurement" => "metric",


                "goodsCharacteristics" => [
                    [
                        "typeCode" => "IMPORTER",
                        "value" => "Registered"
                    ]
                ],
                "additionalQuantityDefinitions" => [
                    [
                        "typeCode" => "DPR",
                        "amount" => (int)$item->sale_price
                    ]
                ],
                "estimatedTariffRateType" => "highest_rate"

            ];
        })->toArray();




        $shipper_zipcode = (string)$data->shipper_zip;
        $reciver_zipcode = (string)$data->reciver_zip;
        return [
            "customerDetails" => [
                "shipperDetails" => [
                    "postalCode" => $shipper_zipcode,
                    "cityName" => $data->shipper_city,
                    "countryCode" => $data->shipper_country,
                ],
                "receiverDetails" => [
                    "postalCode" => $reciver_zipcode,
                    "cityName" => $data->reciver_city,
                    "countryCode" => $data->reciver_country,
                ]
            ],
            "accounts" => [
                [
                    "typeCode" => "shipper",
                    "number" => "*********"
                ]
            ],
            "productCode" => "P",
            "localProductCode" => "P",
            "unitOfMeasurement" => "metric",
            "currencyCode" => getDefaultCurrencyCode(),
            "isCustomsDeclarable" => true,


            "getCostBreakdown" => true,




            "packages" => [
                [
                    "typeCode" => "3BX",
                    "weight" => $data->weight,
                    "dimensions" => [
                        "length" => $data->length,
                        "width" => $data->width,
                        "height" => $data->height,
                    ]
                ]
            ],
            "items" => $lineItems,
            "getTariffFormula" => true,
            "getQuotationID" => false
        ];
    }
}
