<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
// use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Http\Request;
use App\Models\User;
// use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;

class ResetPasswordController extends Controller
{
    use ApiResponseHelpers;
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    // protected $redirectTo = RouteServiceProvider::HOME;

    public function redirectTo()
    {
        return isAdmin() ? '/admin' : '/' . auth()->user()->username;
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    // public function showResetForm($token)
    public function showResetForm(Request $request)
    {
        $tokenData = DB::table('password_resets')->where('token', $request->token)->first();
        if (!$tokenData) return redirect()->to('/');

        return redirect(url('/reset') . '?token=' . $request->token);
        // return view('auth.passwords.reset', compact('token'));
    }

    public function reset(Request $request): JsonResponse
    {
        $this->validate($request, [
            // 'email' => 'required|email|exists:users,email',
            'password' => 'required|confirmed',
            'token' => 'required',
            'g-recaptcha-response' => 'required|recaptchav3:reset_password,0.5',
        ]);

        $password = $request->password;
        $token = trim($request->token);
        $tokenData = DB::table('password_resets')->where('token', $token)->first();
        if (!$tokenData) return respondError('Token not found');

        $user = User::where('email', $tokenData->email)->first();
        if (!$user) return redirect()->to('/');

        $user->password = Hash::make($password);
        $user->update();

        // Auth::login($user);
        DB::table('password_resets')->where('email', $user->email)->delete();

        // $url = isAdmin() ? '/admin' : '/' . auth()->user()->username;

        // return redirect($url)->with('success', 'Your password reset now');

        return $this->respondOk('Password reset done');
    }
}
