<!doctype html>
<html>

<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>{{ config('app.name') }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,700;1,300;1,700&display=swap');
        table[class=body] table[class=order-list]{
            border-left: 1px solid #999999;
            border-top: 1px solid #999999;
        }
        table[class=body] td{
            padding: 0px;
        }
        table[class=body] table[class=order-list] td{
            padding: 5px;
            border-bottom: 1px solid #999999;
            border-right: 1px solid #999999;
        }
        @media only screen and (max-width: 620px) {
            table[class=body] h1 {
                font-size: 16px !important;
                margin-bottom: 10px !important;
            }
            table[class=body] p,
            table[class=body] ul,
            table[class=body] ol,
            table[class=body] td,
            table[class=body] span,
            table[class=body] a {
                font-size: 12px !important;
            }
            table[class=body] .wrapper,
            table[class=body] .article {
                padding: 10px !important;
            }
            table[class=body] .content {
                padding: 0 !important;
            }
            table[class=body] .container {
                padding: 0 !important;
                width: 100% !important;
            }
            table[class=body] .main {
                border-left-width: 0 !important;
                border-radius: 0 !important;
                border-right-width: 0 !important;
            }
            table[class=body] .btn table {
                width: 100% !important;
            }
            table[class=body] .btn a {
                width: 100% !important;
            }
            table[class=body] .img-responsive {
                height: auto !important;
                max-width: 100% !important;
                width: auto !important;
            }
        }
    </style>
</head>

<body style="background-color: #f6f6f6; -webkit-font-smoothing: antialiased; font-size: 14px; line-height: 1.4; margin: 0; padding: 0; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
    <!-- Pre-header Text -->
    <div style="font-size:0px;line-height:1px;mso-line-height-rule:exactly;display:none;max-width:0px;max-height:0px;opacity:0;overflow:hidden;mso-hide:all;">
        {!! $details->pre_header !!}
    </div>

    <table border="0" cellpadding="0" cellspacing="0" class="body" style="font-family: 'Montserrat', Arial, sans-serif; border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background-color: #f6f6f6;">
        <tr>
            <td class="container" style="font-size: 14px; vertical-align: top; display: block; Margin: 0 auto; max-width: 580px; padding: 10px; width: 580px;">
                <div class="content" style="box-sizing: border-box; display: block; Margin: 0 auto; max-width: 580px; padding: 10px;">
                    <div class="header" style="clear: both; Margin-top: 10px; width: 100%;">
                        <table border="0" bgcolor="#f1bb00" cellpadding="0" cellspacing="0" style="border-collapse: separate; width: 100%;">
                            <tr>
                                @if(config('app.email_template_logo'))
                                    <td class="content-block" style="vertical-align: middle; padding-left:20px; padding-bottom: 10px; padding-top: 10px; font-size: 12px; color: #999999;">
                                        <a href="{{ url('/') }}" style="color: #000000; font-size: 12px; text-align: center; text-decoration: none;">
                                            <img style="width:auto;height:50px;" src="{{ asset('storage/setting/' . config('app.email_template_logo')) }}" alt="{{ config('app.name') }}">
                                        </a>
                                    </td>
                                @else
                                    <td style="padding-bottom: 15px; padding-left:20px;">
                                        <h2 style="margin-bottom: 0px">{{ config('app.name') }}</h2>
                                        @if(config('app.site_slogan'))
                                            <p style="margin-bottom: 0px;margin-top:0px;">{{ config('app.site_slogan') }}</p>
                                        @endif
                                    </td>
                                @endif
                                <td style="vertical-align: middle; padding-right:20px; padding-bottom: 10px; padding-top: 10px; text-align:right;">
                                    <strong style="color: #000000;font-weight: bold;font-size:17px; display:block;line-height:17px;">PHONE</strong>
                                    <span style="line-height:17px;color: #000000; font-weight: bold; font-size:17px;">{{ config('app.tel_cell') }}</span>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <table class="main" style="border-collapse: separate; width: 100%; background: #ffffff; border-radius: 3px;">
                        <tr>
                            <td class="wrapper" style="font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 20px;">
                                {!! $details->content !!}
                            </td>
                        </tr>
                    </table>

                    <div class="footer" style="clear: both; Margin-top: 10px; text-align: center; width: 100%;">
                        <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; width: 100%;">
                            <tr>
                                <td class="content-block" style="vertical-align: top; padding-bottom: 10px; padding-top: 10px; font-size: 12px; color: #999999; text-align: center;">
                                    <span class="apple-link" style="color: #999999; font-size: 14px; text-align: center;">
                                        <b>{{ config('app.name') }}</b><br>
                                        {{ config('app.address') }} - {{ config('app.zip_code') }}, {{ config('app.country') }}
                                    </span>
                                    <br>
                                    <span style="color: #999999; font-size: 14px; text-align: center;">If we served you right, refer someone to us!</span>
                                    <br>
                                    <span>&copy; 2018 - {{ date('Y') }} {{ config('app.name') }}</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</body>

</html>
