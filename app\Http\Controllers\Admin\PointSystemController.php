<?php

namespace App\Http\Controllers\Admin;

use App\Models\PointSystem;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PointSystemController extends Controller
{


    public function index()
    {
        $admin_page_title = 'Points System';

        $pointSystems = PointSystem::paginate(10);

        $sl = 1;
        return view('admin.points_system.index', compact('pointSystems', 'admin_page_title', 'sl'));
    }

    public function create()
    {
        $countries = $this->countries();
        $admin_page_title = 'Points System';
        return view('admin.points_system.create', compact('admin_page_title', 'countries'));
    }

    // Store a new point system
    public function store(Request $request)
    {
        $request->validate([
            'country' => 'required',
            'order_points' => 'nullable|numeric',
            'comment_points' => 'nullable|numeric',
            'photo_points' => 'nullable|numeric',
            'video_points' => 'nullable|numeric',
        ]);

        PointSystem::create($request->all());

        return redirect()->route('admin.points-system.index')->with('success', 'Points System created successfully');
    }

    public function edit(PointSystem $pointSystem)
    {
        // dd($pointSystem);
        $countries = $this->countries();
        // dd($countries);
        $admin_page_title = 'Points System';

        return view('admin.points_system.edit', compact('pointSystem', 'countries', 'admin_page_title'));
    }

    public function update(Request $request, PointSystem $pointSystem)
    {
        // Validate input
        $request->validate([
            'country' => 'required|string',
            'order_points' => 'nullable|numeric',
            'comment_points' => 'nullable|numeric',
            'photo_points' => 'nullable|numeric',
            'video_points' => 'nullable|numeric',
        ]);

        $pointSystem->update($request->all());

        return redirect()->route('admin.points-system.index')->with('success', 'Points System updated successfully.');
    }


    public function destroy(PointSystem $pointSystem)
    {
        $pointSystem->delete();

        return redirect()->route('admin.points-system.index')->with('success', 'Points System deleted successfully');
    }
}