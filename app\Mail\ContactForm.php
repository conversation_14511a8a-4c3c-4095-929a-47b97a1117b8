<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\EmailTemplate;

class ContactForm extends Mailable
{
    use Queueable, SerializesModels;

    public $mailData = null;
    public $content = null;
    public $pre_header = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($mailData)
    {
        $email_template = EmailTemplate::find(3);
        $data = [
            ucfirst($mailData->contact_first_name),
            ucfirst($mailData->contact_last_name),
        ];
        $this->subject = $email_template->subject;
        $this->pre_header = $email_template->pre_header;
        $this->content = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->email_body));
        if ($email_template->sms_status) {
            $message = str_replace(app('App\Http\Controllers\Controller')->email_constants(), $data, unserialize($email_template->sms_body));
            if ($checkout_order->customer->tel_cell) {
                app('App\Http\Controllers\Controller')->sendMessage($message, $checkout_order->customer->tel_cell);
            }
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)->view('email_templates.order_confirmed');
    }
}
